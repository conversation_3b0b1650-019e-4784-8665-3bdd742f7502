{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter as _DomAdapter, ɵsetRootDomAdapter as _setRootDomAdapter, ɵparseCookieValue as _parseCookieValue, ɵgetDOM as _getDOM, DOCUMENT, CommonModule, XhrFactory, ɵPLATFORM_BROWSER_ID as _PLATFORM_BROWSER_ID } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal as _global, ɵRuntimeError as _RuntimeError, Injectable, Inject, createPlatformFactory, ɵinternalCreateApplication as _internalCreateApplication, inject, InjectionToken, ApplicationModule, ɵINJECTOR_SCOPE as _INJECTOR_SCOPE, ErrorHandler, RendererFactory2, ɵTESTABILITY_GETTER as _TESTABILITY_GETTER, NgZone, TestabilityRegistry, Testability, ɵTESTABILITY as _TESTABILITY, platformCore, PLATFORM_ID, PLATFORM_INITIALIZER, ɵsetDocument as _setDocument, NgModule } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS, DomRendererFactory2, SharedStylesHost, EventManager } from './dom_renderer-BMDc99h8.mjs';\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass BrowserDomAdapter extends _DomAdapter {\n  supportsDOMEvents = true;\n  static makeCurrent() {\n    _setRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener, options) {\n    el.addEventListener(evt, listener, options);\n    return () => {\n      el.removeEventListener(evt, listener, options);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    node.remove();\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return _parseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.head.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    _global['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new _RuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    _global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    _global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = _global['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!_global['frameworkStabilizers']) {\n      _global['frameworkStabilizers'] = [];\n    }\n    _global['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (_getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n  static ɵfac = function BrowserXhr_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserXhr)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserXhr,\n    factory: BrowserXhr.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler, options) {\n    element.addEventListener(eventName, handler, options);\n    return () => this.removeEventListener(element, eventName, handler, options);\n  }\n  removeEventListener(target, eventName, callback, options) {\n    return target.removeEventListener(eventName, callback, options);\n  }\n  static ɵfac = function DomEventsPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomEventsPlugin,\n    factory: DomEventsPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return _getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n  static ɵfac = function KeyEventsPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: KeyEventsPlugin,\n    factory: KeyEventsPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return _internalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return _internalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  _setDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: _PLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: _TESTABILITY_GETTER,\n  useClass: BrowserGetTestability\n}, {\n  provide: _TESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  // Also provide as `Testability` for backwards-compatibility.\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: _INJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n        optional: true,\n        skipSelf: true\n      });\n      if (providersAlreadyPresent) {\n        throw new _RuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n      }\n    }\n  }\n  static ɵfac = function BrowserModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n    imports: [CommonModule, ApplicationModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], () => [], null);\n})();\nexport { BrowserDomAdapter, BrowserGetTestability, BrowserModule, DomEventsPlugin, KeyEventsPlugin, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport };", "map": {"version": 3, "names": ["ɵDomAdapter", "_DomAdapter", "ɵsetRootDomAdapter", "_setRootDomAdapter", "ɵparseCookieValue", "_parseCookieV<PERSON>ue", "ɵgetDOM", "_getDOM", "DOCUMENT", "CommonModule", "XhrFactory", "ɵPLATFORM_BROWSER_ID", "_PLATFORM_BROWSER_ID", "i0", "ɵglobal", "_global", "ɵRuntimeError", "_RuntimeError", "Injectable", "Inject", "createPlatformFactory", "ɵinternalCreateApplication", "_internalCreateApplication", "inject", "InjectionToken", "ApplicationModule", "ɵINJECTOR_SCOPE", "_INJECTOR_SCOPE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RendererFactory2", "ɵTESTABILITY_GETTER", "_TESTABILITY_GETTER", "NgZone", "TestabilityRegistry", "Testability", "ɵTESTABILITY", "_TESTABILITY", "platformCore", "PLATFORM_ID", "PLATFORM_INITIALIZER", "ɵsetDocument", "_setDocument", "NgModule", "EventManagerPlugin", "EVENT_MANAGER_PLUGINS", "DomRendererFactory2", "SharedStylesHost", "EventManager", "BrowserDomAdapter", "supportsDOMEvents", "makeCurrent", "onAndCancel", "el", "evt", "listener", "options", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "head", "querySelector", "getAttribute", "url", "URL", "baseURI", "pathname", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "ngDevMode", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "length", "decrement", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "BrowserXhr_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ɵsetClassMetadata", "type", "DomEventsPlugin", "constructor", "supports", "eventName", "element", "handler", "DomEventsPlugin_Factory", "ɵɵinject", "undefined", "decorators", "args", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "event", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "manager", "getZone", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "runGuarded", "keyName", "KeyEventsPlugin_Factory", "bootstrapApplication", "rootComponent", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "provide", "useValue", "multi", "useFactory", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "deps", "useExisting", "BrowserModule", "providersAlreadyPresent", "optional", "skipSelf", "BrowserModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/platform-browser/fesm2022/browser-BcrUoxR1.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter as _DomAdapter, ɵsetRootDomAdapter as _setRootDomAdapter, ɵparseCookieValue as _parseCookieValue, ɵgetDOM as _getDOM, DOCUMENT, CommonModule, XhrFactory, ɵPLATFORM_BROWSER_ID as _PLATFORM_BROWSER_ID } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal as _global, ɵRuntimeError as _RuntimeError, Injectable, Inject, createPlatformFactory, ɵinternalCreateApplication as _internalCreateApplication, inject, InjectionToken, ApplicationModule, ɵINJECTOR_SCOPE as _INJECTOR_SCOPE, ErrorHandler, RendererFactory2, ɵTESTABILITY_GETTER as _TESTABILITY_GETTER, NgZone, TestabilityRegistry, Testability, ɵTESTABILITY as _TESTABILITY, platformCore, PLATFORM_ID, PLATFORM_INITIALIZER, ɵsetDocument as _setDocument, NgModule } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS, DomRendererFactory2, SharedStylesHost, EventManager } from './dom_renderer-BMDc99h8.mjs';\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass BrowserDomAdapter extends _DomAdapter {\n    supportsDOMEvents = true;\n    static makeCurrent() {\n        _setRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener, options) {\n        el.addEventListener(evt, listener, options);\n        return () => {\n            el.removeEventListener(evt, listener, options);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        node.remove();\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return _parseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.head.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n    // The base URL doesn't really matter, we just need it so relative paths have something\n    // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n    return new URL(url, document.baseURI).pathname;\n}\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        _global['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new _RuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'Could not find testability for element.');\n            }\n            return testability;\n        };\n        _global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        _global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback) => {\n            const testabilities = _global['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            const decrement = function () {\n                count--;\n                if (count == 0) {\n                    callback();\n                }\n            };\n            testabilities.forEach((testability) => {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!_global['frameworkStabilizers']) {\n            _global['frameworkStabilizers'] = [];\n        }\n        _global['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (_getDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserXhr });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler, options) {\n        element.addEventListener(eventName, handler, options);\n        return () => this.removeEventListener(element, eventName, handler, options);\n    }\n    removeEventListener(target, eventName, callback, options) {\n        return target.removeEventListener(eventName, callback, options);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DomEventsPlugin });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS',\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey,\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler, options) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return _getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach((modifierName) => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach((modifierName) => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        return keyName === 'esc' ? 'escape' : keyName;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: KeyEventsPlugin });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return _internalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return _internalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app\n    // code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    _setDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: _PLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: _TESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n    },\n    {\n        provide: _TESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER],\n    },\n    {\n        provide: Testability, // Also provide as `Testability` for backwards-compatibility.\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER],\n    },\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: _INJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler },\n    {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT],\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] },\n    DomRendererFactory2,\n    SharedStylesHost,\n    EventManager,\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: XhrFactory, useClass: BrowserXhr },\n    typeof ngDevMode === 'undefined' || ngDevMode\n        ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true }\n        : [],\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n                optional: true,\n                skipSelf: true,\n            });\n            if (providersAlreadyPresent) {\n                throw new _RuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                    `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserModule, providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS], imports: [CommonModule, ApplicationModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: () => [] });\n\nexport { BrowserDomAdapter, BrowserGetTestability, BrowserModule, DomEventsPlugin, KeyEventsPlugin, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,IAAIC,WAAW,EAAEC,kBAAkB,IAAIC,kBAAkB,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,OAAO,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAEC,oBAAoB,IAAIC,oBAAoB,QAAQ,iBAAiB;AACpP,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,IAAIC,OAAO,EAAEC,aAAa,IAAIC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,0BAA0B,IAAIC,0BAA0B,EAAEC,MAAM,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,eAAe,IAAIC,eAAe,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,WAAW,EAAEC,YAAY,IAAIC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACnf,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,6BAA6B;;AAE5I;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAAS/C,WAAW,CAAC;EACxCgD,iBAAiB,GAAG,IAAI;EACxB,OAAOC,WAAWA,CAAA,EAAG;IACjB/C,kBAAkB,CAAC,IAAI6C,iBAAiB,CAAC,CAAC,CAAC;EAC/C;EACAG,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACpCH,EAAE,CAACI,gBAAgB,CAACH,GAAG,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IAC3C,OAAO,MAAM;MACTH,EAAE,CAACK,mBAAmB,CAACJ,GAAG,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IAClD,CAAC;EACL;EACAG,aAAaA,CAACN,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACM,aAAa,CAACL,GAAG,CAAC;EACzB;EACAM,MAAMA,CAACC,IAAI,EAAE;IACTA,IAAI,CAACD,MAAM,CAAC,CAAC;EACjB;EACAE,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkBA,CAAA,EAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAaA,CAACT,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACU,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAYA,CAACb,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYc,gBAAgB;EAC3C;EACA;EACAC,oBAAoBA,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,CAAC,CAAC;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgBA,CAAA,EAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOpF,iBAAiB,CAAC6D,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkBA,CAAA,EAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,IAAI,CAACC,aAAa,CAAC,MAAM,CAAC;EAChE,OAAOR,WAAW,GAAGA,WAAW,CAACS,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA,SAASX,YAAYA,CAACY,GAAG,EAAE;EACvB;EACA;EACA,OAAO,IAAIC,GAAG,CAACD,GAAG,EAAE5B,QAAQ,CAAC8B,OAAO,CAAC,CAACC,QAAQ;AAClD;AAEA,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAE;IAClBrF,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAACsF,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAItF,aAAa,CAAC,IAAI,CAAC,8CAA8C,CAAC,OAAOwF,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,yCAAyC,CAAC;MAClD;MACA,OAAOF,WAAW;IACtB,CAAC;IACDxF,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAMqF,QAAQ,CAACM,mBAAmB,CAAC,CAAC;IAC5E3F,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAMqF,QAAQ,CAACO,kBAAkB,CAAC,CAAC;IAC1E,MAAMC,aAAa,GAAIC,QAAQ,IAAK;MAChC,MAAMC,aAAa,GAAG/F,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;MAC7D,IAAIgG,KAAK,GAAGD,aAAa,CAACE,MAAM;MAChC,MAAMC,SAAS,GAAG,SAAAA,CAAA,EAAY;QAC1BF,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAAC,CAAC;QACd;MACJ,CAAC;MACDC,aAAa,CAACI,OAAO,CAAEX,WAAW,IAAK;QACnCA,WAAW,CAACY,UAAU,CAACF,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAClG,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAACqG,IAAI,CAACR,aAAa,CAAC;EACvD;EACAJ,qBAAqBA,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMgB,CAAC,GAAGjB,QAAQ,CAACkB,cAAc,CAACjB,IAAI,CAAC;IACvC,IAAIgB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAACf,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAI/F,OAAO,CAAC,CAAC,CAACkE,YAAY,CAAC4B,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAACf,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACmB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,cAAc,CAAC,CAAC;EAC/B;EACA,OAAOC,IAAI,YAAAC,mBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFL,UAAU;EAAA;EAC7G,OAAOM,KAAK,kBAD6ElH,EAAE,CAAAmH,kBAAA;IAAAC,KAAA,EACYR,UAAU;IAAAS,OAAA,EAAVT,UAAU,CAAAG;EAAA;AACrH;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAH6F5F,EAAE,CAAAsH,iBAAA,CAGJV,UAAU,EAAc,CAAC;IACxGW,IAAI,EAAElH;EACV,CAAC,CAAC;AAAA;AAEV,MAAMmH,eAAe,SAAS1F,kBAAkB,CAAC;EAC7C2F,WAAWA,CAACvE,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACAwE,QAAQA,CAACC,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACAhF,gBAAgBA,CAACiF,OAAO,EAAED,SAAS,EAAEE,OAAO,EAAEnF,OAAO,EAAE;IACnDkF,OAAO,CAACjF,gBAAgB,CAACgF,SAAS,EAAEE,OAAO,EAAEnF,OAAO,CAAC;IACrD,OAAO,MAAM,IAAI,CAACE,mBAAmB,CAACgF,OAAO,EAAED,SAAS,EAAEE,OAAO,EAAEnF,OAAO,CAAC;EAC/E;EACAE,mBAAmBA,CAACmB,MAAM,EAAE4D,SAAS,EAAE3B,QAAQ,EAAEtD,OAAO,EAAE;IACtD,OAAOqB,MAAM,CAACnB,mBAAmB,CAAC+E,SAAS,EAAE3B,QAAQ,EAAEtD,OAAO,CAAC;EACnE;EACA,OAAOqE,IAAI,YAAAe,wBAAAb,iBAAA;IAAA,YAAAA,iBAAA,IAAwFO,eAAe,EAvBzBxH,EAAE,CAAA+H,QAAA,CAuByCpI,QAAQ;EAAA;EAC5I,OAAOuH,KAAK,kBAxB6ElH,EAAE,CAAAmH,kBAAA;IAAAC,KAAA,EAwBYI,eAAe;IAAAH,OAAA,EAAfG,eAAe,CAAAT;EAAA;AAC1H;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA1B6F5F,EAAE,CAAAsH,iBAAA,CA0BJE,eAAe,EAAc,CAAC;IAC7GD,IAAI,EAAElH;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkH,IAAI,EAAES,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CV,IAAI,EAAEjH,MAAM;MACZ4H,IAAI,EAAE,CAACvI,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA,MAAMwI,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGC,KAAK,IAAKA,KAAK,CAACC,MAAM;EAC9B,SAAS,EAAGD,KAAK,IAAKA,KAAK,CAACE,OAAO;EACnC,MAAM,EAAGF,KAAK,IAAKA,KAAK,CAACG,OAAO;EAChC,OAAO,EAAGH,KAAK,IAAKA,KAAK,CAACI;AAC9B,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,SAAS7G,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACI2F,WAAWA,CAACvE,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACIwE,QAAQA,CAACC,SAAS,EAAE;IAChB,OAAOgB,eAAe,CAACC,cAAc,CAACjB,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhF,gBAAgBA,CAACiF,OAAO,EAAED,SAAS,EAAEE,OAAO,EAAEnF,OAAO,EAAE;IACnD,MAAMmG,WAAW,GAAGF,eAAe,CAACC,cAAc,CAACjB,SAAS,CAAC;IAC7D,MAAMmB,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAEhB,OAAO,EAAE,IAAI,CAACmB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IAC7G,OAAO,IAAI,CAACD,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,iBAAiB,CAAC,MAAM;MAClD,OAAOxJ,OAAO,CAAC,CAAC,CAAC4C,WAAW,CAACsF,OAAO,EAAEiB,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,EAAEpG,OAAO,CAAC;IAC/F,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOkG,cAAcA,CAACjB,SAAS,EAAE;IAC7B,MAAMwB,KAAK,GAAGxB,SAAS,CAACyB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC;IAClC,IAAIJ,KAAK,CAAChD,MAAM,KAAK,CAAC,IAAI,EAAEmD,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACjF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGb,eAAe,CAACc,aAAa,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACAxB,aAAa,CAAC9B,OAAO,CAAE0D,YAAY,IAAK;MACpC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAAChD,MAAM,IAAI,CAAC,IAAIqD,GAAG,CAACrD,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAM8D,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqBA,CAAC5B,KAAK,EAAE6B,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAGhC,OAAO,CAACE,KAAK,CAACkB,GAAG,CAAC,IAAIlB,KAAK,CAACkB,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAG9B,KAAK,CAAC+B,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,CAAC,CAAC;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;IACAjC,aAAa,CAAC9B,OAAO,CAAE0D,YAAY,IAAK;MACpC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAGjC,oBAAoB,CAAC0B,YAAY,CAAC;QACzD,IAAIO,cAAc,CAAChC,KAAK,CAAC,EAAE;UACvBkB,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOpB,aAAaA,CAACY,OAAO,EAAE9B,OAAO,EAAE0C,IAAI,EAAE;IACzC,OAAQjC,KAAK,IAAK;MACd,IAAIK,eAAe,CAACuB,qBAAqB,CAAC5B,KAAK,EAAEqB,OAAO,CAAC,EAAE;QACvDY,IAAI,CAACC,UAAU,CAAC,MAAM3C,OAAO,CAACS,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOmB,aAAaA,CAACgB,OAAO,EAAE;IAC1B,OAAOA,OAAO,KAAK,KAAK,GAAG,QAAQ,GAAGA,OAAO;EACjD;EACA,OAAO1D,IAAI,YAAA2D,wBAAAzD,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0B,eAAe,EAnMzB3I,EAAE,CAAA+H,QAAA,CAmMyCpI,QAAQ;EAAA;EAC5I,OAAOuH,KAAK,kBApM6ElH,EAAE,CAAAmH,kBAAA;IAAAC,KAAA,EAoMYuB,eAAe;IAAAtB,OAAA,EAAfsB,eAAe,CAAA5B;EAAA;AAC1H;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAtM6F5F,EAAE,CAAAsH,iBAAA,CAsMJqB,eAAe,EAAc,CAAC;IAC7GpB,IAAI,EAAElH;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkH,IAAI,EAAES,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CV,IAAI,EAAEjH,MAAM;MACZ4H,IAAI,EAAE,CAACvI,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgL,oBAAoBA,CAACC,aAAa,EAAElI,OAAO,EAAE;EAClD,OAAOjC,0BAA0B,CAAC;IAAEmK,aAAa;IAAE,GAAGC,qBAAqB,CAACnI,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoI,iBAAiBA,CAACpI,OAAO,EAAE;EAChC,OAAOjC,0BAA0B,CAACoK,qBAAqB,CAACnI,OAAO,CAAC,CAAC;AACrE;AACA,SAASmI,qBAAqBA,CAACnI,OAAO,EAAE;EACpC,OAAO;IACHqI,YAAY,EAAE,CAAC,GAAGC,wBAAwB,EAAE,IAAItI,OAAO,EAAEuI,SAAS,IAAI,EAAE,CAAC,CAAC;IAC1EC,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC;EACA;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAcA,CAAA,EAAG;EACtBnJ,iBAAiB,CAACE,WAAW,CAAC,CAAC;AACnC;AACA,SAASkJ,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAIxK,YAAY,CAAC,CAAC;AAC7B;AACA,SAASyK,SAASA,CAAA,EAAG;EACjB;EACA5J,YAAY,CAACyB,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAM8H,mCAAmC,GAAG,CACxC;EAAEM,OAAO,EAAEhK,WAAW;EAAEiK,QAAQ,EAAE3L;AAAqB,CAAC,EACxD;EAAE0L,OAAO,EAAE/J,oBAAoB;EAAEgK,QAAQ,EAAEJ,cAAc;EAAEK,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEF,OAAO,EAAE9L,QAAQ;EAAEiM,UAAU,EAAEJ;AAAU,CAAC,CAC/C;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,eAAe,GAAGtL,qBAAqB,CAACiB,YAAY,EAAE,SAAS,EAAE2J,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,+BAA+B,GAAG,IAAInL,cAAc,CAAC,OAAOiF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AACjJ,MAAMyF,qBAAqB,GAAG,CAC1B;EACII,OAAO,EAAEvK,mBAAmB;EAC5B6K,QAAQ,EAAE1G;AACd,CAAC,EACD;EACIoG,OAAO,EAAElK,YAAY;EACrBwK,QAAQ,EAAE1K,WAAW;EACrB2K,IAAI,EAAE,CAAC7K,MAAM,EAAEC,mBAAmB,EAAEF,mBAAmB;AAC3D,CAAC,EACD;EACIuK,OAAO,EAAEpK,WAAW;EAAE;EACtB0K,QAAQ,EAAE1K,WAAW;EACrB2K,IAAI,EAAE,CAAC7K,MAAM,EAAEC,mBAAmB,EAAEF,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAM8J,wBAAwB,GAAG,CAC7B;EAAES,OAAO,EAAE3K,eAAe;EAAE4K,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAED,OAAO,EAAE1K,YAAY;EAAE6K,UAAU,EAAEL;AAAa,CAAC,EACnD;EACIE,OAAO,EAAE1J,qBAAqB;EAC9BgK,QAAQ,EAAEvE,eAAe;EACzBmE,KAAK,EAAE,IAAI;EACXK,IAAI,EAAE,CAACrM,QAAQ;AACnB,CAAC,EACD;EAAE8L,OAAO,EAAE1J,qBAAqB;EAAEgK,QAAQ,EAAEpD,eAAe;EAAEgD,KAAK,EAAE,IAAI;EAAEK,IAAI,EAAE,CAACrM,QAAQ;AAAE,CAAC,EAC5FqC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZ;EAAEuJ,OAAO,EAAEzK,gBAAgB;EAAEiL,WAAW,EAAEjK;AAAoB,CAAC,EAC/D;EAAEyJ,OAAO,EAAE5L,UAAU;EAAEkM,QAAQ,EAAEnF;AAAW,CAAC,EAC7C,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;EAAE6F,OAAO,EAAEK,+BAA+B;EAAEJ,QAAQ,EAAE;AAAK,CAAC,GAC5D,EAAE,CACX;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,aAAa,CAAC;EAChBzE,WAAWA,CAAA,EAAG;IACV,IAAI,OAAO7B,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMuG,uBAAuB,GAAGzL,MAAM,CAACoL,+BAA+B,EAAE;QACpEM,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACd,CAAC,CAAC;MACF,IAAIF,uBAAuB,EAAE;QACzB,MAAM,IAAI/L,aAAa,CAAC,IAAI,CAAC,sDAAsD,oFAAoF,GACnK,mFAAmF,CAAC;MAC5F;IACJ;EACJ;EACA,OAAO2G,IAAI,YAAAuF,sBAAArF,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiF,aAAa;EAAA;EAChH,OAAOK,IAAI,kBAzY8EvM,EAAE,CAAAwM,gBAAA;IAAAjF,IAAA,EAyYS2E;EAAa;EACjH,OAAOO,IAAI,kBA1Y8EzM,EAAE,CAAA0M,gBAAA;IAAAzB,SAAA,EA0YmC,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;IAAAsB,OAAA,GAAY/M,YAAY,EAAEgB,iBAAiB;EAAA;AACpO;AACA;EAAA,QAAAgF,SAAA,oBAAAA,SAAA,KA5Y6F5F,EAAE,CAAAsH,iBAAA,CA4YJ4E,aAAa,EAAc,CAAC;IAC3G3E,IAAI,EAAE1F,QAAQ;IACdqG,IAAI,EAAE,CAAC;MACC+C,SAAS,EAAE,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAClEuB,OAAO,EAAE,CAAChN,YAAY,EAAEgB,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASuB,iBAAiB,EAAEkD,qBAAqB,EAAE6G,aAAa,EAAE1E,eAAe,EAAEmB,eAAe,EAAEgC,oBAAoB,EAAEG,iBAAiB,EAAEe,eAAe,EAAET,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}