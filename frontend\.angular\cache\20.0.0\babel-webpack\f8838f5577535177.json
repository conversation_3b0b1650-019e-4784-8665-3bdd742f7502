{"ast": null, "code": "import { BehaviorSubject, timer } from 'rxjs';\nimport { takeWhile, tap, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class RateLimitService {\n  constructor() {\n    this.STORAGE_KEY = 'rateLimit_status';\n    this.rateLimitSubject = new BehaviorSubject(this.getInitialStatus());\n    this.rateLimit$ = this.rateLimitSubject.asObservable();\n    // Clear any corrupted localStorage data first\n    this.clearCorruptedData();\n    // Restore any valid active rate limit from localStorage on service init\n    this.restoreFromStorage();\n  }\n  /**\n   * Clear corrupted or invalid data from localStorage\n   */\n  clearCorruptedData() {\n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      if (stored) {\n        const data = JSON.parse(stored);\n        // Check for corrupted data (extremely large values indicate corruption)\n        if (data.remainingTime > 3600 || data.retryAfter > 3600 || data.resetTime > Date.now() / 1000 + 3600) {\n          console.warn('🧹 Clearing corrupted rate limit data from localStorage');\n          localStorage.removeItem(this.STORAGE_KEY);\n        }\n      }\n    } catch {\n      // Clear if JSON parsing fails\n      localStorage.removeItem(this.STORAGE_KEY);\n    }\n  }\n  /**\n   * Get initial status (either from localStorage or default)\n   */\n  getInitialStatus() {\n    const stored = this.getStoredStatus();\n    if (stored && stored.isRateLimited && stored.remainingTime > 0) {\n      return stored;\n    }\n    return {\n      isRateLimited: false,\n      retryAfter: 0,\n      remainingTime: 0,\n      message: '',\n      limit: 0,\n      remaining: 0,\n      resetTime: 0\n    };\n  } /**\n    * Restore rate limit status from localStorage and continue countdown if needed\n    */\n  restoreFromStorage() {\n    const stored = this.getStoredStatus();\n    if (!stored || !stored.isRateLimited) return;\n    const now = Math.floor(Date.now() / 1000); // Current time in seconds\n    const resetTime = stored.resetTime; // Already in seconds from backend\n    // Validate the stored data to prevent corruption\n    if (resetTime <= 0 || resetTime < now - 3600 || resetTime > now + 3600) {\n      console.warn('🧹 Invalid resetTime detected, clearing rate limit data');\n      this.clearRateLimit();\n      return;\n    }\n    // Calculate remaining time directly from reset time\n    const remainingTime = Math.max(0, resetTime - now);\n    if (remainingTime > 0 && remainingTime <= 3600) {\n      // Max 1 hour to prevent corruption\n      // Directly update status and start countdown with remaining time\n      this.updateRateLimitStatus({\n        isRateLimited: true,\n        retryAfter: stored.retryAfter,\n        remainingTime,\n        message: this.generateRateLimitMessage(remainingTime, stored.limit),\n        limit: stored.limit,\n        remaining: 0,\n        resetTime: stored.resetTime\n      });\n      // Start countdown from remaining time\n      this.startCountdownFromRemainingTime(remainingTime, stored);\n    } else {\n      // Rate limit has expired or data is invalid\n      this.clearRateLimit();\n    }\n  }\n  /**\n   * Start countdown from a specific remaining time (for restore from storage)\n   */\n  startCountdownFromRemainingTime(remainingTime, originalStatus) {\n    // Stop any existing countdown\n    if (this.countdownSubscription) {\n      this.countdownSubscription.unsubscribe();\n    }\n    let currentRemainingTime = remainingTime;\n    // Start countdown\n    this.countdownSubscription = timer(0, 1000).pipe(takeWhile(() => currentRemainingTime > 0), tap(() => {\n      currentRemainingTime--;\n      this.updateRateLimitStatus({\n        isRateLimited: currentRemainingTime > 0,\n        retryAfter: originalStatus.retryAfter,\n        remainingTime: Math.max(0, currentRemainingTime),\n        message: currentRemainingTime > 0 ? this.generateRateLimitMessage(currentRemainingTime, originalStatus.limit) : 'Rate limit has been reset. You can try again now.',\n        limit: originalStatus.limit,\n        remaining: currentRemainingTime <= 0 ? originalStatus.limit : 0,\n        resetTime: originalStatus.resetTime\n      });\n    }), finalize(() => {\n      // Clear when countdown completes\n      this.clearRateLimit();\n    })).subscribe();\n  }\n  /**\n   * Get stored rate limit status from localStorage\n   */\n  getStoredStatus() {\n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      return stored ? JSON.parse(stored) : null;\n    } catch {\n      return null;\n    }\n  }\n  /**\n   * Save rate limit status to localStorage\n   */\n  saveToStorage(status) {\n    try {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));\n    } catch {\n      // Ignore localStorage errors\n    }\n  }\n  /**\n   * Clear rate limit from localStorage\n   */\n  clearStorage() {\n    try {\n      localStorage.removeItem(this.STORAGE_KEY);\n    } catch {\n      // Ignore localStorage errors\n    }\n  } /**\n    * Handle rate limit response from server\n    */\n  handleRateLimitResponse(headers, error) {\n    const retryAfter = parseInt(headers['retry-after'] || headers['x-ratelimit-retryafter'] || '0');\n    const limit = parseInt(headers['x-ratelimit-limit'] || '0');\n    const remaining = parseInt(headers['x-ratelimit-remaining'] || '0');\n    const resetTime = parseInt(headers['x-ratelimit-reset'] || '0'); // Already in seconds from backend\n    const totalRequests = parseInt(headers['x-ratelimit-totalrequests'] || '0');\n    if (retryAfter > 0) {\n      this.startRateLimitCountdown(retryAfter, limit, remaining, resetTime, totalRequests);\n    } else {\n      this.updateRateLimitStatus({\n        isRateLimited: false,\n        retryAfter: 0,\n        remainingTime: 0,\n        message: '',\n        limit,\n        remaining,\n        resetTime\n      });\n    }\n  }\n  /**\n   * Start countdown timer for rate limit\n   */\n  startRateLimitCountdown(retryAfter, limit, remaining, resetTime, totalRequests) {\n    // Stop any existing countdown\n    if (this.countdownSubscription) {\n      this.countdownSubscription.unsubscribe();\n    }\n    let remainingTime = retryAfter;\n    // Initial status\n    this.updateRateLimitStatus({\n      isRateLimited: true,\n      retryAfter,\n      remainingTime,\n      message: this.generateRateLimitMessage(remainingTime, totalRequests),\n      limit,\n      remaining: 0,\n      resetTime\n    });\n    // Start countdown\n    this.countdownSubscription = timer(0, 1000).pipe(takeWhile(() => remainingTime > 0), tap(() => {\n      remainingTime--;\n      this.updateRateLimitStatus({\n        isRateLimited: remainingTime > 0,\n        retryAfter,\n        remainingTime: Math.max(0, remainingTime),\n        message: remainingTime > 0 ? this.generateRateLimitMessage(remainingTime, totalRequests) : 'Rate limit has been reset. You can try again now.',\n        limit,\n        remaining: remainingTime <= 0 ? limit : 0,\n        resetTime\n      });\n    }), finalize(() => {\n      // Rate limit period has ended\n      this.clearRateLimit();\n    })).subscribe();\n  }\n  /**\n   * Generate user-friendly rate limit message\n   */\n  generateRateLimitMessage(remainingTime, totalRequests) {\n    const minutes = Math.floor(remainingTime / 60);\n    const seconds = remainingTime % 60;\n    let timeString = '';\n    if (minutes > 0) {\n      timeString = `${minutes} minute${minutes > 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}`;\n    } else {\n      timeString = `${seconds} second${seconds !== 1 ? 's' : ''}`;\n    }\n    return `🚫 Rate limit exceeded! You've made too many requests. Please wait ${timeString} before trying again. (Total requests: ${totalRequests})`;\n  }\n  /**\n   * Update rate limit status\n   */\n  updateRateLimitStatus(status) {\n    this.rateLimitSubject.next(status);\n    // Save to localStorage for persistence across page reloads\n    this.saveToStorage(status);\n  }\n  /**\n   * Clear rate limit status\n   */\n  clearRateLimit() {\n    if (this.countdownSubscription) {\n      this.countdownSubscription.unsubscribe();\n      this.countdownSubscription = undefined;\n    }\n    this.updateRateLimitStatus({\n      isRateLimited: false,\n      retryAfter: 0,\n      remainingTime: 0,\n      message: '',\n      limit: 0,\n      remaining: 0,\n      resetTime: 0\n    });\n    // Clear from localStorage\n    this.clearStorage();\n  }\n  /**\n   * Get current rate limit status (synchronous)\n   */\n  getCurrentStatus() {\n    return this.rateLimitSubject.value;\n  }\n  /**\n   * Check if currently rate limited\n   */\n  isRateLimited() {\n    return this.rateLimitSubject.value.isRateLimited;\n  }\n  /**\n   * Get remaining requests\n   */\n  getRemainingRequests() {\n    return this.rateLimitSubject.value.remaining;\n  }\n  /**\n   * Format time remaining for display\n   */\n  static formatTimeRemaining(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    if (minutes === 0) {\n      return `${remainingSeconds}s`;\n    }\n    return `${minutes}m ${remainingSeconds}s`;\n  }\n  /**\n   * Debug method to trigger rate limit popup (for testing)\n   */\n  debugTriggerRateLimit(seconds = 60) {\n    console.log('🧪 Debug: Triggering rate limit popup for', seconds, 'seconds');\n    this.startRateLimitCountdown(seconds, 2, 0, Math.floor(Date.now() / 1000) + seconds, 2);\n  }\n  static #_ = this.ɵfac = function RateLimitService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RateLimitService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RateLimitService,\n    factory: RateLimitService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "timer", "<PERSON><PERSON><PERSON><PERSON>", "tap", "finalize", "RateLimitService", "constructor", "STORAGE_KEY", "rateLimitSubject", "getInitialStatus", "rateLimit$", "asObservable", "clearCorruptedData", "restoreFromStorage", "stored", "localStorage", "getItem", "data", "JSON", "parse", "remainingTime", "retryAfter", "resetTime", "Date", "now", "console", "warn", "removeItem", "getStoredStatus", "isRateLimited", "message", "limit", "remaining", "Math", "floor", "clearRateLimit", "max", "updateRateLimitStatus", "generateRateLimitMessage", "startCountdownFromRemainingTime", "originalStatus", "countdownSubscription", "unsubscribe", "currentRemainingTime", "pipe", "subscribe", "saveToStorage", "status", "setItem", "stringify", "clearStorage", "handleRateLimitResponse", "headers", "error", "parseInt", "totalRequests", "startRateLimitCountdown", "minutes", "seconds", "timeString", "next", "undefined", "getCurrentStatus", "value", "getRemainingRequests", "formatTimeRemaining", "remainingSeconds", "debugTriggerRateLimit", "log", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\services\\rate-limit.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, timer, EMPTY } from 'rxjs';\r\nimport { switchMap, takeWhile, tap, finalize } from 'rxjs/operators';\r\n\r\nexport interface RateLimitStatus {\r\n  isRateLimited: boolean;\r\n  retryAfter: number;\r\n  remainingTime: number;\r\n  message: string;\r\n  limit: number;\r\n  remaining: number;\r\n  resetTime: number;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RateLimitService {\r\n  private readonly STORAGE_KEY = 'rateLimit_status';\r\n  private rateLimitSubject = new BehaviorSubject<RateLimitStatus>(this.getInitialStatus());\r\n  \r\n  public rateLimit$ = this.rateLimitSubject.asObservable();\r\n  private countdownSubscription?: any;\r\n  constructor() {\r\n    // Clear any corrupted localStorage data first\r\n    this.clearCorruptedData();\r\n    // Restore any valid active rate limit from localStorage on service init\r\n    this.restoreFromStorage();\r\n  }\r\n\r\n  /**\r\n   * Clear corrupted or invalid data from localStorage\r\n   */\r\n  private clearCorruptedData(): void {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEY);\r\n      if (stored) {\r\n        const data = JSON.parse(stored);\r\n        // Check for corrupted data (extremely large values indicate corruption)\r\n        if (data.remainingTime > 3600 || data.retryAfter > 3600 || data.resetTime > Date.now() / 1000 + 3600) {\r\n          console.warn('🧹 Clearing corrupted rate limit data from localStorage');\r\n          localStorage.removeItem(this.STORAGE_KEY);\r\n        }\r\n      }\r\n    } catch {\r\n      // Clear if JSON parsing fails\r\n      localStorage.removeItem(this.STORAGE_KEY);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get initial status (either from localStorage or default)\r\n   */\r\n  private getInitialStatus(): RateLimitStatus {\r\n    const stored = this.getStoredStatus();\r\n    if (stored && stored.isRateLimited && stored.remainingTime > 0) {\r\n      return stored;\r\n    }\r\n    \r\n    return {\r\n      isRateLimited: false,\r\n      retryAfter: 0,\r\n      remainingTime: 0,\r\n      message: '',\r\n      limit: 0,\r\n      remaining: 0,\r\n      resetTime: 0\r\n    };\r\n  }  /**\r\n   * Restore rate limit status from localStorage and continue countdown if needed\r\n   */\r\n  private restoreFromStorage(): void {\r\n    const stored = this.getStoredStatus();\r\n    if (!stored || !stored.isRateLimited) return;\r\n\r\n    const now = Math.floor(Date.now() / 1000); // Current time in seconds\r\n    const resetTime = stored.resetTime; // Already in seconds from backend\r\n    \r\n    // Validate the stored data to prevent corruption\r\n    if (resetTime <= 0 || resetTime < now - 3600 || resetTime > now + 3600) {\r\n      console.warn('🧹 Invalid resetTime detected, clearing rate limit data');\r\n      this.clearRateLimit();\r\n      return;\r\n    }\r\n    \r\n    // Calculate remaining time directly from reset time\r\n    const remainingTime = Math.max(0, resetTime - now);\r\n\r\n    if (remainingTime > 0 && remainingTime <= 3600) { // Max 1 hour to prevent corruption\r\n      // Directly update status and start countdown with remaining time\r\n      this.updateRateLimitStatus({\r\n        isRateLimited: true,\r\n        retryAfter: stored.retryAfter,\r\n        remainingTime,\r\n        message: this.generateRateLimitMessage(remainingTime, stored.limit),\r\n        limit: stored.limit,\r\n        remaining: 0,\r\n        resetTime: stored.resetTime\r\n      });\r\n      \r\n      // Start countdown from remaining time\r\n      this.startCountdownFromRemainingTime(remainingTime, stored);\r\n    } else {\r\n      // Rate limit has expired or data is invalid\r\n      this.clearRateLimit();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start countdown from a specific remaining time (for restore from storage)\r\n   */\r\n  private startCountdownFromRemainingTime(remainingTime: number, originalStatus: RateLimitStatus): void {\r\n    // Stop any existing countdown\r\n    if (this.countdownSubscription) {\r\n      this.countdownSubscription.unsubscribe();\r\n    }\r\n\r\n    let currentRemainingTime = remainingTime;\r\n    \r\n    // Start countdown\r\n    this.countdownSubscription = timer(0, 1000).pipe(\r\n      takeWhile(() => currentRemainingTime > 0),\r\n      tap(() => {\r\n        currentRemainingTime--;\r\n        this.updateRateLimitStatus({\r\n          isRateLimited: currentRemainingTime > 0,\r\n          retryAfter: originalStatus.retryAfter,\r\n          remainingTime: Math.max(0, currentRemainingTime),\r\n          message: currentRemainingTime > 0 \r\n            ? this.generateRateLimitMessage(currentRemainingTime, originalStatus.limit)\r\n            : 'Rate limit has been reset. You can try again now.',\r\n          limit: originalStatus.limit,\r\n          remaining: currentRemainingTime <= 0 ? originalStatus.limit : 0,\r\n          resetTime: originalStatus.resetTime\r\n        });\r\n      }),\r\n      finalize(() => {\r\n        // Clear when countdown completes\r\n        this.clearRateLimit();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  /**\r\n   * Get stored rate limit status from localStorage\r\n   */\r\n  private getStoredStatus(): RateLimitStatus | null {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEY);\r\n      return stored ? JSON.parse(stored) : null;\r\n    } catch {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save rate limit status to localStorage\r\n   */\r\n  private saveToStorage(status: RateLimitStatus): void {\r\n    try {\r\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));\r\n    } catch {\r\n      // Ignore localStorage errors\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear rate limit from localStorage\r\n   */\r\n  private clearStorage(): void {\r\n    try {\r\n      localStorage.removeItem(this.STORAGE_KEY);\r\n    } catch {\r\n      // Ignore localStorage errors\r\n    }\r\n  }  /**\r\n   * Handle rate limit response from server\r\n   */\r\n  handleRateLimitResponse(headers: any, error?: any): void {\r\n    const retryAfter = parseInt(headers['retry-after'] || headers['x-ratelimit-retryafter'] || '0');\r\n    const limit = parseInt(headers['x-ratelimit-limit'] || '0');\r\n    const remaining = parseInt(headers['x-ratelimit-remaining'] || '0');\r\n    const resetTime = parseInt(headers['x-ratelimit-reset'] || '0'); // Already in seconds from backend\r\n    const totalRequests = parseInt(headers['x-ratelimit-totalrequests'] || '0');\r\n\r\n    if (retryAfter > 0) {\r\n      this.startRateLimitCountdown(retryAfter, limit, remaining, resetTime, totalRequests);\r\n    } else {\r\n      this.updateRateLimitStatus({\r\n        isRateLimited: false,\r\n        retryAfter: 0,\r\n        remainingTime: 0,\r\n        message: '',\r\n        limit,\r\n        remaining,\r\n        resetTime\r\n      });\r\n    }\r\n  }\r\n  /**\r\n   * Start countdown timer for rate limit\r\n   */\r\n  private startRateLimitCountdown(\r\n    retryAfter: number, \r\n    limit: number, \r\n    remaining: number, \r\n    resetTime: number,\r\n    totalRequests: number\r\n  ): void {\r\n    // Stop any existing countdown\r\n    if (this.countdownSubscription) {\r\n      this.countdownSubscription.unsubscribe();\r\n    }\r\n\r\n    let remainingTime = retryAfter;\r\n    \r\n    // Initial status\r\n    this.updateRateLimitStatus({\r\n      isRateLimited: true,\r\n      retryAfter,\r\n      remainingTime,\r\n      message: this.generateRateLimitMessage(remainingTime, totalRequests),\r\n      limit,\r\n      remaining: 0,\r\n      resetTime\r\n    });\r\n\r\n    // Start countdown\r\n    this.countdownSubscription = timer(0, 1000).pipe(\r\n      takeWhile(() => remainingTime > 0),\r\n      tap(() => {\r\n        remainingTime--;\r\n        this.updateRateLimitStatus({\r\n          isRateLimited: remainingTime > 0,\r\n          retryAfter,\r\n          remainingTime: Math.max(0, remainingTime),\r\n          message: remainingTime > 0 \r\n            ? this.generateRateLimitMessage(remainingTime, totalRequests)\r\n            : 'Rate limit has been reset. You can try again now.',\r\n          limit,\r\n          remaining: remainingTime <= 0 ? limit : 0,\r\n          resetTime\r\n        });\r\n      }),\r\n      finalize(() => {\r\n        // Rate limit period has ended\r\n        this.clearRateLimit();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  /**\r\n   * Generate user-friendly rate limit message\r\n   */\r\n  private generateRateLimitMessage(remainingTime: number, totalRequests: number): string {\r\n    const minutes = Math.floor(remainingTime / 60);\r\n    const seconds = remainingTime % 60;\r\n    \r\n    let timeString = '';\r\n    if (minutes > 0) {\r\n      timeString = `${minutes} minute${minutes > 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n    } else {\r\n      timeString = `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n    }\r\n\r\n    return `🚫 Rate limit exceeded! You've made too many requests. Please wait ${timeString} before trying again. (Total requests: ${totalRequests})`;\r\n  }\r\n  /**\r\n   * Update rate limit status\r\n   */\r\n  private updateRateLimitStatus(status: RateLimitStatus): void {\r\n    this.rateLimitSubject.next(status);\r\n    // Save to localStorage for persistence across page reloads\r\n    this.saveToStorage(status);\r\n  }\r\n  /**\r\n   * Clear rate limit status\r\n   */\r\n  clearRateLimit(): void {\r\n    if (this.countdownSubscription) {\r\n      this.countdownSubscription.unsubscribe();\r\n      this.countdownSubscription = undefined;\r\n    }\r\n\r\n    this.updateRateLimitStatus({\r\n      isRateLimited: false,\r\n      retryAfter: 0,\r\n      remainingTime: 0,\r\n      message: '',\r\n      limit: 0,\r\n      remaining: 0,\r\n      resetTime: 0\r\n    });\r\n    \r\n    // Clear from localStorage\r\n    this.clearStorage();\r\n  }\r\n\r\n  /**\r\n   * Get current rate limit status (synchronous)\r\n   */\r\n  getCurrentStatus(): RateLimitStatus {\r\n    return this.rateLimitSubject.value;\r\n  }\r\n\r\n  /**\r\n   * Check if currently rate limited\r\n   */\r\n  isRateLimited(): boolean {\r\n    return this.rateLimitSubject.value.isRateLimited;\r\n  }\r\n\r\n  /**\r\n   * Get remaining requests\r\n   */\r\n  getRemainingRequests(): number {\r\n    return this.rateLimitSubject.value.remaining;\r\n  }\r\n\r\n  /**\r\n   * Format time remaining for display\r\n   */\r\n  static formatTimeRemaining(seconds: number): string {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    \r\n    if (minutes === 0) {\r\n      return `${remainingSeconds}s`;\r\n    }\r\n    \r\n    return `${minutes}m ${remainingSeconds}s`;\r\n  }\r\n\r\n  /**\r\n   * Debug method to trigger rate limit popup (for testing)\r\n   */\r\n  debugTriggerRateLimit(seconds: number = 60): void {\r\n    console.log('🧪 Debug: Triggering rate limit popup for', seconds, 'seconds');\r\n    this.startRateLimitCountdown(seconds, 2, 0, Math.floor(Date.now() / 1000) + seconds, 2);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,KAAK,QAAe,MAAM;AAChE,SAAoBC,SAAS,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,gBAAgB;;AAepE,OAAM,MAAOC,gBAAgB;EAM3BC,YAAA;IALiB,KAAAC,WAAW,GAAG,kBAAkB;IACzC,KAAAC,gBAAgB,GAAG,IAAIR,eAAe,CAAkB,IAAI,CAACS,gBAAgB,EAAE,CAAC;IAEjF,KAAAC,UAAU,GAAG,IAAI,CAACF,gBAAgB,CAACG,YAAY,EAAE;IAGtD;IACA,IAAI,CAACC,kBAAkB,EAAE;IACzB;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA;;;EAGQD,kBAAkBA,CAAA;IACxB,IAAI;MACF,MAAME,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACT,WAAW,CAAC;MACrD,IAAIO,MAAM,EAAE;QACV,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;QAC/B;QACA,IAAIG,IAAI,CAACG,aAAa,GAAG,IAAI,IAAIH,IAAI,CAACI,UAAU,GAAG,IAAI,IAAIJ,IAAI,CAACK,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;UACpGC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;UACvEX,YAAY,CAACY,UAAU,CAAC,IAAI,CAACpB,WAAW,CAAC;QAC3C;MACF;IACF,CAAC,CAAC,MAAM;MACN;MACAQ,YAAY,CAACY,UAAU,CAAC,IAAI,CAACpB,WAAW,CAAC;IAC3C;EACF;EAEA;;;EAGQE,gBAAgBA,CAAA;IACtB,MAAMK,MAAM,GAAG,IAAI,CAACc,eAAe,EAAE;IACrC,IAAId,MAAM,IAAIA,MAAM,CAACe,aAAa,IAAIf,MAAM,CAACM,aAAa,GAAG,CAAC,EAAE;MAC9D,OAAON,MAAM;IACf;IAEA,OAAO;MACLe,aAAa,EAAE,KAAK;MACpBR,UAAU,EAAE,CAAC;MACbD,aAAa,EAAE,CAAC;MAChBU,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,CAAC;MACZV,SAAS,EAAE;KACZ;EACH,CAAC,CAAE;;;EAGKT,kBAAkBA,CAAA;IACxB,MAAMC,MAAM,GAAG,IAAI,CAACc,eAAe,EAAE;IACrC,IAAI,CAACd,MAAM,IAAI,CAACA,MAAM,CAACe,aAAa,EAAE;IAEtC,MAAML,GAAG,GAAGS,IAAI,CAACC,KAAK,CAACX,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IAC3C,MAAMF,SAAS,GAAGR,MAAM,CAACQ,SAAS,CAAC,CAAC;IAEpC;IACA,IAAIA,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAGE,GAAG,GAAG,IAAI,IAAIF,SAAS,GAAGE,GAAG,GAAG,IAAI,EAAE;MACtEC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;MACvE,IAAI,CAACS,cAAc,EAAE;MACrB;IACF;IAEA;IACA,MAAMf,aAAa,GAAGa,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEd,SAAS,GAAGE,GAAG,CAAC;IAElD,IAAIJ,aAAa,GAAG,CAAC,IAAIA,aAAa,IAAI,IAAI,EAAE;MAAE;MAChD;MACA,IAAI,CAACiB,qBAAqB,CAAC;QACzBR,aAAa,EAAE,IAAI;QACnBR,UAAU,EAAEP,MAAM,CAACO,UAAU;QAC7BD,aAAa;QACbU,OAAO,EAAE,IAAI,CAACQ,wBAAwB,CAAClB,aAAa,EAAEN,MAAM,CAACiB,KAAK,CAAC;QACnEA,KAAK,EAAEjB,MAAM,CAACiB,KAAK;QACnBC,SAAS,EAAE,CAAC;QACZV,SAAS,EAAER,MAAM,CAACQ;OACnB,CAAC;MAEF;MACA,IAAI,CAACiB,+BAA+B,CAACnB,aAAa,EAAEN,MAAM,CAAC;IAC7D,CAAC,MAAM;MACL;MACA,IAAI,CAACqB,cAAc,EAAE;IACvB;EACF;EAEA;;;EAGQI,+BAA+BA,CAACnB,aAAqB,EAAEoB,cAA+B;IAC5F;IACA,IAAI,IAAI,CAACC,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,EAAE;IAC1C;IAEA,IAAIC,oBAAoB,GAAGvB,aAAa;IAExC;IACA,IAAI,CAACqB,qBAAqB,GAAGxC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC2C,IAAI,CAC9C1C,SAAS,CAAC,MAAMyC,oBAAoB,GAAG,CAAC,CAAC,EACzCxC,GAAG,CAAC,MAAK;MACPwC,oBAAoB,EAAE;MACtB,IAAI,CAACN,qBAAqB,CAAC;QACzBR,aAAa,EAAEc,oBAAoB,GAAG,CAAC;QACvCtB,UAAU,EAAEmB,cAAc,CAACnB,UAAU;QACrCD,aAAa,EAAEa,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEO,oBAAoB,CAAC;QAChDb,OAAO,EAAEa,oBAAoB,GAAG,CAAC,GAC7B,IAAI,CAACL,wBAAwB,CAACK,oBAAoB,EAAEH,cAAc,CAACT,KAAK,CAAC,GACzE,mDAAmD;QACvDA,KAAK,EAAES,cAAc,CAACT,KAAK;QAC3BC,SAAS,EAAEW,oBAAoB,IAAI,CAAC,GAAGH,cAAc,CAACT,KAAK,GAAG,CAAC;QAC/DT,SAAS,EAAEkB,cAAc,CAAClB;OAC3B,CAAC;IACJ,CAAC,CAAC,EACFlB,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAAC+B,cAAc,EAAE;IACvB,CAAC,CAAC,CACH,CAACU,SAAS,EAAE;EACf;EAEA;;;EAGQjB,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMd,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACT,WAAW,CAAC;MACrD,OAAOO,MAAM,GAAGI,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC,GAAG,IAAI;IAC3C,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;EAEA;;;EAGQgC,aAAaA,CAACC,MAAuB;IAC3C,IAAI;MACFhC,YAAY,CAACiC,OAAO,CAAC,IAAI,CAACzC,WAAW,EAAEW,IAAI,CAAC+B,SAAS,CAACF,MAAM,CAAC,CAAC;IAChE,CAAC,CAAC,MAAM;MACN;IAAA;EAEJ;EAEA;;;EAGQG,YAAYA,CAAA;IAClB,IAAI;MACFnC,YAAY,CAACY,UAAU,CAAC,IAAI,CAACpB,WAAW,CAAC;IAC3C,CAAC,CAAC,MAAM;MACN;IAAA;EAEJ,CAAC,CAAE;;;EAGH4C,uBAAuBA,CAACC,OAAY,EAAEC,KAAW;IAC/C,MAAMhC,UAAU,GAAGiC,QAAQ,CAACF,OAAO,CAAC,aAAa,CAAC,IAAIA,OAAO,CAAC,wBAAwB,CAAC,IAAI,GAAG,CAAC;IAC/F,MAAMrB,KAAK,GAAGuB,QAAQ,CAACF,OAAO,CAAC,mBAAmB,CAAC,IAAI,GAAG,CAAC;IAC3D,MAAMpB,SAAS,GAAGsB,QAAQ,CAACF,OAAO,CAAC,uBAAuB,CAAC,IAAI,GAAG,CAAC;IACnE,MAAM9B,SAAS,GAAGgC,QAAQ,CAACF,OAAO,CAAC,mBAAmB,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IACjE,MAAMG,aAAa,GAAGD,QAAQ,CAACF,OAAO,CAAC,2BAA2B,CAAC,IAAI,GAAG,CAAC;IAE3E,IAAI/B,UAAU,GAAG,CAAC,EAAE;MAClB,IAAI,CAACmC,uBAAuB,CAACnC,UAAU,EAAEU,KAAK,EAAEC,SAAS,EAAEV,SAAS,EAAEiC,aAAa,CAAC;IACtF,CAAC,MAAM;MACL,IAAI,CAAClB,qBAAqB,CAAC;QACzBR,aAAa,EAAE,KAAK;QACpBR,UAAU,EAAE,CAAC;QACbD,aAAa,EAAE,CAAC;QAChBU,OAAO,EAAE,EAAE;QACXC,KAAK;QACLC,SAAS;QACTV;OACD,CAAC;IACJ;EACF;EACA;;;EAGQkC,uBAAuBA,CAC7BnC,UAAkB,EAClBU,KAAa,EACbC,SAAiB,EACjBV,SAAiB,EACjBiC,aAAqB;IAErB;IACA,IAAI,IAAI,CAACd,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,EAAE;IAC1C;IAEA,IAAItB,aAAa,GAAGC,UAAU;IAE9B;IACA,IAAI,CAACgB,qBAAqB,CAAC;MACzBR,aAAa,EAAE,IAAI;MACnBR,UAAU;MACVD,aAAa;MACbU,OAAO,EAAE,IAAI,CAACQ,wBAAwB,CAAClB,aAAa,EAAEmC,aAAa,CAAC;MACpExB,KAAK;MACLC,SAAS,EAAE,CAAC;MACZV;KACD,CAAC;IAEF;IACA,IAAI,CAACmB,qBAAqB,GAAGxC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC2C,IAAI,CAC9C1C,SAAS,CAAC,MAAMkB,aAAa,GAAG,CAAC,CAAC,EAClCjB,GAAG,CAAC,MAAK;MACPiB,aAAa,EAAE;MACf,IAAI,CAACiB,qBAAqB,CAAC;QACzBR,aAAa,EAAET,aAAa,GAAG,CAAC;QAChCC,UAAU;QACVD,aAAa,EAAEa,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEhB,aAAa,CAAC;QACzCU,OAAO,EAAEV,aAAa,GAAG,CAAC,GACtB,IAAI,CAACkB,wBAAwB,CAAClB,aAAa,EAAEmC,aAAa,CAAC,GAC3D,mDAAmD;QACvDxB,KAAK;QACLC,SAAS,EAAEZ,aAAa,IAAI,CAAC,GAAGW,KAAK,GAAG,CAAC;QACzCT;OACD,CAAC;IACJ,CAAC,CAAC,EACFlB,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAAC+B,cAAc,EAAE;IACvB,CAAC,CAAC,CACH,CAACU,SAAS,EAAE;EACf;EAEA;;;EAGQP,wBAAwBA,CAAClB,aAAqB,EAAEmC,aAAqB;IAC3E,MAAME,OAAO,GAAGxB,IAAI,CAACC,KAAK,CAACd,aAAa,GAAG,EAAE,CAAC;IAC9C,MAAMsC,OAAO,GAAGtC,aAAa,GAAG,EAAE;IAElC,IAAIuC,UAAU,GAAG,EAAE;IACnB,IAAIF,OAAO,GAAG,CAAC,EAAE;MACfE,UAAU,GAAG,GAAGF,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQC,OAAO,UAAUA,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAC5G,CAAC,MAAM;MACLC,UAAU,GAAG,GAAGD,OAAO,UAAUA,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAC7D;IAEA,OAAO,sEAAsEC,UAAU,0CAA0CJ,aAAa,GAAG;EACnJ;EACA;;;EAGQlB,qBAAqBA,CAACU,MAAuB;IACnD,IAAI,CAACvC,gBAAgB,CAACoD,IAAI,CAACb,MAAM,CAAC;IAClC;IACA,IAAI,CAACD,aAAa,CAACC,MAAM,CAAC;EAC5B;EACA;;;EAGAZ,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACM,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,WAAW,EAAE;MACxC,IAAI,CAACD,qBAAqB,GAAGoB,SAAS;IACxC;IAEA,IAAI,CAACxB,qBAAqB,CAAC;MACzBR,aAAa,EAAE,KAAK;MACpBR,UAAU,EAAE,CAAC;MACbD,aAAa,EAAE,CAAC;MAChBU,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,CAAC;MACZV,SAAS,EAAE;KACZ,CAAC;IAEF;IACA,IAAI,CAAC4B,YAAY,EAAE;EACrB;EAEA;;;EAGAY,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACtD,gBAAgB,CAACuD,KAAK;EACpC;EAEA;;;EAGAlC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACrB,gBAAgB,CAACuD,KAAK,CAAClC,aAAa;EAClD;EAEA;;;EAGAmC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACxD,gBAAgB,CAACuD,KAAK,CAAC/B,SAAS;EAC9C;EAEA;;;EAGA,OAAOiC,mBAAmBA,CAACP,OAAe;IACxC,MAAMD,OAAO,GAAGxB,IAAI,CAACC,KAAK,CAACwB,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMQ,gBAAgB,GAAGR,OAAO,GAAG,EAAE;IAErC,IAAID,OAAO,KAAK,CAAC,EAAE;MACjB,OAAO,GAAGS,gBAAgB,GAAG;IAC/B;IAEA,OAAO,GAAGT,OAAO,KAAKS,gBAAgB,GAAG;EAC3C;EAEA;;;EAGAC,qBAAqBA,CAACT,OAAA,GAAkB,EAAE;IACxCjC,OAAO,CAAC2C,GAAG,CAAC,2CAA2C,EAAEV,OAAO,EAAE,SAAS,CAAC;IAC5E,IAAI,CAACF,uBAAuB,CAACE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAEzB,IAAI,CAACC,KAAK,CAACX,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAGkC,OAAO,EAAE,CAAC,CAAC;EACzF;EAAC,QAAAW,CAAA,G;qCAlUUhE,gBAAgB;EAAA;EAAA,QAAAiE,EAAA,G;WAAhBjE,gBAAgB;IAAAkE,OAAA,EAAhBlE,gBAAgB,CAAAmE,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}