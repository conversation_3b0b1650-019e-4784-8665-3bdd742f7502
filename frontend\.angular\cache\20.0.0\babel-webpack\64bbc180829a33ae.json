{"ast": null, "code": "/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\nexport { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };", "map": {"version": 3, "names": ["isFakeMousedownFromScreenReader", "event", "buttons", "detail", "isFakeTouchstartFromScreenReader", "touch", "touches", "changedTouches", "identifier", "radiusX", "radiusY", "a", "i"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/fake-event-detection-DWOdFTFz.mjs"], "sourcesContent": ["/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n    // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n    // `event.detail` is zero depending on the browser:\n    // - `event.buttons` works on Firefox, but fails on Chrome.\n    // - `detail` works on Chrome, but fails on Firefox.\n    return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n    return (!!touch &&\n        touch.identifier === -1 &&\n        (touch.radiusX == null || touch.radiusX === 1) &&\n        (touch.radiusY == null || touch.radiusY === 1));\n}\n\nexport { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };\n"], "mappings": "AAAA;AACA,SAASA,+BAA+BA,CAACC,KAAK,EAA<PERSON>;EAC5C;EACA;EACA;EACA;EACA;EACA,OAAOA,KAAK,CAACC,OAAO,KAAK,CAAC,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC;AACpD;AACA;AACA,SAASC,gCAAgCA,CAACH,KAAK,EAAE;EAC7C,MAAMI,KAAK,GAAIJ,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,IAAML,KAAK,CAACM,cAAc,IAAIN,KAAK,CAACM,cAAc,CAAC,CAAC,CAAE;EACtG;EACA;EACA;EACA;EACA,OAAQ,CAAC,CAACF,KAAK,IACXA,KAAK,CAACG,UAAU,KAAK,CAAC,CAAC,KACtBH,KAAK,CAACI,OAAO,IAAI,IAAI,IAAIJ,KAAK,CAACI,OAAO,KAAK,CAAC,CAAC,KAC7CJ,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIL,KAAK,CAACK,OAAO,KAAK,CAAC,CAAC;AACtD;AAEA,SAASN,gCAAgC,IAAIO,CAAC,EAAEX,+BAA+B,IAAIY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}