#!/usr/bin/env python3
"""
Quick User Creation Script
Simple script to create a single verified user for testing

Usage:
    python create_single_user.py [email] [password] [first_name] [last_name]
    
Example:
    python create_single_user.py <EMAIL> DevPass123! Developer User
"""

import sys
from datetime import datetime, timezone
import secrets
import string

# Set up the Masonite environment
from wsgi import application
from masonite.environment import LoadEnvironment
LoadEnvironment()

# Import after environment setup
from app.models.User import User
from app.models.SecurityEvent import SecurityEvent, SecurityEventType


def generate_secure_token(length=50):
    """Generate a secure random token"""
    alphabet = string.ascii_letters + string.digits + '-_'
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def create_single_user(email, password, first_name="Test", last_name="User"):
    """Create a single verified user"""
    try:
        print(f"🚀 Creating verified user: {email}")
        
        # Check if user already exists
        existing_user = User.where('email', email).first()
        if existing_user:
            print(f"⚠️  User with email {email} already exists (ID: {existing_user.id})")
            if not existing_user.email_verified_at:
                existing_user.email_verified_at = datetime.now(timezone.utc)
                existing_user.save()
                print(f"✅ Email verified for existing user")
            return existing_user
        
        # Create new user
        user = User()
        user.email = email
        user.set_password(password)
        user.first_name = first_name
        user.last_name = last_name
        user.name = f"{first_name} {last_name}"
        user.api_token = generate_secure_token()
        user.email_verified_at = datetime.now(timezone.utc)
        user.is_active = True
        user.login_attempts = 0
        user.two_factor_enabled = False
        user.save()
        
        print(f"✅ User created successfully!")
        print(f"   📧 Email: {user.email}")
        print(f"   🆔 User ID: {user.id}")
        print(f"   👤 Name: {user.name}")
        print(f"   🔑 API Token: {user.api_token[:20]}...")
        
        # Log security event
        try:
            SecurityEvent.log_event(
                event_type=SecurityEventType.ACCOUNT_CREATED.value,
                user_id=user.id,
                message=f"Account created via script for {email}",
                event_data={'created_via': 'single_user_script'}
            )
        except Exception as e:
            print(f"⚠️  Could not log security event: {e}")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return None


def main():
    """Main function"""
    if len(sys.argv) < 3:
        print("Usage: python create_single_user.py [email] [password] [first_name] [last_name]")
        print("Example: python create_single_user.py <EMAIL> DevPass123! Developer User")
        sys.exit(1)
    
    email = sys.argv[1]
    password = sys.argv[2]
    first_name = sys.argv[3] if len(sys.argv) > 3 else "Test"
    last_name = sys.argv[4] if len(sys.argv) > 4 else "User"
    
    print("🔧 Quick User Creation Script")
    print("=" * 40)
    
    user = create_single_user(email, password, first_name, last_name)
    
    if user:
        print(f"\n🎉 Ready to use!")
        print(f"📧 Login: {email}")
        print(f"🔑 Password: {password}")
    else:
        print("\n❌ Failed to create user")
        sys.exit(1)


if __name__ == "__main__":
    main()
