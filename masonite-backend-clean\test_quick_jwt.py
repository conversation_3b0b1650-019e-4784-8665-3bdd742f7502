#!/usr/bin/env python3
"""
Quick JWT test
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:3002/api"
HEADERS = {
    'Content-Type': 'application/json',
    'Origin': 'http://localhost:4200'
}

def quick_test():
    """Quick test"""
    print("🧪 Quick JWT Test...")
    
    try:
        # Test server
        response = requests.get("http://localhost:3002", timeout=5)
        print(f"✅ Server running: {response.status_code}")
        
        # Test login
        login_data = {"email": "<EMAIL>", "password": "AdminPass123!"}
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data, headers=HEADERS, timeout=5)
        print(f"Login: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"Token: {token[:30]}...")
            
            # Test protected endpoint
            auth_headers = {**HEADERS, 'Authorization': f'Bearer {token}'}
            response = requests.get(f"{BASE_URL}/auth/profile", headers=auth_headers, timeout=5)
            print(f"Profile: {response.status_code}")
            print(f"Response: {response.text[:100]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_test()
