#!/usr/bin/env python3
"""
Comprehensive CORS Implementation Script
Adds CORS headers to all controller methods systematically
"""

import os
import re

def add_cors_to_all_controllers():
    """Add CORS headers to all controllers"""
    
    controllers_dir = "app/controllers"
    
    # Controllers to update (excluding AuthController which already has CORS)
    controllers = [
        "TwoFactorController.py",
        "OAuthController.py", 
        "PaymentController.py",
        "OTPController.py",
        "SecurityController.py",
        "AccountController.py",
        "NotificationController.py",
        "QueueController.py"
    ]
    
    print("🔧 Adding CORS headers to all controllers...")
    print("=" * 60)
    
    for controller_file in controllers:
        controller_path = os.path.join(controllers_dir, controller_file)
        
        if not os.path.exists(controller_path):
            print(f"⚠️  {controller_path} not found")
            continue
            
        print(f"📝 Processing {controller_file}...")
        
        try:
            # Read file
            with open(controller_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add CorsController import if not present
            if 'from app.controllers.CorsController import CorsController' not in content:
                # Find a good place to add the import
                import_lines = []
                other_lines = []
                in_imports = True
                
                for line in content.split('\n'):
                    if in_imports and (line.startswith('from ') or line.startswith('import ') or line.strip() == '' or line.startswith('"""') or line.startswith('#')):
                        import_lines.append(line)
                    else:
                        if in_imports:
                            # Add our import before the first non-import line
                            import_lines.append('from app.controllers.CorsController import CorsController')
                            in_imports = False
                        other_lines.append(line)
                
                content = '\n'.join(import_lines + other_lines)
            
            # Add CORS headers before return response.json() calls
            # Use a more specific pattern to avoid issues
            lines = content.split('\n')
            new_lines = []
            
            for i, line in enumerate(lines):
                new_lines.append(line)
                
                # Check if this line has a return response.json() call
                if 'return response.json(' in line and line.strip().startswith('return'):
                    # Get the indentation of the return statement
                    indent = len(line) - len(line.lstrip())
                    indent_str = ' ' * indent
                    
                    # Check if CORS is already added in the previous line
                    if i > 0 and 'CorsController.add_cors_headers' in lines[i-1]:
                        continue  # Already has CORS
                    
                    # Insert CORS line before the return statement
                    cors_line = f"{indent_str}CorsController.add_cors_headers(response, request.header('Origin'))"
                    new_lines.insert(-1, cors_line)  # Insert before the return line we just added
            
            # Write back
            with open(controller_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print(f"   ✅ Added CORS headers to {controller_file}")
            
        except Exception as e:
            print(f"   ❌ Error processing {controller_file}: {e}")
    
    print("=" * 60)
    print("✅ CORS implementation completed!")

if __name__ == "__main__":
    add_cors_to_all_controllers()
