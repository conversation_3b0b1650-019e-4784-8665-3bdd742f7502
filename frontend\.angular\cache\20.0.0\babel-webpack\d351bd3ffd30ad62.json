{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AccountDeletionService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/account`;\n  }\n  /**\n   * Request account deletion with preferences\n   */\n  requestAccountDeletion(preferences) {\n    const headers = this.getAuthHeaders();\n    return this.http.post(`${this.apiUrl}/request-deletion`, preferences, {\n      headers\n    });\n  }\n  /**\n   * Get current deletion status\n   */\n  getDeletionStatus() {\n    const headers = this.getAuthHeaders();\n    return this.http.get(`${this.apiUrl}/deletion-status`, {\n      headers\n    });\n  }\n  /**\n   * Cancel pending account deletion\n   */\n  cancelDeletion() {\n    const headers = this.getAuthHeaders();\n    return this.http.post(`${this.apiUrl}/cancel-deletion`, {}, {\n      headers\n    });\n  }\n  /**\n   * Confirm account deletion with token (public endpoint)\n   */\n  confirmDeletion(token) {\n    return this.http.post(`${this.apiUrl}/confirm-deletion`, {\n      token\n    });\n  }\n  /**\n   * Check for preserved data during signup (public endpoint)\n   */\n  checkPreservedData(email) {\n    return this.http.get(`${this.apiUrl}/check-preserved-data/${encodeURIComponent(email)}`);\n  }\n  /**\n   * Restore preserved data (public endpoint - used during signup)\n   */\n  restoreData(userId, email, restoreOptions) {\n    return this.http.post(`${this.apiUrl}/restore-data`, {\n      userId,\n      email,\n      ...restoreOptions\n    });\n  }\n  /**\n   * Permanently delete all preserved data (public endpoint)\n   */\n  deletePreservedData(email) {\n    return this.http.request('delete', `${this.apiUrl}/delete-preserved-data`, {\n      body: {\n        email\n      }\n    });\n  }\n  /**\n   * Export user data as downloadable file\n   */\n  exportUserData() {\n    const headers = this.getAuthHeaders();\n    return this.http.get(`${this.apiUrl}/export-data`, {\n      headers,\n      responseType: 'blob'\n    });\n  }\n  /**\n   * Request data export via email\n   */\n  requestDataExport() {\n    const headers = this.getAuthHeaders();\n    return this.http.post(`${this.apiUrl}/request-export`, {}, {\n      headers\n    });\n  }\n  /**\n   * Get authentication headers\n   */\n  getAuthHeaders() {\n    const token = localStorage.getItem('authToken');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  /**\n   * Validate deletion preferences\n   */\n  validateDeletionPreferences(preferences) {\n    const errors = [];\n    // Check retention period\n    if (preferences.customRetentionPeriod !== undefined) {\n      if (preferences.customRetentionPeriod < 1 || preferences.customRetentionPeriod > 365) {\n        errors.push('Retention period must be between 1 and 365 days');\n      }\n    }\n    // Warn if no data is being preserved\n    const hasAnyPreservation = preferences.preservePaymentData || preferences.preserveTransactionHistory || preferences.preserveProfileData || preferences.preserveSecurityLogs;\n    if (!hasAnyPreservation) {\n      // This is just a warning, not an error\n      console.warn('No data will be preserved - account deletion will be permanent');\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  /**\n   * Calculate data expiry date\n   */\n  calculateExpiryDate(retentionPeriodDays = 30) {\n    const expiryDate = new Date();\n    expiryDate.setDate(expiryDate.getDate() + retentionPeriodDays);\n    return expiryDate;\n  }\n  /**\n   * Format preserved data summary for display\n   */\n  formatPreservedDataSummary(summary) {\n    const items = [];\n    if (summary.paymentRecords > 0) {\n      items.push(`${summary.paymentRecords} payment record${summary.paymentRecords === 1 ? '' : 's'}`);\n    }\n    if (summary.transactionHistory > 0) {\n      items.push(`${summary.transactionHistory} transaction${summary.transactionHistory === 1 ? '' : 's'}`);\n    }\n    if (summary.profileBackup) {\n      items.push('Profile backup');\n    }\n    if (summary.securityEvents > 0) {\n      items.push(`${summary.securityEvents} security event${summary.securityEvents === 1 ? '' : 's'}`);\n    }\n    if (summary.loginHistory > 0) {\n      items.push(`${summary.loginHistory} login record${summary.loginHistory === 1 ? '' : 's'}`);\n    }\n    return items;\n  }\n  /**\n   * Get recommended deletion preferences based on user activity\n   */\n  getRecommendedPreferences(user) {\n    // Basic recommendations - can be enhanced based on user data\n    return {\n      preservePaymentData: true,\n      // Usually recommended to preserve for legal/tax purposes\n      preserveTransactionHistory: true,\n      // Financial records are important\n      preserveProfileData: false,\n      // Profile data is usually not needed\n      preserveSecurityLogs: false,\n      // Security logs are usually not needed by users\n      customRetentionPeriod: 90,\n      // 3 months is a good default\n      reason: ''\n    };\n  }\n  static #_ = this.ɵfac = function AccountDeletionService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AccountDeletionService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AccountDeletionService,\n    factory: AccountDeletionService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "environment", "AccountDeletionService", "constructor", "http", "apiUrl", "requestAccountDeletion", "preferences", "headers", "getAuthHeaders", "post", "getDeletionStatus", "get", "cancelDeletion", "confirmDeletion", "token", "checkPreservedData", "email", "encodeURIComponent", "restoreData", "userId", "restoreOptions", "deletePreservedData", "request", "body", "exportUserData", "responseType", "requestDataExport", "localStorage", "getItem", "validateDeletionPreferences", "errors", "customRetentionPeriod", "undefined", "push", "hasAnyPreservation", "preservePaymentData", "preserveTransactionHistory", "preserveProfileData", "preserveSecurityLogs", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "length", "calculateExpiryDate", "retentionPeriodDays", "expiryDate", "Date", "setDate", "getDate", "formatPreservedDataSummary", "summary", "items", "paymentRecords", "transactionHistory", "profileBackup", "securityEvents", "loginHistory", "getRecommendedPreferences", "user", "reason", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\services\\account-deletion.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\nimport {\r\n  DeletionPreferences,\r\n  DataRestoreOptions,\r\n  AccountDeletionResponse,\r\n  DataRestorationResponse,\r\n  PreservedDataCheck,\r\n  AccountDeletionRecord\r\n} from '../models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AccountDeletionService {\r\n  private apiUrl = `${environment.apiUrl}/account`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n  /**\r\n   * Request account deletion with preferences\r\n   */\r\n  requestAccountDeletion(preferences: DeletionPreferences): Observable<AccountDeletionResponse> {\r\n    const headers = this.getAuthHeaders();\r\n    return this.http.post<AccountDeletionResponse>(\r\n      `${this.apiUrl}/request-deletion`,\r\n      preferences,\r\n      { headers }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get current deletion status\r\n   */\r\n  getDeletionStatus(): Observable<{\r\n    hasPendingDeletion: boolean;\r\n    deletionRecord?: AccountDeletionRecord;\r\n  }> {\r\n    const headers = this.getAuthHeaders();\r\n    return this.http.get<{\r\n      hasPendingDeletion: boolean;\r\n      deletionRecord?: AccountDeletionRecord;\r\n    }>(`${this.apiUrl}/deletion-status`, { headers });\r\n  }\r\n\r\n  /**\r\n   * Cancel pending account deletion\r\n   */\r\n  cancelDeletion(): Observable<{ message: string }> {\r\n    const headers = this.getAuthHeaders();\r\n    return this.http.post<{ message: string }>(\r\n      `${this.apiUrl}/cancel-deletion`,\r\n      {},\r\n      { headers }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Confirm account deletion with token (public endpoint)\r\n   */\r\n  confirmDeletion(token: string): Observable<{\r\n    message: string;\r\n    deletionId: string;\r\n    preservedDataSummary?: object;\r\n  }> {\r\n    return this.http.post<{\r\n      message: string;\r\n      deletionId: string;\r\n      preservedDataSummary?: object;\r\n    }>(`${this.apiUrl}/confirm-deletion`, { token });\r\n  }\r\n\r\n  /**\r\n   * Check for preserved data during signup (public endpoint)\r\n   */\r\n  checkPreservedData(email: string): Observable<PreservedDataCheck> {\r\n    return this.http.get<PreservedDataCheck>(\r\n      `${this.apiUrl}/check-preserved-data/${encodeURIComponent(email)}`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Restore preserved data (public endpoint - used during signup)\r\n   */\r\n  restoreData(\r\n    userId: string,\r\n    email: string,\r\n    restoreOptions: DataRestoreOptions\r\n  ): Observable<DataRestorationResponse> {\r\n    return this.http.post<DataRestorationResponse>(\r\n      `${this.apiUrl}/restore-data`,\r\n      {\r\n        userId,\r\n        email,\r\n        ...restoreOptions\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Permanently delete all preserved data (public endpoint)\r\n   */\r\n  deletePreservedData(email: string): Observable<{ message: string }> {\r\n    return this.http.request<{ message: string }>('delete', `${this.apiUrl}/delete-preserved-data`, {\r\n      body: { email }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Export user data as downloadable file\r\n   */\r\n  exportUserData(): Observable<Blob> {\r\n    const headers = this.getAuthHeaders();\r\n    return this.http.get(`${this.apiUrl}/export-data`, {\r\n      headers,\r\n      responseType: 'blob'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Request data export via email\r\n   */\r\n  requestDataExport(): Observable<{ message: string }> {\r\n    const headers = this.getAuthHeaders();\r\n    return this.http.post<{ message: string }>(\r\n      `${this.apiUrl}/request-export`,\r\n      {},\r\n      { headers }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get authentication headers\r\n   */\r\n  private getAuthHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('authToken');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate deletion preferences\r\n   */\r\n  validateDeletionPreferences(preferences: DeletionPreferences): {\r\n    isValid: boolean;\r\n    errors: string[];\r\n  } {\r\n    const errors: string[] = [];\r\n\r\n    // Check retention period\r\n    if (preferences.customRetentionPeriod !== undefined) {\r\n      if (preferences.customRetentionPeriod < 1 || preferences.customRetentionPeriod > 365) {\r\n        errors.push('Retention period must be between 1 and 365 days');\r\n      }\r\n    }\r\n\r\n    // Warn if no data is being preserved\r\n    const hasAnyPreservation = preferences.preservePaymentData ||\r\n                              preferences.preserveTransactionHistory ||\r\n                              preferences.preserveProfileData ||\r\n                              preferences.preserveSecurityLogs;\r\n\r\n    if (!hasAnyPreservation) {\r\n      // This is just a warning, not an error\r\n      console.warn('No data will be preserved - account deletion will be permanent');\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate data expiry date\r\n   */\r\n  calculateExpiryDate(retentionPeriodDays: number = 30): Date {\r\n    const expiryDate = new Date();\r\n    expiryDate.setDate(expiryDate.getDate() + retentionPeriodDays);\r\n    return expiryDate;\r\n  }\r\n\r\n  /**\r\n   * Format preserved data summary for display\r\n   */\r\n  formatPreservedDataSummary(summary: any): string[] {\r\n    const items: string[] = [];\r\n\r\n    if (summary.paymentRecords > 0) {\r\n      items.push(`${summary.paymentRecords} payment record${summary.paymentRecords === 1 ? '' : 's'}`);\r\n    }\r\n\r\n    if (summary.transactionHistory > 0) {\r\n      items.push(`${summary.transactionHistory} transaction${summary.transactionHistory === 1 ? '' : 's'}`);\r\n    }\r\n\r\n    if (summary.profileBackup) {\r\n      items.push('Profile backup');\r\n    }\r\n\r\n    if (summary.securityEvents > 0) {\r\n      items.push(`${summary.securityEvents} security event${summary.securityEvents === 1 ? '' : 's'}`);\r\n    }\r\n\r\n    if (summary.loginHistory > 0) {\r\n      items.push(`${summary.loginHistory} login record${summary.loginHistory === 1 ? '' : 's'}`);\r\n    }\r\n\r\n    return items;\r\n  }\r\n\r\n  /**\r\n   * Get recommended deletion preferences based on user activity\r\n   */\r\n  getRecommendedPreferences(user: any): DeletionPreferences {\r\n    // Basic recommendations - can be enhanced based on user data\r\n    return {\r\n      preservePaymentData: true, // Usually recommended to preserve for legal/tax purposes\r\n      preserveTransactionHistory: true, // Financial records are important\r\n      preserveProfileData: false, // Profile data is usually not needed\r\n      preserveSecurityLogs: false, // Security logs are usually not needed by users\r\n      customRetentionPeriod: 90, // 3 months is a good default\r\n      reason: ''\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,WAAW,QAAQ,gCAAgC;;;AAa5D,OAAM,MAAOC,sBAAsB;EAGjCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACI,MAAM,UAAU;EAET;EACvC;;;EAGAC,sBAAsBA,CAACC,WAAgC;IACrD,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,MAAM,mBAAmB,EACjCE,WAAW,EACX;MAAEC;IAAO,CAAE,CACZ;EACH;EAEA;;;EAGAG,iBAAiBA,CAAA;IAIf,MAAMH,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,OAAO,IAAI,CAACL,IAAI,CAACQ,GAAG,CAGjB,GAAG,IAAI,CAACP,MAAM,kBAAkB,EAAE;MAAEG;IAAO,CAAE,CAAC;EACnD;EAEA;;;EAGAK,cAAcA,CAAA;IACZ,MAAML,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,MAAM,kBAAkB,EAChC,EAAE,EACF;MAAEG;IAAO,CAAE,CACZ;EACH;EAEA;;;EAGAM,eAAeA,CAACC,KAAa;IAK3B,OAAO,IAAI,CAACX,IAAI,CAACM,IAAI,CAIlB,GAAG,IAAI,CAACL,MAAM,mBAAmB,EAAE;MAAEU;IAAK,CAAE,CAAC;EAClD;EAEA;;;EAGAC,kBAAkBA,CAACC,KAAa;IAC9B,OAAO,IAAI,CAACb,IAAI,CAACQ,GAAG,CAClB,GAAG,IAAI,CAACP,MAAM,yBAAyBa,kBAAkB,CAACD,KAAK,CAAC,EAAE,CACnE;EACH;EAEA;;;EAGAE,WAAWA,CACTC,MAAc,EACdH,KAAa,EACbI,cAAkC;IAElC,OAAO,IAAI,CAACjB,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,MAAM,eAAe,EAC7B;MACEe,MAAM;MACNH,KAAK;MACL,GAAGI;KACJ,CACF;EACH;EAEA;;;EAGAC,mBAAmBA,CAACL,KAAa;IAC/B,OAAO,IAAI,CAACb,IAAI,CAACmB,OAAO,CAAsB,QAAQ,EAAE,GAAG,IAAI,CAAClB,MAAM,wBAAwB,EAAE;MAC9FmB,IAAI,EAAE;QAAEP;MAAK;KACd,CAAC;EACJ;EAEA;;;EAGAQ,cAAcA,CAAA;IACZ,MAAMjB,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,OAAO,IAAI,CAACL,IAAI,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,MAAM,cAAc,EAAE;MACjDG,OAAO;MACPkB,YAAY,EAAE;KACf,CAAC;EACJ;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,MAAMnB,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,MAAM,iBAAiB,EAC/B,EAAE,EACF;MAAEG;IAAO,CAAE,CACZ;EACH;EAEA;;;EAGQC,cAAcA,CAAA;IACpB,MAAMM,KAAK,GAAGa,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,OAAO,IAAI7B,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUe,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;;;EAGAe,2BAA2BA,CAACvB,WAAgC;IAI1D,MAAMwB,MAAM,GAAa,EAAE;IAE3B;IACA,IAAIxB,WAAW,CAACyB,qBAAqB,KAAKC,SAAS,EAAE;MACnD,IAAI1B,WAAW,CAACyB,qBAAqB,GAAG,CAAC,IAAIzB,WAAW,CAACyB,qBAAqB,GAAG,GAAG,EAAE;QACpFD,MAAM,CAACG,IAAI,CAAC,iDAAiD,CAAC;MAChE;IACF;IAEA;IACA,MAAMC,kBAAkB,GAAG5B,WAAW,CAAC6B,mBAAmB,IAChC7B,WAAW,CAAC8B,0BAA0B,IACtC9B,WAAW,CAAC+B,mBAAmB,IAC/B/B,WAAW,CAACgC,oBAAoB;IAE1D,IAAI,CAACJ,kBAAkB,EAAE;MACvB;MACAK,OAAO,CAACC,IAAI,CAAC,gEAAgE,CAAC;IAChF;IAEA,OAAO;MACLC,OAAO,EAAEX,MAAM,CAACY,MAAM,KAAK,CAAC;MAC5BZ;KACD;EACH;EAEA;;;EAGAa,mBAAmBA,CAACC,mBAAA,GAA8B,EAAE;IAClD,MAAMC,UAAU,GAAG,IAAIC,IAAI,EAAE;IAC7BD,UAAU,CAACE,OAAO,CAACF,UAAU,CAACG,OAAO,EAAE,GAAGJ,mBAAmB,CAAC;IAC9D,OAAOC,UAAU;EACnB;EAEA;;;EAGAI,0BAA0BA,CAACC,OAAY;IACrC,MAAMC,KAAK,GAAa,EAAE;IAE1B,IAAID,OAAO,CAACE,cAAc,GAAG,CAAC,EAAE;MAC9BD,KAAK,CAAClB,IAAI,CAAC,GAAGiB,OAAO,CAACE,cAAc,kBAAkBF,OAAO,CAACE,cAAc,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IAClG;IAEA,IAAIF,OAAO,CAACG,kBAAkB,GAAG,CAAC,EAAE;MAClCF,KAAK,CAAClB,IAAI,CAAC,GAAGiB,OAAO,CAACG,kBAAkB,eAAeH,OAAO,CAACG,kBAAkB,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvG;IAEA,IAAIH,OAAO,CAACI,aAAa,EAAE;MACzBH,KAAK,CAAClB,IAAI,CAAC,gBAAgB,CAAC;IAC9B;IAEA,IAAIiB,OAAO,CAACK,cAAc,GAAG,CAAC,EAAE;MAC9BJ,KAAK,CAAClB,IAAI,CAAC,GAAGiB,OAAO,CAACK,cAAc,kBAAkBL,OAAO,CAACK,cAAc,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IAClG;IAEA,IAAIL,OAAO,CAACM,YAAY,GAAG,CAAC,EAAE;MAC5BL,KAAK,CAAClB,IAAI,CAAC,GAAGiB,OAAO,CAACM,YAAY,gBAAgBN,OAAO,CAACM,YAAY,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IAC5F;IAEA,OAAOL,KAAK;EACd;EAEA;;;EAGAM,yBAAyBA,CAACC,IAAS;IACjC;IACA,OAAO;MACLvB,mBAAmB,EAAE,IAAI;MAAE;MAC3BC,0BAA0B,EAAE,IAAI;MAAE;MAClCC,mBAAmB,EAAE,KAAK;MAAE;MAC5BC,oBAAoB,EAAE,KAAK;MAAE;MAC7BP,qBAAqB,EAAE,EAAE;MAAE;MAC3B4B,MAAM,EAAE;KACT;EACH;EAAC,QAAAC,CAAA,G;qCAnNU3D,sBAAsB,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAtBhE,sBAAsB;IAAAiE,OAAA,EAAtBjE,sBAAsB,CAAAkE,IAAA;IAAAC,UAAA,EAFrB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}