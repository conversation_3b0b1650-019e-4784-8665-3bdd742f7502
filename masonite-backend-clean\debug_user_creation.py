#!/usr/bin/env python3
"""
Debug User Creation and Database Check
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Initialize Masonite application
from wsgi import application
from app.models.User import User
import bcrypt

def debug_user_creation():
    print("🔍 Debug User Creation and Database Check")
    print("=" * 50)
    
    # Test email and password
    test_email = "<EMAIL>"
    test_password = "TestPassword123!"
    
    print(f"\n1️⃣ Checking if user exists...")
    existing_user = User.where('email', test_email).first()
    
    if existing_user:
        print(f"   ✅ User exists with ID: {existing_user.id}")
        print(f"   📧 Email: {existing_user.email}")
        print(f"   🔑 Has password: {'Yes' if existing_user.password else 'No'}")
        print(f"   🎫 API Token: {existing_user.api_token[:20] + '...' if existing_user.api_token else 'None'}")
        
        # Test password verification
        print(f"\n2️⃣ Testing password verification...")
        try:
            is_valid = existing_user.verify_password(test_password)
            print(f"   Password verification result: {is_valid}")
            
            if not is_valid:
                print(f"   ⚠️ Password doesn't match. Let's reset it...")
                existing_user.set_password(test_password)
                existing_user.save()
                print(f"   ✅ Password reset successfully")
                
                # Test again
                is_valid = existing_user.verify_password(test_password)
                print(f"   New password verification result: {is_valid}")
        except Exception as e:
            print(f"   ❌ Password verification error: {e}")
            
        # Generate new token
        print(f"\n3️⃣ Generating new API token...")
        try:
            token = existing_user.generate_api_token()
            print(f"   ✅ New token generated: {token[:20]}...")
            
            # Verify token is saved
            user_check = User.where('api_token', token).first()
            if user_check:
                print(f"   ✅ Token saved correctly in database")
            else:
                print(f"   ❌ Token not found in database")
                
        except Exception as e:
            print(f"   ❌ Token generation error: {e}")
            
    else:
        print(f"   ❌ User doesn't exist. Creating new user...")
        
        try:
            # Create new user
            user = User()
            user.email = test_email
            user.first_name = "Debug"
            user.last_name = "Test"
            user.name = "Debug Test"
            user.set_password(test_password)
            user.email_verified_at = "2024-01-01 00:00:00"  # Mark as verified
            user.is_active = True
            user.save()
            
            print(f"   ✅ User created with ID: {user.id}")
            
            # Generate token
            token = user.generate_api_token()
            print(f"   ✅ Token generated: {token[:20]}...")
            
        except Exception as e:
            print(f"   ❌ User creation error: {e}")
    
    print(f"\n4️⃣ Database connection test...")
    try:
        users_count = User.count()
        print(f"   ✅ Total users in database: {users_count}")
    except Exception as e:
        print(f"   ❌ Database connection error: {e}")

if __name__ == "__main__":
    debug_user_creation()
