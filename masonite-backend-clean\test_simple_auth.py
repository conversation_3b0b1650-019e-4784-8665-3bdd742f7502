#!/usr/bin/env python3
"""
Simple test script to verify authentication endpoints
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"
HEADERS = {
    'Content-Type': 'application/json',
    'Origin': 'http://localhost:4200'
}

def test_server_connection():
    """Test if server is running"""
    try:
        response = requests.get(f"http://localhost:3002", timeout=5)
        print(f"✅ Server is running - Status: {response.status_code}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ Server connection failed: {e}")
        return False

def test_signup():
    """Test user signup"""
    print("\n🧪 Testing User Signup...")
    
    # Test data
    signup_data = {
        "firstName": "Test",
        "lastName": "User", 
        "email": f"testuser_{int(time.time())}@example.com",
        "password": "TestPassword123!",
        "password_confirmation": "TestPassword123!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data, headers=HEADERS, timeout=10)
        print(f"📊 Signup Response Status: {response.status_code}")
        print(f"📊 Signup Response: {response.text[:200]}...")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Signup successful!")
            if 'token' in data:
                token = data['token']
                print(f"🔑 Token received: {token[:50]}...")
                return token, data.get('user', {})
        else:
            print(f"❌ Signup failed")
            
    except Exception as e:
        print(f"❌ Signup request error: {e}")
    
    return None, None

def test_login():
    """Test login with existing user"""
    print("\n🧪 Testing User Login...")
    
    # Use one of the existing test users
    login_data = {
        "email": "<EMAIL>",
        "password": "AdminPass123!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data, headers=HEADERS, timeout=10)
        print(f"📊 Login Response Status: {response.status_code}")
        print(f"📊 Login Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful!")
            if 'token' in data:
                token = data['token']
                print(f"🔑 Token received: {token[:50]}...")
                return token
        else:
            print(f"❌ Login failed")
            
    except Exception as e:
        print(f"❌ Login request error: {e}")
    
    return None

def main():
    """Main test function"""
    print("🚀 Starting Simple Authentication Tests")
    print("=" * 50)
    
    # Test server connection first
    if not test_server_connection():
        print("❌ Cannot connect to server. Make sure it's running on port 3002")
        return
    
    # Test signup
    token, user = test_signup()
    
    # Test login
    login_token = test_login()
    
    print("\n" + "=" * 50)
    print("🏁 Simple Authentication Tests Complete")
    
    if token or login_token:
        print("✅ JWT tokens are being generated successfully")
    else:
        print("❌ No JWT tokens received")

if __name__ == "__main__":
    main()
