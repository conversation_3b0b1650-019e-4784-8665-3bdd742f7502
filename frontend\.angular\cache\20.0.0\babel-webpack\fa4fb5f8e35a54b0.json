{"ast": null, "code": "import { ConnectableObservable } from 'rxjs';\nclass DataSource {}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n  // here, because of some internal apps.\n  return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\nexport { DataSource as D, isDataSource as i };", "map": {"version": 3, "names": ["ConnectableObservable", "DataSource", "isDataSource", "value", "connect", "D", "i"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/data-source-D34wiQZj.mjs"], "sourcesContent": ["import { ConnectableObservable } from 'rxjs';\n\nclass DataSource {\n}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n    // Check if the value is a DataSource by observing if it has a connect function. Cannot\n    // be checked as an `instanceof DataSource` since people could create their own sources\n    // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n    // here, because of some internal apps.\n    return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n\nexport { DataSource as D, isDataSource as i };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,MAAM;AAE5C,MAAMC,UAAU,CAAC;AAEjB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB;EACA;EACA;EACA;EACA,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,OAAO,KAAK,UAAU,IAAI,EAAED,KAAK,YAAYH,qBAAqB,CAAC;AACpG;AAEA,SAASC,UAAU,IAAII,CAAC,EAAEH,YAAY,IAAII,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}