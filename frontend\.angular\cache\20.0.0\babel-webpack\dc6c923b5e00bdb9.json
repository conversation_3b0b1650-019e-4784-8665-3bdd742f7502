{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, signal, EventEmitter, Injectable } from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  get value() {\n    return this.valueSignal();\n  }\n  /**\n   * The current 'ltr' or 'rtl' value.\n   */\n  valueSignal = signal('ltr');\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  change = new EventEmitter();\n  constructor() {\n    const _document = inject(DIR_DOCUMENT, {\n      optional: true\n    });\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.valueSignal.set(_resolveDirectionality(bodyDir || htmlDir || 'ltr'));\n    }\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Directionality_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Directionality)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Directionality,\n    factory: Directionality.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Directionality, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "DOCUMENT", "signal", "EventEmitter", "Injectable", "DIR_DOCUMENT", "providedIn", "factory", "DIR_DOCUMENT_FACTORY", "RTL_LOCALE_PATTERN", "_resolveDirectionality", "rawValue", "value", "toLowerCase", "navigator", "language", "test", "Directionality", "valueSignal", "change", "constructor", "_document", "optional", "bodyDir", "body", "dir", "htmlDir", "documentElement", "set", "ngOnDestroy", "complete", "ɵfac", "Directionality_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "ngDevMode", "ɵsetClassMetadata", "type", "args", "D", "_", "a"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/directionality-CChdj3az.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, signal, EventEmitter, Injectable } from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n    providedIn: 'root',\n    factory: DIR_DOCUMENT_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n    return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n    const value = rawValue?.toLowerCase() || '';\n    if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n        return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n    }\n    return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n    /** The current 'ltr' or 'rtl' value. */\n    get value() {\n        return this.valueSignal();\n    }\n    /**\n     * The current 'ltr' or 'rtl' value.\n     */\n    valueSignal = signal('ltr');\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n    change = new EventEmitter();\n    constructor() {\n        const _document = inject(DIR_DOCUMENT, { optional: true });\n        if (_document) {\n            const bodyDir = _document.body ? _document.body.dir : null;\n            const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n            this.valueSignal.set(_resolveDirectionality(bodyDir || htmlDir || 'ltr'));\n        }\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Directionality, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Directionality, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Directionality, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,QAAQ,eAAe;;AAElG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAIN,cAAc,CAAC,aAAa,EAAE;EACnDO,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoBA,CAAA,EAAG;EAC5B,OAAOR,MAAM,CAACC,QAAQ,CAAC;AAC3B;;AAEA;AACA,MAAMQ,kBAAkB,GAAG,oHAAoH;AAC/I;AACA,SAASC,sBAAsBA,CAACC,QAAQ,EAAE;EACtC,MAAMC,KAAK,GAAGD,QAAQ,EAAEE,WAAW,CAAC,CAAC,IAAI,EAAE;EAC3C,IAAID,KAAK,KAAK,MAAM,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAEC,QAAQ,EAAE;IAC7E,OAAON,kBAAkB,CAACO,IAAI,CAACF,SAAS,CAACC,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK;EACtE;EACA,OAAOH,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;AAC1C;AACA;AACA;AACA;AACA;AACA,MAAMK,cAAc,CAAC;EACjB;EACA,IAAIL,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACM,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIA,WAAW,GAAGhB,MAAM,CAAC,KAAK,CAAC;EAC3B;EACAiB,MAAM,GAAG,IAAIhB,YAAY,CAAC,CAAC;EAC3BiB,WAAWA,CAAA,EAAG;IACV,MAAMC,SAAS,GAAGrB,MAAM,CAACK,YAAY,EAAE;MAAEiB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC1D,IAAID,SAAS,EAAE;MACX,MAAME,OAAO,GAAGF,SAAS,CAACG,IAAI,GAAGH,SAAS,CAACG,IAAI,CAACC,GAAG,GAAG,IAAI;MAC1D,MAAMC,OAAO,GAAGL,SAAS,CAACM,eAAe,GAAGN,SAAS,CAACM,eAAe,CAACF,GAAG,GAAG,IAAI;MAChF,IAAI,CAACP,WAAW,CAACU,GAAG,CAAClB,sBAAsB,CAACa,OAAO,IAAIG,OAAO,IAAI,KAAK,CAAC,CAAC;IAC7E;EACJ;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC;EAC1B;EACA,OAAOC,IAAI,YAAAC,uBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFhB,cAAc;EAAA;EACjH,OAAOiB,KAAK,kBAD6EpC,EAAE,CAAAqC,kBAAA;IAAAC,KAAA,EACYnB,cAAc;IAAAV,OAAA,EAAdU,cAAc,CAAAc,IAAA;IAAAzB,UAAA,EAAc;EAAM;AAC7I;AACA;EAAA,QAAA+B,SAAA,oBAAAA,SAAA,KAH6FvC,EAAE,CAAAwC,iBAAA,CAGJrB,cAAc,EAAc,CAAC;IAC5GsB,IAAI,EAAEnC,UAAU;IAChBoC,IAAI,EAAE,CAAC;MAAElC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASW,cAAc,IAAIwB,CAAC,EAAE/B,sBAAsB,IAAIgC,CAAC,EAAErC,YAAY,IAAIsC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}