#!/usr/bin/env python3
"""
Test CORS Configuration Fix
Tests that CORS is properly configured for frontend communication
"""

import requests
import json

BASE_URL = "http://localhost:3002/api"

def test_cors_preflight(endpoint):
    """Test CORS preflight request"""
    url = f"{BASE_URL}{endpoint}"
    
    # Simulate preflight request from frontend
    headers = {
        'Origin': 'http://localhost:4200',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
    }
    
    try:
        response = requests.options(url, headers=headers, timeout=10)
        print(f"🔍 CORS Preflight for {endpoint}:")
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        # Check for required CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }
        
        print(f"   CORS Headers: {cors_headers}")
        return response.status_code, cors_headers
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None, {}

def test_actual_request(endpoint, method='GET', data=None):
    """Test actual request with CORS headers"""
    url = f"{BASE_URL}{endpoint}"
    
    headers = {
        'Origin': 'http://localhost:4200',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            return None, {}
        
        print(f"🔍 Actual {method} request for {endpoint}:")
        print(f"   Status: {response.status_code}")
        
        # Check for CORS headers in response
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Expose-Headers': response.headers.get('Access-Control-Expose-Headers')
        }
        
        print(f"   CORS Headers: {cors_headers}")
        return response.status_code, cors_headers
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None, {}

def main():
    print("🧪 Testing CORS Configuration Fix...")
    print("=" * 60)
    
    # Test endpoints that frontend will use
    test_endpoints = [
        ("/auth/signup", "POST"),
        ("/auth/login", "POST"),
        ("/auth/profile", "GET"),
        ("/auth/me", "GET"),
        ("/2fa/setup", "POST"),
        ("/auth/oauth/providers", "GET"),
        ("/payments/my-payments", "GET"),
        ("/otp/send", "POST")
    ]
    
    for endpoint, method in test_endpoints:
        print(f"\n🎯 Testing {method} {endpoint}")
        print("-" * 40)
        
        # Test preflight request
        if method == 'POST':
            preflight_status, preflight_headers = test_cors_preflight(endpoint)
            
            if preflight_status == 200:
                print("   ✅ Preflight request successful")
            else:
                print(f"   ❌ Preflight request failed: {preflight_status}")
        
        # Test actual request
        test_data = None
        if method == 'POST':
            if 'signup' in endpoint:
                test_data = {
                    'firstName': 'Test',
                    'lastName': 'User',
                    'email': '<EMAIL>',
                    'password': 'TestPass123!',
                    'password_confirmation': 'TestPass123!'
                }
            elif 'login' in endpoint:
                test_data = {
                    'email': '<EMAIL>',
                    'password': 'TestPass123!'
                }
            else:
                test_data = {}
        
        actual_status, actual_headers = test_actual_request(endpoint, method, test_data)
        
        if actual_status:
            print(f"   ✅ Actual request completed: {actual_status}")
        else:
            print("   ❌ Actual request failed")
    
    print("\n" + "=" * 60)
    print("✅ CORS Configuration Test Completed!")
    print("🎯 Check that all requests show proper CORS headers")
    print("🔍 Frontend should now be able to communicate with backend")

if __name__ == "__main__":
    main()
