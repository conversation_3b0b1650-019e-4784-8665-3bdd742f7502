{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"../../services/account-deletion.service\";\nimport * as i4 from \"../../services/auth.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction DeletionConfirmationComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"mat-spinner\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Processing your account deletion confirmation...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeletionConfirmationComponent_div_9_div_8_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1);\n  }\n}\nfunction DeletionConfirmationComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h4\");\n    i0.ɵɵtext(2, \"Preserved Data Summary:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, DeletionConfirmationComponent_div_9_div_8_li_4_Template, 2, 1, \"li\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 17)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Your preserved data will be automatically deleted after the retention period expires. \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.preservedDataItems);\n  }\n}\nfunction DeletionConfirmationComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Your account has been successfully scheduled for deletion.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Deletion ID: \");\n    i0.ɵɵelementStart(6, \"code\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, DeletionConfirmationComponent_div_9_div_8_Template, 9, 1, \"div\", 13);\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"h4\");\n    i0.ɵɵtext(11, \"What happens next:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ul\")(13, \"li\");\n    i0.ɵɵtext(14, \"Your account is now permanently deleted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"li\");\n    i0.ɵɵtext(16, \"You will no longer be able to log in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"li\");\n    i0.ɵɵtext(18, \"If you preserved any data, you'll receive an email before it's permanently deleted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"You can create a new account at any time using the same email address\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.deletionId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.preservedDataSummary);\n  }\n}\nfunction DeletionConfirmationComponent_div_10_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nfunction DeletionConfirmationComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"We couldn't process your deletion confirmation.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, DeletionConfirmationComponent_div_10_p_4_Template, 2, 1, \"p\", 19);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"This could happen if:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\")(8, \"li\");\n    i0.ɵɵtext(9, \"The confirmation link has expired\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"li\");\n    i0.ɵɵtext(11, \"The confirmation link has already been used\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"li\");\n    i0.ɵɵtext(13, \"There was a technical error\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.errorMessage);\n  }\n}\nfunction DeletionConfirmationComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Invalid confirmation link.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"The confirmation token is missing or invalid. Please check your email and try again with the correct confirmation link.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeletionConfirmationComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DeletionConfirmationComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Create New Account \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeletionConfirmationComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function DeletionConfirmationComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToSupport());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Contact Support \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class DeletionConfirmationComponent {\n  constructor(route, router, snackBar, accountDeletionService, authService) {\n    this.route = route;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.accountDeletionService = accountDeletionService;\n    this.authService = authService;\n    this.status = 'loading';\n    this.token = null;\n    this.deletionId = '';\n    this.preservedDataSummary = null;\n    this.preservedDataItems = [];\n    this.errorMessage = '';\n  }\n  ngOnInit() {\n    this.token = this.route.snapshot.queryParamMap.get('token');\n    if (!this.token) {\n      this.status = 'invalid';\n      return;\n    }\n    this.confirmDeletion();\n  }\n  confirmDeletion() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.token) return;\n      try {\n        const result = yield _this.accountDeletionService.confirmDeletion(_this.token).toPromise();\n        _this.status = 'success';\n        _this.deletionId = result?.deletionId || '';\n        _this.preservedDataSummary = result?.preservedDataSummary;\n        if (_this.preservedDataSummary) {\n          _this.preservedDataItems = _this.accountDeletionService.formatPreservedDataSummary(_this.preservedDataSummary);\n        }\n        // Clear any existing authentication since account is deleted\n        _this.authService.logout();\n      } catch (error) {\n        console.error('Error confirming deletion:', error);\n        _this.status = 'error';\n        _this.errorMessage = error.error?.message || error.message || 'An unexpected error occurred';\n      }\n    })();\n  }\n  getStatusIcon() {\n    switch (this.status) {\n      case 'loading':\n        return 'hourglass_empty';\n      case 'success':\n        return 'check_circle';\n      case 'error':\n        return 'error';\n      case 'invalid':\n        return 'warning';\n      default:\n        return 'help';\n    }\n  }\n  getStatusTitle() {\n    switch (this.status) {\n      case 'loading':\n        return 'Confirming Account Deletion';\n      case 'success':\n        return 'Account Deletion Confirmed';\n      case 'error':\n        return 'Deletion Confirmation Failed';\n      case 'invalid':\n        return 'Invalid Confirmation Link';\n      default:\n        return 'Account Deletion';\n    }\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  goToHome() {\n    this.router.navigate(['/']);\n  }\n  goToSupport() {\n    // You can implement a support page or external link\n    window.open('mailto:<EMAIL>?subject=Account Deletion Issue', '_blank');\n  }\n  static #_ = this.ɵfac = function DeletionConfirmationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DeletionConfirmationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MatSnackBar), i0.ɵɵdirectiveInject(i3.AccountDeletionService), i0.ɵɵdirectiveInject(i4.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DeletionConfirmationComponent,\n    selectors: [[\"app-deletion-confirmation\"]],\n    decls: 19,\n    vars: 9,\n    consts: [[1, \"confirmation-container\"], [1, \"confirmation-card\"], [3, \"color\"], [\"class\", \"loading-section\", 4, \"ngIf\"], [\"class\", \"success-section\", 4, \"ngIf\"], [\"class\", \"error-section\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"loading-section\"], [\"diameter\", \"40\"], [1, \"success-section\"], [\"class\", \"preserved-data-info\", 4, \"ngIf\"], [1, \"next-steps\"], [1, \"preserved-data-info\"], [4, \"ngFor\", \"ngForOf\"], [1, \"info-text\"], [1, \"error-section\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n    template: function DeletionConfirmationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n        i0.ɵɵtext(5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"mat-card-content\");\n        i0.ɵɵtemplate(8, DeletionConfirmationComponent_div_8_Template, 4, 0, \"div\", 3)(9, DeletionConfirmationComponent_div_9_Template, 21, 2, \"div\", 4)(10, DeletionConfirmationComponent_div_10_Template, 14, 1, \"div\", 5)(11, DeletionConfirmationComponent_div_11_Template, 6, 0, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"mat-card-actions\", 6);\n        i0.ɵɵtemplate(13, DeletionConfirmationComponent_button_13_Template, 4, 0, \"button\", 7)(14, DeletionConfirmationComponent_button_14_Template, 4, 0, \"button\", 8);\n        i0.ɵɵelementStart(15, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function DeletionConfirmationComponent_Template_button_click_15_listener() {\n          return ctx.goToHome();\n        });\n        i0.ɵɵelementStart(16, \"mat-icon\");\n        i0.ɵɵtext(17, \"home\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(18, \" Go to Home \");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"color\", ctx.status === \"loading\" ? \"primary\" : ctx.status === \"success\" ? \"primary\" : \"warn\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.getStatusIcon(), \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.getStatusTitle(), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"loading\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"success\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"error\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"invalid\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"success\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"error\" || ctx.status === \"invalid\");\n      }\n    },\n    dependencies: [CommonModule, i5.NgForOf, i5.NgIf, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, MatButtonModule, i7.MatButton, MatIconModule, i8.MatIcon, MatProgressSpinnerModule, i9.MatProgressSpinner],\n    styles: [\".confirmation-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      padding: 20px;\\n      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n    }\\n\\n    .confirmation-card[_ngcontent-%COMP%] {\\n      max-width: 600px;\\n      width: 100%;\\n    }\\n\\n    .loading-section[_ngcontent-%COMP%] {\\n      text-align: center;\\n      padding: 2rem 0;\\n    }\\n\\n    .loading-section[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n      margin: 0 auto 1rem;\\n    }\\n\\n    .success-section[_ngcontent-%COMP%], .error-section[_ngcontent-%COMP%] {\\n      padding: 1rem 0;\\n    }\\n\\n    .preserved-data-info[_ngcontent-%COMP%] {\\n      background-color: #f8f9fa;\\n      padding: 1rem;\\n      border-radius: 8px;\\n      margin: 1rem 0;\\n    }\\n\\n    .preserved-data-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n      margin-top: 0;\\n      color: #495057;\\n    }\\n\\n    .preserved-data-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n      margin: 0.5rem 0;\\n    }\\n\\n    .info-text[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.5rem;\\n      font-size: 0.9rem;\\n      color: #6c757d;\\n      margin-top: 1rem;\\n    }\\n\\n    .next-steps[_ngcontent-%COMP%] {\\n      margin-top: 1.5rem;\\n    }\\n\\n    .next-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n      color: #495057;\\n      margin-bottom: 0.5rem;\\n    }\\n\\n    .next-steps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n      line-height: 1.6;\\n    }\\n\\n    code[_ngcontent-%COMP%] {\\n      background-color: #f8f9fa;\\n      padding: 0.2rem 0.4rem;\\n      border-radius: 4px;\\n      font-family: 'Courier New', monospace;\\n      font-size: 0.9rem;\\n    }\\n\\n    mat-card-title[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.5rem;\\n    }\\n\\n    mat-card-actions[_ngcontent-%COMP%] {\\n      gap: 0.5rem;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hY2NvdW50LWRlbGV0aW9uL2RlbGV0aW9uLWNvbmZpcm1hdGlvbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLHVCQUF1QjtNQUN2QixtQkFBbUI7TUFDbkIsaUJBQWlCO01BQ2pCLGFBQWE7TUFDYiw2REFBNkQ7SUFDL0Q7O0lBRUE7TUFDRSxnQkFBZ0I7TUFDaEIsV0FBVztJQUNiOztJQUVBO01BQ0Usa0JBQWtCO01BQ2xCLGVBQWU7SUFDakI7O0lBRUE7TUFDRSxtQkFBbUI7SUFDckI7O0lBRUE7TUFDRSxlQUFlO0lBQ2pCOztJQUVBO01BQ0UseUJBQXlCO01BQ3pCLGFBQWE7TUFDYixrQkFBa0I7TUFDbEIsY0FBYztJQUNoQjs7SUFFQTtNQUNFLGFBQWE7TUFDYixjQUFjO0lBQ2hCOztJQUVBO01BQ0UsZ0JBQWdCO0lBQ2xCOztJQUVBO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixXQUFXO01BQ1gsaUJBQWlCO01BQ2pCLGNBQWM7TUFDZCxnQkFBZ0I7SUFDbEI7O0lBRUE7TUFDRSxrQkFBa0I7SUFDcEI7O0lBRUE7TUFDRSxjQUFjO01BQ2QscUJBQXFCO0lBQ3ZCOztJQUVBO01BQ0UsZ0JBQWdCO0lBQ2xCOztJQUVBO01BQ0UseUJBQXlCO01BQ3pCLHNCQUFzQjtNQUN0QixrQkFBa0I7TUFDbEIscUNBQXFDO01BQ3JDLGlCQUFpQjtJQUNuQjs7SUFFQTtNQUNFLGFBQWE7TUFDYixtQkFBbUI7TUFDbkIsV0FBVztJQUNiOztJQUVBO01BQ0UsV0FBVztJQUNiIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmNvbmZpcm1hdGlvbi1jb250YWluZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNWY3ZmEgMCUsICNjM2NmZTIgMTAwJSk7XG4gICAgfVxuXG4gICAgLmNvbmZpcm1hdGlvbi1jYXJkIHtcbiAgICAgIG1heC13aWR0aDogNjAwcHg7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG5cbiAgICAubG9hZGluZy1zZWN0aW9uIHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIHBhZGRpbmc6IDJyZW0gMDtcbiAgICB9XG5cbiAgICAubG9hZGluZy1zZWN0aW9uIG1hdC1zcGlubmVyIHtcbiAgICAgIG1hcmdpbjogMCBhdXRvIDFyZW07XG4gICAgfVxuXG4gICAgLnN1Y2Nlc3Mtc2VjdGlvbiwgLmVycm9yLXNlY3Rpb24ge1xuICAgICAgcGFkZGluZzogMXJlbSAwO1xuICAgIH1cblxuICAgIC5wcmVzZXJ2ZWQtZGF0YS1pbmZvIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG4gICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgbWFyZ2luOiAxcmVtIDA7XG4gICAgfVxuXG4gICAgLnByZXNlcnZlZC1kYXRhLWluZm8gaDQge1xuICAgICAgbWFyZ2luLXRvcDogMDtcbiAgICAgIGNvbG9yOiAjNDk1MDU3O1xuICAgIH1cblxuICAgIC5wcmVzZXJ2ZWQtZGF0YS1pbmZvIHVsIHtcbiAgICAgIG1hcmdpbjogMC41cmVtIDA7XG4gICAgfVxuXG4gICAgLmluZm8tdGV4dCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMC41cmVtO1xuICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICBjb2xvcjogIzZjNzU3ZDtcbiAgICAgIG1hcmdpbi10b3A6IDFyZW07XG4gICAgfVxuXG4gICAgLm5leHQtc3RlcHMge1xuICAgICAgbWFyZ2luLXRvcDogMS41cmVtO1xuICAgIH1cblxuICAgIC5uZXh0LXN0ZXBzIGg0IHtcbiAgICAgIGNvbG9yOiAjNDk1MDU3O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuICAgIH1cblxuICAgIC5uZXh0LXN0ZXBzIHVsIHtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XG4gICAgfVxuXG4gICAgY29kZSB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgICAgcGFkZGluZzogMC4ycmVtIDAuNHJlbTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7XG4gICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICB9XG5cbiAgICBtYXQtY2FyZC10aXRsZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMC41cmVtO1xuICAgIH1cblxuICAgIG1hdC1jYXJkLWFjdGlvbnMge1xuICAgICAgZ2FwOiAwLjVyZW07XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r1", "ɵɵtemplate", "DeletionConfirmationComponent_div_9_div_8_li_4_Template", "ɵɵproperty", "ctx_r1", "preservedDataItems", "DeletionConfirmationComponent_div_9_div_8_Template", "deletionId", "preservedDataSummary", "errorMessage", "DeletionConfirmationComponent_div_10_p_4_Template", "ɵɵlistener", "DeletionConfirmationComponent_button_13_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "goToLogin", "DeletionConfirmationComponent_button_14_Template_button_click_0_listener", "_r4", "goToSupport", "DeletionConfirmationComponent", "constructor", "route", "router", "snackBar", "accountDeletionService", "authService", "status", "token", "ngOnInit", "snapshot", "queryParamMap", "get", "confirmDeletion", "_this", "_asyncToGenerator", "result", "to<PERSON>romise", "formatPreservedDataSummary", "logout", "error", "console", "message", "getStatusIcon", "getStatusTitle", "navigate", "goToHome", "window", "open", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "MatSnackBar", "i3", "AccountDeletionService", "i4", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "DeletionConfirmationComponent_Template", "rf", "ctx", "DeletionConfirmationComponent_div_8_Template", "DeletionConfirmationComponent_div_9_Template", "DeletionConfirmationComponent_div_10_Template", "DeletionConfirmationComponent_div_11_Template", "DeletionConfirmationComponent_button_13_Template", "DeletionConfirmationComponent_button_14_Template", "DeletionConfirmationComponent_Template_button_click_15_listener", "ɵɵtextInterpolate1", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i7", "MatButton", "i8", "MatIcon", "i9", "MatProgressSpinner", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\account-deletion\\deletion-confirmation.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { AccountDeletionService } from '../../services/account-deletion.service';\r\nimport { AuthService } from '../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-deletion-confirmation',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatProgressSpinnerModule\r\n  ],\r\n  template: `\r\n    <div class=\"confirmation-container\">\r\n      <mat-card class=\"confirmation-card\">\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon [color]=\"status === 'loading' ? 'primary' : (status === 'success' ? 'primary' : 'warn')\">\r\n              {{ getStatusIcon() }}\r\n            </mat-icon>\r\n            {{ getStatusTitle() }}\r\n          </mat-card-title>\r\n        </mat-card-header>\r\n        \r\n        <mat-card-content>\r\n          <div *ngIf=\"status === 'loading'\" class=\"loading-section\">\r\n            <mat-spinner diameter=\"40\"></mat-spinner>\r\n            <p>Processing your account deletion confirmation...</p>\r\n          </div>\r\n\r\n          <div *ngIf=\"status === 'success'\" class=\"success-section\">\r\n            <p><strong>Your account has been successfully scheduled for deletion.</strong></p>\r\n            <p>Deletion ID: <code>{{ deletionId }}</code></p>\r\n            \r\n            <div *ngIf=\"preservedDataSummary\" class=\"preserved-data-info\">\r\n              <h4>Preserved Data Summary:</h4>\r\n              <ul>\r\n                <li *ngFor=\"let item of preservedDataItems\">{{ item }}</li>\r\n              </ul>\r\n              <p class=\"info-text\">\r\n                <mat-icon>info</mat-icon>\r\n                Your preserved data will be automatically deleted after the retention period expires.\r\n              </p>\r\n            </div>\r\n\r\n            <div class=\"next-steps\">\r\n              <h4>What happens next:</h4>\r\n              <ul>\r\n                <li>Your account is now permanently deleted</li>\r\n                <li>You will no longer be able to log in</li>\r\n                <li>If you preserved any data, you'll receive an email before it's permanently deleted</li>\r\n                <li>You can create a new account at any time using the same email address</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"status === 'error'\" class=\"error-section\">\r\n            <p><strong>We couldn't process your deletion confirmation.</strong></p>\r\n            <p *ngIf=\"errorMessage\">{{ errorMessage }}</p>\r\n            <p>This could happen if:</p>\r\n            <ul>\r\n              <li>The confirmation link has expired</li>\r\n              <li>The confirmation link has already been used</li>\r\n              <li>There was a technical error</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div *ngIf=\"status === 'invalid'\" class=\"error-section\">\r\n            <p><strong>Invalid confirmation link.</strong></p>\r\n            <p>The confirmation token is missing or invalid. Please check your email and try again with the correct confirmation link.</p>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions align=\"end\">\r\n          <button *ngIf=\"status === 'success'\" mat-raised-button color=\"primary\" (click)=\"goToLogin()\">\r\n            <mat-icon>login</mat-icon>\r\n            Create New Account\r\n          </button>\r\n          \r\n          <button *ngIf=\"status === 'error' || status === 'invalid'\" mat-button (click)=\"goToSupport()\">\r\n            <mat-icon>help</mat-icon>\r\n            Contact Support\r\n          </button>\r\n          \r\n          <button mat-button (click)=\"goToHome()\">\r\n            <mat-icon>home</mat-icon>\r\n            Go to Home\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .confirmation-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      padding: 20px;\r\n      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n    }\r\n\r\n    .confirmation-card {\r\n      max-width: 600px;\r\n      width: 100%;\r\n    }\r\n\r\n    .loading-section {\r\n      text-align: center;\r\n      padding: 2rem 0;\r\n    }\r\n\r\n    .loading-section mat-spinner {\r\n      margin: 0 auto 1rem;\r\n    }\r\n\r\n    .success-section, .error-section {\r\n      padding: 1rem 0;\r\n    }\r\n\r\n    .preserved-data-info {\r\n      background-color: #f8f9fa;\r\n      padding: 1rem;\r\n      border-radius: 8px;\r\n      margin: 1rem 0;\r\n    }\r\n\r\n    .preserved-data-info h4 {\r\n      margin-top: 0;\r\n      color: #495057;\r\n    }\r\n\r\n    .preserved-data-info ul {\r\n      margin: 0.5rem 0;\r\n    }\r\n\r\n    .info-text {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      font-size: 0.9rem;\r\n      color: #6c757d;\r\n      margin-top: 1rem;\r\n    }\r\n\r\n    .next-steps {\r\n      margin-top: 1.5rem;\r\n    }\r\n\r\n    .next-steps h4 {\r\n      color: #495057;\r\n      margin-bottom: 0.5rem;\r\n    }\r\n\r\n    .next-steps ul {\r\n      line-height: 1.6;\r\n    }\r\n\r\n    code {\r\n      background-color: #f8f9fa;\r\n      padding: 0.2rem 0.4rem;\r\n      border-radius: 4px;\r\n      font-family: 'Courier New', monospace;\r\n      font-size: 0.9rem;\r\n    }\r\n\r\n    mat-card-title {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n    }\r\n\r\n    mat-card-actions {\r\n      gap: 0.5rem;\r\n    }\r\n  `]\r\n})\r\nexport class DeletionConfirmationComponent implements OnInit {\r\n  status: 'loading' | 'success' | 'error' | 'invalid' = 'loading';\r\n  token: string | null = null;\r\n  deletionId: string = '';\r\n  preservedDataSummary: any = null;\r\n  preservedDataItems: string[] = [];\r\n  errorMessage: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar,\r\n    private accountDeletionService: AccountDeletionService,\r\n    private authService: AuthService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.token = this.route.snapshot.queryParamMap.get('token');\r\n    \r\n    if (!this.token) {\r\n      this.status = 'invalid';\r\n      return;\r\n    }\r\n\r\n    this.confirmDeletion();\r\n  }\r\n\r\n  private async confirmDeletion(): Promise<void> {\r\n    if (!this.token) return;\r\n\r\n    try {\r\n      const result = await this.accountDeletionService.confirmDeletion(this.token).toPromise();\r\n      \r\n      this.status = 'success';\r\n      this.deletionId = result?.deletionId || '';\r\n      this.preservedDataSummary = result?.preservedDataSummary;\r\n      \r\n      if (this.preservedDataSummary) {\r\n        this.preservedDataItems = this.accountDeletionService.formatPreservedDataSummary(this.preservedDataSummary);\r\n      }\r\n\r\n      // Clear any existing authentication since account is deleted\r\n      this.authService.logout();\r\n      \r\n    } catch (error: any) {\r\n      console.error('Error confirming deletion:', error);\r\n      this.status = 'error';\r\n      this.errorMessage = error.error?.message || error.message || 'An unexpected error occurred';\r\n    }\r\n  }\r\n\r\n  getStatusIcon(): string {\r\n    switch (this.status) {\r\n      case 'loading': return 'hourglass_empty';\r\n      case 'success': return 'check_circle';\r\n      case 'error': return 'error';\r\n      case 'invalid': return 'warning';\r\n      default: return 'help';\r\n    }\r\n  }\r\n\r\n  getStatusTitle(): string {\r\n    switch (this.status) {\r\n      case 'loading': return 'Confirming Account Deletion';\r\n      case 'success': return 'Account Deletion Confirmed';\r\n      case 'error': return 'Deletion Confirmation Failed';\r\n      case 'invalid': return 'Invalid Confirmation Link';\r\n      default: return 'Account Deletion';\r\n    }\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  goToHome(): void {\r\n    this.router.navigate(['/']);\r\n  }\r\n\r\n  goToSupport(): void {\r\n    // You can implement a support page or external link\r\n    window.open('mailto:<EMAIL>?subject=Account Deletion Issue', '_blank');\r\n  }\r\n}\r\n"], "mappings": ";AAGA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;;;;;;;;;;;;;IA2BnEC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,sBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,uDAAgD;IACrDH,EADqD,CAAAI,YAAA,EAAI,EACnD;;;;;IASAJ,EAAA,CAAAC,cAAA,SAA4C;IAAAD,EAAA,CAAAG,MAAA,GAAU;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAAfJ,EAAA,CAAAK,SAAA,EAAU;IAAVL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAU;;;;;IAFxDP,EADF,CAAAC,cAAA,cAA8D,SACxD;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAQ,UAAA,IAAAC,uDAAA,iBAA4C;IAC9CT,EAAA,CAAAI,YAAA,EAAK;IAEHJ,EADF,CAAAC,cAAA,YAAqB,eACT;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,8FACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACA;;;;IANmBJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAU,UAAA,YAAAC,MAAA,CAAAC,kBAAA,CAAqB;;;;;IAN3CZ,EADL,CAAAC,cAAA,cAA0D,QACrD,aAAQ;IAAAD,EAAA,CAAAG,MAAA,iEAA0D;IAASH,EAAT,CAAAI,YAAA,EAAS,EAAI;IAClFJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAOH,EAAP,CAAAI,YAAA,EAAO,EAAI;IAEjDJ,EAAA,CAAAQ,UAAA,IAAAK,kDAAA,kBAA8D;IAY5Db,EADF,CAAAC,cAAA,cAAwB,UAClB;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEzBJ,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAG,MAAA,+CAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,4CAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,0FAAkF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3FJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,6EAAqE;IAG/EH,EAH+E,CAAAI,YAAA,EAAK,EAC3E,EACD,EACF;;;;IAtBkBJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAK,MAAA,CAAAG,UAAA,CAAgB;IAEhCd,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAU,UAAA,SAAAC,MAAA,CAAAI,oBAAA,CAA0B;;;;;IAwBhCf,EAAA,CAAAC,cAAA,QAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAtBJ,EAAA,CAAAK,SAAA,EAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAK,MAAA,CAAAK,YAAA,CAAkB;;;;;IADvChB,EADL,CAAAC,cAAA,cAAsD,QACjD,aAAQ;IAAAD,EAAA,CAAAG,MAAA,sDAA+C;IAASH,EAAT,CAAAI,YAAA,EAAS,EAAI;IACvEJ,EAAA,CAAAQ,UAAA,IAAAS,iDAAA,gBAAwB;IACxBjB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE1BJ,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAG,MAAA,wCAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1CJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,mDAA2C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpDJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,mCAA2B;IAEnCH,EAFmC,CAAAI,YAAA,EAAK,EACjC,EACD;;;;IAPAJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAU,UAAA,SAAAC,MAAA,CAAAK,YAAA,CAAkB;;;;;IAUnBhB,EADL,CAAAC,cAAA,cAAwD,QACnD,aAAQ;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAASH,EAAT,CAAAI,YAAA,EAAS,EAAI;IAClDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8HAAuH;IAC5HH,EAD4H,CAAAI,YAAA,EAAI,EAC1H;;;;;;IAINJ,EAAA,CAAAC,cAAA,iBAA6F;IAAtBD,EAAA,CAAAkB,UAAA,mBAAAC,yEAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAX,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASZ,MAAA,CAAAa,SAAA,EAAW;IAAA,EAAC;IAC1FxB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAETJ,EAAA,CAAAC,cAAA,gBAA8F;IAAxBD,EAAA,CAAAkB,UAAA,mBAAAO,yEAAA;MAAAzB,EAAA,CAAAoB,aAAA,CAAAM,GAAA;MAAA,MAAAf,MAAA,GAAAX,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASZ,MAAA,CAAAgB,WAAA,EAAa;IAAA,EAAC;IAC3F3B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;AA+FnB,OAAM,MAAOwB,6BAA6B;EAQxCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,QAAqB,EACrBC,sBAA8C,EAC9CC,WAAwB;IAJxB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,WAAW,GAAXA,WAAW;IAZrB,KAAAC,MAAM,GAAgD,SAAS;IAC/D,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAtB,UAAU,GAAW,EAAE;IACvB,KAAAC,oBAAoB,GAAQ,IAAI;IAChC,KAAAH,kBAAkB,GAAa,EAAE;IACjC,KAAAI,YAAY,GAAW,EAAE;EAQtB;EAEHqB,QAAQA,CAAA;IACN,IAAI,CAACD,KAAK,GAAG,IAAI,CAACN,KAAK,CAACQ,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC;IAE3D,IAAI,CAAC,IAAI,CAACJ,KAAK,EAAE;MACf,IAAI,CAACD,MAAM,GAAG,SAAS;MACvB;IACF;IAEA,IAAI,CAACM,eAAe,EAAE;EACxB;EAEcA,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAI,CAACD,KAAI,CAACN,KAAK,EAAE;MAEjB,IAAI;QACF,MAAMQ,MAAM,SAASF,KAAI,CAACT,sBAAsB,CAACQ,eAAe,CAACC,KAAI,CAACN,KAAK,CAAC,CAACS,SAAS,EAAE;QAExFH,KAAI,CAACP,MAAM,GAAG,SAAS;QACvBO,KAAI,CAAC5B,UAAU,GAAG8B,MAAM,EAAE9B,UAAU,IAAI,EAAE;QAC1C4B,KAAI,CAAC3B,oBAAoB,GAAG6B,MAAM,EAAE7B,oBAAoB;QAExD,IAAI2B,KAAI,CAAC3B,oBAAoB,EAAE;UAC7B2B,KAAI,CAAC9B,kBAAkB,GAAG8B,KAAI,CAACT,sBAAsB,CAACa,0BAA0B,CAACJ,KAAI,CAAC3B,oBAAoB,CAAC;QAC7G;QAEA;QACA2B,KAAI,CAACR,WAAW,CAACa,MAAM,EAAE;MAE3B,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDN,KAAI,CAACP,MAAM,GAAG,OAAO;QACrBO,KAAI,CAAC1B,YAAY,GAAGgC,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAIF,KAAK,CAACE,OAAO,IAAI,8BAA8B;MAC7F;IAAC;EACH;EAEAC,aAAaA,CAAA;IACX,QAAQ,IAAI,CAAChB,MAAM;MACjB,KAAK,SAAS;QAAE,OAAO,iBAAiB;MACxC,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,MAAM;IACxB;EACF;EAEAiB,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAACjB,MAAM;MACjB,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,SAAS;QAAE,OAAO,4BAA4B;MACnD,KAAK,OAAO;QAAE,OAAO,8BAA8B;MACnD,KAAK,SAAS;QAAE,OAAO,2BAA2B;MAClD;QAAS,OAAO,kBAAkB;IACpC;EACF;EAEAX,SAASA,CAAA;IACP,IAAI,CAACO,MAAM,CAACsB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACvB,MAAM,CAACsB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEA1B,WAAWA,CAAA;IACT;IACA4B,MAAM,CAACC,IAAI,CAAC,2DAA2D,EAAE,QAAQ,CAAC;EACpF;EAAC,QAAAC,CAAA,G;qCAlFU7B,6BAA6B,EAAA5B,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7D,EAAA,CAAA0D,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAA0D,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAAjE,EAAA,CAAA0D,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7BxC,6BAA6B;IAAAyC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhK9B3E,EAJR,CAAAC,cAAA,aAAoC,kBACE,sBACjB,qBACC,kBACqF;QACjGD,EAAA,CAAAG,MAAA,GACF;QAAAH,EAAA,CAAAI,YAAA,EAAW;QACXJ,EAAA,CAAAG,MAAA,GACF;QACFH,EADE,CAAAI,YAAA,EAAiB,EACD;QAElBJ,EAAA,CAAAC,cAAA,uBAAkB;QA2ChBD,EA1CA,CAAAQ,UAAA,IAAAqE,4CAAA,iBAA0D,IAAAC,4CAAA,kBAKA,KAAAC,6CAAA,kBA0BJ,KAAAC,6CAAA,iBAWE;QAI1DhF,EAAA,CAAAI,YAAA,EAAmB;QAEnBJ,EAAA,CAAAC,cAAA,2BAA8B;QAM5BD,EALA,CAAAQ,UAAA,KAAAyE,gDAAA,oBAA6F,KAAAC,gDAAA,oBAKC;QAK9FlF,EAAA,CAAAC,cAAA,iBAAwC;QAArBD,EAAA,CAAAkB,UAAA,mBAAAiE,gEAAA;UAAA,OAASP,GAAA,CAAAtB,QAAA,EAAU;QAAA,EAAC;QACrCtD,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAG,MAAA,YAAI;QAAAH,EAAA,CAAAI,YAAA,EAAW;QACzBJ,EAAA,CAAAG,MAAA,oBACF;QAGNH,EAHM,CAAAI,YAAA,EAAS,EACQ,EACV,EACP;;;QAzEYJ,EAAA,CAAAK,SAAA,GAAwF;QAAxFL,EAAA,CAAAU,UAAA,UAAAkE,GAAA,CAAAzC,MAAA,6BAAAyC,GAAA,CAAAzC,MAAA,oCAAwF;QAChGnC,EAAA,CAAAK,SAAA,EACF;QADEL,EAAA,CAAAoF,kBAAA,MAAAR,GAAA,CAAAzB,aAAA,QACF;QACAnD,EAAA,CAAAK,SAAA,EACF;QADEL,EAAA,CAAAoF,kBAAA,MAAAR,GAAA,CAAAxB,cAAA,QACF;QAIMpD,EAAA,CAAAK,SAAA,GAA0B;QAA1BL,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAAzC,MAAA,eAA0B;QAK1BnC,EAAA,CAAAK,SAAA,EAA0B;QAA1BL,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAAzC,MAAA,eAA0B;QA0B1BnC,EAAA,CAAAK,SAAA,EAAwB;QAAxBL,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAAzC,MAAA,aAAwB;QAWxBnC,EAAA,CAAAK,SAAA,EAA0B;QAA1BL,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAAzC,MAAA,eAA0B;QAOvBnC,EAAA,CAAAK,SAAA,GAA0B;QAA1BL,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAAzC,MAAA,eAA0B;QAK1BnC,EAAA,CAAAK,SAAA,EAAgD;QAAhDL,EAAA,CAAAU,UAAA,SAAAkE,GAAA,CAAAzC,MAAA,gBAAAyC,GAAA,CAAAzC,MAAA,eAAgD;;;mBAzE/DxC,YAAY,EAAA0F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ3F,aAAa,EAAA4F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,YAAA,EACbhG,eAAe,EAAAiG,EAAA,CAAAC,SAAA,EACfjG,aAAa,EAAAkG,EAAA,CAAAC,OAAA,EACblG,wBAAwB,EAAAmG,EAAA,CAAAC,kBAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}