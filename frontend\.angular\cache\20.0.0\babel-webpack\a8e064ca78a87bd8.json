{"ast": null, "code": "import { a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n        operation = _ViewRepeaterOperation.INSERTED;\n      } else if (currentIndex == null) {\n        viewContainerRef.remove(adjustedPreviousIndex);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {}\n}\nexport { _DisposeViewRepeaterStrategy as _ };", "map": {"version": 3, "names": ["a", "_ViewRepeaterOperation", "_DisposeViewRepeaterStrategy", "applyChanges", "changes", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "insertContext", "createEmbeddedView", "templateRef", "context", "index", "INSERTED", "remove", "REMOVED", "get", "move", "MOVED", "detach", "_"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/dispose-view-repeater-strategy-D_JReLI1.mjs"], "sourcesContent": ["import { a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n                operation = _ViewRepeaterOperation.INSERTED;\n            }\n            else if (currentIndex == null) {\n                viewContainerRef.remove(adjustedPreviousIndex);\n                operation = _ViewRepeaterOperation.REMOVED;\n            }\n            else {\n                view = viewContainerRef.get(adjustedPreviousIndex);\n                viewContainerRef.move(view, currentIndex);\n                operation = _ViewRepeaterOperation.MOVED;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() { }\n}\n\nexport { _DisposeViewRepeaterStrategy as _ };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,sBAAsB,QAAQ,+CAA+C;;AAE3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5FJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B,MAAMC,aAAa,GAAGV,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QACrFC,IAAI,GAAGR,gBAAgB,CAACY,kBAAkB,CAACD,aAAa,CAACE,WAAW,EAAEF,aAAa,CAACG,OAAO,EAAEH,aAAa,CAACI,KAAK,CAAC;QACjHN,SAAS,GAAGb,sBAAsB,CAACoB,QAAQ;MAC/C,CAAC,MACI,IAAIT,YAAY,IAAI,IAAI,EAAE;QAC3BP,gBAAgB,CAACiB,MAAM,CAACX,qBAAqB,CAAC;QAC9CG,SAAS,GAAGb,sBAAsB,CAACsB,OAAO;MAC9C,CAAC,MACI;QACDV,IAAI,GAAGR,gBAAgB,CAACmB,GAAG,CAACb,qBAAqB,CAAC;QAClDN,gBAAgB,CAACoB,IAAI,CAACZ,IAAI,EAAED,YAAY,CAAC;QACzCE,SAAS,GAAGb,sBAAsB,CAACyB,KAAK;MAC5C;MACA,IAAIlB,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAiB,MAAMA,CAAA,EAAG,CAAE;AACf;AAEA,SAASzB,4BAA4B,IAAI0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}