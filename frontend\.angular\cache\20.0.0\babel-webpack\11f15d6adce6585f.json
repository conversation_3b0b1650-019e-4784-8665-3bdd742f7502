{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/two-factor.service\";\nimport * as i3 from \"../../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction Disable2FAConfirmComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"mat-spinner\", 6);\n    i0.ɵɵelementStart(2, \"h2\");\n    i0.ɵɵtext(3, \"Processing your request...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please wait while we disable two-factor authentication on your account.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Disable2FAConfirmComponent_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h3\");\n    i0.ɵɵtext(2, \"Account Details:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"strong\");\n    i0.ɵɵtext(9, \"Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userInfo.email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.userInfo.firstName, \" \", ctx_r1.userInfo.lastName);\n  }\n}\nfunction Disable2FAConfirmComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\", 8);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Two-Factor Authentication Disabled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6, \" Two-factor authentication has been successfully disabled on your account. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, Disable2FAConfirmComponent_div_3_div_7_Template, 11, 3, \"div\", 10);\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"mat-icon\", 12);\n    i0.ɵɵtext(10, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"h4\");\n    i0.ɵɵtext(13, \"Important Security Notice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15, \"Your account is now less secure without two-factor authentication. Consider:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ul\")(17, \"li\");\n    i0.ɵɵtext(18, \"Using a strong, unique password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"Re-enabling 2FA from your profile settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"li\");\n    i0.ɵɵtext(22, \"Monitoring your account for any suspicious activity\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_3_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToDashboard());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Go to Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_3_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToProfile());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Profile Settings \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userInfo);\n  }\n}\nfunction Disable2FAConfirmComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Unable to Disable 2FA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"h4\");\n    i0.ɵɵtext(9, \"What you can do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ul\")(11, \"li\");\n    i0.ɵɵtext(12, \"Check if the link has expired (links are valid for 1 hour)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"li\");\n    i0.ɵɵtext(14, \"Request a new disable email from the login screen\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"li\");\n    i0.ɵɵtext(16, \"Contact support if you continue to have issues\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_4_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_4_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestNewLink());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Request New Link \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nexport class Disable2FAConfirmComponent {\n  constructor(route, router, twoFactorService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.twoFactorService = twoFactorService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.loading = true;\n    this.success = false;\n    this.errorMessage = '';\n    this.userInfo = null;\n  }\n  ngOnInit() {\n    const token = this.route.snapshot.queryParamMap.get('token');\n    if (!token) {\n      this.loading = false;\n      this.success = false;\n      this.errorMessage = 'Invalid or missing disable token. Please request a new disable link.';\n      return;\n    }\n    this.confirmDisable2FA(token);\n  }\n  confirmDisable2FA(token) {\n    this.twoFactorService.confirmDisable2FA(token).subscribe({\n      next: response => {\n        this.loading = false;\n        this.success = response.success;\n        this.userInfo = response.user;\n        if (response.success) {\n          // Update current user if they're logged in\n          if (this.authService.currentUserValue) {\n            const updatedUser = {\n              ...this.authService.currentUserValue\n            };\n            updatedUser.twoFactorEnabled = false;\n            // Update the user in auth service if needed\n          }\n          this.snackBar.open('Two-factor authentication has been disabled successfully.', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = response.message || 'Failed to disable 2FA';\n        }\n      },\n      error: error => {\n        console.error('Error confirming 2FA disable:', error);\n        this.loading = false;\n        this.success = false;\n        if (error.status === 400) {\n          this.errorMessage = 'The disable link has expired or is invalid. Please request a new one.';\n        } else {\n          this.errorMessage = error.error?.message || 'An unexpected error occurred. Please try again.';\n        }\n      }\n    });\n  }\n  goToDashboard() {\n    this.router.navigate(['/dashboard']);\n  }\n  goToProfile() {\n    this.router.navigate(['/profile']);\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  requestNewLink() {\n    this.router.navigate(['/auth/login'], {\n      queryParams: {\n        message: 'Please use the \"Request disable 2FA\" option to get a new link.'\n      }\n    });\n  }\n  static #_ = this.ɵfac = function Disable2FAConfirmComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Disable2FAConfirmComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: Disable2FAConfirmComponent,\n    selectors: [[\"app-disable-2fa-confirm\"]],\n    standalone: false,\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"disable-confirm-container\"], [1, \"disable-confirm-card\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"success-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"60\"], [1, \"success-state\"], [1, \"success-icon\"], [1, \"success-message\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"warning-icon\"], [1, \"notice-content\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"user-info\"], [1, \"error-state\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-help\"]],\n    template: function Disable2FAConfirmComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1);\n        i0.ɵɵtemplate(2, Disable2FAConfirmComponent_div_2_Template, 6, 0, \"div\", 2)(3, Disable2FAConfirmComponent_div_3_Template, 32, 1, \"div\", 3)(4, Disable2FAConfirmComponent_div_4_Template, 26, 1, \"div\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.success);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.success);\n      }\n    },\n    dependencies: [i5.NgIf, i6.MatCard, i7.MatButton, i8.MatIcon, i9.MatProgressSpinner],\n    styles: [\".disable-confirm-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.disable-confirm-card[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  width: 100%;\\n  padding: 40px;\\n  text-align: center;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n}\\n\\n.loading-state[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 20px 0 10px 0;\\n  color: #333;\\n}\\n\\n.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.success-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.success-icon[_ngcontent-%COMP%] {\\n  font-size: 80px;\\n  width: 80px;\\n  height: 80px;\\n  color: #4caf50;\\n  margin-bottom: 20px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  font-size: 80px;\\n  width: 80px;\\n  height: 80px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.success-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #333;\\n}\\n\\n.success-message[_ngcontent-%COMP%], .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin-bottom: 30px;\\n  line-height: 1.6;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin: 20px 0;\\n  text-align: left;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #333;\\n  font-size: 16px;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  color: #555;\\n}\\n\\n.security-notice[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 1px solid #ff9800;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin: 20px 0;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  text-align: left;\\n}\\n\\n.warning-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  flex-shrink: 0;\\n}\\n\\n.notice-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #f57c00;\\n  font-size: 16px;\\n}\\n\\n.notice-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #e65100;\\n}\\n\\n.notice-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #e65100;\\n}\\n\\n.notice-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 5px;\\n}\\n\\n.error-help[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  border: 1px solid #f44336;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin: 20px 0;\\n  text-align: left;\\n}\\n\\n.error-help[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #d32f2f;\\n  font-size: 16px;\\n}\\n\\n.error-help[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #c62828;\\n}\\n\\n.error-help[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  display: flex;\\n  gap: 16px;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n}\\n\\n@media (max-width: 600px) {\\n  .disable-confirm-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .disable-confirm-card[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 250px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "userInfo", "email", "ɵɵtextInterpolate2", "firstName", "lastName", "ɵɵtemplate", "Disable2FAConfirmComponent_div_3_div_7_Template", "ɵɵlistener", "Disable2FAConfirmComponent_div_3_Template_button_click_24_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "goToDashboard", "Disable2FAConfirmComponent_div_3_Template_button_click_28_listener", "goToProfile", "ɵɵproperty", "Disable2FAConfirmComponent_div_4_Template_button_click_18_listener", "_r3", "goToLogin", "Disable2FAConfirmComponent_div_4_Template_button_click_22_listener", "requestNewLink", "ɵɵtextInterpolate", "errorMessage", "Disable2FAConfirmComponent", "constructor", "route", "router", "twoFactorService", "authService", "snackBar", "loading", "success", "ngOnInit", "token", "snapshot", "queryParamMap", "get", "confirmDisable2FA", "subscribe", "next", "response", "user", "currentUserValue", "updatedUser", "twoFactorEnabled", "open", "duration", "panelClass", "message", "error", "console", "status", "navigate", "queryParams", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "TwoFactorService", "i3", "AuthService", "i4", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "Disable2FAConfirmComponent_Template", "rf", "ctx", "Disable2FAConfirmComponent_div_2_Template", "Disable2FAConfirmComponent_div_3_Template", "Disable2FAConfirmComponent_div_4_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\disable-2fa-confirm\\disable-2fa-confirm.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\disable-2fa-confirm\\disable-2fa-confirm.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { TwoFactorService } from '../../../services/two-factor.service';\r\nimport { AuthService } from '../../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-disable-2fa-confirm',\r\n  templateUrl: './disable-2fa-confirm.component.html',\r\n  styleUrls: ['./disable-2fa-confirm.component.scss'],\r\n  standalone: false\r\n})\r\nexport class Disable2FAConfirmComponent implements OnInit {\r\n  loading = true;\r\n  success = false;\r\n  errorMessage = '';\r\n  userInfo: any = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private twoFactorService: TwoFactorService,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const token = this.route.snapshot.queryParamMap.get('token');\r\n    \r\n    if (!token) {\r\n      this.loading = false;\r\n      this.success = false;\r\n      this.errorMessage = 'Invalid or missing disable token. Please request a new disable link.';\r\n      return;\r\n    }\r\n\r\n    this.confirmDisable2FA(token);\r\n  }\r\n\r\n  confirmDisable2FA(token: string): void {\r\n    this.twoFactorService.confirmDisable2FA(token).subscribe({\r\n      next: (response) => {\r\n        this.loading = false;\r\n        this.success = response.success;\r\n        this.userInfo = response.user;\r\n        \r\n        if (response.success) {\r\n          // Update current user if they're logged in\r\n          if (this.authService.currentUserValue) {\r\n            const updatedUser = { ...this.authService.currentUserValue };\r\n            updatedUser.twoFactorEnabled = false;\r\n            // Update the user in auth service if needed\r\n          }\r\n          \r\n          this.snackBar.open(\r\n            'Two-factor authentication has been disabled successfully.',\r\n            'Close',\r\n            { duration: 5000, panelClass: ['success-snackbar'] }\r\n          );\r\n        } else {\r\n          this.errorMessage = response.message || 'Failed to disable 2FA';\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error confirming 2FA disable:', error);\r\n        this.loading = false;\r\n        this.success = false;\r\n        \r\n        if (error.status === 400) {\r\n          this.errorMessage = 'The disable link has expired or is invalid. Please request a new one.';\r\n        } else {\r\n          this.errorMessage = error.error?.message || 'An unexpected error occurred. Please try again.';\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  goToDashboard(): void {\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n\r\n  goToProfile(): void {\r\n    this.router.navigate(['/profile']);\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  requestNewLink(): void {\r\n    this.router.navigate(['/auth/login'], {\r\n      queryParams: { message: 'Please use the \"Request disable 2FA\" option to get a new link.' }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"disable-confirm-container\">\r\n  <mat-card class=\"disable-confirm-card\">\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"loading\" class=\"loading-state\">\r\n      <mat-spinner diameter=\"60\"></mat-spinner>\r\n      <h2>Processing your request...</h2>\r\n      <p>Please wait while we disable two-factor authentication on your account.</p>\r\n    </div>\r\n\r\n    <!-- Success State -->\r\n    <div *ngIf=\"!loading && success\" class=\"success-state\">\r\n      <mat-icon class=\"success-icon\">check_circle</mat-icon>\r\n      <h2>Two-Factor Authentication Disabled</h2>\r\n      <p class=\"success-message\">\r\n        Two-factor authentication has been successfully disabled on your account.\r\n      </p>\r\n      \r\n      <div class=\"user-info\" *ngIf=\"userInfo\">\r\n        <h3>Account Details:</h3>\r\n        <p><strong>Email:</strong> {{ userInfo.email }}</p>\r\n        <p><strong>Name:</strong> {{ userInfo.firstName }} {{ userInfo.lastName }}</p>\r\n      </div>\r\n\r\n      <div class=\"security-notice\">\r\n        <mat-icon class=\"warning-icon\">warning</mat-icon>\r\n        <div class=\"notice-content\">\r\n          <h4>Important Security Notice</h4>\r\n          <p>Your account is now less secure without two-factor authentication. Consider:</p>\r\n          <ul>\r\n            <li>Using a strong, unique password</li>\r\n            <li>Re-enabling 2FA from your profile settings</li>\r\n            <li>Monitoring your account for any suspicious activity</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" (click)=\"goToDashboard()\">\r\n          <mat-icon>dashboard</mat-icon>\r\n          Go to Dashboard\r\n        </button>\r\n        <button mat-button (click)=\"goToProfile()\">\r\n          <mat-icon>settings</mat-icon>\r\n          Profile Settings\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div *ngIf=\"!loading && !success\" class=\"error-state\">\r\n      <mat-icon class=\"error-icon\">error</mat-icon>\r\n      <h2>Unable to Disable 2FA</h2>\r\n      <p class=\"error-message\">{{ errorMessage }}</p>\r\n      \r\n      <div class=\"error-help\">\r\n        <h4>What you can do:</h4>\r\n        <ul>\r\n          <li>Check if the link has expired (links are valid for 1 hour)</li>\r\n          <li>Request a new disable email from the login screen</li>\r\n          <li>Contact support if you continue to have issues</li>\r\n        </ul>\r\n      </div>\r\n\r\n      <div class=\"action-buttons\">\r\n        <button mat-raised-button color=\"primary\" (click)=\"goToLogin()\">\r\n          <mat-icon>login</mat-icon>\r\n          Back to Login\r\n        </button>\r\n        <button mat-button (click)=\"requestNewLink()\">\r\n          <mat-icon>email</mat-icon>\r\n          Request New Link\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;ICGIA,EAAA,CAAAC,cAAA,aAA2C;IACzCD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,8EAAuE;IAC5EH,EAD4E,CAAAI,YAAA,EAAI,EAC1E;;;;;IAWFJ,EADF,CAAAC,cAAA,cAAwC,SAClC;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAH,CAAAC,cAAA,QAAG,aAAQ;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAH,CAAAC,cAAA,QAAG,aAAQ;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,IAAgD;IAC5EH,EAD4E,CAAAI,YAAA,EAAI,EAC1E;;;;IAFuBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,QAAA,CAAAC,KAAA,CAAoB;IACrBT,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAC,QAAA,CAAAG,SAAA,OAAAJ,MAAA,CAAAC,QAAA,CAAAI,QAAA,CAAgD;;;;;;IAT5EZ,EADF,CAAAC,cAAA,aAAuD,kBACtB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACtDJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,yCAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3CJ,EAAA,CAAAC,cAAA,WAA2B;IACzBD,EAAA,CAAAG,MAAA,kFACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAa,UAAA,IAAAC,+CAAA,mBAAwC;IAOtCd,EADF,CAAAC,cAAA,cAA6B,mBACI;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE/CJ,EADF,CAAAC,cAAA,eAA4B,UACtB;IAAAD,EAAA,CAAAG,MAAA,iCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,oFAA4E;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjFJ,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAG,MAAA,uCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,kDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,2DAAmD;IAG7DH,EAH6D,CAAAI,YAAA,EAAK,EACzD,EACD,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA4B,kBAC0C;IAA1BD,EAAA,CAAAe,UAAA,mBAAAC,mEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IACjErB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA2C;IAAxBD,EAAA,CAAAe,UAAA,mBAAAO,mEAAA;MAAAtB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAgB,WAAA,EAAa;IAAA,EAAC;IACxCvB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,0BACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IA7BoBJ,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAwB,UAAA,SAAAjB,MAAA,CAAAC,QAAA,CAAc;;;;;;IAiCtCR,EADF,CAAAC,cAAA,cAAsD,mBACvB;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7CJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG7CJ,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEvBJ,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAG,MAAA,kEAA0D;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnEJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,yDAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1DJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,sDAA8C;IAEtDH,EAFsD,CAAAI,YAAA,EAAK,EACpD,EACD;IAGJJ,EADF,CAAAC,cAAA,eAA4B,kBACsC;IAAtBD,EAAA,CAAAe,UAAA,mBAAAU,mEAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAS,GAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAoB,SAAA,EAAW;IAAA,EAAC;IAC7D3B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8C;IAA3BD,EAAA,CAAAe,UAAA,mBAAAa,mEAAA;MAAA5B,EAAA,CAAAiB,aAAA,CAAAS,GAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAsB,cAAA,EAAgB;IAAA,EAAC;IAC3C7B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,0BACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IArBqBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA8B,iBAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAkB;;;ADxCjD,OAAM,MAAOC,0BAA0B;EAMrCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,gBAAkC,EAClCC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAVlB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAT,YAAY,GAAG,EAAE;IACjB,KAAAvB,QAAQ,GAAQ,IAAI;EAQjB;EAEHiC,QAAQA,CAAA;IACN,MAAMC,KAAK,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC;IAE5D,IAAI,CAACH,KAAK,EAAE;MACV,IAAI,CAACH,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,OAAO,GAAG,KAAK;MACpB,IAAI,CAACT,YAAY,GAAG,sEAAsE;MAC1F;IACF;IAEA,IAAI,CAACe,iBAAiB,CAACJ,KAAK,CAAC;EAC/B;EAEAI,iBAAiBA,CAACJ,KAAa;IAC7B,IAAI,CAACN,gBAAgB,CAACU,iBAAiB,CAACJ,KAAK,CAAC,CAACK,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACV,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,OAAO,GAAGS,QAAQ,CAACT,OAAO;QAC/B,IAAI,CAAChC,QAAQ,GAAGyC,QAAQ,CAACC,IAAI;QAE7B,IAAID,QAAQ,CAACT,OAAO,EAAE;UACpB;UACA,IAAI,IAAI,CAACH,WAAW,CAACc,gBAAgB,EAAE;YACrC,MAAMC,WAAW,GAAG;cAAE,GAAG,IAAI,CAACf,WAAW,CAACc;YAAgB,CAAE;YAC5DC,WAAW,CAACC,gBAAgB,GAAG,KAAK;YACpC;UACF;UAEA,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAChB,2DAA2D,EAC3D,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,kBAAkB;UAAC,CAAE,CACrD;QACH,CAAC,MAAM;UACL,IAAI,CAACzB,YAAY,GAAGkB,QAAQ,CAACQ,OAAO,IAAI,uBAAuB;QACjE;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACnB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,OAAO,GAAG,KAAK;QAEpB,IAAIkB,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;UACxB,IAAI,CAAC7B,YAAY,GAAG,uEAAuE;QAC7F,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,GAAG2B,KAAK,CAACA,KAAK,EAAED,OAAO,IAAI,iDAAiD;QAC/F;MACF;KACD,CAAC;EACJ;EAEApC,aAAaA,CAAA;IACX,IAAI,CAACc,MAAM,CAAC0B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAtC,WAAWA,CAAA;IACT,IAAI,CAACY,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAlC,SAASA,CAAA;IACP,IAAI,CAACQ,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAhC,cAAcA,CAAA;IACZ,IAAI,CAACM,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;MACpCC,WAAW,EAAE;QAAEL,OAAO,EAAE;MAAgE;KACzF,CAAC;EACJ;EAAC,QAAAM,CAAA,G;qCAjFU/B,0BAA0B,EAAAhC,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAnE,EAAA,CAAAgE,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAArE,EAAA,CAAAgE,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAvE,EAAA,CAAAgE,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1B1C,0BAA0B;IAAA2C,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXrClF,EADF,CAAAC,cAAA,aAAuC,kBACE;QAgDrCD,EA9CA,CAAAa,UAAA,IAAAuE,yCAAA,iBAA2C,IAAAC,yCAAA,kBAOY,IAAAC,yCAAA,kBAuCD;QA0B1DtF,EADE,CAAAI,YAAA,EAAW,EACP;;;QAxEIJ,EAAA,CAAAK,SAAA,GAAa;QAAbL,EAAA,CAAAwB,UAAA,SAAA2D,GAAA,CAAA5C,OAAA,CAAa;QAObvC,EAAA,CAAAK,SAAA,EAAyB;QAAzBL,EAAA,CAAAwB,UAAA,UAAA2D,GAAA,CAAA5C,OAAA,IAAA4C,GAAA,CAAA3C,OAAA,CAAyB;QAuCzBxC,EAAA,CAAAK,SAAA,EAA0B;QAA1BL,EAAA,CAAAwB,UAAA,UAAA2D,GAAA,CAAA5C,OAAA,KAAA4C,GAAA,CAAA3C,OAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}