{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { validateStyleProperty, containsElement, getParentElement, invokeQuery, dashCaseToCamelCase, invalidCssUnitValue, invalidExpression, invalidTransitionAlias, visitDslNode, invalidTrigger, invalidDefinition, extractStyleParams, invalidState, invalidStyleValue, SUBSTITUTION_EXPR_START, invalidParallelAnimation, validateStyleParams, invalidKeyframes, invalidOffset, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, getOrSetDefaultValue, invalidStagger, resolveTiming, normalizeAnimationEntry, NG_TRIGGER_SELECTOR, NG_ANIMATING_SELECTOR, resolveTimingValue, interpolateParams, invalidQuery, registerFailed, normalizeKeyframes, LEAVE_CLASSNAME, ENTER_CLASSNAME, missingOrDestroyedAnimation, createAnimationFailed, optimizeGroupPlayer, missingPlayer, listenOnPlayer, makeAnimationEvent, triggerTransitionsFailed, eraseStyles, setStyles, transitionFailed, missingTrigger, missingEvent, unsupportedTriggerEvent, unregisteredTrigger, NG_TRIGGER_CLASSNAME, NG_ANIMATING_CLASSNAME, triggerBuildFailed, parseTimelineCommand, computeStyle, camelCaseToDashCase, validateWebAnimatableStyleProperty, allowPreviousPlayerStylesMerge, normalizeKeyframes$1, balancePreviousStylesIntoKeyframes, validationFailed, normalizeStyles, buildingFailed } from './util-D9FfmVnv.mjs';\nimport { NoopAnimationPlayer, AnimationMetadataType, style, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationGroupPlayer } from './private_export-faY_wCkZ.mjs';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\nclass NoopAnimationDriver {\n  /**\n   * @returns Whether `prop` is a valid CSS property\n   */\n  validateStyleProperty(prop) {\n    return validateStyleProperty(prop);\n  }\n  /**\n   *\n   * @returns Whether elm1 contains elm2.\n   */\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n  /**\n   * @returns Rhe parent of the given element or `null` if the element is the `document`\n   */\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n  /**\n   * @returns The result of the query selector on the element. The array will contain up to 1 item\n   *     if `multi` is  `false`.\n   */\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n  /**\n   * @returns The `defaultValue` or empty string\n   */\n  computeStyle(element, prop, defaultValue) {\n    return defaultValue || '';\n  }\n  /**\n   * @returns An `NoopAnimationPlayer`\n   */\n  animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n    return new NoopAnimationPlayer(duration, delay);\n  }\n  static ɵfac = function NoopAnimationDriver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoopAnimationDriver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NoopAnimationDriver,\n    factory: NoopAnimationDriver.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationDriver, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @publicApi\n */\nclass AnimationDriver {\n  /**\n   * @deprecated Use the NoopAnimationDriver class.\n   */\n  static NOOP = new NoopAnimationDriver();\n}\nclass AnimationStyleNormalizer {}\nclass NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return propertyName;\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    return value;\n  }\n}\nconst DIMENSIONAL_PROP_SET = new Set(['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight', 'left', 'top', 'bottom', 'right', 'fontSize', 'outlineWidth', 'outlineOffset', 'paddingTop', 'paddingLeft', 'paddingBottom', 'paddingRight', 'marginTop', 'marginLeft', 'marginBottom', 'marginRight', 'borderRadius', 'borderWidth', 'borderTopWidth', 'borderLeftWidth', 'borderRightWidth', 'borderBottomWidth', 'textIndent', 'perspective']);\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return dashCaseToCamelCase(propertyName);\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    let unit = '';\n    const strVal = value.toString().trim();\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\nfunction createListOfWarnings(warnings) {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings.filter(Boolean).map(warning => warning).join(LINE_START)}`;\n}\nfunction warnValidation(warnings) {\n  console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnTriggerBuild(name, warnings) {\n  console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnRegister(warnings) {\n  console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\nconst ANY_STATE = '*';\nfunction parseTransitionExpr(transitionValue, errors) {\n  const expressions = [];\n  if (typeof transitionValue == 'string') {\n    transitionValue.split(/\\s*,\\s*/).forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(transitionValue);\n  }\n  return expressions;\n}\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result;\n  }\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n  return;\n}\nfunction parseAnimationAlias(alias, errors) {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\nfunction makeLambdaFromStates(lhs, rhs) {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n  return (fromState, toState) => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n    return lhsMatch && rhsMatch;\n  };\n}\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /* @__PURE__ */new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\nconst ROOT_SELECTOR = '';\nclass AnimationAstBuilderVisitor {\n  _driver;\n  constructor(_driver) {\n    this._driver = _driver;\n  }\n  build(metadata, errors, warnings) {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    const ast = visitDslNode(this, normalizeAnimationEntry(metadata), context);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [...context.unsupportedCSSPropertiesFound.keys()]);\n      }\n    }\n    return ast;\n  }\n  _resetContextStyleTimingState(context) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n  visitTrigger(metadata, context) {\n    let queryCount = context.queryCount = 0;\n    let depCount = context.depCount = 0;\n    const states = [];\n    const transitions = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def;\n        const name = stateDef.name;\n        name.toString().split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null\n    };\n  }\n  visitState(metadata, context) {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = metadata.options && metadata.options.params || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set();\n      const params = astParams || {};\n      styleAst.styles.forEach(style => {\n        if (style instanceof Map) {\n          style.forEach(value => {\n            extractStyleParams(value).forEach(sub => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n      }\n    }\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {\n        params: astParams\n      } : null\n    };\n  }\n  visitTransition(metadata, context) {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitSequence(metadata, context) {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitGroup(metadata, context) {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimate(metadata, context) {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst;\n    let styleMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null\n    };\n  }\n  visitStyle(metadata, context) {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n  _makeStyleAst(metadata, context) {\n    const styles = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(new Map(Object.entries(styleTuple)));\n      }\n    }\n    let containsDynamicStyles = false;\n    let collectedEasing = null;\n    styles.forEach(styleData => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing');\n          styleData.delete('easing');\n        }\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null\n    };\n  }\n  _validateStyleAst(ast, context) {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n    ast.styles.forEach(tuple => {\n      if (typeof tuple === 'string') return;\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        }\n        // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime && endTime <= collectedEntry.endTime) {\n            context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n            updateCollectedStyle = false;\n          }\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {\n            startTime,\n            endTime\n          });\n        }\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n  visitKeyframes(metadata, context) {\n    const ast = {\n      type: AnimationMetadataType.Keyframes,\n      styles: [],\n      options: null\n    };\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n    const MAX_KEYFRAME_OFFSET = 1;\n    let totalKeyframesWithOffsets = 0;\n    const offsets = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset = 0;\n    const keyframes = metadata.steps.map(styles => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? i == limit ? 1 : generatedOffset * i : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n      ast.styles.push(kf);\n    });\n    return ast;\n  }\n  visitReference(metadata, context) {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateChild(metadata, context) {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateRef(metadata, context) {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitQuery(metadata, context) {\n    const parentSelector = context.currentQuerySelector;\n    const options = metadata.options || {};\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length ? parentSelector + ' ' + selector : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitStagger(metadata, context) {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n    const timings = metadata.timings === 'full' ? {\n      duration: 0,\n      delay: 0,\n      easing: 'full'\n    } : resolveTiming(metadata.timings, context.errors, true);\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null\n    };\n  }\n}\nfunction normalizeSelector(selector) {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n  // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n  selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR).replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.slice(1)).replace(/:animating/g, NG_ANIMATING_SELECTOR);\n  return [selector, hasAmpersand];\n}\nfunction normalizeParams(obj) {\n  return obj ? {\n    ...obj\n  } : null;\n}\nclass AnimationAstBuilderContext {\n  errors;\n  queryCount = 0;\n  depCount = 0;\n  currentTransition = null;\n  currentQuery = null;\n  currentQuerySelector = null;\n  currentAnimateTimings = null;\n  currentTime = 0;\n  collectedStyles = new Map();\n  options = null;\n  unsupportedCSSPropertiesFound = new Set();\n  constructor(errors) {\n    this.errors = errors;\n  }\n}\nfunction consumeOffset(styles) {\n  if (typeof styles == 'string') return null;\n  let offset = null;\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset'));\n    obj.delete('offset');\n  }\n  return offset;\n}\nfunction constructTimingAst(value, errors) {\n  if (value.hasOwnProperty('duration')) {\n    return value;\n  }\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n  const strValue = value;\n  const isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '');\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast;\n  }\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\nfunction normalizeAnimationOptions(options) {\n  if (options) {\n    options = {\n      ...options\n    };\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params']);\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\nfunction makeTimingAst(duration, delay, easing) {\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n  return {\n    type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline\n  };\n}\nclass ElementInstructionMap {\n  _map = new Map();\n  get(element) {\n    return this._map.get(element) || [];\n  }\n  append(element, instructions) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n  has(element) {\n    return this._map.has(element);\n  }\n  clear() {\n    this._map.clear();\n  }\n}\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /* @__PURE__ */new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /* @__PURE__ */new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```ts\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```ts\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nclass AnimationTimelineBuilderVisitor {\n  buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n    visitDslNode(this, ast, context);\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline;\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n  visitTrigger(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitState(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitTransition(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitAnimateChild(ast, context) {\n    const elementInstructions = context.subInstructions.get(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitAnimateRef(ast, context) {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n  _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n      if (animationDelay) {\n        const animationDelayValue = typeof animationDelay === 'number' ? animationDelay : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n  _visitSubInstructions(instructions, context, options) {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n    return furthestTime;\n  }\n  visitReference(ast, context) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n  visitSequence(ast, context) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n    if (ast.steps.length) {\n      ast.steps.forEach(s => visitDslNode(this, s, ctx));\n      // this is here just in case the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitGroup(ast, context) {\n    const innerTimelines = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n    ast.steps.forEach(s => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n  _visitTiming(ast, context) {\n    if (ast.dynamic) {\n      const strValue = ast.strValue;\n      const timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {\n        duration: ast.duration,\n        delay: ast.delay,\n        easing: ast.easing\n      };\n    }\n  }\n  visitAnimate(ast, context) {\n    const timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style, context);\n      timeline.applyStylesToKeyframe();\n    }\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n  visitStyle(ast, context) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings;\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n    const easing = timings && timings.easing || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n    context.previousNode = ast;\n  }\n  visitKeyframes(ast, context) {\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const startTime = context.currentTimeline.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n    ast.styles.forEach(step => {\n      const offset = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n  visitQuery(ast, context) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = ast.options || {};\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    if (delay && (context.previousNode.type === AnimationMetadataType.Style || startTime == 0 && context.currentTimeline.hasCurrentStyleProperties())) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n      visitDslNode(this, ast.animation, innerContext);\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n    context.previousNode = ast;\n  }\n  visitStagger(ast, context) {\n    const parentContext = context.parentContext;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime = tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\nclass AnimationTimelineContext {\n  _driver;\n  element;\n  subInstructions;\n  _enterClassName;\n  _leaveClassName;\n  errors;\n  timelines;\n  parentContext = null;\n  currentTimeline;\n  currentAnimateTimings = null;\n  previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n  subContextCount = 0;\n  options = {};\n  currentQueryIndex = 0;\n  currentQueryTotal = 0;\n  currentStaggerTime = 0;\n  constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n    this._driver = _driver;\n    this.element = element;\n    this.subInstructions = subInstructions;\n    this._enterClassName = _enterClassName;\n    this._leaveClassName = _leaveClassName;\n    this.errors = errors;\n    this.timelines = timelines;\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n  get params() {\n    return this.options.params;\n  }\n  updateOptions(options, skipIfExists) {\n    if (!options) return;\n    const newOptions = options;\n    let optionsToUpdate = this.options;\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n    }\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate = optionsToUpdate.params;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n  _copyOptions() {\n    const options = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params = options['params'] = {};\n        Object.keys(oldParams).forEach(name => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n    return options;\n  }\n  createSubContext(options = null, element, newTime) {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n  transformIntoNewTimeline(newTime) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n  appendInstructionToTimeline(instruction, duration, delay) {\n    const updatedTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n  incrementTime(time) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n  delayNextStep(delay) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n  invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n    let results = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) : elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n    return results;\n  }\n}\nclass TimelineBuilder {\n  _driver;\n  element;\n  startTime;\n  _elementTimelineStylesLookup;\n  duration = 0;\n  easing = null;\n  _previousKeyframe = new Map();\n  _currentKeyframe = new Map();\n  _keyframes = new Map();\n  _styleSummary = new Map();\n  _localTimelineStyles = new Map();\n  _globalTimelineStyles;\n  _pendingStyles = new Map();\n  _backFill = new Map();\n  _currentEmptyStepKeyframe = null;\n  constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n    this._driver = _driver;\n    this.element = element;\n    this.startTime = startTime;\n    this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map();\n    }\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n  containsAnimation() {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.hasCurrentStyleProperties();\n      default:\n        return true;\n    }\n  }\n  hasCurrentStyleProperties() {\n    return this._currentKeyframe.size > 0;\n  }\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n  delayNextStep(delay) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n  fork(element, currentTime) {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n  _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration);\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n  forwardTime(time) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n  _updateStyle(prop, value) {\n    this._localTimelineStyles.set(prop, value);\n    this._globalTimelineStyles.set(prop, value);\n    this._styleSummary.set(prop, {\n      time: this.currentTime,\n      value\n    });\n  }\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n  applyEmptyStep(easing) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n  setStyles(input, easing, errors, options) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    const params = options && options.params || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n      this._pendingStyles.set(prop, val);\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n      this._updateStyle(prop, val);\n    }\n  }\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n    this._pendingStyles.clear();\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n      this._updateStyle(prop, val);\n    }\n  }\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n  get properties() {\n    const properties = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n  mergeTimelineCollectedStyles(timeline) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n  buildKeyframes() {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set();\n    const postStyleProps = new Set();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n    let finalKeyframes = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === _PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n    const preProps = [...preStyleProps.values()];\n    const postProps = [...postStyleProps.values()];\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n    return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n  }\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n  keyframes;\n  preStyleProps;\n  postStyleProps;\n  _stretchStartingKeyframe;\n  timings;\n  constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n    super(driver, element, timings.delay);\n    this.keyframes = keyframes;\n    this.preStyleProps = preStyleProps;\n    this.postStyleProps = postStyleProps;\n    this._stretchStartingKeyframe = _stretchStartingKeyframe;\n    this.timings = {\n      duration: timings.duration,\n      delay: timings.delay,\n      easing: timings.easing\n    };\n  }\n  containsAnimation() {\n    return this.keyframes.length > 1;\n  }\n  buildKeyframes() {\n    let keyframes = this.keyframes;\n    let {\n      delay,\n      duration,\n      easing\n    } = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = new Map(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n      const oldFirstKeyframe = new Map(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n               delay=1000, duration=1000, keyframes = 0 .5 1\n               turns into\n               delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = new Map(keyframes[i]);\n        const oldOffset = kf.get('offset');\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      }\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n      keyframes = newKeyframes;\n    }\n    return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n  }\n}\nfunction roundOffset(offset, decimalPoints = 3) {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\nfunction flattenStyles(input, allStyles) {\n  const styles = new Map();\n  let allProperties;\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties ??= allStyles.keys();\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      for (let [prop, val] of token) {\n        styles.set(prop, val);\n      }\n    }\n  });\n  return styles;\n}\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n  return {\n    type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors\n  };\n}\nconst EMPTY_OBJECT = {};\nclass AnimationTransitionFactory {\n  _triggerName;\n  ast;\n  _stateStyles;\n  constructor(_triggerName, ast, _stateStyles) {\n    this._triggerName = _triggerName;\n    this.ast = ast;\n    this._stateStyles = _stateStyles;\n  }\n  match(currentState, nextState, element, params) {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n  buildStyles(stateName, params, errors) {\n    let styler = this._stateStyles.get('*');\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n  build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n    const errors = [];\n    const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n    const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n    const queriedElements = new Set();\n    const preStyleMap = new Map();\n    const postStyleMap = new Map();\n    const isRemoval = nextState === 'void';\n    const animationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay\n    };\n    const timelines = skipAstBuild ? [] : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n    let totalTime = 0;\n    timelines.forEach(tl => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n    if (errors.length) {\n      return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n    }\n    timelines.forEach(tl => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n      tl.preStyleProps.forEach(prop => preProps.add(prop));\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n      tl.postStyleProps.forEach(prop => postProps.add(prop));\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n    return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, [...queriedElements.values()], preStyleMap, postStyleMap, totalTime);\n  }\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n  const allowedNonAnimatableProps = new Set([\n  // 'easing' is a utility/synthetic prop we use to represent\n  // easing functions, it represents a property of the animation\n  // which is not animatable but different values can be used\n  // in different steps\n  'easing']);\n  const invalidNonAnimatableProps = new Set();\n  timelines.forEach(({\n    keyframes\n  }) => {\n    const nonAnimatablePropsInitialValues = new Map();\n    keyframes.forEach(keyframe => {\n      const entriesToCheck = Array.from(keyframe.entries()).filter(([prop]) => !allowedNonAnimatableProps.has(prop));\n      for (const [prop, value] of entriesToCheck) {\n        if (!driver.validateAnimatableStyleProperty(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` + ' not animatable properties: ' + Array.from(invalidNonAnimatableProps).join(', ') + '\\n' + '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n  }\n}\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n  return matchFns.some(fn => fn(currentState, nextState, element, params));\n}\nfunction applyParamDefaults(userParams, defaults) {\n  const result = {\n    ...defaults\n  };\n  Object.entries(userParams).forEach(([key, value]) => {\n    if (value != null) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\nclass AnimationStateStyles {\n  styles;\n  defaultParams;\n  normalizer;\n  constructor(styles, defaultParams, normalizer) {\n    this.styles = styles;\n    this.defaultParams = defaultParams;\n    this.normalizer = normalizer;\n  }\n  buildStyles(params, errors) {\n    const finalStyles = new Map();\n    const combinedParams = applyParamDefaults(params, this.defaultParams);\n    this.styles.styles.forEach(value => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(prop, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\nfunction buildTrigger(name, ast, normalizer) {\n  return new AnimationTrigger(name, ast, normalizer);\n}\nclass AnimationTrigger {\n  name;\n  ast;\n  _normalizer;\n  transitionFactories = [];\n  fallbackTransition;\n  states = new Map();\n  constructor(name, ast, _normalizer) {\n    this.name = name;\n    this.ast = ast;\n    this._normalizer = _normalizer;\n    ast.states.forEach(ast => {\n      const defaultParams = ast.options && ast.options.params || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n  matchTransition(currentState, nextState, element, params) {\n    const entry = this.transitionFactories.find(f => f.match(currentState, nextState, element, params));\n    return entry || null;\n  }\n  matchStyles(currentState, params, errors) {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\nfunction createFallbackTransition(triggerName, states, normalizer) {\n  const matchers = [(fromState, toState) => true];\n  const animation = {\n    type: AnimationMetadataType.Sequence,\n    steps: [],\n    options: null\n  };\n  const transition = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\nfunction balanceProperties(stateMap, key1, key2) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1));\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2));\n  }\n}\nconst EMPTY_INSTRUCTION_MAP = /* @__PURE__ */new ElementInstructionMap();\nclass TimelineAnimationEngine {\n  bodyNode;\n  _driver;\n  _normalizer;\n  _animations = new Map();\n  _playersById = new Map();\n  players = [];\n  constructor(bodyNode, _driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n  }\n  register(id, metadata) {\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnRegister(warnings);\n        }\n      }\n      this._animations.set(id, ast);\n    }\n  }\n  _buildPlayer(i, preStyles, postStyles) {\n    const element = i.element;\n    const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n  create(id, element, options = {}) {\n    const errors = [];\n    const ast = this._animations.get(id);\n    let instructions;\n    const autoStylesMap = new Map();\n    if (ast) {\n      instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n        inst.postStyleProps.forEach(prop => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n    const players = instructions.map(i => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById.set(id, player);\n    player.onDestroy(() => this.destroy(id));\n    this.players.push(player);\n    return player;\n  }\n  destroy(id) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    this._playersById.delete(id);\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n  _getPlayer(id) {\n    const player = this._playersById.get(id);\n    if (!player) {\n      throw missingPlayer(id);\n    }\n    return player;\n  }\n  listen(id, element, eventName, callback) {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n  command(id, element, command, args) {\n    if (command == 'register') {\n      this.register(id, args[0]);\n      return;\n    }\n    if (command == 'create') {\n      const options = args[0] || {};\n      this.create(id, element, options);\n      return;\n    }\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0]));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\nconst REMOVAL_FLAG = '__ng_removed';\nclass StateValue {\n  namespaceId;\n  value;\n  options;\n  get params() {\n    return this.options.params;\n  }\n  constructor(input, namespaceId = '') {\n    this.namespaceId = namespaceId;\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      // we drop the value property from options.\n      const {\n        value,\n        ...options\n      } = input;\n      this.options = options;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n  absorbOptions(options) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params;\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /* @__PURE__ */new StateValue(VOID_VALUE);\nclass AnimationTransitionNamespace {\n  id;\n  hostElement;\n  _engine;\n  players = [];\n  _triggers = new Map();\n  _queue = [];\n  _elementListeners = new Map();\n  _hostClassName;\n  constructor(id, hostElement, _engine) {\n    this.id = id;\n    this.hostElement = hostElement;\n    this._engine = _engine;\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n  listen(element, name, phase, callback) {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {\n      name,\n      phase,\n      callback\n    };\n    listeners.push(data);\n    const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n  register(name, ast) {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n      return true;\n    }\n  }\n  _getTrigger(name) {\n    const trigger = this._triggers.get(name);\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n    return trigger;\n  }\n  trigger(element, triggerName, value, defaultToFallback = true) {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = new Map());\n    }\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n    triggersWithStates.set(triggerName, toState);\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n    const isRemoval = toState.value === VOID_VALUE;\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n    const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n    let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n    this._engine.totalQueuedPlayers++;\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition\n    });\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n    this.players.push(player);\n    playersOnElement.push(player);\n    return player;\n  }\n  deregister(name) {\n    this._triggers.delete(name);\n    this._engine.statesByElement.forEach(stateMap => stateMap.delete(name));\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(element, listeners.filter(entry => {\n        return entry.name != name;\n      }));\n    });\n  }\n  clearElementCache(element) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n  _signalRemovalForInnerTriggers(rootElement, context) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    elements.forEach(elm => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n    // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n    this._engine.afterFlushAnimationsDone(() => elements.forEach(elm => this.clearElementCache(elm)));\n  }\n  triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n    const triggerStates = this._engine.statesByElement.get(element);\n    const previousTriggersValues = new Map();\n    if (triggerStates) {\n      const players = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value);\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n  prepareLeaveAnimationListeners(element) {\n    const listeners = this._elementListeners.get(element);\n    const elementStates = this._engine.statesByElement.get(element);\n    // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set();\n      listeners.forEach(listener => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n        const trigger = this._triggers.get(triggerName);\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n  }\n  removeNode(element, context) {\n    const engine = this._engine;\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    }\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while (parent = parent.parentNode) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n  insertNode(element, parent) {\n    addClass(element, this._hostClassName);\n  }\n  drainQueuedTransitions(microtaskId) {\n    const instructions = [];\n    this._queue.forEach(entry => {\n      const player = entry.player;\n      if (player.destroyed) return;\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach(listener => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            baseEvent['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n    this._queue = [];\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n  destroy(context) {\n    this.players.forEach(p => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n}\nclass TransitionAnimationEngine {\n  bodyNode;\n  driver;\n  _normalizer;\n  players = [];\n  newHostElements = new Map();\n  playersByElement = new Map();\n  playersByQueriedElement = new Map();\n  statesByElement = new Map();\n  disabledNodes = new Set();\n  totalAnimations = 0;\n  totalQueuedPlayers = 0;\n  _namespaceLookup = {};\n  _namespaceList = [];\n  _flushFns = [];\n  _whenQuietFns = [];\n  namespacesByHostElement = new Map();\n  collectedEnterElements = [];\n  collectedLeaveElements = [];\n  // this method is designed to be overridden by the code that uses this engine\n  onRemovalComplete = (element, context) => {};\n  /** @internal */\n  _onRemovalComplete(element, context) {\n    this.onRemovalComplete(element, context);\n  }\n  constructor(bodyNode, driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this.driver = driver;\n    this._normalizer = _normalizer;\n  }\n  get queuedPlayers() {\n    const players = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n  createNamespace(namespaceId, hostElement) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n      // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n  _balanceNamespaceList(ns, hostElement) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n      let ancestor = this.driver.getParentElement(hostElement);\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n  register(namespaceId, hostElement) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n  registerTrigger(namespaceId, name, trigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n  destroy(namespaceId, context) {\n    if (!namespaceId) return;\n    this.afterFlush(() => {});\n    this.afterFlushAnimationsDone(() => {\n      const ns = this._fetchNamespace(namespaceId);\n      this.namespacesByHostElement.delete(ns.hostElement);\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n      ns.destroy(context);\n      delete this._namespaceLookup[namespaceId];\n    });\n  }\n  _fetchNamespace(id) {\n    return this._namespaceLookup[id];\n  }\n  fetchNamespacesByElement(element) {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n  trigger(namespaceId, element, name, value) {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n  insertNode(namespaceId, element, parent, insertBefore) {\n    if (!isElementNode(element)) return;\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n  collectEnterElement(element) {\n    this.collectedEnterElements.push(element);\n  }\n  markElementAsDisabled(element, value) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n  removeNode(namespaceId, element, context) {\n    if (isElementNode(element)) {\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n      const hostNS = this.namespacesByHostElement.get(element);\n      if (hostNS && hostNS.id !== namespaceId) {\n        hostNS.removeNode(element, context);\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n  markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues\n    };\n  }\n  listen(namespaceId, element, name, phase, callback) {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n  _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n    return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n  }\n  destroyInnerAnimations(containerElement) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n    if (this.playersByQueriedElement.size == 0) return;\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n  }\n  destroyActiveAnimationsForElement(element) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach(player => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n  finishActiveQueriedAnimationOnElement(element) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach(player => player.finish());\n    }\n  }\n  whenRenderingDone() {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n  processLeaveNode(element) {\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n  flush(microtaskId = -1) {\n    let players = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n    if (this._namespaceList.length && (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      const cleanupFns = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach(fn => fn());\n        });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n  reportError(errors) {\n    throw triggerTransitionsFailed(errors);\n  }\n  _flushAnimations(cleanupFns, microtaskId) {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers = [];\n    const skippedPlayersMap = new Map();\n    const queuedInstructions = [];\n    const queriedElements = new Map();\n    const allPreStyleElements = new Map();\n    const allPostStyleElements = new Map();\n    const disabledElementsSet = new Set();\n    this.disabledNodes.forEach(node => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    const allLeaveNodes = [];\n    const mergedLeaveNodes = new Set();\n    const leaveNodesWithoutAnimations = new Set();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG];\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n    const leaveNodeMapIds = new Map();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      allLeaveNodes.forEach(element => {\n        this.processLeaveNode(element);\n      });\n    });\n    const allPlayers = [];\n    const erroneousTransitions = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG];\n          // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n          if (details && details.setForMove) {\n            if (details.previousTriggersValues && details.previousTriggersValues.has(entry.triggerName)) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName);\n              // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n              const triggersWithStates = this.statesByElement.get(entry.element);\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName);\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n            player.destroy();\n            return;\n          }\n        }\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element);\n        const enterClassName = enterNodeMapIds.get(element);\n        const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n        // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n        const timelines = [];\n        instruction.timelines.forEach(tl => {\n          tl.stretchStartingKeyframe = true;\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n        subTimelines.append(element, instruction.timelines);\n        const tuple = {\n          instruction,\n          player,\n          element\n        };\n        queuedInstructions.push(tuple);\n        instruction.queriedElements.forEach(element => getOrSetDefaultValue(queriedElements, element, []).push(player));\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal = allPreStyleElements.get(element);\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set());\n            }\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal = allPostStyleElements.get(element);\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set());\n          }\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n    if (erroneousTransitions.length) {\n      const errors = [];\n      erroneousTransitions.forEach(instruction => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n      });\n      allPlayers.forEach(player => player.destroy());\n      this.reportError(errors);\n    }\n    const allPreviousPlayersMap = new Map();\n    // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map();\n    queuedInstructions.forEach(entry => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n    skippedPlayers.forEach(player => {\n      const element = player.element;\n      const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(prevPlayer => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n    // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n    const replaceNodes = allLeaveNodes.filter(node => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n    allLeaveQueriedNodes.forEach(node => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, _PRE_STYLE);\n    });\n    replaceNodes.forEach(node => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n    });\n    const rootPlayers = [];\n    const subPlayers = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach(entry => {\n      const {\n        element,\n        player,\n        instruction\n      } = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd = [];\n          while (elm = elm.parentNode) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n        }\n        const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n        player.setRealPlayer(innerPlayer);\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n    // find all of the sub players' corresponding inner animation players\n    subPlayers.forEach(player => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG];\n      removeClass(element, LEAVE_CLASSNAME);\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n      let players = [];\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      const activePlayers = players.filter(p => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n    return rootPlayers;\n  }\n  afterFlush(callback) {\n    this._flushFns.push(callback);\n  }\n  afterFlushAnimationsDone(callback) {\n    this._whenQuietFns.push(callback);\n  }\n  _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n    let players = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n  _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n    const targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const realPlayer = player.getRealPlayer();\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n  _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers = [];\n    const allConsumedElements = new Set();\n    const allSubElements = new Set();\n    const allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map(p => p.getRealPlayer())).filter(p => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p;\n        return pp.element ? pp.element === element : false;\n      });\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n      const keyframes = normalizeKeyframes(this._normalizer, timelineInstruction.keyframes, preStyles, postStyles);\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n      return player;\n    });\n    allQueriedPlayers.forEach(player => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(element => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n    return player;\n  }\n  _buildPlayer(instruction, keyframes, previousPlayers) {\n    if (keyframes.length > 0) {\n      return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n    }\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\nclass TransitionAnimationPlayer {\n  namespaceId;\n  triggerName;\n  element;\n  _player = new NoopAnimationPlayer();\n  _containsRealPlayer = false;\n  _queuedCallbacks = new Map();\n  destroyed = false;\n  parentPlayer = null;\n  markedForDestroy = false;\n  disabled = false;\n  queued = true;\n  totalTime = 0;\n  constructor(namespaceId, triggerName, element) {\n    this.namespaceId = namespaceId;\n    this.triggerName = triggerName;\n    this.element = element;\n  }\n  setRealPlayer(player) {\n    if (this._containsRealPlayer) return;\n    this._player = player;\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach(callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks.clear();\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    this.queued = false;\n  }\n  getRealPlayer() {\n    return this._player;\n  }\n  overrideTotalTime(totalTime) {\n    this.totalTime = totalTime;\n  }\n  syncPlayerEvents(player) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n  _queueEvent(name, callback) {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n  onDone(fn) {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n  onStart(fn) {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n  onDestroy(fn) {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n  init() {\n    this._player.init();\n  }\n  hasStarted() {\n    return this.queued ? false : this._player.hasStarted();\n  }\n  play() {\n    !this.queued && this._player.play();\n  }\n  pause() {\n    !this.queued && this._player.pause();\n  }\n  restart() {\n    !this.queued && this._player.restart();\n  }\n  finish() {\n    this._player.finish();\n  }\n  destroy() {\n    this.destroyed = true;\n    this._player.destroy();\n  }\n  reset() {\n    !this.queued && this._player.reset();\n  }\n  setPosition(p) {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n  getPosition() {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\nfunction deleteOrUnsetInMap(map, key, value) {\n  let currentValues = map.get(key);\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n  return currentValues;\n}\nfunction normalizeTriggerValue(value) {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\nfunction isElementNode(node) {\n  return node && node['nodeType'] === 1;\n}\nfunction isTriggerEventValid(eventName) {\n  return eventName == 'start' || eventName == 'done';\n}\nfunction cloakElement(element, value) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n  const cloakVals = [];\n  elements.forEach(element => cloakVals.push(cloakElement(element)));\n  const failedElements = [];\n  elementPropsMap.forEach((props, element) => {\n    const styles = new Map();\n    props.forEach(prop => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value);\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach(element => cloakElement(element, cloakVals[i++]));\n  return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots, nodes) {\n  const rootMap = new Map();\n  roots.forEach(root => rootMap.set(root, []));\n  if (nodes.length == 0) return rootMap;\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map();\n  function getRoot(node) {\n    if (!node) return NULL_NODE;\n    let root = localRootMap.get(node);\n    if (root) return root;\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n    localRootMap.set(node, root);\n    return root;\n  }\n  nodes.forEach(node => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root).push(node);\n    }\n  });\n  return rootMap;\n}\nfunction addClass(element, className) {\n  element.classList?.add(className);\n}\nfunction removeClass(element, className) {\n  element.classList?.remove(className);\n}\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\nfunction flattenGroupPlayers(players) {\n  const finalPlayers = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof AnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\nfunction objEquals(a, b) {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach(data => preEntry.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n  allPostStyleElements.delete(element);\n  return true;\n}\nclass AnimationEngine {\n  _driver;\n  _normalizer;\n  _transitionEngine;\n  _timelineEngine;\n  _triggerCache = {};\n  // this method is designed to be overridden by the code that uses this engine\n  onRemovalComplete = (element, context) => {};\n  constructor(doc, _driver, _normalizer) {\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n    this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n  }\n  registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors = [];\n      const warnings = [];\n      const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnTriggerBuild(name, warnings);\n        }\n      }\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n  register(namespaceId, hostElement) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n  destroy(namespaceId, context) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n  onInsert(namespaceId, element, parent, insertBefore) {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n  onRemove(namespaceId, element, context) {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n  disableAnimations(element, disable) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n  process(namespaceId, element, property, value) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value;\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n  listen(namespaceId, element, eventName, eventPhase, callback) {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n  flush(microtaskId = -1) {\n    this._transitionEngine.flush(microtaskId);\n  }\n  get players() {\n    return [...this._transitionEngine.players, ...this._timelineEngine.players];\n  }\n  whenRenderingDone() {\n    return this._transitionEngine.whenRenderingDone();\n  }\n  afterFlushAnimationsDone(cb) {\n    this._transitionEngine.afterFlushAnimationsDone(cb);\n  }\n}\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nfunction packageNonAnimatableStyles(element, styles) {\n  let startStyles = null;\n  let endStyles = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nclass SpecialCasedStyles {\n  _element;\n  _startStyles;\n  _endStyles;\n  static initialStylesByElement = /* @__PURE__ */new WeakMap();\n  _state = 0 /* SpecialCasedStylesState.Pending */;\n  _initialStyles;\n  constructor(_element, _startStyles, _endStyles) {\n    this._element = _element;\n    this._startStyles = _startStyles;\n    this._endStyles = _endStyles;\n    let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n    if (!initialStyles) {\n      SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = new Map());\n    }\n    this._initialStyles = initialStyles;\n  }\n  start() {\n    if (this._state < 1 /* SpecialCasedStylesState.Started */) {\n      if (this._startStyles) {\n        setStyles(this._element, this._startStyles, this._initialStyles);\n      }\n      this._state = 1 /* SpecialCasedStylesState.Started */;\n    }\n  }\n  finish() {\n    this.start();\n    if (this._state < 2 /* SpecialCasedStylesState.Finished */) {\n      setStyles(this._element, this._initialStyles);\n      if (this._endStyles) {\n        setStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      this._state = 1 /* SpecialCasedStylesState.Started */;\n    }\n  }\n  destroy() {\n    this.finish();\n    if (this._state < 3 /* SpecialCasedStylesState.Destroyed */) {\n      SpecialCasedStyles.initialStylesByElement.delete(this._element);\n      if (this._startStyles) {\n        eraseStyles(this._element, this._startStyles);\n        this._endStyles = null;\n      }\n      if (this._endStyles) {\n        eraseStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      setStyles(this._element, this._initialStyles);\n      this._state = 3 /* SpecialCasedStylesState.Destroyed */;\n    }\n  }\n}\nfunction filterNonAnimatableStyles(styles) {\n  let result = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\nfunction isNonAnimatableStyle(prop) {\n  return prop === 'display' || prop === 'position';\n}\nclass WebAnimationsPlayer {\n  element;\n  keyframes;\n  options;\n  _specialStyles;\n  _onDoneFns = [];\n  _onStartFns = [];\n  _onDestroyFns = [];\n  _duration;\n  _delay;\n  _initialized = false;\n  _finished = false;\n  _started = false;\n  _destroyed = false;\n  _finalKeyframe;\n  // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n  // and are used to reset the fns to their original values upon reset()\n  // (since the _onStartFns and _onDoneFns get deleted after they are called)\n  _originalOnDoneFns = [];\n  _originalOnStartFns = [];\n  // using non-null assertion because it's re(set) by init();\n  domPlayer;\n  time = 0;\n  parentPlayer = null;\n  currentSnapshot = new Map();\n  constructor(element, keyframes, options, _specialStyles) {\n    this.element = element;\n    this.keyframes = keyframes;\n    this.options = options;\n    this._specialStyles = _specialStyles;\n    this._duration = options['duration'];\n    this._delay = options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  init() {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n  _buildPlayer() {\n    if (this._initialized) return;\n    this._initialized = true;\n    const keyframes = this.keyframes;\n    // @ts-expect-error overwriting a readonly property\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    const onFinish = () => this._onFinish();\n    this.domPlayer.addEventListener('finish', onFinish);\n    this.onDestroy(() => {\n      // We must remove the `finish` event listener once an animation has completed all its\n      // iterations. This action is necessary to prevent a memory leak since the listener captures\n      // `this`, creating a closure that prevents `this` from being garbage collected.\n      this.domPlayer.removeEventListener('finish', onFinish);\n    });\n  }\n  _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n  _convertKeyframesToObject(keyframes) {\n    const kfs = [];\n    keyframes.forEach(frame => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n  /** @internal */\n  _triggerWebAnimation(element, keyframes, options) {\n    return element.animate(this._convertKeyframesToObject(keyframes), options);\n  }\n  onStart(fn) {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n  onDone(fn) {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n  play() {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n  pause() {\n    this.init();\n    this.domPlayer.pause();\n  }\n  finish() {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n  reset() {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n  _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n  restart() {\n    this.reset();\n    this.play();\n  }\n  hasStarted() {\n    return this._started;\n  }\n  destroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  setPosition(p) {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n    this.domPlayer.currentTime = p * this.time;\n  }\n  getPosition() {\n    // tsc is complaining with TS2362 without the conversion to number\n    return +(this.domPlayer.currentTime ?? 0) / this.time;\n  }\n  get totalTime() {\n    return this._delay + this._duration;\n  }\n  beforeDestroy() {\n    const styles = new Map();\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\nclass WebAnimationsDriver {\n  validateStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n    return true;\n  }\n  validateAnimatableStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n    return true;\n  }\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n  computeStyle(element, prop, defaultValue) {\n    return computeStyle(element, prop);\n  }\n  animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions = {\n      duration,\n      delay,\n      fill\n    };\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n    const previousStyles = new Map();\n    const previousWebAnimationPlayers = previousPlayers.filter(player => player instanceof WebAnimationsPlayer);\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach(player => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n    let _keyframes = normalizeKeyframes$1(keyframes).map(styles => new Map(styles));\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n}\nfunction createEngine(type, doc) {\n  // TODO: find a way to make this tree shakable.\n  if (type === 'noop') {\n    return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n  }\n  return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\nclass Animation {\n  _driver;\n  _animationAst;\n  constructor(_driver, input) {\n    this._driver = _driver;\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (warnings.length) {\n        warnValidation(warnings);\n      }\n    }\n    this._animationAst = ast;\n  }\n  buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n    const start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : startingStyles;\n    const dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : destinationStyles;\n    const errors = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n    return result;\n  }\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass BaseAnimationRenderer {\n  namespaceId;\n  delegate;\n  engine;\n  _onDestroy;\n  // We need to explicitly type this property because of an api-extractor bug\n  // See https://github.com/microsoft/rushstack/issues/4390\n  ɵtype = 0 /* AnimationRendererType.Regular */;\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroyNode(node) {\n    this.delegate.destroyNode?.(node);\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n    // doesn't have a parent. There appears to be some animation-specific downstream logic that\n    // depends on the null check happening before the animation engine. This check keeps the old\n    // behavior while allowing `core` to not have to check for the parent element anymore.\n    if (this.parentNode(oldChild)) {\n      this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback, options) {\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  factory;\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback, options) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nclass AnimationRendererFactory {\n  delegate;\n  engine;\n  _zone;\n  _currentId = 0;\n  _microtaskId = 1;\n  _animationCallbacksBuffer = [];\n  _rendererCache = new Map();\n  _cdRecurDepth = 0;\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    engine.onRemovalComplete = (element, delegate) => {\n      delegate?.removeChild(null, element);\n    };\n  }\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = '';\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type?.data?.['animation']) {\n      const cache = this._rendererCache;\n      let renderer = cache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => cache.delete(delegate);\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n        // only cache this result when the base renderer is used\n        cache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n  _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n    const animationCallbacksBuffer = this._animationCallbacksBuffer;\n    if (animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    animationCallbacksBuffer.push([fn, data]);\n  }\n  end() {\n    this._cdRecurDepth--;\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    // Flush the engine since the renderer destruction waits for animations to be done.\n    this.engine.flush();\n    this.delegate.componentReplaced?.(componentId);\n  }\n}\nexport { AnimationDriver, NoopAnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, BaseAnimationRenderer as ɵBaseAnimationRenderer, ENTER_CLASSNAME as ɵENTER_CLASSNAME, LEAVE_CLASSNAME as ɵLEAVE_CLASSNAME, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, TransitionAnimationPlayer as ɵTransitionAnimationPlayer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, containsElement as ɵcontainsElement, createEngine as ɵcreateEngine, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes$1 as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty };", "map": {"version": 3, "names": ["i0", "Injectable", "validateStyleProperty", "containsElement", "getParentElement", "invoke<PERSON><PERSON>y", "dashCaseToCamelCase", "invalidCssUnitValue", "invalidExpression", "invalidTransitionAlias", "visitDslNode", "invalid<PERSON><PERSON>ger", "invalidDefinition", "extractStyleParams", "invalidState", "invalidStyleV<PERSON>ue", "SUBSTITUTION_EXPR_START", "invalidParallelAnimation", "validateStyleParams", "invalidKeyframes", "invalidOffset", "keyframeOffsetsOutOfOrder", "keyframesMissingOffsets", "getOrSetDefaultValue", "invalidStagger", "resolveTiming", "normalizeAnimationEntry", "NG_TRIGGER_SELECTOR", "NG_ANIMATING_SELECTOR", "resolveTimingValue", "interpolateParams", "<PERSON><PERSON><PERSON><PERSON>", "registerFailed", "normalizeKeyframes", "LEAVE_CLASSNAME", "ENTER_CLASSNAME", "missingOrDestroyedAnimation", "createAnimationFailed", "optimizeGroupPlayer", "missingPlayer", "listenOnPlayer", "makeAnimationEvent", "triggerTransitionsFailed", "eraseStyles", "setStyles", "transitionFailed", "missing<PERSON><PERSON>ger", "missingEvent", "unsupportedTriggerEvent", "unregisteredTrigger", "NG_TRIGGER_CLASSNAME", "NG_ANIMATING_CLASSNAME", "triggerBuildFailed", "parseTimelineCommand", "computeStyle", "camelCaseToDashCase", "validateWebAnimatableStyleProperty", "allowPreviousPlayerStylesMerge", "normalizeKeyframes$1", "balancePreviousStylesIntoKeyframes", "validationFailed", "normalizeStyles", "buildingFailed", "NoopAnimationPlayer", "AnimationMetadataType", "style", "AUTO_STYLE", "ɵPRE_STYLE", "_PRE_STYLE", "AnimationGroupPlayer", "NoopAnimationDriver", "prop", "elm1", "elm2", "element", "query", "selector", "multi", "defaultValue", "animate", "keyframes", "duration", "delay", "easing", "previousPlayers", "scrubberAccessRequested", "ɵfac", "NoopAnimationDriver_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "AnimationDriver", "NOOP", "AnimationStyleNormalizer", "NoopAnimationStyleNormalizer", "normalizePropertyName", "propertyName", "errors", "normalizeStyleValue", "userProvidedProperty", "normalizedProperty", "value", "DIMENSIONAL_PROP_SET", "Set", "WebAnimationsStyleNormalizer", "unit", "strVal", "toString", "trim", "has", "valAndSuffixMatch", "match", "length", "push", "createListOfWarnings", "warnings", "LINE_START", "filter", "Boolean", "map", "warning", "join", "warnValidation", "console", "warn", "warnTriggerBuild", "name", "warnRegister", "pushUnrecognizedPropertiesWarning", "props", "ANY_STATE", "parseTransitionExpr", "transitionValue", "expressions", "split", "for<PERSON>ach", "str", "parseInnerTransitionStr", "eventStr", "result", "parseAnimationAlias", "fromState", "separator", "toState", "makeLambdaFromStates", "isFullAnyStateExpr", "alias", "parseFloat", "TRUE_BOOLEAN_VALUES", "FALSE_BOOLEAN_VALUES", "lhs", "rhs", "LHS_MATCH_BOOLEAN", "RHS_MATCH_BOOLEAN", "lhsMatch", "rhsMatch", "SELF_TOKEN", "SELF_TOKEN_REGEX", "RegExp", "buildAnimationAst", "driver", "metadata", "AnimationAstBuilderVisitor", "build", "ROOT_SELECTOR", "_driver", "constructor", "context", "AnimationAstBuilderContext", "_resetContextStyleTimingState", "ast", "unsupportedCSSPropertiesFound", "size", "keys", "currentQuerySelector", "collectedStyles", "Map", "set", "currentTime", "visitTrigger", "queryCount", "depCount", "states", "transitions", "char<PERSON>t", "definitions", "def", "State", "stateDef", "n", "visitState", "Transition", "transition", "visitTransition", "<PERSON><PERSON>", "options", "styleAst", "visitStyle", "styles", "astParams", "params", "containsDynamicStyles", "missingSubs", "sub", "hasOwnProperty", "add", "values", "animation", "matchers", "expr", "normalizeAnimationOptions", "visitSequence", "Sequence", "steps", "s", "visitGroup", "furthestTime", "step", "innerAst", "Math", "max", "Group", "visitAnimate", "timingAst", "constructTimingAst", "timings", "currentAnimateTimings", "styleMetadata", "Keyframes", "visitKeyframes", "isEmpty", "newStyleData", "_styleAst", "isEmptyStep", "Animate", "_makeStyleAst", "_validateStyleAst", "metadataStyles", "Array", "isArray", "styleTuple", "Object", "entries", "collectedEasing", "styleData", "get", "delete", "indexOf", "Style", "offset", "endTime", "startTime", "tuple", "collectedEntry", "updateCollectedStyle", "MAX_KEYFRAME_OFFSET", "totalKeyframesWithOffsets", "offsets", "offsetsOutOfOrder", "keyframesOutOfRange", "previousOffset", "offsetVal", "consumeOffset", "generatedOffset", "limit", "animateDuration", "kf", "i", "durationUpToThisFrame", "visitReference", "Reference", "visitAnimateChild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visitAnimateRef", "AnimateRef", "visit<PERSON><PERSON><PERSON>", "parentSelector", "<PERSON><PERSON><PERSON><PERSON>", "includeSelf", "normalizeSelector", "Query", "optional", "originalSelector", "visitStagger", "Stagger", "hasAmpersand", "find", "replace", "slice", "normalizeParams", "obj", "currentTransition", "makeTimingAst", "strValue", "isDynamic", "some", "v", "dynamic", "createTimelineInstruction", "preStyleProps", "postStyleProps", "subTimeline", "totalTime", "ElementInstructionMap", "_map", "append", "instructions", "existingInstructions", "clear", "ONE_FRAME_IN_MILLISECONDS", "ENTER_TOKEN", "ENTER_TOKEN_REGEX", "LEAVE_TOKEN", "LEAVE_TOKEN_REGEX", "buildAnimationTimelines", "rootElement", "enterClassName", "leaveClassName", "startingStyles", "finalStyles", "subInstructions", "AnimationTimelineBuilderVisitor", "buildKeyframes", "AnimationTimelineContext", "currentTimeline", "delayNextStep", "timelines", "timeline", "containsAnimation", "lastRootTimeline", "allowOnlyTimelineStyles", "elementInstructions", "innerContext", "createSubContext", "_visitSubInstructions", "transformIntoNewTimeline", "previousNode", "_applyAnimationRefDelays", "animationsRefsOptions", "animationRefOptions", "animationDelay", "animationDelayValue", "instruction", "instructionTimings", "appendInstructionToTimeline", "updateOptions", "subContextCount", "ctx", "snapshotCurrentStyles", "DEFAULT_NOOP_PREVIOUS_NODE", "applyStylesToKeyframe", "innerTimelines", "mergeTimelineCollectedStyles", "_visitTiming", "timingValue", "incrementTime", "hasCurrentStyleProperties", "<PERSON><PERSON><PERSON><PERSON>", "applyEmptyStep", "innerTimeline", "forwardTime", "elms", "currentQueryTotal", "sameElementTimeline", "currentQueryIndex", "parentContext", "tl", "abs", "maxTime", "staggerTransformer", "currentStaggerTime", "startingTime", "_enterClassName", "_leaveClassName", "initialTimeline", "TimelineBuilder", "skipIfExists", "newOptions", "optionsToUpdate", "newParams", "paramsToUpdate", "_copyOptions", "oldParams", "newTime", "target", "fork", "updatedTimings", "builder", "SubTimelineBuilder", "stretchStartingKeyframe", "time", "results", "elements", "_elementTimelineStylesLookup", "_previousKeyframe", "_currentKeyframe", "_keyframes", "_styleSummary", "_localTimelineStyles", "_globalTimelineStyles", "_pendingStyles", "_backFill", "_currentEmptyStepKeyframe", "_loadKeyframe", "hasPreStyleStep", "_updateStyle", "input", "flattenStyles", "val", "getFinalKeyframe", "properties", "details1", "details0", "finalKeyframes", "keyframe", "finalKeyframe", "preProps", "postProps", "kf0", "kf1", "_stretchStartingKeyframe", "newKeyframes", "startingGap", "newFirstKeyframe", "oldFirstKeyframe", "roundOffset", "oldOffset", "timeAtKeyframe", "decimalPoints", "mult", "pow", "round", "allStyles", "allProperties", "createTransitionInstruction", "triggerName", "isRemovalTransition", "fromStyles", "to<PERSON><PERSON>les", "queriedElements", "EMPTY_OBJECT", "AnimationTransitionFactory", "_triggerName", "_stateStyles", "currentState", "nextState", "oneOrMoreTransitionsMatch", "buildStyles", "stateName", "styler", "undefined", "currentOptions", "nextOptions", "skipAstBuild", "transitionAnimationParams", "currentAnimationParams", "currentStateStyles", "nextAnimationParams", "nextStateStyles", "preStyleMap", "postStyleMap", "isRemoval", "animationOptions", "applyParamDefaults", "elm", "checkNonAnimatableInTimelines", "validateAnimatableStyleProperty", "allowedNonAnimatableProps", "invalidNonAnimatableProps", "nonAnimatablePropsInitialValues", "entriesToCheck", "from", "propInitialValue", "matchFns", "fn", "userParams", "defaults", "key", "AnimationStateStyles", "defaultParams", "normalizer", "combinedParams", "normalizedProp", "buildTrigger", "AnimationTrigger", "_normalizer", "transitionFactories", "fallbackTransition", "balanceProperties", "createFallbackTransition", "containsQueries", "matchTransition", "entry", "f", "matchStyles", "stateMap", "key1", "key2", "EMPTY_INSTRUCTION_MAP", "TimelineAnimationEngine", "bodyNode", "_animations", "_playersById", "players", "register", "id", "_buildPlayer", "preStyles", "postStyles", "create", "autoStylesMap", "inst", "_", "player", "onDestroy", "destroy", "_getPlayer", "index", "splice", "listen", "eventName", "callback", "baseEvent", "command", "args", "play", "pause", "reset", "restart", "finish", "init", "setPosition", "QUEUED_CLASSNAME", "QUEUED_SELECTOR", "DISABLED_CLASSNAME", "DISABLED_SELECTOR", "STAR_CLASSNAME", "STAR_SELECTOR", "EMPTY_PLAYER_ARRAY", "NULL_REMOVAL_STATE", "namespaceId", "setForRemoval", "setForMove", "hasAnimation", "removed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NULL_REMOVED_QUERIED_STATE", "REMOVAL_FLAG", "StateValue", "isObj", "normalizeTriggerValue", "absorbOptions", "VOID_VALUE", "DEFAULT_STATE_VALUE", "AnimationTransitionNamespace", "hostElement", "_engine", "_triggers", "_queue", "_elementListeners", "_hostClassName", "addClass", "phase", "isTriggerEventValid", "listeners", "data", "triggersWithStates", "statesByElement", "afterFlush", "_getTrigger", "trigger", "defaultToFallback", "TransitionAnimationPlayer", "obj<PERSON><PERSON><PERSON>", "reportError", "playersOnElement", "players<PERSON>y<PERSON><PERSON>", "queued", "isFallbackTransition", "totalQueuedPlayers", "onStart", "removeClass", "onDone", "deregister", "clearElementCache", "elementPlayers", "_signalRemovalForInnerTriggers", "namespaces", "fetchNamespacesByElement", "ns", "triggerLeaveAnimation", "afterFlushAnimationsDone", "destroyAfterComplete", "triggerStates", "previousTriggersValues", "state", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>oved", "processLeaveNode", "prepareLeaveAnimationListeners", "elementStates", "visitedTriggers", "listener", "removeNode", "engine", "childElementCount", "containsPotentialParentTransition", "totalAnimations", "currentPlayers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "parentNode", "triggers", "removalFlag", "destroyInnerAnimations", "_onRemovalComplete", "insertNode", "drainQueuedTransitions", "microtaskId", "destroyed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "d0", "d1", "p", "TransitionAnimationEngine", "newHostElements", "disabledNodes", "_namespaceLookup", "_namespaceList", "_flushFns", "_whenQuietFns", "namespacesByHostElement", "collectedEnterElements", "collectedLeaveElements", "onRemovalComplete", "queuedPlayers", "createNamespace", "_balanceNamespaceList", "collectEnterElement", "namespaceList", "found", "ancestor", "ancestorNs", "unshift", "registerTrigger", "_fetchNamespace", "stateValue", "isElementNode", "insertBefore", "details", "markElementAsDisabled", "hostNS", "_buildInstruction", "subTimelines", "skipBuildAst", "containerElement", "destroyActiveAnimationsForElement", "finishActiveQueriedAnimationOnElement", "whenRenderingDone", "Promise", "resolve", "classList", "contains", "node", "flush", "cleanupFns", "_flushAnimations", "quietFns", "skippedPlayers", "skippedPlayersMap", "queuedInstructions", "allPreStyleElements", "allPostStyleElements", "disabledElementsSet", "nodesThatAreDisabled", "allTriggerElements", "enterNodeMap", "buildRootMap", "enterNodeMapIds", "nodes", "root", "className", "allLeaveNodes", "mergedLeaveNodes", "leaveNodesWithoutAnimations", "leaveNodeMapIds", "leaveNodeMap", "allPlayers", "erroneousTransitions", "previousValue", "nodeIsOrphaned", "stringMap", "setVal", "allPreviousPlayersMap", "animationElementMap", "_beforeAnimationBuild", "_getPreviousPlayers", "prevPlayer", "replaceNodes", "replacePostStylesAsPre", "postStylesMap", "allLeaveQueriedNodes", "cloakAndComputeStyles", "preStylesMap", "post", "pre", "rootPlayers", "subPlayers", "NO_PARENT_ANIMATION_ELEMENT_DETECTED", "disabled", "overrideTotalTime", "parentWithAnimation", "parentsToAdd", "detectedParent", "innerPlayer", "_buildAnimation", "setRealPlayer", "parentPlayers", "parentPlayer", "playersFor<PERSON>lement", "syncPlayerEvents", "queriedPlayerResults", "queriedInnerElements", "j", "queriedPlayers", "activePlayers", "removeNodesAfterAnimationDone", "isQueriedElement", "toStateValue", "queriedElementPlayers", "isRemovalAnimation", "targetNameSpaceId", "targetTriggerName", "timelineInstruction", "realPlayer", "getRealPlayer", "<PERSON><PERSON><PERSON><PERSON>", "allQueriedPlayers", "allConsumedElements", "allSubElements", "allNewPlayers", "flattenGroupPlayers", "pp", "wrappedPlayer", "deleteOrUnsetInMap", "_player", "_containsRealPlayer", "_queuedCallbacks", "callbacks", "triggerCallback", "_queueEvent", "hasStarted", "getPosition", "phaseName", "currentV<PERSON>ues", "cloakElement", "oldValue", "display", "valuesMap", "elementPropsMap", "defaultStyle", "cloakVals", "failedElements", "roots", "rootMap", "NULL_NODE", "nodeSet", "localRootMap", "getRoot", "remove", "finalPlayers", "_flattenGroupPlayersRecur", "k1", "k2", "postEntry", "preEntry", "AnimationEngine", "_transitionEngine", "_timelineEngine", "_triggerCache", "doc", "body", "componentId", "cache<PERSON>ey", "onInsert", "onRemove", "disableAnimations", "disable", "process", "property", "action", "eventPhase", "cb", "packageNonAnimatableStyles", "startStyles", "endStyles", "filterNonAnimatableStyles", "SpecialCasedStyles", "_element", "_startStyles", "_endStyles", "initialStylesByElement", "WeakMap", "_state", "_initialStyles", "initialStyles", "start", "isNonAnimatableStyle", "WebAnimationsPlayer", "_specialStyles", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_duration", "_delay", "_initialized", "_finished", "_started", "_destroyed", "_finalKeyframe", "_originalOnDoneFns", "_originalOnStartFns", "domPlayer", "currentSnapshot", "_onFinish", "_preparePlayerBeforeStart", "_triggerWebAnimation", "onFinish", "addEventListener", "removeEventListener", "_resetDomPlayerState", "_convertKeyframesToObject", "kfs", "frame", "fromEntries", "cancel", "methods", "WebAnimationsDriver", "cssProp", "fill", "playerOptions", "previousStyles", "previousWebAnimationPlayers", "specialStyles", "createEngine", "Animation", "_animationAst", "buildTimelines", "destinationStyles", "dest", "ANIMATION_PREFIX", "DISABLE_ANIMATIONS_FLAG", "BaseAnimationRenderer", "delegate", "_onD<PERSON>roy", "ɵtype", "destroyNode", "queueMicrotask", "createElement", "namespace", "createComment", "createText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isHostElement", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "setAttribute", "el", "removeAttribute", "setStyle", "flags", "removeStyle", "setProperty", "setValue", "<PERSON><PERSON><PERSON><PERSON>", "resolveElementFromTarget", "parseTriggerCallbackName", "event", "countId", "scheduleListenerCallback", "document", "window", "dotIndex", "substring", "AnimationRendererFactory", "_zone", "_currentId", "_microtaskId", "_animationCallbacksBuffer", "_rendererCache", "_cdRecurDepth", "<PERSON><PERSON><PERSON><PERSON>", "EMPTY_NAMESPACE_ID", "cache", "renderer", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animationTriggers", "begin", "_scheduleCountTask", "count", "run", "animationCallbacksBuffer", "end", "runOutsideAngular", "componentReplaced", "ɵAnimation", "ɵAnimationEngine", "ɵAnimationRenderer", "ɵAnimationRendererFactory", "ɵAnimationStyleNormalizer", "ɵBaseAnimationRenderer", "ɵENTER_CLASSNAME", "ɵLEAVE_CLASSNAME", "ɵNoopAnimationStyleNormalizer", "ɵTransitionAnimationPlayer", "ɵWebAnimationsDriver", "ɵWebAnimationsPlayer", "ɵWebAnimationsStyleNormalizer", "ɵallowPreviousPlayerStylesMerge", "ɵcamelCaseToDashCase", "ɵcontainsElement", "ɵcreateEngine", "ɵgetParentElement", "ɵinvokeQuery", "ɵnormalizeKeyframes", "ɵvalidateStyleProperty", "ɵvalidateWebAnimatableStyleProperty"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/animations/fesm2022/browser.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { validateStyleProperty, containsElement, getParentElement, invokeQuery, dashCaseToCamelCase, invalidCssUnitValue, invalidExpression, invalidTransitionAlias, visitDslNode, invalidTrigger, invalidDefinition, extractStyleParams, invalidState, invalidStyleValue, SUBSTITUTION_EXPR_START, invalidParallelAnimation, validateStyleParams, invalidKeyframes, invalidOffset, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, getOrSetDefaultValue, invalidStagger, resolveTiming, normalizeAnimationEntry, NG_TRIGGER_SELECTOR, NG_ANIMATING_SELECTOR, resolveTimingValue, interpolateParams, invalidQuery, registerFailed, normalizeKeyframes, LEAVE_CLASSNAME, ENTER_CLASSNAME, missingOrDestroyedAnimation, createAnimationFailed, optimizeGroupPlayer, missingPlayer, listenOnPlayer, makeAnimationEvent, triggerTransitionsFailed, eraseStyles, setStyles, transitionFailed, missingTrigger, missingEvent, unsupportedTriggerEvent, unregisteredTrigger, NG_TRIGGER_CLASSNAME, NG_ANIMATING_CLASSNAME, triggerBuildFailed, parseTimelineCommand, computeStyle, camelCaseToDashCase, validateWebAnimatableStyleProperty, allowPreviousPlayerStylesMerge, normalizeKeyframes$1, balancePreviousStylesIntoKeyframes, validationFailed, normalizeStyles, buildingFailed } from './util-D9FfmVnv.mjs';\nimport { NoopAnimationPlayer, AnimationMetadataType, style, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationGroupPlayer } from './private_export-faY_wCkZ.mjs';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\nclass NoopAnimationDriver {\n    /**\n     * @returns Whether `prop` is a valid CSS property\n     */\n    validateStyleProperty(prop) {\n        return validateStyleProperty(prop);\n    }\n    /**\n     *\n     * @returns Whether elm1 contains elm2.\n     */\n    containsElement(elm1, elm2) {\n        return containsElement(elm1, elm2);\n    }\n    /**\n     * @returns Rhe parent of the given element or `null` if the element is the `document`\n     */\n    getParentElement(element) {\n        return getParentElement(element);\n    }\n    /**\n     * @returns The result of the query selector on the element. The array will contain up to 1 item\n     *     if `multi` is  `false`.\n     */\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @returns The `defaultValue` or empty string\n     */\n    computeStyle(element, prop, defaultValue) {\n        return defaultValue || '';\n    }\n    /**\n     * @returns An `NoopAnimationPlayer`\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n        return new NoopAnimationPlayer(duration, delay);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NoopAnimationDriver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NoopAnimationDriver });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NoopAnimationDriver, decorators: [{\n            type: Injectable\n        }] });\n/**\n * @publicApi\n */\nclass AnimationDriver {\n    /**\n     * @deprecated Use the NoopAnimationDriver class.\n     */\n    static NOOP = new NoopAnimationDriver();\n}\n\nclass AnimationStyleNormalizer {\n}\nclass NoopAnimationStyleNormalizer {\n    normalizePropertyName(propertyName, errors) {\n        return propertyName;\n    }\n    normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n        return value;\n    }\n}\n\nconst DIMENSIONAL_PROP_SET = new Set([\n    'width',\n    'height',\n    'minWidth',\n    'minHeight',\n    'maxWidth',\n    'maxHeight',\n    'left',\n    'top',\n    'bottom',\n    'right',\n    'fontSize',\n    'outlineWidth',\n    'outlineOffset',\n    'paddingTop',\n    'paddingLeft',\n    'paddingBottom',\n    'paddingRight',\n    'marginTop',\n    'marginLeft',\n    'marginBottom',\n    'marginRight',\n    'borderRadius',\n    'borderWidth',\n    'borderTopWidth',\n    'borderLeftWidth',\n    'borderRightWidth',\n    'borderBottomWidth',\n    'textIndent',\n    'perspective',\n]);\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n    normalizePropertyName(propertyName, errors) {\n        return dashCaseToCamelCase(propertyName);\n    }\n    normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n        let unit = '';\n        const strVal = value.toString().trim();\n        if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n            if (typeof value === 'number') {\n                unit = 'px';\n            }\n            else {\n                const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n                if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n                    errors.push(invalidCssUnitValue(userProvidedProperty, value));\n                }\n            }\n        }\n        return strVal + unit;\n    }\n}\n\nfunction createListOfWarnings(warnings) {\n    const LINE_START = '\\n - ';\n    return `${LINE_START}${warnings\n        .filter(Boolean)\n        .map((warning) => warning)\n        .join(LINE_START)}`;\n}\nfunction warnValidation(warnings) {\n    console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnTriggerBuild(name, warnings) {\n    console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnRegister(warnings) {\n    console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n    if (props.length) {\n        warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n    }\n}\n\nconst ANY_STATE = '*';\nfunction parseTransitionExpr(transitionValue, errors) {\n    const expressions = [];\n    if (typeof transitionValue == 'string') {\n        transitionValue\n            .split(/\\s*,\\s*/)\n            .forEach((str) => parseInnerTransitionStr(str, expressions, errors));\n    }\n    else {\n        expressions.push(transitionValue);\n    }\n    return expressions;\n}\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n    if (eventStr[0] == ':') {\n        const result = parseAnimationAlias(eventStr, errors);\n        if (typeof result == 'function') {\n            expressions.push(result);\n            return;\n        }\n        eventStr = result;\n    }\n    const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n    if (match == null || match.length < 4) {\n        errors.push(invalidExpression(eventStr));\n        return expressions;\n    }\n    const fromState = match[1];\n    const separator = match[2];\n    const toState = match[3];\n    expressions.push(makeLambdaFromStates(fromState, toState));\n    const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n    if (separator[0] == '<' && !isFullAnyStateExpr) {\n        expressions.push(makeLambdaFromStates(toState, fromState));\n    }\n    return;\n}\nfunction parseAnimationAlias(alias, errors) {\n    switch (alias) {\n        case ':enter':\n            return 'void => *';\n        case ':leave':\n            return '* => void';\n        case ':increment':\n            return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n        case ':decrement':\n            return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n        default:\n            errors.push(invalidTransitionAlias(alias));\n            return '* => *';\n    }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\nfunction makeLambdaFromStates(lhs, rhs) {\n    const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n    const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n    return (fromState, toState) => {\n        let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n        let rhsMatch = rhs == ANY_STATE || rhs == toState;\n        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n        }\n        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n        }\n        return lhsMatch && rhsMatch;\n    };\n}\n\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /* @__PURE__ */ new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n    return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\nconst ROOT_SELECTOR = '';\nclass AnimationAstBuilderVisitor {\n    _driver;\n    constructor(_driver) {\n        this._driver = _driver;\n    }\n    build(metadata, errors, warnings) {\n        const context = new AnimationAstBuilderContext(errors);\n        this._resetContextStyleTimingState(context);\n        const ast = (visitDslNode(this, normalizeAnimationEntry(metadata), context));\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (context.unsupportedCSSPropertiesFound.size) {\n                pushUnrecognizedPropertiesWarning(warnings, [\n                    ...context.unsupportedCSSPropertiesFound.keys(),\n                ]);\n            }\n        }\n        return ast;\n    }\n    _resetContextStyleTimingState(context) {\n        context.currentQuerySelector = ROOT_SELECTOR;\n        context.collectedStyles = new Map();\n        context.collectedStyles.set(ROOT_SELECTOR, new Map());\n        context.currentTime = 0;\n    }\n    visitTrigger(metadata, context) {\n        let queryCount = (context.queryCount = 0);\n        let depCount = (context.depCount = 0);\n        const states = [];\n        const transitions = [];\n        if (metadata.name.charAt(0) == '@') {\n            context.errors.push(invalidTrigger());\n        }\n        metadata.definitions.forEach((def) => {\n            this._resetContextStyleTimingState(context);\n            if (def.type == AnimationMetadataType.State) {\n                const stateDef = def;\n                const name = stateDef.name;\n                name\n                    .toString()\n                    .split(/\\s*,\\s*/)\n                    .forEach((n) => {\n                    stateDef.name = n;\n                    states.push(this.visitState(stateDef, context));\n                });\n                stateDef.name = name;\n            }\n            else if (def.type == AnimationMetadataType.Transition) {\n                const transition = this.visitTransition(def, context);\n                queryCount += transition.queryCount;\n                depCount += transition.depCount;\n                transitions.push(transition);\n            }\n            else {\n                context.errors.push(invalidDefinition());\n            }\n        });\n        return {\n            type: AnimationMetadataType.Trigger,\n            name: metadata.name,\n            states,\n            transitions,\n            queryCount,\n            depCount,\n            options: null,\n        };\n    }\n    visitState(metadata, context) {\n        const styleAst = this.visitStyle(metadata.styles, context);\n        const astParams = (metadata.options && metadata.options.params) || null;\n        if (styleAst.containsDynamicStyles) {\n            const missingSubs = new Set();\n            const params = astParams || {};\n            styleAst.styles.forEach((style) => {\n                if (style instanceof Map) {\n                    style.forEach((value) => {\n                        extractStyleParams(value).forEach((sub) => {\n                            if (!params.hasOwnProperty(sub)) {\n                                missingSubs.add(sub);\n                            }\n                        });\n                    });\n                }\n            });\n            if (missingSubs.size) {\n                context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n            }\n        }\n        return {\n            type: AnimationMetadataType.State,\n            name: metadata.name,\n            style: styleAst,\n            options: astParams ? { params: astParams } : null,\n        };\n    }\n    visitTransition(metadata, context) {\n        context.queryCount = 0;\n        context.depCount = 0;\n        const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        const matchers = parseTransitionExpr(metadata.expr, context.errors);\n        return {\n            type: AnimationMetadataType.Transition,\n            matchers,\n            animation,\n            queryCount: context.queryCount,\n            depCount: context.depCount,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitSequence(metadata, context) {\n        return {\n            type: AnimationMetadataType.Sequence,\n            steps: metadata.steps.map((s) => visitDslNode(this, s, context)),\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitGroup(metadata, context) {\n        const currentTime = context.currentTime;\n        let furthestTime = 0;\n        const steps = metadata.steps.map((step) => {\n            context.currentTime = currentTime;\n            const innerAst = visitDslNode(this, step, context);\n            furthestTime = Math.max(furthestTime, context.currentTime);\n            return innerAst;\n        });\n        context.currentTime = furthestTime;\n        return {\n            type: AnimationMetadataType.Group,\n            steps,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitAnimate(metadata, context) {\n        const timingAst = constructTimingAst(metadata.timings, context.errors);\n        context.currentAnimateTimings = timingAst;\n        let styleAst;\n        let styleMetadata = metadata.styles\n            ? metadata.styles\n            : style({});\n        if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n            styleAst = this.visitKeyframes(styleMetadata, context);\n        }\n        else {\n            let styleMetadata = metadata.styles;\n            let isEmpty = false;\n            if (!styleMetadata) {\n                isEmpty = true;\n                const newStyleData = {};\n                if (timingAst.easing) {\n                    newStyleData['easing'] = timingAst.easing;\n                }\n                styleMetadata = style(newStyleData);\n            }\n            context.currentTime += timingAst.duration + timingAst.delay;\n            const _styleAst = this.visitStyle(styleMetadata, context);\n            _styleAst.isEmptyStep = isEmpty;\n            styleAst = _styleAst;\n        }\n        context.currentAnimateTimings = null;\n        return {\n            type: AnimationMetadataType.Animate,\n            timings: timingAst,\n            style: styleAst,\n            options: null,\n        };\n    }\n    visitStyle(metadata, context) {\n        const ast = this._makeStyleAst(metadata, context);\n        this._validateStyleAst(ast, context);\n        return ast;\n    }\n    _makeStyleAst(metadata, context) {\n        const styles = [];\n        const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n        for (let styleTuple of metadataStyles) {\n            if (typeof styleTuple === 'string') {\n                if (styleTuple === AUTO_STYLE) {\n                    styles.push(styleTuple);\n                }\n                else {\n                    context.errors.push(invalidStyleValue(styleTuple));\n                }\n            }\n            else {\n                styles.push(new Map(Object.entries(styleTuple)));\n            }\n        }\n        let containsDynamicStyles = false;\n        let collectedEasing = null;\n        styles.forEach((styleData) => {\n            if (styleData instanceof Map) {\n                if (styleData.has('easing')) {\n                    collectedEasing = styleData.get('easing');\n                    styleData.delete('easing');\n                }\n                if (!containsDynamicStyles) {\n                    for (let value of styleData.values()) {\n                        if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n                            containsDynamicStyles = true;\n                            break;\n                        }\n                    }\n                }\n            }\n        });\n        return {\n            type: AnimationMetadataType.Style,\n            styles,\n            easing: collectedEasing,\n            offset: metadata.offset,\n            containsDynamicStyles,\n            options: null,\n        };\n    }\n    _validateStyleAst(ast, context) {\n        const timings = context.currentAnimateTimings;\n        let endTime = context.currentTime;\n        let startTime = context.currentTime;\n        if (timings && startTime > 0) {\n            startTime -= timings.duration + timings.delay;\n        }\n        ast.styles.forEach((tuple) => {\n            if (typeof tuple === 'string')\n                return;\n            tuple.forEach((value, prop) => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    if (!this._driver.validateStyleProperty(prop)) {\n                        tuple.delete(prop);\n                        context.unsupportedCSSPropertiesFound.add(prop);\n                        return;\n                    }\n                }\n                // This is guaranteed to have a defined Map at this querySelector location making it\n                // safe to add the assertion here. It is set as a default empty map in prior methods.\n                const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n                const collectedEntry = collectedStyles.get(prop);\n                let updateCollectedStyle = true;\n                if (collectedEntry) {\n                    if (startTime != endTime &&\n                        startTime >= collectedEntry.startTime &&\n                        endTime <= collectedEntry.endTime) {\n                        context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n                        updateCollectedStyle = false;\n                    }\n                    // we always choose the smaller start time value since we\n                    // want to have a record of the entire animation window where\n                    // the style property is being animated in between\n                    startTime = collectedEntry.startTime;\n                }\n                if (updateCollectedStyle) {\n                    collectedStyles.set(prop, { startTime, endTime });\n                }\n                if (context.options) {\n                    validateStyleParams(value, context.options, context.errors);\n                }\n            });\n        });\n    }\n    visitKeyframes(metadata, context) {\n        const ast = { type: AnimationMetadataType.Keyframes, styles: [], options: null };\n        if (!context.currentAnimateTimings) {\n            context.errors.push(invalidKeyframes());\n            return ast;\n        }\n        const MAX_KEYFRAME_OFFSET = 1;\n        let totalKeyframesWithOffsets = 0;\n        const offsets = [];\n        let offsetsOutOfOrder = false;\n        let keyframesOutOfRange = false;\n        let previousOffset = 0;\n        const keyframes = metadata.steps.map((styles) => {\n            const style = this._makeStyleAst(styles, context);\n            let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n            let offset = 0;\n            if (offsetVal != null) {\n                totalKeyframesWithOffsets++;\n                offset = style.offset = offsetVal;\n            }\n            keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n            offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n            previousOffset = offset;\n            offsets.push(offset);\n            return style;\n        });\n        if (keyframesOutOfRange) {\n            context.errors.push(invalidOffset());\n        }\n        if (offsetsOutOfOrder) {\n            context.errors.push(keyframeOffsetsOutOfOrder());\n        }\n        const length = metadata.steps.length;\n        let generatedOffset = 0;\n        if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n            context.errors.push(keyframesMissingOffsets());\n        }\n        else if (totalKeyframesWithOffsets == 0) {\n            generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n        }\n        const limit = length - 1;\n        const currentTime = context.currentTime;\n        const currentAnimateTimings = context.currentAnimateTimings;\n        const animateDuration = currentAnimateTimings.duration;\n        keyframes.forEach((kf, i) => {\n            const offset = generatedOffset > 0 ? (i == limit ? 1 : generatedOffset * i) : offsets[i];\n            const durationUpToThisFrame = offset * animateDuration;\n            context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n            currentAnimateTimings.duration = durationUpToThisFrame;\n            this._validateStyleAst(kf, context);\n            kf.offset = offset;\n            ast.styles.push(kf);\n        });\n        return ast;\n    }\n    visitReference(metadata, context) {\n        return {\n            type: AnimationMetadataType.Reference,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitAnimateChild(metadata, context) {\n        context.depCount++;\n        return {\n            type: AnimationMetadataType.AnimateChild,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitAnimateRef(metadata, context) {\n        return {\n            type: AnimationMetadataType.AnimateRef,\n            animation: this.visitReference(metadata.animation, context),\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitQuery(metadata, context) {\n        const parentSelector = context.currentQuerySelector;\n        const options = (metadata.options || {});\n        context.queryCount++;\n        context.currentQuery = metadata;\n        const [selector, includeSelf] = normalizeSelector(metadata.selector);\n        context.currentQuerySelector = parentSelector.length\n            ? parentSelector + ' ' + selector\n            : selector;\n        getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n        const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        context.currentQuery = null;\n        context.currentQuerySelector = parentSelector;\n        return {\n            type: AnimationMetadataType.Query,\n            selector,\n            limit: options.limit || 0,\n            optional: !!options.optional,\n            includeSelf,\n            animation,\n            originalSelector: metadata.selector,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitStagger(metadata, context) {\n        if (!context.currentQuery) {\n            context.errors.push(invalidStagger());\n        }\n        const timings = metadata.timings === 'full'\n            ? { duration: 0, delay: 0, easing: 'full' }\n            : resolveTiming(metadata.timings, context.errors, true);\n        return {\n            type: AnimationMetadataType.Stagger,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            timings,\n            options: null,\n        };\n    }\n}\nfunction normalizeSelector(selector) {\n    const hasAmpersand = selector.split(/\\s*,\\s*/).find((token) => token == SELF_TOKEN)\n        ? true\n        : false;\n    if (hasAmpersand) {\n        selector = selector.replace(SELF_TOKEN_REGEX, '');\n    }\n    // Note: the :enter and :leave aren't normalized here since those\n    // selectors are filled in at runtime during timeline building\n    selector = selector\n        .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n        .replace(/@\\w+/g, (match) => NG_TRIGGER_SELECTOR + '-' + match.slice(1))\n        .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n    return [selector, hasAmpersand];\n}\nfunction normalizeParams(obj) {\n    return obj ? { ...obj } : null;\n}\nclass AnimationAstBuilderContext {\n    errors;\n    queryCount = 0;\n    depCount = 0;\n    currentTransition = null;\n    currentQuery = null;\n    currentQuerySelector = null;\n    currentAnimateTimings = null;\n    currentTime = 0;\n    collectedStyles = new Map();\n    options = null;\n    unsupportedCSSPropertiesFound = new Set();\n    constructor(errors) {\n        this.errors = errors;\n    }\n}\nfunction consumeOffset(styles) {\n    if (typeof styles == 'string')\n        return null;\n    let offset = null;\n    if (Array.isArray(styles)) {\n        styles.forEach((styleTuple) => {\n            if (styleTuple instanceof Map && styleTuple.has('offset')) {\n                const obj = styleTuple;\n                offset = parseFloat(obj.get('offset'));\n                obj.delete('offset');\n            }\n        });\n    }\n    else if (styles instanceof Map && styles.has('offset')) {\n        const obj = styles;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n    }\n    return offset;\n}\nfunction constructTimingAst(value, errors) {\n    if (value.hasOwnProperty('duration')) {\n        return value;\n    }\n    if (typeof value == 'number') {\n        const duration = resolveTiming(value, errors).duration;\n        return makeTimingAst(duration, 0, '');\n    }\n    const strValue = value;\n    const isDynamic = strValue.split(/\\s+/).some((v) => v.charAt(0) == '{' && v.charAt(1) == '{');\n    if (isDynamic) {\n        const ast = makeTimingAst(0, 0, '');\n        ast.dynamic = true;\n        ast.strValue = strValue;\n        return ast;\n    }\n    const timings = resolveTiming(strValue, errors);\n    return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\nfunction normalizeAnimationOptions(options) {\n    if (options) {\n        options = { ...options };\n        if (options['params']) {\n            options['params'] = normalizeParams(options['params']);\n        }\n    }\n    else {\n        options = {};\n    }\n    return options;\n}\nfunction makeTimingAst(duration, delay, easing) {\n    return { duration, delay, easing };\n}\n\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n    return {\n        type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,\n        element,\n        keyframes,\n        preStyleProps,\n        postStyleProps,\n        duration,\n        delay,\n        totalTime: duration + delay,\n        easing,\n        subTimeline,\n    };\n}\n\nclass ElementInstructionMap {\n    _map = new Map();\n    get(element) {\n        return this._map.get(element) || [];\n    }\n    append(element, instructions) {\n        let existingInstructions = this._map.get(element);\n        if (!existingInstructions) {\n            this._map.set(element, (existingInstructions = []));\n        }\n        existingInstructions.push(...instructions);\n    }\n    has(element) {\n        return this._map.has(element);\n    }\n    clear() {\n        this._map.clear();\n    }\n}\n\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /* @__PURE__ */ new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /* @__PURE__ */ new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```ts\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```ts\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n    return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nclass AnimationTimelineBuilderVisitor {\n    buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n        subInstructions = subInstructions || new ElementInstructionMap();\n        const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n        context.options = options;\n        const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        context.currentTimeline.delayNextStep(delay);\n        context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n        visitDslNode(this, ast, context);\n        // this checks to see if an actual animation happened\n        const timelines = context.timelines.filter((timeline) => timeline.containsAnimation());\n        // note: we just want to apply the final styles for the rootElement, so we do not\n        //       just apply the styles to the last timeline but the last timeline which\n        //       element is the root one (basically `*`-styles are replaced with the actual\n        //       state style values only for the root element)\n        if (timelines.length && finalStyles.size) {\n            let lastRootTimeline;\n            for (let i = timelines.length - 1; i >= 0; i--) {\n                const timeline = timelines[i];\n                if (timeline.element === rootElement) {\n                    lastRootTimeline = timeline;\n                    break;\n                }\n            }\n            if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n                lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n            }\n        }\n        return timelines.length\n            ? timelines.map((timeline) => timeline.buildKeyframes())\n            : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n    }\n    visitTrigger(ast, context) {\n        // these values are not visited in this AST\n    }\n    visitState(ast, context) {\n        // these values are not visited in this AST\n    }\n    visitTransition(ast, context) {\n        // these values are not visited in this AST\n    }\n    visitAnimateChild(ast, context) {\n        const elementInstructions = context.subInstructions.get(context.element);\n        if (elementInstructions) {\n            const innerContext = context.createSubContext(ast.options);\n            const startTime = context.currentTimeline.currentTime;\n            const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n            if (startTime != endTime) {\n                // we do this on the upper context because we created a sub context for\n                // the sub child animations\n                context.transformIntoNewTimeline(endTime);\n            }\n        }\n        context.previousNode = ast;\n    }\n    visitAnimateRef(ast, context) {\n        const innerContext = context.createSubContext(ast.options);\n        innerContext.transformIntoNewTimeline();\n        this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n        this.visitReference(ast.animation, innerContext);\n        context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n        context.previousNode = ast;\n    }\n    _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n        for (const animationRefOptions of animationsRefsOptions) {\n            const animationDelay = animationRefOptions?.delay;\n            if (animationDelay) {\n                const animationDelayValue = typeof animationDelay === 'number'\n                    ? animationDelay\n                    : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n                innerContext.delayNextStep(animationDelayValue);\n            }\n        }\n    }\n    _visitSubInstructions(instructions, context, options) {\n        const startTime = context.currentTimeline.currentTime;\n        let furthestTime = startTime;\n        // this is a special-case for when a user wants to skip a sub\n        // animation from being fired entirely.\n        const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n        const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n        if (duration !== 0) {\n            instructions.forEach((instruction) => {\n                const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n                furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n            });\n        }\n        return furthestTime;\n    }\n    visitReference(ast, context) {\n        context.updateOptions(ast.options, true);\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n    }\n    visitSequence(ast, context) {\n        const subContextCount = context.subContextCount;\n        let ctx = context;\n        const options = ast.options;\n        if (options && (options.params || options.delay)) {\n            ctx = context.createSubContext(options);\n            ctx.transformIntoNewTimeline();\n            if (options.delay != null) {\n                if (ctx.previousNode.type == AnimationMetadataType.Style) {\n                    ctx.currentTimeline.snapshotCurrentStyles();\n                    ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n                }\n                const delay = resolveTimingValue(options.delay);\n                ctx.delayNextStep(delay);\n            }\n        }\n        if (ast.steps.length) {\n            ast.steps.forEach((s) => visitDslNode(this, s, ctx));\n            // this is here just in case the inner steps only contain or end with a style() call\n            ctx.currentTimeline.applyStylesToKeyframe();\n            // this means that some animation function within the sequence\n            // ended up creating a sub timeline (which means the current\n            // timeline cannot overlap with the contents of the sequence)\n            if (ctx.subContextCount > subContextCount) {\n                ctx.transformIntoNewTimeline();\n            }\n        }\n        context.previousNode = ast;\n    }\n    visitGroup(ast, context) {\n        const innerTimelines = [];\n        let furthestTime = context.currentTimeline.currentTime;\n        const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n        ast.steps.forEach((s) => {\n            const innerContext = context.createSubContext(ast.options);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            visitDslNode(this, s, innerContext);\n            furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n            innerTimelines.push(innerContext.currentTimeline);\n        });\n        // this operation is run after the AST loop because otherwise\n        // if the parent timeline's collected styles were updated then\n        // it would pass in invalid data into the new-to-be forked items\n        innerTimelines.forEach((timeline) => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n        context.transformIntoNewTimeline(furthestTime);\n        context.previousNode = ast;\n    }\n    _visitTiming(ast, context) {\n        if (ast.dynamic) {\n            const strValue = ast.strValue;\n            const timingValue = context.params\n                ? interpolateParams(strValue, context.params, context.errors)\n                : strValue;\n            return resolveTiming(timingValue, context.errors);\n        }\n        else {\n            return { duration: ast.duration, delay: ast.delay, easing: ast.easing };\n        }\n    }\n    visitAnimate(ast, context) {\n        const timings = (context.currentAnimateTimings = this._visitTiming(ast.timings, context));\n        const timeline = context.currentTimeline;\n        if (timings.delay) {\n            context.incrementTime(timings.delay);\n            timeline.snapshotCurrentStyles();\n        }\n        const style = ast.style;\n        if (style.type == AnimationMetadataType.Keyframes) {\n            this.visitKeyframes(style, context);\n        }\n        else {\n            context.incrementTime(timings.duration);\n            this.visitStyle(style, context);\n            timeline.applyStylesToKeyframe();\n        }\n        context.currentAnimateTimings = null;\n        context.previousNode = ast;\n    }\n    visitStyle(ast, context) {\n        const timeline = context.currentTimeline;\n        const timings = context.currentAnimateTimings;\n        // this is a special case for when a style() call\n        // directly follows  an animate() call (but not inside of an animate() call)\n        if (!timings && timeline.hasCurrentStyleProperties()) {\n            timeline.forwardFrame();\n        }\n        const easing = (timings && timings.easing) || ast.easing;\n        if (ast.isEmptyStep) {\n            timeline.applyEmptyStep(easing);\n        }\n        else {\n            timeline.setStyles(ast.styles, easing, context.errors, context.options);\n        }\n        context.previousNode = ast;\n    }\n    visitKeyframes(ast, context) {\n        const currentAnimateTimings = context.currentAnimateTimings;\n        const startTime = context.currentTimeline.duration;\n        const duration = currentAnimateTimings.duration;\n        const innerContext = context.createSubContext();\n        const innerTimeline = innerContext.currentTimeline;\n        innerTimeline.easing = currentAnimateTimings.easing;\n        ast.styles.forEach((step) => {\n            const offset = step.offset || 0;\n            innerTimeline.forwardTime(offset * duration);\n            innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n            innerTimeline.applyStylesToKeyframe();\n        });\n        // this will ensure that the parent timeline gets all the styles from\n        // the child even if the new timeline below is not used\n        context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n        // we do this because the window between this timeline and the sub timeline\n        // should ensure that the styles within are exactly the same as they were before\n        context.transformIntoNewTimeline(startTime + duration);\n        context.previousNode = ast;\n    }\n    visitQuery(ast, context) {\n        // in the event that the first step before this is a style step we need\n        // to ensure the styles are applied before the children are animated\n        const startTime = context.currentTimeline.currentTime;\n        const options = (ast.options || {});\n        const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        if (delay &&\n            (context.previousNode.type === AnimationMetadataType.Style ||\n                (startTime == 0 && context.currentTimeline.hasCurrentStyleProperties()))) {\n            context.currentTimeline.snapshotCurrentStyles();\n            context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        let furthestTime = startTime;\n        const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n        context.currentQueryTotal = elms.length;\n        let sameElementTimeline = null;\n        elms.forEach((element, i) => {\n            context.currentQueryIndex = i;\n            const innerContext = context.createSubContext(ast.options, element);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            if (element === context.element) {\n                sameElementTimeline = innerContext.currentTimeline;\n            }\n            visitDslNode(this, ast.animation, innerContext);\n            // this is here just incase the inner steps only contain or end\n            // with a style() call (which is here to signal that this is a preparatory\n            // call to style an element before it is animated again)\n            innerContext.currentTimeline.applyStylesToKeyframe();\n            const endTime = innerContext.currentTimeline.currentTime;\n            furthestTime = Math.max(furthestTime, endTime);\n        });\n        context.currentQueryIndex = 0;\n        context.currentQueryTotal = 0;\n        context.transformIntoNewTimeline(furthestTime);\n        if (sameElementTimeline) {\n            context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n            context.currentTimeline.snapshotCurrentStyles();\n        }\n        context.previousNode = ast;\n    }\n    visitStagger(ast, context) {\n        const parentContext = context.parentContext;\n        const tl = context.currentTimeline;\n        const timings = ast.timings;\n        const duration = Math.abs(timings.duration);\n        const maxTime = duration * (context.currentQueryTotal - 1);\n        let delay = duration * context.currentQueryIndex;\n        let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n        switch (staggerTransformer) {\n            case 'reverse':\n                delay = maxTime - delay;\n                break;\n            case 'full':\n                delay = parentContext.currentStaggerTime;\n                break;\n        }\n        const timeline = context.currentTimeline;\n        if (delay) {\n            timeline.delayNextStep(delay);\n        }\n        const startingTime = timeline.currentTime;\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n        // time = duration + delay\n        // the reason why this computation is so complex is because\n        // the inner timeline may either have a delay value or a stretched\n        // keyframe depending on if a subtimeline is not used or is used.\n        parentContext.currentStaggerTime =\n            tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n    }\n}\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\nclass AnimationTimelineContext {\n    _driver;\n    element;\n    subInstructions;\n    _enterClassName;\n    _leaveClassName;\n    errors;\n    timelines;\n    parentContext = null;\n    currentTimeline;\n    currentAnimateTimings = null;\n    previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    subContextCount = 0;\n    options = {};\n    currentQueryIndex = 0;\n    currentQueryTotal = 0;\n    currentStaggerTime = 0;\n    constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n        this._driver = _driver;\n        this.element = element;\n        this.subInstructions = subInstructions;\n        this._enterClassName = _enterClassName;\n        this._leaveClassName = _leaveClassName;\n        this.errors = errors;\n        this.timelines = timelines;\n        this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n        timelines.push(this.currentTimeline);\n    }\n    get params() {\n        return this.options.params;\n    }\n    updateOptions(options, skipIfExists) {\n        if (!options)\n            return;\n        const newOptions = options;\n        let optionsToUpdate = this.options;\n        // NOTE: this will get patched up when other animation methods support duration overrides\n        if (newOptions.duration != null) {\n            optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n        }\n        if (newOptions.delay != null) {\n            optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n        }\n        const newParams = newOptions.params;\n        if (newParams) {\n            let paramsToUpdate = optionsToUpdate.params;\n            if (!paramsToUpdate) {\n                paramsToUpdate = this.options.params = {};\n            }\n            Object.keys(newParams).forEach((name) => {\n                if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n                    paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n                }\n            });\n        }\n    }\n    _copyOptions() {\n        const options = {};\n        if (this.options) {\n            const oldParams = this.options.params;\n            if (oldParams) {\n                const params = (options['params'] = {});\n                Object.keys(oldParams).forEach((name) => {\n                    params[name] = oldParams[name];\n                });\n            }\n        }\n        return options;\n    }\n    createSubContext(options = null, element, newTime) {\n        const target = element || this.element;\n        const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n        context.previousNode = this.previousNode;\n        context.currentAnimateTimings = this.currentAnimateTimings;\n        context.options = this._copyOptions();\n        context.updateOptions(options);\n        context.currentQueryIndex = this.currentQueryIndex;\n        context.currentQueryTotal = this.currentQueryTotal;\n        context.parentContext = this;\n        this.subContextCount++;\n        return context;\n    }\n    transformIntoNewTimeline(newTime) {\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n        this.timelines.push(this.currentTimeline);\n        return this.currentTimeline;\n    }\n    appendInstructionToTimeline(instruction, duration, delay) {\n        const updatedTimings = {\n            duration: duration != null ? duration : instruction.duration,\n            delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n            easing: '',\n        };\n        const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n        this.timelines.push(builder);\n        return updatedTimings;\n    }\n    incrementTime(time) {\n        this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n    }\n    delayNextStep(delay) {\n        // negative delays are not yet supported\n        if (delay > 0) {\n            this.currentTimeline.delayNextStep(delay);\n        }\n    }\n    invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n        let results = [];\n        if (includeSelf) {\n            results.push(this.element);\n        }\n        if (selector.length > 0) {\n            // only if :self is used then the selector can be empty\n            selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n            selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n            const multi = limit != 1;\n            let elements = this._driver.query(this.element, selector, multi);\n            if (limit !== 0) {\n                elements =\n                    limit < 0\n                        ? elements.slice(elements.length + limit, elements.length)\n                        : elements.slice(0, limit);\n            }\n            results.push(...elements);\n        }\n        if (!optional && results.length == 0) {\n            errors.push(invalidQuery(originalSelector));\n        }\n        return results;\n    }\n}\nclass TimelineBuilder {\n    _driver;\n    element;\n    startTime;\n    _elementTimelineStylesLookup;\n    duration = 0;\n    easing = null;\n    _previousKeyframe = new Map();\n    _currentKeyframe = new Map();\n    _keyframes = new Map();\n    _styleSummary = new Map();\n    _localTimelineStyles = new Map();\n    _globalTimelineStyles;\n    _pendingStyles = new Map();\n    _backFill = new Map();\n    _currentEmptyStepKeyframe = null;\n    constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n        this._driver = _driver;\n        this.element = element;\n        this.startTime = startTime;\n        this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n        if (!this._elementTimelineStylesLookup) {\n            this._elementTimelineStylesLookup = new Map();\n        }\n        this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n        if (!this._globalTimelineStyles) {\n            this._globalTimelineStyles = this._localTimelineStyles;\n            this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n        }\n        this._loadKeyframe();\n    }\n    containsAnimation() {\n        switch (this._keyframes.size) {\n            case 0:\n                return false;\n            case 1:\n                return this.hasCurrentStyleProperties();\n            default:\n                return true;\n        }\n    }\n    hasCurrentStyleProperties() {\n        return this._currentKeyframe.size > 0;\n    }\n    get currentTime() {\n        return this.startTime + this.duration;\n    }\n    delayNextStep(delay) {\n        // in the event that a style() step is placed right before a stagger()\n        // and that style() step is the very first style() value in the animation\n        // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n        // properly applies the style() values to work with the stagger...\n        const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n        if (this.duration || hasPreStyleStep) {\n            this.forwardTime(this.currentTime + delay);\n            if (hasPreStyleStep) {\n                this.snapshotCurrentStyles();\n            }\n        }\n        else {\n            this.startTime += delay;\n        }\n    }\n    fork(element, currentTime) {\n        this.applyStylesToKeyframe();\n        return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n    }\n    _loadKeyframe() {\n        if (this._currentKeyframe) {\n            this._previousKeyframe = this._currentKeyframe;\n        }\n        this._currentKeyframe = this._keyframes.get(this.duration);\n        if (!this._currentKeyframe) {\n            this._currentKeyframe = new Map();\n            this._keyframes.set(this.duration, this._currentKeyframe);\n        }\n    }\n    forwardFrame() {\n        this.duration += ONE_FRAME_IN_MILLISECONDS;\n        this._loadKeyframe();\n    }\n    forwardTime(time) {\n        this.applyStylesToKeyframe();\n        this.duration = time;\n        this._loadKeyframe();\n    }\n    _updateStyle(prop, value) {\n        this._localTimelineStyles.set(prop, value);\n        this._globalTimelineStyles.set(prop, value);\n        this._styleSummary.set(prop, { time: this.currentTime, value });\n    }\n    allowOnlyTimelineStyles() {\n        return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n    }\n    applyEmptyStep(easing) {\n        if (easing) {\n            this._previousKeyframe.set('easing', easing);\n        }\n        // special case for animate(duration):\n        // all missing styles are filled with a `*` value then\n        // if any destination styles are filled in later on the same\n        // keyframe then they will override the overridden styles\n        // We use `_globalTimelineStyles` here because there may be\n        // styles in previous keyframes that are not present in this timeline\n        for (let [prop, value] of this._globalTimelineStyles) {\n            this._backFill.set(prop, value || AUTO_STYLE);\n            this._currentKeyframe.set(prop, AUTO_STYLE);\n        }\n        this._currentEmptyStepKeyframe = this._currentKeyframe;\n    }\n    setStyles(input, easing, errors, options) {\n        if (easing) {\n            this._previousKeyframe.set('easing', easing);\n        }\n        const params = (options && options.params) || {};\n        const styles = flattenStyles(input, this._globalTimelineStyles);\n        for (let [prop, value] of styles) {\n            const val = interpolateParams(value, params, errors);\n            this._pendingStyles.set(prop, val);\n            if (!this._localTimelineStyles.has(prop)) {\n                this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n            }\n            this._updateStyle(prop, val);\n        }\n    }\n    applyStylesToKeyframe() {\n        if (this._pendingStyles.size == 0)\n            return;\n        this._pendingStyles.forEach((val, prop) => {\n            this._currentKeyframe.set(prop, val);\n        });\n        this._pendingStyles.clear();\n        this._localTimelineStyles.forEach((val, prop) => {\n            if (!this._currentKeyframe.has(prop)) {\n                this._currentKeyframe.set(prop, val);\n            }\n        });\n    }\n    snapshotCurrentStyles() {\n        for (let [prop, val] of this._localTimelineStyles) {\n            this._pendingStyles.set(prop, val);\n            this._updateStyle(prop, val);\n        }\n    }\n    getFinalKeyframe() {\n        return this._keyframes.get(this.duration);\n    }\n    get properties() {\n        const properties = [];\n        for (let prop in this._currentKeyframe) {\n            properties.push(prop);\n        }\n        return properties;\n    }\n    mergeTimelineCollectedStyles(timeline) {\n        timeline._styleSummary.forEach((details1, prop) => {\n            const details0 = this._styleSummary.get(prop);\n            if (!details0 || details1.time > details0.time) {\n                this._updateStyle(prop, details1.value);\n            }\n        });\n    }\n    buildKeyframes() {\n        this.applyStylesToKeyframe();\n        const preStyleProps = new Set();\n        const postStyleProps = new Set();\n        const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n        let finalKeyframes = [];\n        this._keyframes.forEach((keyframe, time) => {\n            const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n            finalKeyframe.forEach((value, prop) => {\n                if (value === _PRE_STYLE) {\n                    preStyleProps.add(prop);\n                }\n                else if (value === AUTO_STYLE) {\n                    postStyleProps.add(prop);\n                }\n            });\n            if (!isEmpty) {\n                finalKeyframe.set('offset', time / this.duration);\n            }\n            finalKeyframes.push(finalKeyframe);\n        });\n        const preProps = [...preStyleProps.values()];\n        const postProps = [...postStyleProps.values()];\n        // special case for a 0-second animation (which is designed just to place styles onscreen)\n        if (isEmpty) {\n            const kf0 = finalKeyframes[0];\n            const kf1 = new Map(kf0);\n            kf0.set('offset', 0);\n            kf1.set('offset', 1);\n            finalKeyframes = [kf0, kf1];\n        }\n        return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n    }\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n    keyframes;\n    preStyleProps;\n    postStyleProps;\n    _stretchStartingKeyframe;\n    timings;\n    constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n        super(driver, element, timings.delay);\n        this.keyframes = keyframes;\n        this.preStyleProps = preStyleProps;\n        this.postStyleProps = postStyleProps;\n        this._stretchStartingKeyframe = _stretchStartingKeyframe;\n        this.timings = { duration: timings.duration, delay: timings.delay, easing: timings.easing };\n    }\n    containsAnimation() {\n        return this.keyframes.length > 1;\n    }\n    buildKeyframes() {\n        let keyframes = this.keyframes;\n        let { delay, duration, easing } = this.timings;\n        if (this._stretchStartingKeyframe && delay) {\n            const newKeyframes = [];\n            const totalTime = duration + delay;\n            const startingGap = delay / totalTime;\n            // the original starting keyframe now starts once the delay is done\n            const newFirstKeyframe = new Map(keyframes[0]);\n            newFirstKeyframe.set('offset', 0);\n            newKeyframes.push(newFirstKeyframe);\n            const oldFirstKeyframe = new Map(keyframes[0]);\n            oldFirstKeyframe.set('offset', roundOffset(startingGap));\n            newKeyframes.push(oldFirstKeyframe);\n            /*\n              When the keyframe is stretched then it means that the delay before the animation\n              starts is gone. Instead the first keyframe is placed at the start of the animation\n              and it is then copied to where it starts when the original delay is over. This basically\n              means nothing animates during that delay, but the styles are still rendered. For this\n              to work the original offset values that exist in the original keyframes must be \"warped\"\n              so that they can take the new keyframe + delay into account.\n      \n              delay=1000, duration=1000, keyframes = 0 .5 1\n      \n              turns into\n      \n              delay=0, duration=2000, keyframes = 0 .33 .66 1\n             */\n            // offsets between 1 ... n -1 are all warped by the keyframe stretch\n            const limit = keyframes.length - 1;\n            for (let i = 1; i <= limit; i++) {\n                let kf = new Map(keyframes[i]);\n                const oldOffset = kf.get('offset');\n                const timeAtKeyframe = delay + oldOffset * duration;\n                kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n                newKeyframes.push(kf);\n            }\n            // the new starting keyframe should be added at the start\n            duration = totalTime;\n            delay = 0;\n            easing = '';\n            keyframes = newKeyframes;\n        }\n        return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n    }\n}\nfunction roundOffset(offset, decimalPoints = 3) {\n    const mult = Math.pow(10, decimalPoints - 1);\n    return Math.round(offset * mult) / mult;\n}\nfunction flattenStyles(input, allStyles) {\n    const styles = new Map();\n    let allProperties;\n    input.forEach((token) => {\n        if (token === '*') {\n            allProperties ??= allStyles.keys();\n            for (let prop of allProperties) {\n                styles.set(prop, AUTO_STYLE);\n            }\n        }\n        else {\n            for (let [prop, val] of token) {\n                styles.set(prop, val);\n            }\n        }\n    });\n    return styles;\n}\n\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n    return {\n        type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,\n        element,\n        triggerName,\n        isRemovalTransition,\n        fromState,\n        fromStyles,\n        toState,\n        toStyles,\n        timelines,\n        queriedElements,\n        preStyleProps,\n        postStyleProps,\n        totalTime,\n        errors,\n    };\n}\n\nconst EMPTY_OBJECT = {};\nclass AnimationTransitionFactory {\n    _triggerName;\n    ast;\n    _stateStyles;\n    constructor(_triggerName, ast, _stateStyles) {\n        this._triggerName = _triggerName;\n        this.ast = ast;\n        this._stateStyles = _stateStyles;\n    }\n    match(currentState, nextState, element, params) {\n        return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n    }\n    buildStyles(stateName, params, errors) {\n        let styler = this._stateStyles.get('*');\n        if (stateName !== undefined) {\n            styler = this._stateStyles.get(stateName?.toString()) || styler;\n        }\n        return styler ? styler.buildStyles(params, errors) : new Map();\n    }\n    build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n        const errors = [];\n        const transitionAnimationParams = (this.ast.options && this.ast.options.params) || EMPTY_OBJECT;\n        const currentAnimationParams = (currentOptions && currentOptions.params) || EMPTY_OBJECT;\n        const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n        const nextAnimationParams = (nextOptions && nextOptions.params) || EMPTY_OBJECT;\n        const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n        const queriedElements = new Set();\n        const preStyleMap = new Map();\n        const postStyleMap = new Map();\n        const isRemoval = nextState === 'void';\n        const animationOptions = {\n            params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n            delay: this.ast.options?.delay,\n        };\n        const timelines = skipAstBuild\n            ? []\n            : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n        let totalTime = 0;\n        timelines.forEach((tl) => {\n            totalTime = Math.max(tl.duration + tl.delay, totalTime);\n        });\n        if (errors.length) {\n            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n        }\n        timelines.forEach((tl) => {\n            const elm = tl.element;\n            const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n            tl.preStyleProps.forEach((prop) => preProps.add(prop));\n            const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n            tl.postStyleProps.forEach((prop) => postProps.add(prop));\n            if (elm !== element) {\n                queriedElements.add(elm);\n            }\n        });\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n        }\n        return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, [...queriedElements.values()], preStyleMap, postStyleMap, totalTime);\n    }\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n    if (!driver.validateAnimatableStyleProperty) {\n        return;\n    }\n    const allowedNonAnimatableProps = new Set([\n        // 'easing' is a utility/synthetic prop we use to represent\n        // easing functions, it represents a property of the animation\n        // which is not animatable but different values can be used\n        // in different steps\n        'easing',\n    ]);\n    const invalidNonAnimatableProps = new Set();\n    timelines.forEach(({ keyframes }) => {\n        const nonAnimatablePropsInitialValues = new Map();\n        keyframes.forEach((keyframe) => {\n            const entriesToCheck = Array.from(keyframe.entries()).filter(([prop]) => !allowedNonAnimatableProps.has(prop));\n            for (const [prop, value] of entriesToCheck) {\n                if (!driver.validateAnimatableStyleProperty(prop)) {\n                    if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n                        const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n                        if (propInitialValue !== value) {\n                            invalidNonAnimatableProps.add(prop);\n                        }\n                    }\n                    else {\n                        nonAnimatablePropsInitialValues.set(prop, value);\n                    }\n                }\n            }\n        });\n    });\n    if (invalidNonAnimatableProps.size > 0) {\n        console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` +\n            ' not animatable properties: ' +\n            Array.from(invalidNonAnimatableProps).join(', ') +\n            '\\n' +\n            '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n    }\n}\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n    return matchFns.some((fn) => fn(currentState, nextState, element, params));\n}\nfunction applyParamDefaults(userParams, defaults) {\n    const result = { ...defaults };\n    Object.entries(userParams).forEach(([key, value]) => {\n        if (value != null) {\n            result[key] = value;\n        }\n    });\n    return result;\n}\nclass AnimationStateStyles {\n    styles;\n    defaultParams;\n    normalizer;\n    constructor(styles, defaultParams, normalizer) {\n        this.styles = styles;\n        this.defaultParams = defaultParams;\n        this.normalizer = normalizer;\n    }\n    buildStyles(params, errors) {\n        const finalStyles = new Map();\n        const combinedParams = applyParamDefaults(params, this.defaultParams);\n        this.styles.styles.forEach((value) => {\n            if (typeof value !== 'string') {\n                value.forEach((val, prop) => {\n                    if (val) {\n                        val = interpolateParams(val, combinedParams, errors);\n                    }\n                    const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n                    val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n                    finalStyles.set(prop, val);\n                });\n            }\n        });\n        return finalStyles;\n    }\n}\n\nfunction buildTrigger(name, ast, normalizer) {\n    return new AnimationTrigger(name, ast, normalizer);\n}\nclass AnimationTrigger {\n    name;\n    ast;\n    _normalizer;\n    transitionFactories = [];\n    fallbackTransition;\n    states = new Map();\n    constructor(name, ast, _normalizer) {\n        this.name = name;\n        this.ast = ast;\n        this._normalizer = _normalizer;\n        ast.states.forEach((ast) => {\n            const defaultParams = (ast.options && ast.options.params) || {};\n            this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n        });\n        balanceProperties(this.states, 'true', '1');\n        balanceProperties(this.states, 'false', '0');\n        ast.transitions.forEach((ast) => {\n            this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n        });\n        this.fallbackTransition = createFallbackTransition(name, this.states);\n    }\n    get containsQueries() {\n        return this.ast.queryCount > 0;\n    }\n    matchTransition(currentState, nextState, element, params) {\n        const entry = this.transitionFactories.find((f) => f.match(currentState, nextState, element, params));\n        return entry || null;\n    }\n    matchStyles(currentState, params, errors) {\n        return this.fallbackTransition.buildStyles(currentState, params, errors);\n    }\n}\nfunction createFallbackTransition(triggerName, states, normalizer) {\n    const matchers = [(fromState, toState) => true];\n    const animation = { type: AnimationMetadataType.Sequence, steps: [], options: null };\n    const transition = {\n        type: AnimationMetadataType.Transition,\n        animation,\n        matchers,\n        options: null,\n        queryCount: 0,\n        depCount: 0,\n    };\n    return new AnimationTransitionFactory(triggerName, transition, states);\n}\nfunction balanceProperties(stateMap, key1, key2) {\n    if (stateMap.has(key1)) {\n        if (!stateMap.has(key2)) {\n            stateMap.set(key2, stateMap.get(key1));\n        }\n    }\n    else if (stateMap.has(key2)) {\n        stateMap.set(key1, stateMap.get(key2));\n    }\n}\n\nconst EMPTY_INSTRUCTION_MAP = /* @__PURE__ */ new ElementInstructionMap();\nclass TimelineAnimationEngine {\n    bodyNode;\n    _driver;\n    _normalizer;\n    _animations = new Map();\n    _playersById = new Map();\n    players = [];\n    constructor(bodyNode, _driver, _normalizer) {\n        this.bodyNode = bodyNode;\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n    }\n    register(id, metadata) {\n        const errors = [];\n        const warnings = [];\n        const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n        if (errors.length) {\n            throw registerFailed(errors);\n        }\n        else {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                if (warnings.length) {\n                    warnRegister(warnings);\n                }\n            }\n            this._animations.set(id, ast);\n        }\n    }\n    _buildPlayer(i, preStyles, postStyles) {\n        const element = i.element;\n        const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n        return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n    }\n    create(id, element, options = {}) {\n        const errors = [];\n        const ast = this._animations.get(id);\n        let instructions;\n        const autoStylesMap = new Map();\n        if (ast) {\n            instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n            instructions.forEach((inst) => {\n                const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n                inst.postStyleProps.forEach((prop) => styles.set(prop, null));\n            });\n        }\n        else {\n            errors.push(missingOrDestroyedAnimation());\n            instructions = [];\n        }\n        if (errors.length) {\n            throw createAnimationFailed(errors);\n        }\n        autoStylesMap.forEach((styles, element) => {\n            styles.forEach((_, prop) => {\n                styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n            });\n        });\n        const players = instructions.map((i) => {\n            const styles = autoStylesMap.get(i.element);\n            return this._buildPlayer(i, new Map(), styles);\n        });\n        const player = optimizeGroupPlayer(players);\n        this._playersById.set(id, player);\n        player.onDestroy(() => this.destroy(id));\n        this.players.push(player);\n        return player;\n    }\n    destroy(id) {\n        const player = this._getPlayer(id);\n        player.destroy();\n        this._playersById.delete(id);\n        const index = this.players.indexOf(player);\n        if (index >= 0) {\n            this.players.splice(index, 1);\n        }\n    }\n    _getPlayer(id) {\n        const player = this._playersById.get(id);\n        if (!player) {\n            throw missingPlayer(id);\n        }\n        return player;\n    }\n    listen(id, element, eventName, callback) {\n        // triggerName, fromState, toState are all ignored for timeline animations\n        const baseEvent = makeAnimationEvent(element, '', '', '');\n        listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n        return () => { };\n    }\n    command(id, element, command, args) {\n        if (command == 'register') {\n            this.register(id, args[0]);\n            return;\n        }\n        if (command == 'create') {\n            const options = (args[0] || {});\n            this.create(id, element, options);\n            return;\n        }\n        const player = this._getPlayer(id);\n        switch (command) {\n            case 'play':\n                player.play();\n                break;\n            case 'pause':\n                player.pause();\n                break;\n            case 'reset':\n                player.reset();\n                break;\n            case 'restart':\n                player.restart();\n                break;\n            case 'finish':\n                player.finish();\n                break;\n            case 'init':\n                player.init();\n                break;\n            case 'setPosition':\n                player.setPosition(parseFloat(args[0]));\n                break;\n            case 'destroy':\n                this.destroy(id);\n                break;\n        }\n    }\n}\n\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n    namespaceId: '',\n    setForRemoval: false,\n    setForMove: false,\n    hasAnimation: false,\n    removedBeforeQueried: false,\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n    namespaceId: '',\n    setForMove: false,\n    setForRemoval: false,\n    hasAnimation: false,\n    removedBeforeQueried: true,\n};\nconst REMOVAL_FLAG = '__ng_removed';\nclass StateValue {\n    namespaceId;\n    value;\n    options;\n    get params() {\n        return this.options.params;\n    }\n    constructor(input, namespaceId = '') {\n        this.namespaceId = namespaceId;\n        const isObj = input && input.hasOwnProperty('value');\n        const value = isObj ? input['value'] : input;\n        this.value = normalizeTriggerValue(value);\n        if (isObj) {\n            // we drop the value property from options.\n            const { value, ...options } = input;\n            this.options = options;\n        }\n        else {\n            this.options = {};\n        }\n        if (!this.options.params) {\n            this.options.params = {};\n        }\n    }\n    absorbOptions(options) {\n        const newParams = options.params;\n        if (newParams) {\n            const oldParams = this.options.params;\n            Object.keys(newParams).forEach((prop) => {\n                if (oldParams[prop] == null) {\n                    oldParams[prop] = newParams[prop];\n                }\n            });\n        }\n    }\n}\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /* @__PURE__ */ new StateValue(VOID_VALUE);\nclass AnimationTransitionNamespace {\n    id;\n    hostElement;\n    _engine;\n    players = [];\n    _triggers = new Map();\n    _queue = [];\n    _elementListeners = new Map();\n    _hostClassName;\n    constructor(id, hostElement, _engine) {\n        this.id = id;\n        this.hostElement = hostElement;\n        this._engine = _engine;\n        this._hostClassName = 'ng-tns-' + id;\n        addClass(hostElement, this._hostClassName);\n    }\n    listen(element, name, phase, callback) {\n        if (!this._triggers.has(name)) {\n            throw missingTrigger(phase, name);\n        }\n        if (phase == null || phase.length == 0) {\n            throw missingEvent(name);\n        }\n        if (!isTriggerEventValid(phase)) {\n            throw unsupportedTriggerEvent(phase, name);\n        }\n        const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n        const data = { name, phase, callback };\n        listeners.push(data);\n        const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n        if (!triggersWithStates.has(name)) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n            triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n        }\n        return () => {\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            this._engine.afterFlush(() => {\n                const index = listeners.indexOf(data);\n                if (index >= 0) {\n                    listeners.splice(index, 1);\n                }\n                if (!this._triggers.has(name)) {\n                    triggersWithStates.delete(name);\n                }\n            });\n        };\n    }\n    register(name, ast) {\n        if (this._triggers.has(name)) {\n            // throw\n            return false;\n        }\n        else {\n            this._triggers.set(name, ast);\n            return true;\n        }\n    }\n    _getTrigger(name) {\n        const trigger = this._triggers.get(name);\n        if (!trigger) {\n            throw unregisteredTrigger(name);\n        }\n        return trigger;\n    }\n    trigger(element, triggerName, value, defaultToFallback = true) {\n        const trigger = this._getTrigger(triggerName);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        let triggersWithStates = this._engine.statesByElement.get(element);\n        if (!triggersWithStates) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n            this._engine.statesByElement.set(element, (triggersWithStates = new Map()));\n        }\n        let fromState = triggersWithStates.get(triggerName);\n        const toState = new StateValue(value, this.id);\n        const isObj = value && value.hasOwnProperty('value');\n        if (!isObj && fromState) {\n            toState.absorbOptions(fromState.options);\n        }\n        triggersWithStates.set(triggerName, toState);\n        if (!fromState) {\n            fromState = DEFAULT_STATE_VALUE;\n        }\n        const isRemoval = toState.value === VOID_VALUE;\n        // normally this isn't reached by here, however, if an object expression\n        // is passed in then it may be a new object each time. Comparing the value\n        // is important since that will stay the same despite there being a new object.\n        // The removal arc here is special cased because the same element is triggered\n        // twice in the event that it contains animations on the outer/inner portions\n        // of the host container\n        if (!isRemoval && fromState.value === toState.value) {\n            // this means that despite the value not changing, some inner params\n            // have changed which means that the animation final styles need to be applied\n            if (!objEquals(fromState.params, toState.params)) {\n                const errors = [];\n                const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n                const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n                if (errors.length) {\n                    this._engine.reportError(errors);\n                }\n                else {\n                    this._engine.afterFlush(() => {\n                        eraseStyles(element, fromStyles);\n                        setStyles(element, toStyles);\n                    });\n                }\n            }\n            return;\n        }\n        const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n        playersOnElement.forEach((player) => {\n            // only remove the player if it is queued on the EXACT same trigger/namespace\n            // we only also deal with queued players here because if the animation has\n            // started then we want to keep the player alive until the flush happens\n            // (which is where the previousPlayers are passed into the new player)\n            if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n                player.destroy();\n            }\n        });\n        let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n        let isFallbackTransition = false;\n        if (!transition) {\n            if (!defaultToFallback)\n                return;\n            transition = trigger.fallbackTransition;\n            isFallbackTransition = true;\n        }\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n            element,\n            triggerName,\n            transition,\n            fromState,\n            toState,\n            player,\n            isFallbackTransition,\n        });\n        if (!isFallbackTransition) {\n            addClass(element, QUEUED_CLASSNAME);\n            player.onStart(() => {\n                removeClass(element, QUEUED_CLASSNAME);\n            });\n        }\n        player.onDone(() => {\n            let index = this.players.indexOf(player);\n            if (index >= 0) {\n                this.players.splice(index, 1);\n            }\n            const players = this._engine.playersByElement.get(element);\n            if (players) {\n                let index = players.indexOf(player);\n                if (index >= 0) {\n                    players.splice(index, 1);\n                }\n            }\n        });\n        this.players.push(player);\n        playersOnElement.push(player);\n        return player;\n    }\n    deregister(name) {\n        this._triggers.delete(name);\n        this._engine.statesByElement.forEach((stateMap) => stateMap.delete(name));\n        this._elementListeners.forEach((listeners, element) => {\n            this._elementListeners.set(element, listeners.filter((entry) => {\n                return entry.name != name;\n            }));\n        });\n    }\n    clearElementCache(element) {\n        this._engine.statesByElement.delete(element);\n        this._elementListeners.delete(element);\n        const elementPlayers = this._engine.playersByElement.get(element);\n        if (elementPlayers) {\n            elementPlayers.forEach((player) => player.destroy());\n            this._engine.playersByElement.delete(element);\n        }\n    }\n    _signalRemovalForInnerTriggers(rootElement, context) {\n        const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n        // emulate a leave animation for all inner nodes within this node.\n        // If there are no animations found for any of the nodes then clear the cache\n        // for the element.\n        elements.forEach((elm) => {\n            // this means that an inner remove() operation has already kicked off\n            // the animation on this element...\n            if (elm[REMOVAL_FLAG])\n                return;\n            const namespaces = this._engine.fetchNamespacesByElement(elm);\n            if (namespaces.size) {\n                namespaces.forEach((ns) => ns.triggerLeaveAnimation(elm, context, false, true));\n            }\n            else {\n                this.clearElementCache(elm);\n            }\n        });\n        // If the child elements were removed along with the parent, their animations might not\n        // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n        this._engine.afterFlushAnimationsDone(() => elements.forEach((elm) => this.clearElementCache(elm)));\n    }\n    triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n        const triggerStates = this._engine.statesByElement.get(element);\n        const previousTriggersValues = new Map();\n        if (triggerStates) {\n            const players = [];\n            triggerStates.forEach((state, triggerName) => {\n                previousTriggersValues.set(triggerName, state.value);\n                // this check is here in the event that an element is removed\n                // twice (both on the host level and the component level)\n                if (this._triggers.has(triggerName)) {\n                    const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n                    if (player) {\n                        players.push(player);\n                    }\n                }\n            });\n            if (players.length) {\n                this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n                if (destroyAfterComplete) {\n                    optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n                }\n                return true;\n            }\n        }\n        return false;\n    }\n    prepareLeaveAnimationListeners(element) {\n        const listeners = this._elementListeners.get(element);\n        const elementStates = this._engine.statesByElement.get(element);\n        // if this statement fails then it means that the element was picked up\n        // by an earlier flush (or there are no listeners at all to track the leave).\n        if (listeners && elementStates) {\n            const visitedTriggers = new Set();\n            listeners.forEach((listener) => {\n                const triggerName = listener.name;\n                if (visitedTriggers.has(triggerName))\n                    return;\n                visitedTriggers.add(triggerName);\n                const trigger = this._triggers.get(triggerName);\n                const transition = trigger.fallbackTransition;\n                const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n                const toState = new StateValue(VOID_VALUE);\n                const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n                this._engine.totalQueuedPlayers++;\n                this._queue.push({\n                    element,\n                    triggerName,\n                    transition,\n                    fromState,\n                    toState,\n                    player,\n                    isFallbackTransition: true,\n                });\n            });\n        }\n    }\n    removeNode(element, context) {\n        const engine = this._engine;\n        if (element.childElementCount) {\n            this._signalRemovalForInnerTriggers(element, context);\n        }\n        // this means that a * => VOID animation was detected and kicked off\n        if (this.triggerLeaveAnimation(element, context, true))\n            return;\n        // find the player that is animating and make sure that the\n        // removal is delayed until that player has completed\n        let containsPotentialParentTransition = false;\n        if (engine.totalAnimations) {\n            const currentPlayers = engine.players.length\n                ? engine.playersByQueriedElement.get(element)\n                : [];\n            // when this `if statement` does not continue forward it means that\n            // a previous animation query has selected the current element and\n            // is animating it. In this situation want to continue forwards and\n            // allow the element to be queued up for animation later.\n            if (currentPlayers && currentPlayers.length) {\n                containsPotentialParentTransition = true;\n            }\n            else {\n                let parent = element;\n                while ((parent = parent.parentNode)) {\n                    const triggers = engine.statesByElement.get(parent);\n                    if (triggers) {\n                        containsPotentialParentTransition = true;\n                        break;\n                    }\n                }\n            }\n        }\n        // at this stage we know that the element will either get removed\n        // during flush or will be picked up by a parent query. Either way\n        // we need to fire the listeners for this element when it DOES get\n        // removed (once the query parent animation is done or after flush)\n        this.prepareLeaveAnimationListeners(element);\n        // whether or not a parent has an animation we need to delay the deferral of the leave\n        // operation until we have more information (which we do after flush() has been called)\n        if (containsPotentialParentTransition) {\n            engine.markElementAsRemoved(this.id, element, false, context);\n        }\n        else {\n            const removalFlag = element[REMOVAL_FLAG];\n            if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n                // we do this after the flush has occurred such\n                // that the callbacks can be fired\n                engine.afterFlush(() => this.clearElementCache(element));\n                engine.destroyInnerAnimations(element);\n                engine._onRemovalComplete(element, context);\n            }\n        }\n    }\n    insertNode(element, parent) {\n        addClass(element, this._hostClassName);\n    }\n    drainQueuedTransitions(microtaskId) {\n        const instructions = [];\n        this._queue.forEach((entry) => {\n            const player = entry.player;\n            if (player.destroyed)\n                return;\n            const element = entry.element;\n            const listeners = this._elementListeners.get(element);\n            if (listeners) {\n                listeners.forEach((listener) => {\n                    if (listener.name == entry.triggerName) {\n                        const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n                        baseEvent['_data'] = microtaskId;\n                        listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n                    }\n                });\n            }\n            if (player.markedForDestroy) {\n                this._engine.afterFlush(() => {\n                    // now we can destroy the element properly since the event listeners have\n                    // been bound to the player\n                    player.destroy();\n                });\n            }\n            else {\n                instructions.push(entry);\n            }\n        });\n        this._queue = [];\n        return instructions.sort((a, b) => {\n            // if depCount == 0 them move to front\n            // otherwise if a contains b then move back\n            const d0 = a.transition.ast.depCount;\n            const d1 = b.transition.ast.depCount;\n            if (d0 == 0 || d1 == 0) {\n                return d0 - d1;\n            }\n            return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n        });\n    }\n    destroy(context) {\n        this.players.forEach((p) => p.destroy());\n        this._signalRemovalForInnerTriggers(this.hostElement, context);\n    }\n}\nclass TransitionAnimationEngine {\n    bodyNode;\n    driver;\n    _normalizer;\n    players = [];\n    newHostElements = new Map();\n    playersByElement = new Map();\n    playersByQueriedElement = new Map();\n    statesByElement = new Map();\n    disabledNodes = new Set();\n    totalAnimations = 0;\n    totalQueuedPlayers = 0;\n    _namespaceLookup = {};\n    _namespaceList = [];\n    _flushFns = [];\n    _whenQuietFns = [];\n    namespacesByHostElement = new Map();\n    collectedEnterElements = [];\n    collectedLeaveElements = [];\n    // this method is designed to be overridden by the code that uses this engine\n    onRemovalComplete = (element, context) => { };\n    /** @internal */\n    _onRemovalComplete(element, context) {\n        this.onRemovalComplete(element, context);\n    }\n    constructor(bodyNode, driver, _normalizer) {\n        this.bodyNode = bodyNode;\n        this.driver = driver;\n        this._normalizer = _normalizer;\n    }\n    get queuedPlayers() {\n        const players = [];\n        this._namespaceList.forEach((ns) => {\n            ns.players.forEach((player) => {\n                if (player.queued) {\n                    players.push(player);\n                }\n            });\n        });\n        return players;\n    }\n    createNamespace(namespaceId, hostElement) {\n        const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n        if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n            this._balanceNamespaceList(ns, hostElement);\n        }\n        else {\n            // defer this later until flush during when the host element has\n            // been inserted so that we know exactly where to place it in\n            // the namespace list\n            this.newHostElements.set(hostElement, ns);\n            // given that this host element is a part of the animation code, it\n            // may or may not be inserted by a parent node that is of an\n            // animation renderer type. If this happens then we can still have\n            // access to this item when we query for :enter nodes. If the parent\n            // is a renderer then the set data-structure will normalize the entry\n            this.collectEnterElement(hostElement);\n        }\n        return (this._namespaceLookup[namespaceId] = ns);\n    }\n    _balanceNamespaceList(ns, hostElement) {\n        const namespaceList = this._namespaceList;\n        const namespacesByHostElement = this.namespacesByHostElement;\n        const limit = namespaceList.length - 1;\n        if (limit >= 0) {\n            let found = false;\n            // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n            // establishing a top-down ordering of namespaces in `this._namespaceList`.\n            let ancestor = this.driver.getParentElement(hostElement);\n            while (ancestor) {\n                const ancestorNs = namespacesByHostElement.get(ancestor);\n                if (ancestorNs) {\n                    // An animation namespace has been registered for this ancestor, so we insert `ns`\n                    // right after it to establish top-down ordering of animation namespaces.\n                    const index = namespaceList.indexOf(ancestorNs);\n                    namespaceList.splice(index + 1, 0, ns);\n                    found = true;\n                    break;\n                }\n                ancestor = this.driver.getParentElement(ancestor);\n            }\n            if (!found) {\n                // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n                // ensure that any existing descendants are ordered after `ns`, retaining the desired\n                // top-down ordering.\n                namespaceList.unshift(ns);\n            }\n        }\n        else {\n            namespaceList.push(ns);\n        }\n        namespacesByHostElement.set(hostElement, ns);\n        return ns;\n    }\n    register(namespaceId, hostElement) {\n        let ns = this._namespaceLookup[namespaceId];\n        if (!ns) {\n            ns = this.createNamespace(namespaceId, hostElement);\n        }\n        return ns;\n    }\n    registerTrigger(namespaceId, name, trigger) {\n        let ns = this._namespaceLookup[namespaceId];\n        if (ns && ns.register(name, trigger)) {\n            this.totalAnimations++;\n        }\n    }\n    destroy(namespaceId, context) {\n        if (!namespaceId)\n            return;\n        this.afterFlush(() => { });\n        this.afterFlushAnimationsDone(() => {\n            const ns = this._fetchNamespace(namespaceId);\n            this.namespacesByHostElement.delete(ns.hostElement);\n            const index = this._namespaceList.indexOf(ns);\n            if (index >= 0) {\n                this._namespaceList.splice(index, 1);\n            }\n            ns.destroy(context);\n            delete this._namespaceLookup[namespaceId];\n        });\n    }\n    _fetchNamespace(id) {\n        return this._namespaceLookup[id];\n    }\n    fetchNamespacesByElement(element) {\n        // normally there should only be one namespace per element, however\n        // if @triggers are placed on both the component element and then\n        // its host element (within the component code) then there will be\n        // two namespaces returned. We use a set here to simply deduplicate\n        // the namespaces in case (for the reason described above) there are multiple triggers\n        const namespaces = new Set();\n        const elementStates = this.statesByElement.get(element);\n        if (elementStates) {\n            for (let stateValue of elementStates.values()) {\n                if (stateValue.namespaceId) {\n                    const ns = this._fetchNamespace(stateValue.namespaceId);\n                    if (ns) {\n                        namespaces.add(ns);\n                    }\n                }\n            }\n        }\n        return namespaces;\n    }\n    trigger(namespaceId, element, name, value) {\n        if (isElementNode(element)) {\n            const ns = this._fetchNamespace(namespaceId);\n            if (ns) {\n                ns.trigger(element, name, value);\n                return true;\n            }\n        }\n        return false;\n    }\n    insertNode(namespaceId, element, parent, insertBefore) {\n        if (!isElementNode(element))\n            return;\n        // special case for when an element is removed and reinserted (move operation)\n        // when this occurs we do not want to use the element for deletion later\n        const details = element[REMOVAL_FLAG];\n        if (details && details.setForRemoval) {\n            details.setForRemoval = false;\n            details.setForMove = true;\n            const index = this.collectedLeaveElements.indexOf(element);\n            if (index >= 0) {\n                this.collectedLeaveElements.splice(index, 1);\n            }\n        }\n        // in the event that the namespaceId is blank then the caller\n        // code does not contain any animation code in it, but it is\n        // just being called so that the node is marked as being inserted\n        if (namespaceId) {\n            const ns = this._fetchNamespace(namespaceId);\n            // This if-statement is a workaround for router issue #21947.\n            // The router sometimes hits a race condition where while a route\n            // is being instantiated a new navigation arrives, triggering leave\n            // animation of DOM that has not been fully initialized, until this\n            // is resolved, we need to handle the scenario when DOM is not in a\n            // consistent state during the animation.\n            if (ns) {\n                ns.insertNode(element, parent);\n            }\n        }\n        // only *directives and host elements are inserted before\n        if (insertBefore) {\n            this.collectEnterElement(element);\n        }\n    }\n    collectEnterElement(element) {\n        this.collectedEnterElements.push(element);\n    }\n    markElementAsDisabled(element, value) {\n        if (value) {\n            if (!this.disabledNodes.has(element)) {\n                this.disabledNodes.add(element);\n                addClass(element, DISABLED_CLASSNAME);\n            }\n        }\n        else if (this.disabledNodes.has(element)) {\n            this.disabledNodes.delete(element);\n            removeClass(element, DISABLED_CLASSNAME);\n        }\n    }\n    removeNode(namespaceId, element, context) {\n        if (isElementNode(element)) {\n            const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n            if (ns) {\n                ns.removeNode(element, context);\n            }\n            else {\n                this.markElementAsRemoved(namespaceId, element, false, context);\n            }\n            const hostNS = this.namespacesByHostElement.get(element);\n            if (hostNS && hostNS.id !== namespaceId) {\n                hostNS.removeNode(element, context);\n            }\n        }\n        else {\n            this._onRemovalComplete(element, context);\n        }\n    }\n    markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n        this.collectedLeaveElements.push(element);\n        element[REMOVAL_FLAG] = {\n            namespaceId,\n            setForRemoval: context,\n            hasAnimation,\n            removedBeforeQueried: false,\n            previousTriggersValues,\n        };\n    }\n    listen(namespaceId, element, name, phase, callback) {\n        if (isElementNode(element)) {\n            return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n        }\n        return () => { };\n    }\n    _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n        return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n    }\n    destroyInnerAnimations(containerElement) {\n        let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n        elements.forEach((element) => this.destroyActiveAnimationsForElement(element));\n        if (this.playersByQueriedElement.size == 0)\n            return;\n        elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n        elements.forEach((element) => this.finishActiveQueriedAnimationOnElement(element));\n    }\n    destroyActiveAnimationsForElement(element) {\n        const players = this.playersByElement.get(element);\n        if (players) {\n            players.forEach((player) => {\n                // special case for when an element is set for destruction, but hasn't started.\n                // in this situation we want to delay the destruction until the flush occurs\n                // so that any event listeners attached to the player are triggered.\n                if (player.queued) {\n                    player.markedForDestroy = true;\n                }\n                else {\n                    player.destroy();\n                }\n            });\n        }\n    }\n    finishActiveQueriedAnimationOnElement(element) {\n        const players = this.playersByQueriedElement.get(element);\n        if (players) {\n            players.forEach((player) => player.finish());\n        }\n    }\n    whenRenderingDone() {\n        return new Promise((resolve) => {\n            if (this.players.length) {\n                return optimizeGroupPlayer(this.players).onDone(() => resolve());\n            }\n            else {\n                resolve();\n            }\n        });\n    }\n    processLeaveNode(element) {\n        const details = element[REMOVAL_FLAG];\n        if (details && details.setForRemoval) {\n            // this will prevent it from removing it twice\n            element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n            if (details.namespaceId) {\n                this.destroyInnerAnimations(element);\n                const ns = this._fetchNamespace(details.namespaceId);\n                if (ns) {\n                    ns.clearElementCache(element);\n                }\n            }\n            this._onRemovalComplete(element, details.setForRemoval);\n        }\n        if (element.classList?.contains(DISABLED_CLASSNAME)) {\n            this.markElementAsDisabled(element, false);\n        }\n        this.driver.query(element, DISABLED_SELECTOR, true).forEach((node) => {\n            this.markElementAsDisabled(node, false);\n        });\n    }\n    flush(microtaskId = -1) {\n        let players = [];\n        if (this.newHostElements.size) {\n            this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n            this.newHostElements.clear();\n        }\n        if (this.totalAnimations && this.collectedEnterElements.length) {\n            for (let i = 0; i < this.collectedEnterElements.length; i++) {\n                const elm = this.collectedEnterElements[i];\n                addClass(elm, STAR_CLASSNAME);\n            }\n        }\n        if (this._namespaceList.length &&\n            (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n            const cleanupFns = [];\n            try {\n                players = this._flushAnimations(cleanupFns, microtaskId);\n            }\n            finally {\n                for (let i = 0; i < cleanupFns.length; i++) {\n                    cleanupFns[i]();\n                }\n            }\n        }\n        else {\n            for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n                const element = this.collectedLeaveElements[i];\n                this.processLeaveNode(element);\n            }\n        }\n        this.totalQueuedPlayers = 0;\n        this.collectedEnterElements.length = 0;\n        this.collectedLeaveElements.length = 0;\n        this._flushFns.forEach((fn) => fn());\n        this._flushFns = [];\n        if (this._whenQuietFns.length) {\n            // we move these over to a variable so that\n            // if any new callbacks are registered in another\n            // flush they do not populate the existing set\n            const quietFns = this._whenQuietFns;\n            this._whenQuietFns = [];\n            if (players.length) {\n                optimizeGroupPlayer(players).onDone(() => {\n                    quietFns.forEach((fn) => fn());\n                });\n            }\n            else {\n                quietFns.forEach((fn) => fn());\n            }\n        }\n    }\n    reportError(errors) {\n        throw triggerTransitionsFailed(errors);\n    }\n    _flushAnimations(cleanupFns, microtaskId) {\n        const subTimelines = new ElementInstructionMap();\n        const skippedPlayers = [];\n        const skippedPlayersMap = new Map();\n        const queuedInstructions = [];\n        const queriedElements = new Map();\n        const allPreStyleElements = new Map();\n        const allPostStyleElements = new Map();\n        const disabledElementsSet = new Set();\n        this.disabledNodes.forEach((node) => {\n            disabledElementsSet.add(node);\n            const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n            for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n                disabledElementsSet.add(nodesThatAreDisabled[i]);\n            }\n        });\n        const bodyNode = this.bodyNode;\n        const allTriggerElements = Array.from(this.statesByElement.keys());\n        const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n        // this must occur before the instructions are built below such that\n        // the :enter queries match the elements (since the timeline queries\n        // are fired during instruction building).\n        const enterNodeMapIds = new Map();\n        let i = 0;\n        enterNodeMap.forEach((nodes, root) => {\n            const className = ENTER_CLASSNAME + i++;\n            enterNodeMapIds.set(root, className);\n            nodes.forEach((node) => addClass(node, className));\n        });\n        const allLeaveNodes = [];\n        const mergedLeaveNodes = new Set();\n        const leaveNodesWithoutAnimations = new Set();\n        for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n            const element = this.collectedLeaveElements[i];\n            const details = element[REMOVAL_FLAG];\n            if (details && details.setForRemoval) {\n                allLeaveNodes.push(element);\n                mergedLeaveNodes.add(element);\n                if (details.hasAnimation) {\n                    this.driver\n                        .query(element, STAR_SELECTOR, true)\n                        .forEach((elm) => mergedLeaveNodes.add(elm));\n                }\n                else {\n                    leaveNodesWithoutAnimations.add(element);\n                }\n            }\n        }\n        const leaveNodeMapIds = new Map();\n        const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n        leaveNodeMap.forEach((nodes, root) => {\n            const className = LEAVE_CLASSNAME + i++;\n            leaveNodeMapIds.set(root, className);\n            nodes.forEach((node) => addClass(node, className));\n        });\n        cleanupFns.push(() => {\n            enterNodeMap.forEach((nodes, root) => {\n                const className = enterNodeMapIds.get(root);\n                nodes.forEach((node) => removeClass(node, className));\n            });\n            leaveNodeMap.forEach((nodes, root) => {\n                const className = leaveNodeMapIds.get(root);\n                nodes.forEach((node) => removeClass(node, className));\n            });\n            allLeaveNodes.forEach((element) => {\n                this.processLeaveNode(element);\n            });\n        });\n        const allPlayers = [];\n        const erroneousTransitions = [];\n        for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n            const ns = this._namespaceList[i];\n            ns.drainQueuedTransitions(microtaskId).forEach((entry) => {\n                const player = entry.player;\n                const element = entry.element;\n                allPlayers.push(player);\n                if (this.collectedEnterElements.length) {\n                    const details = element[REMOVAL_FLAG];\n                    // animations for move operations (elements being removed and reinserted,\n                    // e.g. when the order of an *ngFor list changes) are currently not supported\n                    if (details && details.setForMove) {\n                        if (details.previousTriggersValues &&\n                            details.previousTriggersValues.has(entry.triggerName)) {\n                            const previousValue = details.previousTriggersValues.get(entry.triggerName);\n                            // we need to restore the previous trigger value since the element has\n                            // only been moved and hasn't actually left the DOM\n                            const triggersWithStates = this.statesByElement.get(entry.element);\n                            if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                                const state = triggersWithStates.get(entry.triggerName);\n                                state.value = previousValue;\n                                triggersWithStates.set(entry.triggerName, state);\n                            }\n                        }\n                        player.destroy();\n                        return;\n                    }\n                }\n                const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n                const leaveClassName = leaveNodeMapIds.get(element);\n                const enterClassName = enterNodeMapIds.get(element);\n                const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n                if (instruction.errors && instruction.errors.length) {\n                    erroneousTransitions.push(instruction);\n                    return;\n                }\n                // even though the element may not be in the DOM, it may still\n                // be added at a later point (due to the mechanics of content\n                // projection and/or dynamic component insertion) therefore it's\n                // important to still style the element.\n                if (nodeIsOrphaned) {\n                    player.onStart(() => eraseStyles(element, instruction.fromStyles));\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // if an unmatched transition is queued and ready to go\n                // then it SHOULD NOT render an animation and cancel the\n                // previously running animations.\n                if (entry.isFallbackTransition) {\n                    player.onStart(() => eraseStyles(element, instruction.fromStyles));\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this means that if a parent animation uses this animation as a sub-trigger\n                // then it will instruct the timeline builder not to add a player delay, but\n                // instead stretch the first keyframe gap until the animation starts. This is\n                // important in order to prevent extra initialization styles from being\n                // required by the user for the animation.\n                const timelines = [];\n                instruction.timelines.forEach((tl) => {\n                    tl.stretchStartingKeyframe = true;\n                    if (!this.disabledNodes.has(tl.element)) {\n                        timelines.push(tl);\n                    }\n                });\n                instruction.timelines = timelines;\n                subTimelines.append(element, instruction.timelines);\n                const tuple = { instruction, player, element };\n                queuedInstructions.push(tuple);\n                instruction.queriedElements.forEach((element) => getOrSetDefaultValue(queriedElements, element, []).push(player));\n                instruction.preStyleProps.forEach((stringMap, element) => {\n                    if (stringMap.size) {\n                        let setVal = allPreStyleElements.get(element);\n                        if (!setVal) {\n                            allPreStyleElements.set(element, (setVal = new Set()));\n                        }\n                        stringMap.forEach((_, prop) => setVal.add(prop));\n                    }\n                });\n                instruction.postStyleProps.forEach((stringMap, element) => {\n                    let setVal = allPostStyleElements.get(element);\n                    if (!setVal) {\n                        allPostStyleElements.set(element, (setVal = new Set()));\n                    }\n                    stringMap.forEach((_, prop) => setVal.add(prop));\n                });\n            });\n        }\n        if (erroneousTransitions.length) {\n            const errors = [];\n            erroneousTransitions.forEach((instruction) => {\n                errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n            });\n            allPlayers.forEach((player) => player.destroy());\n            this.reportError(errors);\n        }\n        const allPreviousPlayersMap = new Map();\n        // this map tells us which element in the DOM tree is contained by\n        // which animation. Further down this map will get populated once\n        // the players are built and in doing so we can use it to efficiently\n        // figure out if a sub player is skipped due to a parent player having priority.\n        const animationElementMap = new Map();\n        queuedInstructions.forEach((entry) => {\n            const element = entry.element;\n            if (subTimelines.has(element)) {\n                animationElementMap.set(element, element);\n                this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n            }\n        });\n        skippedPlayers.forEach((player) => {\n            const element = player.element;\n            const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n            previousPlayers.forEach((prevPlayer) => {\n                getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n                prevPlayer.destroy();\n            });\n        });\n        // this is a special case for nodes that will be removed either by\n        // having their own leave animations or by being queried in a container\n        // that will be removed once a parent animation is complete. The idea\n        // here is that * styles must be identical to ! styles because of\n        // backwards compatibility (* is also filled in by default in many places).\n        // Otherwise * styles will return an empty value or \"auto\" since the element\n        // passed to getComputedStyle will not be visible (since * === destination)\n        const replaceNodes = allLeaveNodes.filter((node) => {\n            return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n        });\n        // POST STAGE: fill the * styles\n        const postStylesMap = new Map();\n        const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n        allLeaveQueriedNodes.forEach((node) => {\n            if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n                replaceNodes.push(node);\n            }\n        });\n        // PRE STAGE: fill the ! styles\n        const preStylesMap = new Map();\n        enterNodeMap.forEach((nodes, root) => {\n            cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, _PRE_STYLE);\n        });\n        replaceNodes.forEach((node) => {\n            const post = postStylesMap.get(node);\n            const pre = preStylesMap.get(node);\n            postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n        });\n        const rootPlayers = [];\n        const subPlayers = [];\n        const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n        queuedInstructions.forEach((entry) => {\n            const { element, player, instruction } = entry;\n            // this means that it was never consumed by a parent animation which\n            // means that it is independent and therefore should be set for animation\n            if (subTimelines.has(element)) {\n                if (disabledElementsSet.has(element)) {\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    player.disabled = true;\n                    player.overrideTotalTime(instruction.totalTime);\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this will flow up the DOM and query the map to figure out\n                // if a parent animation has priority over it. In the situation\n                // that a parent is detected then it will cancel the loop. If\n                // nothing is detected, or it takes a few hops to find a parent,\n                // then it will fill in the missing nodes and signal them as having\n                // a detected parent (or a NO_PARENT value via a special constant).\n                let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n                if (animationElementMap.size > 1) {\n                    let elm = element;\n                    const parentsToAdd = [];\n                    while ((elm = elm.parentNode)) {\n                        const detectedParent = animationElementMap.get(elm);\n                        if (detectedParent) {\n                            parentWithAnimation = detectedParent;\n                            break;\n                        }\n                        parentsToAdd.push(elm);\n                    }\n                    parentsToAdd.forEach((parent) => animationElementMap.set(parent, parentWithAnimation));\n                }\n                const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n                player.setRealPlayer(innerPlayer);\n                if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n                    rootPlayers.push(player);\n                }\n                else {\n                    const parentPlayers = this.playersByElement.get(parentWithAnimation);\n                    if (parentPlayers && parentPlayers.length) {\n                        player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n                    }\n                    skippedPlayers.push(player);\n                }\n            }\n            else {\n                eraseStyles(element, instruction.fromStyles);\n                player.onDestroy(() => setStyles(element, instruction.toStyles));\n                // there still might be a ancestor player animating this\n                // element therefore we will still add it as a sub player\n                // even if its animation may be disabled\n                subPlayers.push(player);\n                if (disabledElementsSet.has(element)) {\n                    skippedPlayers.push(player);\n                }\n            }\n        });\n        // find all of the sub players' corresponding inner animation players\n        subPlayers.forEach((player) => {\n            // even if no players are found for a sub animation it\n            // will still complete itself after the next tick since it's Noop\n            const playersForElement = skippedPlayersMap.get(player.element);\n            if (playersForElement && playersForElement.length) {\n                const innerPlayer = optimizeGroupPlayer(playersForElement);\n                player.setRealPlayer(innerPlayer);\n            }\n        });\n        // the reason why we don't actually play the animation is\n        // because all that a skipped player is designed to do is to\n        // fire the start/done transition callback events\n        skippedPlayers.forEach((player) => {\n            if (player.parentPlayer) {\n                player.syncPlayerEvents(player.parentPlayer);\n            }\n            else {\n                player.destroy();\n            }\n        });\n        // run through all of the queued removals and see if they\n        // were picked up by a query. If not then perform the removal\n        // operation right away unless a parent animation is ongoing.\n        for (let i = 0; i < allLeaveNodes.length; i++) {\n            const element = allLeaveNodes[i];\n            const details = element[REMOVAL_FLAG];\n            removeClass(element, LEAVE_CLASSNAME);\n            // this means the element has a removal animation that is being\n            // taken care of and therefore the inner elements will hang around\n            // until that animation is over (or the parent queried animation)\n            if (details && details.hasAnimation)\n                continue;\n            let players = [];\n            // if this element is queried or if it contains queried children\n            // then we want for the element not to be removed from the page\n            // until the queried animations have finished\n            if (queriedElements.size) {\n                let queriedPlayerResults = queriedElements.get(element);\n                if (queriedPlayerResults && queriedPlayerResults.length) {\n                    players.push(...queriedPlayerResults);\n                }\n                let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n                for (let j = 0; j < queriedInnerElements.length; j++) {\n                    let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n                    if (queriedPlayers && queriedPlayers.length) {\n                        players.push(...queriedPlayers);\n                    }\n                }\n            }\n            const activePlayers = players.filter((p) => !p.destroyed);\n            if (activePlayers.length) {\n                removeNodesAfterAnimationDone(this, element, activePlayers);\n            }\n            else {\n                this.processLeaveNode(element);\n            }\n        }\n        // this is required so the cleanup method doesn't remove them\n        allLeaveNodes.length = 0;\n        rootPlayers.forEach((player) => {\n            this.players.push(player);\n            player.onDone(() => {\n                player.destroy();\n                const index = this.players.indexOf(player);\n                this.players.splice(index, 1);\n            });\n            player.play();\n        });\n        return rootPlayers;\n    }\n    afterFlush(callback) {\n        this._flushFns.push(callback);\n    }\n    afterFlushAnimationsDone(callback) {\n        this._whenQuietFns.push(callback);\n    }\n    _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n        let players = [];\n        if (isQueriedElement) {\n            const queriedElementPlayers = this.playersByQueriedElement.get(element);\n            if (queriedElementPlayers) {\n                players = queriedElementPlayers;\n            }\n        }\n        else {\n            const elementPlayers = this.playersByElement.get(element);\n            if (elementPlayers) {\n                const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n                elementPlayers.forEach((player) => {\n                    if (player.queued)\n                        return;\n                    if (!isRemovalAnimation && player.triggerName != triggerName)\n                        return;\n                    players.push(player);\n                });\n            }\n        }\n        if (namespaceId || triggerName) {\n            players = players.filter((player) => {\n                if (namespaceId && namespaceId != player.namespaceId)\n                    return false;\n                if (triggerName && triggerName != player.triggerName)\n                    return false;\n                return true;\n            });\n        }\n        return players;\n    }\n    _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n        const triggerName = instruction.triggerName;\n        const rootElement = instruction.element;\n        // when a removal animation occurs, ALL previous players are collected\n        // and destroyed (even if they are outside of the current namespace)\n        const targetNameSpaceId = instruction.isRemovalTransition\n            ? undefined\n            : namespaceId;\n        const targetTriggerName = instruction.isRemovalTransition\n            ? undefined\n            : triggerName;\n        for (const timelineInstruction of instruction.timelines) {\n            const element = timelineInstruction.element;\n            const isQueriedElement = element !== rootElement;\n            const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n            const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n            previousPlayers.forEach((player) => {\n                const realPlayer = player.getRealPlayer();\n                if (realPlayer.beforeDestroy) {\n                    realPlayer.beforeDestroy();\n                }\n                player.destroy();\n                players.push(player);\n            });\n        }\n        // this needs to be done so that the PRE/POST styles can be\n        // computed properly without interfering with the previous animation\n        eraseStyles(rootElement, instruction.fromStyles);\n    }\n    _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n        const triggerName = instruction.triggerName;\n        const rootElement = instruction.element;\n        // we first run this so that the previous animation player\n        // data can be passed into the successive animation players\n        const allQueriedPlayers = [];\n        const allConsumedElements = new Set();\n        const allSubElements = new Set();\n        const allNewPlayers = instruction.timelines.map((timelineInstruction) => {\n            const element = timelineInstruction.element;\n            allConsumedElements.add(element);\n            // FIXME (matsko): make sure to-be-removed animations are removed properly\n            const details = element[REMOVAL_FLAG];\n            if (details && details.removedBeforeQueried)\n                return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n            const isQueriedElement = element !== rootElement;\n            const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map((p) => p.getRealPlayer())).filter((p) => {\n                // the `element` is not apart of the AnimationPlayer definition, but\n                // Mock/WebAnimations\n                // use the element within their implementation. This will be added in Angular5 to\n                // AnimationPlayer\n                const pp = p;\n                return pp.element ? pp.element === element : false;\n            });\n            const preStyles = preStylesMap.get(element);\n            const postStyles = postStylesMap.get(element);\n            const keyframes = normalizeKeyframes(this._normalizer, timelineInstruction.keyframes, preStyles, postStyles);\n            const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n            // this means that this particular player belongs to a sub trigger. It is\n            // important that we match this player up with the corresponding (@trigger.listener)\n            if (timelineInstruction.subTimeline && skippedPlayersMap) {\n                allSubElements.add(element);\n            }\n            if (isQueriedElement) {\n                const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n                wrappedPlayer.setRealPlayer(player);\n                allQueriedPlayers.push(wrappedPlayer);\n            }\n            return player;\n        });\n        allQueriedPlayers.forEach((player) => {\n            getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n            player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n        });\n        allConsumedElements.forEach((element) => addClass(element, NG_ANIMATING_CLASSNAME));\n        const player = optimizeGroupPlayer(allNewPlayers);\n        player.onDestroy(() => {\n            allConsumedElements.forEach((element) => removeClass(element, NG_ANIMATING_CLASSNAME));\n            setStyles(rootElement, instruction.toStyles);\n        });\n        // this basically makes all of the callbacks for sub element animations\n        // be dependent on the upper players for when they finish\n        allSubElements.forEach((element) => {\n            getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n        });\n        return player;\n    }\n    _buildPlayer(instruction, keyframes, previousPlayers) {\n        if (keyframes.length > 0) {\n            return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n        }\n        // special case for when an empty transition|definition is provided\n        // ... there is no point in rendering an empty animation\n        return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n    }\n}\nclass TransitionAnimationPlayer {\n    namespaceId;\n    triggerName;\n    element;\n    _player = new NoopAnimationPlayer();\n    _containsRealPlayer = false;\n    _queuedCallbacks = new Map();\n    destroyed = false;\n    parentPlayer = null;\n    markedForDestroy = false;\n    disabled = false;\n    queued = true;\n    totalTime = 0;\n    constructor(namespaceId, triggerName, element) {\n        this.namespaceId = namespaceId;\n        this.triggerName = triggerName;\n        this.element = element;\n    }\n    setRealPlayer(player) {\n        if (this._containsRealPlayer)\n            return;\n        this._player = player;\n        this._queuedCallbacks.forEach((callbacks, phase) => {\n            callbacks.forEach((callback) => listenOnPlayer(player, phase, undefined, callback));\n        });\n        this._queuedCallbacks.clear();\n        this._containsRealPlayer = true;\n        this.overrideTotalTime(player.totalTime);\n        this.queued = false;\n    }\n    getRealPlayer() {\n        return this._player;\n    }\n    overrideTotalTime(totalTime) {\n        this.totalTime = totalTime;\n    }\n    syncPlayerEvents(player) {\n        const p = this._player;\n        if (p.triggerCallback) {\n            player.onStart(() => p.triggerCallback('start'));\n        }\n        player.onDone(() => this.finish());\n        player.onDestroy(() => this.destroy());\n    }\n    _queueEvent(name, callback) {\n        getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n    }\n    onDone(fn) {\n        if (this.queued) {\n            this._queueEvent('done', fn);\n        }\n        this._player.onDone(fn);\n    }\n    onStart(fn) {\n        if (this.queued) {\n            this._queueEvent('start', fn);\n        }\n        this._player.onStart(fn);\n    }\n    onDestroy(fn) {\n        if (this.queued) {\n            this._queueEvent('destroy', fn);\n        }\n        this._player.onDestroy(fn);\n    }\n    init() {\n        this._player.init();\n    }\n    hasStarted() {\n        return this.queued ? false : this._player.hasStarted();\n    }\n    play() {\n        !this.queued && this._player.play();\n    }\n    pause() {\n        !this.queued && this._player.pause();\n    }\n    restart() {\n        !this.queued && this._player.restart();\n    }\n    finish() {\n        this._player.finish();\n    }\n    destroy() {\n        this.destroyed = true;\n        this._player.destroy();\n    }\n    reset() {\n        !this.queued && this._player.reset();\n    }\n    setPosition(p) {\n        if (!this.queued) {\n            this._player.setPosition(p);\n        }\n    }\n    getPosition() {\n        return this.queued ? 0 : this._player.getPosition();\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const p = this._player;\n        if (p.triggerCallback) {\n            p.triggerCallback(phaseName);\n        }\n    }\n}\nfunction deleteOrUnsetInMap(map, key, value) {\n    let currentValues = map.get(key);\n    if (currentValues) {\n        if (currentValues.length) {\n            const index = currentValues.indexOf(value);\n            currentValues.splice(index, 1);\n        }\n        if (currentValues.length == 0) {\n            map.delete(key);\n        }\n    }\n    return currentValues;\n}\nfunction normalizeTriggerValue(value) {\n    // we use `!= null` here because it's the most simple\n    // way to test against a \"falsy\" value without mixing\n    // in empty strings or a zero value. DO NOT OPTIMIZE.\n    return value != null ? value : null;\n}\nfunction isElementNode(node) {\n    return node && node['nodeType'] === 1;\n}\nfunction isTriggerEventValid(eventName) {\n    return eventName == 'start' || eventName == 'done';\n}\nfunction cloakElement(element, value) {\n    const oldValue = element.style.display;\n    element.style.display = value != null ? value : 'none';\n    return oldValue;\n}\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n    const cloakVals = [];\n    elements.forEach((element) => cloakVals.push(cloakElement(element)));\n    const failedElements = [];\n    elementPropsMap.forEach((props, element) => {\n        const styles = new Map();\n        props.forEach((prop) => {\n            const value = driver.computeStyle(element, prop, defaultStyle);\n            styles.set(prop, value);\n            // there is no easy way to detect this because a sub element could be removed\n            // by a parent animation element being detached.\n            if (!value || value.length == 0) {\n                element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n                failedElements.push(element);\n            }\n        });\n        valuesMap.set(element, styles);\n    });\n    // we use a index variable here since Set.forEach(a, i) does not return\n    // an index value for the closure (but instead just the value)\n    let i = 0;\n    elements.forEach((element) => cloakElement(element, cloakVals[i++]));\n    return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots, nodes) {\n    const rootMap = new Map();\n    roots.forEach((root) => rootMap.set(root, []));\n    if (nodes.length == 0)\n        return rootMap;\n    const NULL_NODE = 1;\n    const nodeSet = new Set(nodes);\n    const localRootMap = new Map();\n    function getRoot(node) {\n        if (!node)\n            return NULL_NODE;\n        let root = localRootMap.get(node);\n        if (root)\n            return root;\n        const parent = node.parentNode;\n        if (rootMap.has(parent)) {\n            // ngIf inside @trigger\n            root = parent;\n        }\n        else if (nodeSet.has(parent)) {\n            // ngIf inside ngIf\n            root = NULL_NODE;\n        }\n        else {\n            // recurse upwards\n            root = getRoot(parent);\n        }\n        localRootMap.set(node, root);\n        return root;\n    }\n    nodes.forEach((node) => {\n        const root = getRoot(node);\n        if (root !== NULL_NODE) {\n            rootMap.get(root).push(node);\n        }\n    });\n    return rootMap;\n}\nfunction addClass(element, className) {\n    element.classList?.add(className);\n}\nfunction removeClass(element, className) {\n    element.classList?.remove(className);\n}\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n    optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\nfunction flattenGroupPlayers(players) {\n    const finalPlayers = [];\n    _flattenGroupPlayersRecur(players, finalPlayers);\n    return finalPlayers;\n}\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n    for (let i = 0; i < players.length; i++) {\n        const player = players[i];\n        if (player instanceof AnimationGroupPlayer) {\n            _flattenGroupPlayersRecur(player.players, finalPlayers);\n        }\n        else {\n            finalPlayers.push(player);\n        }\n    }\n}\nfunction objEquals(a, b) {\n    const k1 = Object.keys(a);\n    const k2 = Object.keys(b);\n    if (k1.length != k2.length)\n        return false;\n    for (let i = 0; i < k1.length; i++) {\n        const prop = k1[i];\n        if (!b.hasOwnProperty(prop) || a[prop] !== b[prop])\n            return false;\n    }\n    return true;\n}\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n    const postEntry = allPostStyleElements.get(element);\n    if (!postEntry)\n        return false;\n    let preEntry = allPreStyleElements.get(element);\n    if (preEntry) {\n        postEntry.forEach((data) => preEntry.add(data));\n    }\n    else {\n        allPreStyleElements.set(element, postEntry);\n    }\n    allPostStyleElements.delete(element);\n    return true;\n}\n\nclass AnimationEngine {\n    _driver;\n    _normalizer;\n    _transitionEngine;\n    _timelineEngine;\n    _triggerCache = {};\n    // this method is designed to be overridden by the code that uses this engine\n    onRemovalComplete = (element, context) => { };\n    constructor(doc, _driver, _normalizer) {\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n        this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n        this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n        this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n    }\n    registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n        const cacheKey = componentId + '-' + name;\n        let trigger = this._triggerCache[cacheKey];\n        if (!trigger) {\n            const errors = [];\n            const warnings = [];\n            const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n            if (errors.length) {\n                throw triggerBuildFailed(name, errors);\n            }\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                if (warnings.length) {\n                    warnTriggerBuild(name, warnings);\n                }\n            }\n            trigger = buildTrigger(name, ast, this._normalizer);\n            this._triggerCache[cacheKey] = trigger;\n        }\n        this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n    }\n    register(namespaceId, hostElement) {\n        this._transitionEngine.register(namespaceId, hostElement);\n    }\n    destroy(namespaceId, context) {\n        this._transitionEngine.destroy(namespaceId, context);\n    }\n    onInsert(namespaceId, element, parent, insertBefore) {\n        this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n    }\n    onRemove(namespaceId, element, context) {\n        this._transitionEngine.removeNode(namespaceId, element, context);\n    }\n    disableAnimations(element, disable) {\n        this._transitionEngine.markElementAsDisabled(element, disable);\n    }\n    process(namespaceId, element, property, value) {\n        if (property.charAt(0) == '@') {\n            const [id, action] = parseTimelineCommand(property);\n            const args = value;\n            this._timelineEngine.command(id, element, action, args);\n        }\n        else {\n            this._transitionEngine.trigger(namespaceId, element, property, value);\n        }\n    }\n    listen(namespaceId, element, eventName, eventPhase, callback) {\n        // @@listen\n        if (eventName.charAt(0) == '@') {\n            const [id, action] = parseTimelineCommand(eventName);\n            return this._timelineEngine.listen(id, element, action, callback);\n        }\n        return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n    }\n    flush(microtaskId = -1) {\n        this._transitionEngine.flush(microtaskId);\n    }\n    get players() {\n        return [...this._transitionEngine.players, ...this._timelineEngine.players];\n    }\n    whenRenderingDone() {\n        return this._transitionEngine.whenRenderingDone();\n    }\n    afterFlushAnimationsDone(cb) {\n        this._transitionEngine.afterFlushAnimationsDone(cb);\n    }\n}\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nfunction packageNonAnimatableStyles(element, styles) {\n    let startStyles = null;\n    let endStyles = null;\n    if (Array.isArray(styles) && styles.length) {\n        startStyles = filterNonAnimatableStyles(styles[0]);\n        if (styles.length > 1) {\n            endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n        }\n    }\n    else if (styles instanceof Map) {\n        startStyles = filterNonAnimatableStyles(styles);\n    }\n    return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nclass SpecialCasedStyles {\n    _element;\n    _startStyles;\n    _endStyles;\n    static initialStylesByElement = /* @__PURE__ */ new WeakMap();\n    _state = 0 /* SpecialCasedStylesState.Pending */;\n    _initialStyles;\n    constructor(_element, _startStyles, _endStyles) {\n        this._element = _element;\n        this._startStyles = _startStyles;\n        this._endStyles = _endStyles;\n        let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n        if (!initialStyles) {\n            SpecialCasedStyles.initialStylesByElement.set(_element, (initialStyles = new Map()));\n        }\n        this._initialStyles = initialStyles;\n    }\n    start() {\n        if (this._state < 1 /* SpecialCasedStylesState.Started */) {\n            if (this._startStyles) {\n                setStyles(this._element, this._startStyles, this._initialStyles);\n            }\n            this._state = 1 /* SpecialCasedStylesState.Started */;\n        }\n    }\n    finish() {\n        this.start();\n        if (this._state < 2 /* SpecialCasedStylesState.Finished */) {\n            setStyles(this._element, this._initialStyles);\n            if (this._endStyles) {\n                setStyles(this._element, this._endStyles);\n                this._endStyles = null;\n            }\n            this._state = 1 /* SpecialCasedStylesState.Started */;\n        }\n    }\n    destroy() {\n        this.finish();\n        if (this._state < 3 /* SpecialCasedStylesState.Destroyed */) {\n            SpecialCasedStyles.initialStylesByElement.delete(this._element);\n            if (this._startStyles) {\n                eraseStyles(this._element, this._startStyles);\n                this._endStyles = null;\n            }\n            if (this._endStyles) {\n                eraseStyles(this._element, this._endStyles);\n                this._endStyles = null;\n            }\n            setStyles(this._element, this._initialStyles);\n            this._state = 3 /* SpecialCasedStylesState.Destroyed */;\n        }\n    }\n}\nfunction filterNonAnimatableStyles(styles) {\n    let result = null;\n    styles.forEach((val, prop) => {\n        if (isNonAnimatableStyle(prop)) {\n            result = result || new Map();\n            result.set(prop, val);\n        }\n    });\n    return result;\n}\nfunction isNonAnimatableStyle(prop) {\n    return prop === 'display' || prop === 'position';\n}\n\nclass WebAnimationsPlayer {\n    element;\n    keyframes;\n    options;\n    _specialStyles;\n    _onDoneFns = [];\n    _onStartFns = [];\n    _onDestroyFns = [];\n    _duration;\n    _delay;\n    _initialized = false;\n    _finished = false;\n    _started = false;\n    _destroyed = false;\n    _finalKeyframe;\n    // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n    // and are used to reset the fns to their original values upon reset()\n    // (since the _onStartFns and _onDoneFns get deleted after they are called)\n    _originalOnDoneFns = [];\n    _originalOnStartFns = [];\n    // using non-null assertion because it's re(set) by init();\n    domPlayer;\n    time = 0;\n    parentPlayer = null;\n    currentSnapshot = new Map();\n    constructor(element, keyframes, options, _specialStyles) {\n        this.element = element;\n        this.keyframes = keyframes;\n        this.options = options;\n        this._specialStyles = _specialStyles;\n        this._duration = options['duration'];\n        this._delay = options['delay'] || 0;\n        this.time = this._duration + this._delay;\n    }\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach((fn) => fn());\n            this._onDoneFns = [];\n        }\n    }\n    init() {\n        this._buildPlayer();\n        this._preparePlayerBeforeStart();\n    }\n    _buildPlayer() {\n        if (this._initialized)\n            return;\n        this._initialized = true;\n        const keyframes = this.keyframes;\n        // @ts-expect-error overwriting a readonly property\n        this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n        this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n        const onFinish = () => this._onFinish();\n        this.domPlayer.addEventListener('finish', onFinish);\n        this.onDestroy(() => {\n            // We must remove the `finish` event listener once an animation has completed all its\n            // iterations. This action is necessary to prevent a memory leak since the listener captures\n            // `this`, creating a closure that prevents `this` from being garbage collected.\n            this.domPlayer.removeEventListener('finish', onFinish);\n        });\n    }\n    _preparePlayerBeforeStart() {\n        // this is required so that the player doesn't start to animate right away\n        if (this._delay) {\n            this._resetDomPlayerState();\n        }\n        else {\n            this.domPlayer.pause();\n        }\n    }\n    _convertKeyframesToObject(keyframes) {\n        const kfs = [];\n        keyframes.forEach((frame) => {\n            kfs.push(Object.fromEntries(frame));\n        });\n        return kfs;\n    }\n    /** @internal */\n    _triggerWebAnimation(element, keyframes, options) {\n        return element.animate(this._convertKeyframesToObject(keyframes), options);\n    }\n    onStart(fn) {\n        this._originalOnStartFns.push(fn);\n        this._onStartFns.push(fn);\n    }\n    onDone(fn) {\n        this._originalOnDoneFns.push(fn);\n        this._onDoneFns.push(fn);\n    }\n    onDestroy(fn) {\n        this._onDestroyFns.push(fn);\n    }\n    play() {\n        this._buildPlayer();\n        if (!this.hasStarted()) {\n            this._onStartFns.forEach((fn) => fn());\n            this._onStartFns = [];\n            this._started = true;\n            if (this._specialStyles) {\n                this._specialStyles.start();\n            }\n        }\n        this.domPlayer.play();\n    }\n    pause() {\n        this.init();\n        this.domPlayer.pause();\n    }\n    finish() {\n        this.init();\n        if (this._specialStyles) {\n            this._specialStyles.finish();\n        }\n        this._onFinish();\n        this.domPlayer.finish();\n    }\n    reset() {\n        this._resetDomPlayerState();\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n        this._onStartFns = this._originalOnStartFns;\n        this._onDoneFns = this._originalOnDoneFns;\n    }\n    _resetDomPlayerState() {\n        if (this.domPlayer) {\n            this.domPlayer.cancel();\n        }\n    }\n    restart() {\n        this.reset();\n        this.play();\n    }\n    hasStarted() {\n        return this._started;\n    }\n    destroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._resetDomPlayerState();\n            this._onFinish();\n            if (this._specialStyles) {\n                this._specialStyles.destroy();\n            }\n            this._onDestroyFns.forEach((fn) => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    setPosition(p) {\n        if (this.domPlayer === undefined) {\n            this.init();\n        }\n        this.domPlayer.currentTime = p * this.time;\n    }\n    getPosition() {\n        // tsc is complaining with TS2362 without the conversion to number\n        return +(this.domPlayer.currentTime ?? 0) / this.time;\n    }\n    get totalTime() {\n        return this._delay + this._duration;\n    }\n    beforeDestroy() {\n        const styles = new Map();\n        if (this.hasStarted()) {\n            // note: this code is invoked only when the `play` function was called prior to this\n            // (thus `hasStarted` returns true), this implies that the code that initializes\n            // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n            const finalKeyframe = this._finalKeyframe;\n            finalKeyframe.forEach((val, prop) => {\n                if (prop !== 'offset') {\n                    styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n                }\n            });\n        }\n        this.currentSnapshot = styles;\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach((fn) => fn());\n        methods.length = 0;\n    }\n}\n\nclass WebAnimationsDriver {\n    validateStyleProperty(prop) {\n        // Perform actual validation in dev mode only, in prod mode this check is a noop.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            return validateStyleProperty(prop);\n        }\n        return true;\n    }\n    validateAnimatableStyleProperty(prop) {\n        // Perform actual validation in dev mode only, in prod mode this check is a noop.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const cssProp = camelCaseToDashCase(prop);\n            return validateWebAnimatableStyleProperty(cssProp);\n        }\n        return true;\n    }\n    containsElement(elm1, elm2) {\n        return containsElement(elm1, elm2);\n    }\n    getParentElement(element) {\n        return getParentElement(element);\n    }\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    computeStyle(element, prop, defaultValue) {\n        return computeStyle(element, prop);\n    }\n    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n        const fill = delay == 0 ? 'both' : 'forwards';\n        const playerOptions = { duration, delay, fill };\n        // we check for this to avoid having a null|undefined value be present\n        // for the easing (which results in an error for certain browsers #9752)\n        if (easing) {\n            playerOptions['easing'] = easing;\n        }\n        const previousStyles = new Map();\n        const previousWebAnimationPlayers = (previousPlayers.filter((player) => player instanceof WebAnimationsPlayer));\n        if (allowPreviousPlayerStylesMerge(duration, delay)) {\n            previousWebAnimationPlayers.forEach((player) => {\n                player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n            });\n        }\n        let _keyframes = normalizeKeyframes$1(keyframes).map((styles) => new Map(styles));\n        _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n        const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n        return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n    }\n}\n\nfunction createEngine(type, doc) {\n    // TODO: find a way to make this tree shakable.\n    if (type === 'noop') {\n        return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n    }\n    return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\n\nclass Animation {\n    _driver;\n    _animationAst;\n    constructor(_driver, input) {\n        this._driver = _driver;\n        const errors = [];\n        const warnings = [];\n        const ast = buildAnimationAst(_driver, input, errors, warnings);\n        if (errors.length) {\n            throw validationFailed(errors);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (warnings.length) {\n                warnValidation(warnings);\n            }\n        }\n        this._animationAst = ast;\n    }\n    buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n        const start = Array.isArray(startingStyles)\n            ? normalizeStyles(startingStyles)\n            : startingStyles;\n        const dest = Array.isArray(destinationStyles)\n            ? normalizeStyles(destinationStyles)\n            : destinationStyles;\n        const errors = [];\n        subInstructions = subInstructions || new ElementInstructionMap();\n        const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n        if (errors.length) {\n            throw buildingFailed(errors);\n        }\n        return result;\n    }\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass BaseAnimationRenderer {\n    namespaceId;\n    delegate;\n    engine;\n    _onDestroy;\n    // We need to explicitly type this property because of an api-extractor bug\n    // See https://github.com/microsoft/rushstack/issues/4390\n    ɵtype = 0 /* AnimationRendererType.Regular */;\n    constructor(namespaceId, delegate, engine, _onDestroy) {\n        this.namespaceId = namespaceId;\n        this.delegate = delegate;\n        this.engine = engine;\n        this._onDestroy = _onDestroy;\n    }\n    get data() {\n        return this.delegate.data;\n    }\n    destroyNode(node) {\n        this.delegate.destroyNode?.(node);\n    }\n    destroy() {\n        this.engine.destroy(this.namespaceId, this.delegate);\n        this.engine.afterFlushAnimationsDone(() => {\n            // Call the renderer destroy method after the animations has finished as otherwise\n            // styles will be removed too early which will cause an unstyled animation.\n            queueMicrotask(() => {\n                this.delegate.destroy();\n            });\n        });\n        this._onDestroy?.();\n    }\n    createElement(name, namespace) {\n        return this.delegate.createElement(name, namespace);\n    }\n    createComment(value) {\n        return this.delegate.createComment(value);\n    }\n    createText(value) {\n        return this.delegate.createText(value);\n    }\n    appendChild(parent, newChild) {\n        this.delegate.appendChild(parent, newChild);\n        this.engine.onInsert(this.namespaceId, newChild, parent, false);\n    }\n    insertBefore(parent, newChild, refChild, isMove = true) {\n        this.delegate.insertBefore(parent, newChild, refChild);\n        // If `isMove` true than we should animate this insert.\n        this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n    }\n    removeChild(parent, oldChild, isHostElement) {\n        // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n        // doesn't have a parent. There appears to be some animation-specific downstream logic that\n        // depends on the null check happening before the animation engine. This check keeps the old\n        // behavior while allowing `core` to not have to check for the parent element anymore.\n        if (this.parentNode(oldChild)) {\n            this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n    }\n    parentNode(node) {\n        return this.delegate.parentNode(node);\n    }\n    nextSibling(node) {\n        return this.delegate.nextSibling(node);\n    }\n    setAttribute(el, name, value, namespace) {\n        this.delegate.setAttribute(el, name, value, namespace);\n    }\n    removeAttribute(el, name, namespace) {\n        this.delegate.removeAttribute(el, name, namespace);\n    }\n    addClass(el, name) {\n        this.delegate.addClass(el, name);\n    }\n    removeClass(el, name) {\n        this.delegate.removeClass(el, name);\n    }\n    setStyle(el, style, value, flags) {\n        this.delegate.setStyle(el, style, value, flags);\n    }\n    removeStyle(el, style, flags) {\n        this.delegate.removeStyle(el, style, flags);\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n            this.disableAnimations(el, !!value);\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    setValue(node, value) {\n        this.delegate.setValue(node, value);\n    }\n    listen(target, eventName, callback, options) {\n        return this.delegate.listen(target, eventName, callback, options);\n    }\n    disableAnimations(element, value) {\n        this.engine.disableAnimations(element, value);\n    }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n    factory;\n    constructor(factory, namespaceId, delegate, engine, onDestroy) {\n        super(namespaceId, delegate, engine, onDestroy);\n        this.factory = factory;\n        this.namespaceId = namespaceId;\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX) {\n            if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n                value = value === undefined ? true : !!value;\n                this.disableAnimations(el, value);\n            }\n            else {\n                this.engine.process(this.namespaceId, el, name.slice(1), value);\n            }\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    listen(target, eventName, callback, options) {\n        if (eventName.charAt(0) == ANIMATION_PREFIX) {\n            const element = resolveElementFromTarget(target);\n            let name = eventName.slice(1);\n            let phase = '';\n            // @listener.phase is for trigger animation callbacks\n            // @@listener is for animation builder callbacks\n            if (name.charAt(0) != ANIMATION_PREFIX) {\n                [name, phase] = parseTriggerCallbackName(name);\n            }\n            return this.engine.listen(this.namespaceId, element, name, phase, (event) => {\n                const countId = event['_data'] || -1;\n                this.factory.scheduleListenerCallback(countId, callback, event);\n            });\n        }\n        return this.delegate.listen(target, eventName, callback, options);\n    }\n}\nfunction resolveElementFromTarget(target) {\n    switch (target) {\n        case 'body':\n            return document.body;\n        case 'document':\n            return document;\n        case 'window':\n            return window;\n        default:\n            return target;\n    }\n}\nfunction parseTriggerCallbackName(triggerName) {\n    const dotIndex = triggerName.indexOf('.');\n    const trigger = triggerName.substring(0, dotIndex);\n    const phase = triggerName.slice(dotIndex + 1);\n    return [trigger, phase];\n}\n\nclass AnimationRendererFactory {\n    delegate;\n    engine;\n    _zone;\n    _currentId = 0;\n    _microtaskId = 1;\n    _animationCallbacksBuffer = [];\n    _rendererCache = new Map();\n    _cdRecurDepth = 0;\n    constructor(delegate, engine, _zone) {\n        this.delegate = delegate;\n        this.engine = engine;\n        this._zone = _zone;\n        engine.onRemovalComplete = (element, delegate) => {\n            delegate?.removeChild(null, element);\n        };\n    }\n    createRenderer(hostElement, type) {\n        const EMPTY_NAMESPACE_ID = '';\n        // cache the delegates to find out which cached delegate can\n        // be used by which cached renderer\n        const delegate = this.delegate.createRenderer(hostElement, type);\n        if (!hostElement || !type?.data?.['animation']) {\n            const cache = this._rendererCache;\n            let renderer = cache.get(delegate);\n            if (!renderer) {\n                // Ensure that the renderer is removed from the cache on destroy\n                // since it may contain references to detached DOM nodes.\n                const onRendererDestroy = () => cache.delete(delegate);\n                renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n                // only cache this result when the base renderer is used\n                cache.set(delegate, renderer);\n            }\n            return renderer;\n        }\n        const componentId = type.id;\n        const namespaceId = type.id + '-' + this._currentId;\n        this._currentId++;\n        this.engine.register(namespaceId, hostElement);\n        const registerTrigger = (trigger) => {\n            if (Array.isArray(trigger)) {\n                trigger.forEach(registerTrigger);\n            }\n            else {\n                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n            }\n        };\n        const animationTriggers = type.data['animation'];\n        animationTriggers.forEach(registerTrigger);\n        return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n        this._cdRecurDepth++;\n        if (this.delegate.begin) {\n            this.delegate.begin();\n        }\n    }\n    _scheduleCountTask() {\n        queueMicrotask(() => {\n            this._microtaskId++;\n        });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n        if (count >= 0 && count < this._microtaskId) {\n            this._zone.run(() => fn(data));\n            return;\n        }\n        const animationCallbacksBuffer = this._animationCallbacksBuffer;\n        if (animationCallbacksBuffer.length == 0) {\n            queueMicrotask(() => {\n                this._zone.run(() => {\n                    animationCallbacksBuffer.forEach((tuple) => {\n                        const [fn, data] = tuple;\n                        fn(data);\n                    });\n                    this._animationCallbacksBuffer = [];\n                });\n            });\n        }\n        animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n        this._cdRecurDepth--;\n        // this is to prevent animations from running twice when an inner\n        // component does CD when a parent component instead has inserted it\n        if (this._cdRecurDepth == 0) {\n            this._zone.runOutsideAngular(() => {\n                this._scheduleCountTask();\n                this.engine.flush(this._microtaskId);\n            });\n        }\n        if (this.delegate.end) {\n            this.delegate.end();\n        }\n    }\n    whenRenderingDone() {\n        return this.engine.whenRenderingDone();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n        // Flush the engine since the renderer destruction waits for animations to be done.\n        this.engine.flush();\n        this.delegate.componentReplaced?.(componentId);\n    }\n}\n\nexport { AnimationDriver, NoopAnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, BaseAnimationRenderer as ɵBaseAnimationRenderer, ENTER_CLASSNAME as ɵENTER_CLASSNAME, LEAVE_CLASSNAME as ɵLEAVE_CLASSNAME, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, TransitionAnimationPlayer as ɵTransitionAnimationPlayer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, containsElement as ɵcontainsElement, createEngine as ɵcreateEngine, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes$1 as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,qBAAqB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,yBAAyB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,kCAAkC,EAAEC,8BAA8B,EAAEC,oBAAoB,EAAEC,kCAAkC,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AAChvC,SAASC,mBAAmB,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,IAAIC,UAAU,EAAEC,oBAAoB,QAAQ,+BAA+B;;AAE7J;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtB;AACJ;AACA;EACIpE,qBAAqBA,CAACqE,IAAI,EAAE;IACxB,OAAOrE,qBAAqB,CAACqE,IAAI,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIpE,eAAeA,CAACqE,IAAI,EAAEC,IAAI,EAAE;IACxB,OAAOtE,eAAe,CAACqE,IAAI,EAAEC,IAAI,CAAC;EACtC;EACA;AACJ;AACA;EACIrE,gBAAgBA,CAACsE,OAAO,EAAE;IACtB,OAAOtE,gBAAgB,CAACsE,OAAO,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACIC,KAAKA,CAACD,OAAO,EAAEE,QAAQ,EAAEC,KAAK,EAAE;IAC5B,OAAOxE,WAAW,CAACqE,OAAO,EAAEE,QAAQ,EAAEC,KAAK,CAAC;EAChD;EACA;AACJ;AACA;EACIvB,YAAYA,CAACoB,OAAO,EAAEH,IAAI,EAAEO,YAAY,EAAE;IACtC,OAAOA,YAAY,IAAI,EAAE;EAC7B;EACA;AACJ;AACA;EACIC,OAAOA,CAACL,OAAO,EAAEM,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,GAAG,EAAE,EAAEC,uBAAuB,EAAE;IAChG,OAAO,IAAItB,mBAAmB,CAACkB,QAAQ,EAAEC,KAAK,CAAC;EACnD;EACA,OAAOI,IAAI,YAAAC,4BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFlB,mBAAmB;EAAA;EACtH,OAAOmB,KAAK,kBAD6EzF,EAAE,CAAA0F,kBAAA;IAAAC,KAAA,EACYrB,mBAAmB;IAAAsB,OAAA,EAAnBtB,mBAAmB,CAAAgB;EAAA;AAC9H;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAH6F7F,EAAE,CAAA8F,iBAAA,CAGJxB,mBAAmB,EAAc,CAAC;IACjHyB,IAAI,EAAE9F;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAM+F,eAAe,CAAC;EAClB;AACJ;AACA;EACI,OAAOC,IAAI,GAAG,IAAI3B,mBAAmB,CAAC,CAAC;AAC3C;AAEA,MAAM4B,wBAAwB,CAAC;AAE/B,MAAMC,4BAA4B,CAAC;EAC/BC,qBAAqBA,CAACC,YAAY,EAAEC,MAAM,EAAE;IACxC,OAAOD,YAAY;EACvB;EACAE,mBAAmBA,CAACC,oBAAoB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEJ,MAAM,EAAE;IACzE,OAAOI,KAAK;EAChB;AACJ;AAEA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CACjC,OAAO,EACP,QAAQ,EACR,UAAU,EACV,WAAW,EACX,UAAU,EACV,WAAW,EACX,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,cAAc,EACd,eAAe,EACf,YAAY,EACZ,aAAa,EACb,eAAe,EACf,cAAc,EACd,WAAW,EACX,YAAY,EACZ,cAAc,EACd,aAAa,EACb,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,mBAAmB,EACnB,YAAY,EACZ,aAAa,CAChB,CAAC;AACF,MAAMC,4BAA4B,SAASX,wBAAwB,CAAC;EAChEE,qBAAqBA,CAACC,YAAY,EAAEC,MAAM,EAAE;IACxC,OAAOhG,mBAAmB,CAAC+F,YAAY,CAAC;EAC5C;EACAE,mBAAmBA,CAACC,oBAAoB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEJ,MAAM,EAAE;IACzE,IAAIQ,IAAI,GAAG,EAAE;IACb,MAAMC,MAAM,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IACtC,IAAIN,oBAAoB,CAACO,GAAG,CAACT,kBAAkB,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,GAAG,EAAE;MAC9E,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3BI,IAAI,GAAG,IAAI;MACf,CAAC,MACI;QACD,MAAMK,iBAAiB,GAAGT,KAAK,CAACU,KAAK,CAAC,wBAAwB,CAAC;QAC/D,IAAID,iBAAiB,IAAIA,iBAAiB,CAAC,CAAC,CAAC,CAACE,MAAM,IAAI,CAAC,EAAE;UACvDf,MAAM,CAACgB,IAAI,CAAC/G,mBAAmB,CAACiG,oBAAoB,EAAEE,KAAK,CAAC,CAAC;QACjE;MACJ;IACJ;IACA,OAAOK,MAAM,GAAGD,IAAI;EACxB;AACJ;AAEA,SAASS,oBAAoBA,CAACC,QAAQ,EAAE;EACpC,MAAMC,UAAU,GAAG,OAAO;EAC1B,OAAO,GAAGA,UAAU,GAAGD,QAAQ,CAC1BE,MAAM,CAACC,OAAO,CAAC,CACfC,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAAC,CACzBC,IAAI,CAACL,UAAU,CAAC,EAAE;AAC3B;AACA,SAASM,cAAcA,CAACP,QAAQ,EAAE;EAC9BQ,OAAO,CAACC,IAAI,CAAC,iCAAiCV,oBAAoB,CAACC,QAAQ,CAAC,EAAE,CAAC;AACnF;AACA,SAASU,gBAAgBA,CAACC,IAAI,EAAEX,QAAQ,EAAE;EACtCQ,OAAO,CAACC,IAAI,CAAC,0BAA0BE,IAAI,2CAA2CZ,oBAAoB,CAACC,QAAQ,CAAC,EAAE,CAAC;AAC3H;AACA,SAASY,YAAYA,CAACZ,QAAQ,EAAE;EAC5BQ,OAAO,CAACC,IAAI,CAAC,+CAA+CV,oBAAoB,CAACC,QAAQ,CAAC,EAAE,CAAC;AACjG;AACA,SAASa,iCAAiCA,CAACb,QAAQ,EAAEc,KAAK,EAAE;EACxD,IAAIA,KAAK,CAACjB,MAAM,EAAE;IACdG,QAAQ,CAACF,IAAI,CAAC,yDAAyDgB,KAAK,CAACR,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAC9F;AACJ;AAEA,MAAMS,SAAS,GAAG,GAAG;AACrB,SAASC,mBAAmBA,CAACC,eAAe,EAAEnC,MAAM,EAAE;EAClD,MAAMoC,WAAW,GAAG,EAAE;EACtB,IAAI,OAAOD,eAAe,IAAI,QAAQ,EAAE;IACpCA,eAAe,CACVE,KAAK,CAAC,SAAS,CAAC,CAChBC,OAAO,CAAEC,GAAG,IAAKC,uBAAuB,CAACD,GAAG,EAAEH,WAAW,EAAEpC,MAAM,CAAC,CAAC;EAC5E,CAAC,MACI;IACDoC,WAAW,CAACpB,IAAI,CAACmB,eAAe,CAAC;EACrC;EACA,OAAOC,WAAW;AACtB;AACA,SAASI,uBAAuBA,CAACC,QAAQ,EAAEL,WAAW,EAAEpC,MAAM,EAAE;EAC5D,IAAIyC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;IACpB,MAAMC,MAAM,GAAGC,mBAAmB,CAACF,QAAQ,EAAEzC,MAAM,CAAC;IACpD,IAAI,OAAO0C,MAAM,IAAI,UAAU,EAAE;MAC7BN,WAAW,CAACpB,IAAI,CAAC0B,MAAM,CAAC;MACxB;IACJ;IACAD,QAAQ,GAAGC,MAAM;EACrB;EACA,MAAM5B,KAAK,GAAG2B,QAAQ,CAAC3B,KAAK,CAAC,yCAAyC,CAAC;EACvE,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACnCf,MAAM,CAACgB,IAAI,CAAC9G,iBAAiB,CAACuI,QAAQ,CAAC,CAAC;IACxC,OAAOL,WAAW;EACtB;EACA,MAAMQ,SAAS,GAAG9B,KAAK,CAAC,CAAC,CAAC;EAC1B,MAAM+B,SAAS,GAAG/B,KAAK,CAAC,CAAC,CAAC;EAC1B,MAAMgC,OAAO,GAAGhC,KAAK,CAAC,CAAC,CAAC;EACxBsB,WAAW,CAACpB,IAAI,CAAC+B,oBAAoB,CAACH,SAAS,EAAEE,OAAO,CAAC,CAAC;EAC1D,MAAME,kBAAkB,GAAGJ,SAAS,IAAIX,SAAS,IAAIa,OAAO,IAAIb,SAAS;EACzE,IAAIY,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAACG,kBAAkB,EAAE;IAC5CZ,WAAW,CAACpB,IAAI,CAAC+B,oBAAoB,CAACD,OAAO,EAAEF,SAAS,CAAC,CAAC;EAC9D;EACA;AACJ;AACA,SAASD,mBAAmBA,CAACM,KAAK,EAAEjD,MAAM,EAAE;EACxC,QAAQiD,KAAK;IACT,KAAK,QAAQ;MACT,OAAO,WAAW;IACtB,KAAK,QAAQ;MACT,OAAO,WAAW;IACtB,KAAK,YAAY;MACb,OAAO,CAACL,SAAS,EAAEE,OAAO,KAAKI,UAAU,CAACJ,OAAO,CAAC,GAAGI,UAAU,CAACN,SAAS,CAAC;IAC9E,KAAK,YAAY;MACb,OAAO,CAACA,SAAS,EAAEE,OAAO,KAAKI,UAAU,CAACJ,OAAO,CAAC,GAAGI,UAAU,CAACN,SAAS,CAAC;IAC9E;MACI5C,MAAM,CAACgB,IAAI,CAAC7G,sBAAsB,CAAC8I,KAAK,CAAC,CAAC;MAC1C,OAAO,QAAQ;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,mBAAmB,GAAG,IAAI7C,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAClD,MAAM8C,oBAAoB,GAAG,IAAI9C,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACpD,SAASyC,oBAAoBA,CAACM,GAAG,EAAEC,GAAG,EAAE;EACpC,MAAMC,iBAAiB,GAAGJ,mBAAmB,CAACvC,GAAG,CAACyC,GAAG,CAAC,IAAID,oBAAoB,CAACxC,GAAG,CAACyC,GAAG,CAAC;EACvF,MAAMG,iBAAiB,GAAGL,mBAAmB,CAACvC,GAAG,CAAC0C,GAAG,CAAC,IAAIF,oBAAoB,CAACxC,GAAG,CAAC0C,GAAG,CAAC;EACvF,OAAO,CAACV,SAAS,EAAEE,OAAO,KAAK;IAC3B,IAAIW,QAAQ,GAAGJ,GAAG,IAAIpB,SAAS,IAAIoB,GAAG,IAAIT,SAAS;IACnD,IAAIc,QAAQ,GAAGJ,GAAG,IAAIrB,SAAS,IAAIqB,GAAG,IAAIR,OAAO;IACjD,IAAI,CAACW,QAAQ,IAAIF,iBAAiB,IAAI,OAAOX,SAAS,KAAK,SAAS,EAAE;MAClEa,QAAQ,GAAGb,SAAS,GAAGO,mBAAmB,CAACvC,GAAG,CAACyC,GAAG,CAAC,GAAGD,oBAAoB,CAACxC,GAAG,CAACyC,GAAG,CAAC;IACvF;IACA,IAAI,CAACK,QAAQ,IAAIF,iBAAiB,IAAI,OAAOV,OAAO,KAAK,SAAS,EAAE;MAChEY,QAAQ,GAAGZ,OAAO,GAAGK,mBAAmB,CAACvC,GAAG,CAAC0C,GAAG,CAAC,GAAGF,oBAAoB,CAACxC,GAAG,CAAC0C,GAAG,CAAC;IACrF;IACA,OAAOG,QAAQ,IAAIC,QAAQ;EAC/B,CAAC;AACL;AAEA,MAAMC,UAAU,GAAG,OAAO;AAC1B,MAAMC,gBAAgB,GAAG,eAAgB,IAAIC,MAAM,CAAC,KAAKF,UAAU,MAAM,EAAE,GAAG,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEC,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,EAAE;EAC3D,OAAO,IAAI+C,0BAA0B,CAACF,MAAM,CAAC,CAACG,KAAK,CAACF,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,CAAC;AACnF;AACA,MAAMiD,aAAa,GAAG,EAAE;AACxB,MAAMF,0BAA0B,CAAC;EAC7BG,OAAO;EACPC,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAF,KAAKA,CAACF,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,EAAE;IAC9B,MAAMoD,OAAO,GAAG,IAAIC,0BAA0B,CAACvE,MAAM,CAAC;IACtD,IAAI,CAACwE,6BAA6B,CAACF,OAAO,CAAC;IAC3C,MAAMG,GAAG,GAAIrK,YAAY,CAAC,IAAI,EAAEgB,uBAAuB,CAAC4I,QAAQ,CAAC,EAAEM,OAAO,CAAE;IAC5E,IAAI,OAAO/E,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI+E,OAAO,CAACI,6BAA6B,CAACC,IAAI,EAAE;QAC5C5C,iCAAiC,CAACb,QAAQ,EAAE,CACxC,GAAGoD,OAAO,CAACI,6BAA6B,CAACE,IAAI,CAAC,CAAC,CAClD,CAAC;MACN;IACJ;IACA,OAAOH,GAAG;EACd;EACAD,6BAA6BA,CAACF,OAAO,EAAE;IACnCA,OAAO,CAACO,oBAAoB,GAAGV,aAAa;IAC5CG,OAAO,CAACQ,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnCT,OAAO,CAACQ,eAAe,CAACE,GAAG,CAACb,aAAa,EAAE,IAAIY,GAAG,CAAC,CAAC,CAAC;IACrDT,OAAO,CAACW,WAAW,GAAG,CAAC;EAC3B;EACAC,YAAYA,CAAClB,QAAQ,EAAEM,OAAO,EAAE;IAC5B,IAAIa,UAAU,GAAIb,OAAO,CAACa,UAAU,GAAG,CAAE;IACzC,IAAIC,QAAQ,GAAId,OAAO,CAACc,QAAQ,GAAG,CAAE;IACrC,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAItB,QAAQ,CAACnC,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAChCjB,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAC3G,cAAc,CAAC,CAAC,CAAC;IACzC;IACA2J,QAAQ,CAACwB,WAAW,CAAClD,OAAO,CAAEmD,GAAG,IAAK;MAClC,IAAI,CAACjB,6BAA6B,CAACF,OAAO,CAAC;MAC3C,IAAImB,GAAG,CAAChG,IAAI,IAAI/B,qBAAqB,CAACgI,KAAK,EAAE;QACzC,MAAMC,QAAQ,GAAGF,GAAG;QACpB,MAAM5D,IAAI,GAAG8D,QAAQ,CAAC9D,IAAI;QAC1BA,IAAI,CACCnB,QAAQ,CAAC,CAAC,CACV2B,KAAK,CAAC,SAAS,CAAC,CAChBC,OAAO,CAAEsD,CAAC,IAAK;UAChBD,QAAQ,CAAC9D,IAAI,GAAG+D,CAAC;UACjBP,MAAM,CAACrE,IAAI,CAAC,IAAI,CAAC6E,UAAU,CAACF,QAAQ,EAAErB,OAAO,CAAC,CAAC;QACnD,CAAC,CAAC;QACFqB,QAAQ,CAAC9D,IAAI,GAAGA,IAAI;MACxB,CAAC,MACI,IAAI4D,GAAG,CAAChG,IAAI,IAAI/B,qBAAqB,CAACoI,UAAU,EAAE;QACnD,MAAMC,UAAU,GAAG,IAAI,CAACC,eAAe,CAACP,GAAG,EAAEnB,OAAO,CAAC;QACrDa,UAAU,IAAIY,UAAU,CAACZ,UAAU;QACnCC,QAAQ,IAAIW,UAAU,CAACX,QAAQ;QAC/BE,WAAW,CAACtE,IAAI,CAAC+E,UAAU,CAAC;MAChC,CAAC,MACI;QACDzB,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAC1G,iBAAiB,CAAC,CAAC,CAAC;MAC5C;IACJ,CAAC,CAAC;IACF,OAAO;MACHmF,IAAI,EAAE/B,qBAAqB,CAACuI,OAAO;MACnCpE,IAAI,EAAEmC,QAAQ,CAACnC,IAAI;MACnBwD,MAAM;MACNC,WAAW;MACXH,UAAU;MACVC,QAAQ;MACRc,OAAO,EAAE;IACb,CAAC;EACL;EACAL,UAAUA,CAAC7B,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAM6B,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACpC,QAAQ,CAACqC,MAAM,EAAE/B,OAAO,CAAC;IAC1D,MAAMgC,SAAS,GAAItC,QAAQ,CAACkC,OAAO,IAAIlC,QAAQ,CAACkC,OAAO,CAACK,MAAM,IAAK,IAAI;IACvE,IAAIJ,QAAQ,CAACK,qBAAqB,EAAE;MAChC,MAAMC,WAAW,GAAG,IAAInG,GAAG,CAAC,CAAC;MAC7B,MAAMiG,MAAM,GAAGD,SAAS,IAAI,CAAC,CAAC;MAC9BH,QAAQ,CAACE,MAAM,CAAC/D,OAAO,CAAE3E,KAAK,IAAK;QAC/B,IAAIA,KAAK,YAAYoH,GAAG,EAAE;UACtBpH,KAAK,CAAC2E,OAAO,CAAElC,KAAK,IAAK;YACrB7F,kBAAkB,CAAC6F,KAAK,CAAC,CAACkC,OAAO,CAAEoE,GAAG,IAAK;cACvC,IAAI,CAACH,MAAM,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;gBAC7BD,WAAW,CAACG,GAAG,CAACF,GAAG,CAAC;cACxB;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACF,IAAID,WAAW,CAAC9B,IAAI,EAAE;QAClBL,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACxG,YAAY,CAACwJ,QAAQ,CAACnC,IAAI,EAAE,CAAC,GAAG4E,WAAW,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/E;IACJ;IACA,OAAO;MACHpH,IAAI,EAAE/B,qBAAqB,CAACgI,KAAK;MACjC7D,IAAI,EAAEmC,QAAQ,CAACnC,IAAI;MACnBlE,KAAK,EAAEwI,QAAQ;MACfD,OAAO,EAAEI,SAAS,GAAG;QAAEC,MAAM,EAAED;MAAU,CAAC,GAAG;IACjD,CAAC;EACL;EACAN,eAAeA,CAAChC,QAAQ,EAAEM,OAAO,EAAE;IAC/BA,OAAO,CAACa,UAAU,GAAG,CAAC;IACtBb,OAAO,CAACc,QAAQ,GAAG,CAAC;IACpB,MAAM0B,SAAS,GAAG1M,YAAY,CAAC,IAAI,EAAEgB,uBAAuB,CAAC4I,QAAQ,CAAC8C,SAAS,CAAC,EAAExC,OAAO,CAAC;IAC1F,MAAMyC,QAAQ,GAAG7E,mBAAmB,CAAC8B,QAAQ,CAACgD,IAAI,EAAE1C,OAAO,CAACtE,MAAM,CAAC;IACnE,OAAO;MACHP,IAAI,EAAE/B,qBAAqB,CAACoI,UAAU;MACtCiB,QAAQ;MACRD,SAAS;MACT3B,UAAU,EAAEb,OAAO,CAACa,UAAU;MAC9BC,QAAQ,EAAEd,OAAO,CAACc,QAAQ;MAC1Bc,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACAgB,aAAaA,CAAClD,QAAQ,EAAEM,OAAO,EAAE;IAC7B,OAAO;MACH7E,IAAI,EAAE/B,qBAAqB,CAACyJ,QAAQ;MACpCC,KAAK,EAAEpD,QAAQ,CAACoD,KAAK,CAAC9F,GAAG,CAAE+F,CAAC,IAAKjN,YAAY,CAAC,IAAI,EAAEiN,CAAC,EAAE/C,OAAO,CAAC,CAAC;MAChE4B,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACAoB,UAAUA,CAACtD,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAMW,WAAW,GAAGX,OAAO,CAACW,WAAW;IACvC,IAAIsC,YAAY,GAAG,CAAC;IACpB,MAAMH,KAAK,GAAGpD,QAAQ,CAACoD,KAAK,CAAC9F,GAAG,CAAEkG,IAAI,IAAK;MACvClD,OAAO,CAACW,WAAW,GAAGA,WAAW;MACjC,MAAMwC,QAAQ,GAAGrN,YAAY,CAAC,IAAI,EAAEoN,IAAI,EAAElD,OAAO,CAAC;MAClDiD,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEjD,OAAO,CAACW,WAAW,CAAC;MAC1D,OAAOwC,QAAQ;IACnB,CAAC,CAAC;IACFnD,OAAO,CAACW,WAAW,GAAGsC,YAAY;IAClC,OAAO;MACH9H,IAAI,EAAE/B,qBAAqB,CAACkK,KAAK;MACjCR,KAAK;MACLlB,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACA2B,YAAYA,CAAC7D,QAAQ,EAAEM,OAAO,EAAE;IAC5B,MAAMwD,SAAS,GAAGC,kBAAkB,CAAC/D,QAAQ,CAACgE,OAAO,EAAE1D,OAAO,CAACtE,MAAM,CAAC;IACtEsE,OAAO,CAAC2D,qBAAqB,GAAGH,SAAS;IACzC,IAAI3B,QAAQ;IACZ,IAAI+B,aAAa,GAAGlE,QAAQ,CAACqC,MAAM,GAC7BrC,QAAQ,CAACqC,MAAM,GACf1I,KAAK,CAAC,CAAC,CAAC,CAAC;IACf,IAAIuK,aAAa,CAACzI,IAAI,IAAI/B,qBAAqB,CAACyK,SAAS,EAAE;MACvDhC,QAAQ,GAAG,IAAI,CAACiC,cAAc,CAACF,aAAa,EAAE5D,OAAO,CAAC;IAC1D,CAAC,MACI;MACD,IAAI4D,aAAa,GAAGlE,QAAQ,CAACqC,MAAM;MACnC,IAAIgC,OAAO,GAAG,KAAK;MACnB,IAAI,CAACH,aAAa,EAAE;QAChBG,OAAO,GAAG,IAAI;QACd,MAAMC,YAAY,GAAG,CAAC,CAAC;QACvB,IAAIR,SAAS,CAACjJ,MAAM,EAAE;UAClByJ,YAAY,CAAC,QAAQ,CAAC,GAAGR,SAAS,CAACjJ,MAAM;QAC7C;QACAqJ,aAAa,GAAGvK,KAAK,CAAC2K,YAAY,CAAC;MACvC;MACAhE,OAAO,CAACW,WAAW,IAAI6C,SAAS,CAACnJ,QAAQ,GAAGmJ,SAAS,CAAClJ,KAAK;MAC3D,MAAM2J,SAAS,GAAG,IAAI,CAACnC,UAAU,CAAC8B,aAAa,EAAE5D,OAAO,CAAC;MACzDiE,SAAS,CAACC,WAAW,GAAGH,OAAO;MAC/BlC,QAAQ,GAAGoC,SAAS;IACxB;IACAjE,OAAO,CAAC2D,qBAAqB,GAAG,IAAI;IACpC,OAAO;MACHxI,IAAI,EAAE/B,qBAAqB,CAAC+K,OAAO;MACnCT,OAAO,EAAEF,SAAS;MAClBnK,KAAK,EAAEwI,QAAQ;MACfD,OAAO,EAAE;IACb,CAAC;EACL;EACAE,UAAUA,CAACpC,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAMG,GAAG,GAAG,IAAI,CAACiE,aAAa,CAAC1E,QAAQ,EAAEM,OAAO,CAAC;IACjD,IAAI,CAACqE,iBAAiB,CAAClE,GAAG,EAAEH,OAAO,CAAC;IACpC,OAAOG,GAAG;EACd;EACAiE,aAAaA,CAAC1E,QAAQ,EAAEM,OAAO,EAAE;IAC7B,MAAM+B,MAAM,GAAG,EAAE;IACjB,MAAMuC,cAAc,GAAGC,KAAK,CAACC,OAAO,CAAC9E,QAAQ,CAACqC,MAAM,CAAC,GAAGrC,QAAQ,CAACqC,MAAM,GAAG,CAACrC,QAAQ,CAACqC,MAAM,CAAC;IAC3F,KAAK,IAAI0C,UAAU,IAAIH,cAAc,EAAE;MACnC,IAAI,OAAOG,UAAU,KAAK,QAAQ,EAAE;QAChC,IAAIA,UAAU,KAAKnL,UAAU,EAAE;UAC3ByI,MAAM,CAACrF,IAAI,CAAC+H,UAAU,CAAC;QAC3B,CAAC,MACI;UACDzE,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACvG,iBAAiB,CAACsO,UAAU,CAAC,CAAC;QACtD;MACJ,CAAC,MACI;QACD1C,MAAM,CAACrF,IAAI,CAAC,IAAI+D,GAAG,CAACiE,MAAM,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC;MACpD;IACJ;IACA,IAAIvC,qBAAqB,GAAG,KAAK;IACjC,IAAI0C,eAAe,GAAG,IAAI;IAC1B7C,MAAM,CAAC/D,OAAO,CAAE6G,SAAS,IAAK;MAC1B,IAAIA,SAAS,YAAYpE,GAAG,EAAE;QAC1B,IAAIoE,SAAS,CAACvI,GAAG,CAAC,QAAQ,CAAC,EAAE;UACzBsI,eAAe,GAAGC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;UACzCD,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;QAC9B;QACA,IAAI,CAAC7C,qBAAqB,EAAE;UACxB,KAAK,IAAIpG,KAAK,IAAI+I,SAAS,CAACtC,MAAM,CAAC,CAAC,EAAE;YAClC,IAAIzG,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC4I,OAAO,CAAC5O,uBAAuB,CAAC,IAAI,CAAC,EAAE;cACxD8L,qBAAqB,GAAG,IAAI;cAC5B;YACJ;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,OAAO;MACH/G,IAAI,EAAE/B,qBAAqB,CAAC6L,KAAK;MACjClD,MAAM;MACNxH,MAAM,EAAEqK,eAAe;MACvBM,MAAM,EAAExF,QAAQ,CAACwF,MAAM;MACvBhD,qBAAqB;MACrBN,OAAO,EAAE;IACb,CAAC;EACL;EACAyC,iBAAiBA,CAAClE,GAAG,EAAEH,OAAO,EAAE;IAC5B,MAAM0D,OAAO,GAAG1D,OAAO,CAAC2D,qBAAqB;IAC7C,IAAIwB,OAAO,GAAGnF,OAAO,CAACW,WAAW;IACjC,IAAIyE,SAAS,GAAGpF,OAAO,CAACW,WAAW;IACnC,IAAI+C,OAAO,IAAI0B,SAAS,GAAG,CAAC,EAAE;MAC1BA,SAAS,IAAI1B,OAAO,CAACrJ,QAAQ,GAAGqJ,OAAO,CAACpJ,KAAK;IACjD;IACA6F,GAAG,CAAC4B,MAAM,CAAC/D,OAAO,CAAEqH,KAAK,IAAK;MAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACzB;MACJA,KAAK,CAACrH,OAAO,CAAC,CAAClC,KAAK,EAAEnC,IAAI,KAAK;QAC3B,IAAI,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAAC,IAAI,CAAC6E,OAAO,CAACxK,qBAAqB,CAACqE,IAAI,CAAC,EAAE;YAC3C0L,KAAK,CAACN,MAAM,CAACpL,IAAI,CAAC;YAClBqG,OAAO,CAACI,6BAA6B,CAACkC,GAAG,CAAC3I,IAAI,CAAC;YAC/C;UACJ;QACJ;QACA;QACA;QACA,MAAM6G,eAAe,GAAGR,OAAO,CAACQ,eAAe,CAACsE,GAAG,CAAC9E,OAAO,CAACO,oBAAoB,CAAC;QACjF,MAAM+E,cAAc,GAAG9E,eAAe,CAACsE,GAAG,CAACnL,IAAI,CAAC;QAChD,IAAI4L,oBAAoB,GAAG,IAAI;QAC/B,IAAID,cAAc,EAAE;UAChB,IAAIF,SAAS,IAAID,OAAO,IACpBC,SAAS,IAAIE,cAAc,CAACF,SAAS,IACrCD,OAAO,IAAIG,cAAc,CAACH,OAAO,EAAE;YACnCnF,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACrG,wBAAwB,CAACsD,IAAI,EAAE2L,cAAc,CAACF,SAAS,EAAEE,cAAc,CAACH,OAAO,EAAEC,SAAS,EAAED,OAAO,CAAC,CAAC;YACzHI,oBAAoB,GAAG,KAAK;UAChC;UACA;UACA;UACA;UACAH,SAAS,GAAGE,cAAc,CAACF,SAAS;QACxC;QACA,IAAIG,oBAAoB,EAAE;UACtB/E,eAAe,CAACE,GAAG,CAAC/G,IAAI,EAAE;YAAEyL,SAAS;YAAED;UAAQ,CAAC,CAAC;QACrD;QACA,IAAInF,OAAO,CAAC4B,OAAO,EAAE;UACjBtL,mBAAmB,CAACwF,KAAK,EAAEkE,OAAO,CAAC4B,OAAO,EAAE5B,OAAO,CAACtE,MAAM,CAAC;QAC/D;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAoI,cAAcA,CAACpE,QAAQ,EAAEM,OAAO,EAAE;IAC9B,MAAMG,GAAG,GAAG;MAAEhF,IAAI,EAAE/B,qBAAqB,CAACyK,SAAS;MAAE9B,MAAM,EAAE,EAAE;MAAEH,OAAO,EAAE;IAAK,CAAC;IAChF,IAAI,CAAC5B,OAAO,CAAC2D,qBAAqB,EAAE;MAChC3D,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACnG,gBAAgB,CAAC,CAAC,CAAC;MACvC,OAAO4J,GAAG;IACd;IACA,MAAMqF,mBAAmB,GAAG,CAAC;IAC7B,IAAIC,yBAAyB,GAAG,CAAC;IACjC,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,cAAc,GAAG,CAAC;IACtB,MAAMzL,SAAS,GAAGsF,QAAQ,CAACoD,KAAK,CAAC9F,GAAG,CAAE+E,MAAM,IAAK;MAC7C,MAAM1I,KAAK,GAAG,IAAI,CAAC+K,aAAa,CAACrC,MAAM,EAAE/B,OAAO,CAAC;MACjD,IAAI8F,SAAS,GAAGzM,KAAK,CAAC6L,MAAM,IAAI,IAAI,GAAG7L,KAAK,CAAC6L,MAAM,GAAGa,aAAa,CAAC1M,KAAK,CAAC0I,MAAM,CAAC;MACjF,IAAImD,MAAM,GAAG,CAAC;MACd,IAAIY,SAAS,IAAI,IAAI,EAAE;QACnBL,yBAAyB,EAAE;QAC3BP,MAAM,GAAG7L,KAAK,CAAC6L,MAAM,GAAGY,SAAS;MACrC;MACAF,mBAAmB,GAAGA,mBAAmB,IAAIV,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,CAAC;MACrES,iBAAiB,GAAGA,iBAAiB,IAAIT,MAAM,GAAGW,cAAc;MAChEA,cAAc,GAAGX,MAAM;MACvBQ,OAAO,CAAChJ,IAAI,CAACwI,MAAM,CAAC;MACpB,OAAO7L,KAAK;IAChB,CAAC,CAAC;IACF,IAAIuM,mBAAmB,EAAE;MACrB5F,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAClG,aAAa,CAAC,CAAC,CAAC;IACxC;IACA,IAAImP,iBAAiB,EAAE;MACnB3F,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACjG,yBAAyB,CAAC,CAAC,CAAC;IACpD;IACA,MAAMgG,MAAM,GAAGiD,QAAQ,CAACoD,KAAK,CAACrG,MAAM;IACpC,IAAIuJ,eAAe,GAAG,CAAC;IACvB,IAAIP,yBAAyB,GAAG,CAAC,IAAIA,yBAAyB,GAAGhJ,MAAM,EAAE;MACrEuD,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAChG,uBAAuB,CAAC,CAAC,CAAC;IAClD,CAAC,MACI,IAAI+O,yBAAyB,IAAI,CAAC,EAAE;MACrCO,eAAe,GAAGR,mBAAmB,IAAI/I,MAAM,GAAG,CAAC,CAAC;IACxD;IACA,MAAMwJ,KAAK,GAAGxJ,MAAM,GAAG,CAAC;IACxB,MAAMkE,WAAW,GAAGX,OAAO,CAACW,WAAW;IACvC,MAAMgD,qBAAqB,GAAG3D,OAAO,CAAC2D,qBAAqB;IAC3D,MAAMuC,eAAe,GAAGvC,qBAAqB,CAACtJ,QAAQ;IACtDD,SAAS,CAAC4D,OAAO,CAAC,CAACmI,EAAE,EAAEC,CAAC,KAAK;MACzB,MAAMlB,MAAM,GAAGc,eAAe,GAAG,CAAC,GAAII,CAAC,IAAIH,KAAK,GAAG,CAAC,GAAGD,eAAe,GAAGI,CAAC,GAAIV,OAAO,CAACU,CAAC,CAAC;MACxF,MAAMC,qBAAqB,GAAGnB,MAAM,GAAGgB,eAAe;MACtDlG,OAAO,CAACW,WAAW,GAAGA,WAAW,GAAGgD,qBAAqB,CAACrJ,KAAK,GAAG+L,qBAAqB;MACvF1C,qBAAqB,CAACtJ,QAAQ,GAAGgM,qBAAqB;MACtD,IAAI,CAAChC,iBAAiB,CAAC8B,EAAE,EAAEnG,OAAO,CAAC;MACnCmG,EAAE,CAACjB,MAAM,GAAGA,MAAM;MAClB/E,GAAG,CAAC4B,MAAM,CAACrF,IAAI,CAACyJ,EAAE,CAAC;IACvB,CAAC,CAAC;IACF,OAAOhG,GAAG;EACd;EACAmG,cAAcA,CAAC5G,QAAQ,EAAEM,OAAO,EAAE;IAC9B,OAAO;MACH7E,IAAI,EAAE/B,qBAAqB,CAACmN,SAAS;MACrC/D,SAAS,EAAE1M,YAAY,CAAC,IAAI,EAAEgB,uBAAuB,CAAC4I,QAAQ,CAAC8C,SAAS,CAAC,EAAExC,OAAO,CAAC;MACnF4B,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACA4E,iBAAiBA,CAAC9G,QAAQ,EAAEM,OAAO,EAAE;IACjCA,OAAO,CAACc,QAAQ,EAAE;IAClB,OAAO;MACH3F,IAAI,EAAE/B,qBAAqB,CAACqN,YAAY;MACxC7E,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACA8E,eAAeA,CAAChH,QAAQ,EAAEM,OAAO,EAAE;IAC/B,OAAO;MACH7E,IAAI,EAAE/B,qBAAqB,CAACuN,UAAU;MACtCnE,SAAS,EAAE,IAAI,CAAC8D,cAAc,CAAC5G,QAAQ,CAAC8C,SAAS,EAAExC,OAAO,CAAC;MAC3D4B,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACAgF,UAAUA,CAAClH,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAM6G,cAAc,GAAG7G,OAAO,CAACO,oBAAoB;IACnD,MAAMqB,OAAO,GAAIlC,QAAQ,CAACkC,OAAO,IAAI,CAAC,CAAE;IACxC5B,OAAO,CAACa,UAAU,EAAE;IACpBb,OAAO,CAAC8G,YAAY,GAAGpH,QAAQ;IAC/B,MAAM,CAAC1F,QAAQ,EAAE+M,WAAW,CAAC,GAAGC,iBAAiB,CAACtH,QAAQ,CAAC1F,QAAQ,CAAC;IACpEgG,OAAO,CAACO,oBAAoB,GAAGsG,cAAc,CAACpK,MAAM,GAC9CoK,cAAc,GAAG,GAAG,GAAG7M,QAAQ,GAC/BA,QAAQ;IACdrD,oBAAoB,CAACqJ,OAAO,CAACQ,eAAe,EAAER,OAAO,CAACO,oBAAoB,EAAE,IAAIE,GAAG,CAAC,CAAC,CAAC;IACtF,MAAM+B,SAAS,GAAG1M,YAAY,CAAC,IAAI,EAAEgB,uBAAuB,CAAC4I,QAAQ,CAAC8C,SAAS,CAAC,EAAExC,OAAO,CAAC;IAC1FA,OAAO,CAAC8G,YAAY,GAAG,IAAI;IAC3B9G,OAAO,CAACO,oBAAoB,GAAGsG,cAAc;IAC7C,OAAO;MACH1L,IAAI,EAAE/B,qBAAqB,CAAC6N,KAAK;MACjCjN,QAAQ;MACRiM,KAAK,EAAErE,OAAO,CAACqE,KAAK,IAAI,CAAC;MACzBiB,QAAQ,EAAE,CAAC,CAACtF,OAAO,CAACsF,QAAQ;MAC5BH,WAAW;MACXvE,SAAS;MACT2E,gBAAgB,EAAEzH,QAAQ,CAAC1F,QAAQ;MACnC4H,OAAO,EAAEe,yBAAyB,CAACjD,QAAQ,CAACkC,OAAO;IACvD,CAAC;EACL;EACAwF,YAAYA,CAAC1H,QAAQ,EAAEM,OAAO,EAAE;IAC5B,IAAI,CAACA,OAAO,CAAC8G,YAAY,EAAE;MACvB9G,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAC9F,cAAc,CAAC,CAAC,CAAC;IACzC;IACA,MAAM8M,OAAO,GAAGhE,QAAQ,CAACgE,OAAO,KAAK,MAAM,GACrC;MAAErJ,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAO,CAAC,GACzC1D,aAAa,CAAC6I,QAAQ,CAACgE,OAAO,EAAE1D,OAAO,CAACtE,MAAM,EAAE,IAAI,CAAC;IAC3D,OAAO;MACHP,IAAI,EAAE/B,qBAAqB,CAACiO,OAAO;MACnC7E,SAAS,EAAE1M,YAAY,CAAC,IAAI,EAAEgB,uBAAuB,CAAC4I,QAAQ,CAAC8C,SAAS,CAAC,EAAExC,OAAO,CAAC;MACnF0D,OAAO;MACP9B,OAAO,EAAE;IACb,CAAC;EACL;AACJ;AACA,SAASoF,iBAAiBA,CAAChN,QAAQ,EAAE;EACjC,MAAMsN,YAAY,GAAGtN,QAAQ,CAAC+D,KAAK,CAAC,SAAS,CAAC,CAACwJ,IAAI,CAAExM,KAAK,IAAKA,KAAK,IAAIsE,UAAU,CAAC,GAC7E,IAAI,GACJ,KAAK;EACX,IAAIiI,YAAY,EAAE;IACdtN,QAAQ,GAAGA,QAAQ,CAACwN,OAAO,CAAClI,gBAAgB,EAAE,EAAE,CAAC;EACrD;EACA;EACA;EACAtF,QAAQ,GAAGA,QAAQ,CACdwN,OAAO,CAAC,MAAM,EAAEzQ,mBAAmB,CAAC,CACpCyQ,OAAO,CAAC,OAAO,EAAGhL,KAAK,IAAKzF,mBAAmB,GAAG,GAAG,GAAGyF,KAAK,CAACiL,KAAK,CAAC,CAAC,CAAC,CAAC,CACvED,OAAO,CAAC,aAAa,EAAExQ,qBAAqB,CAAC;EAClD,OAAO,CAACgD,QAAQ,EAAEsN,YAAY,CAAC;AACnC;AACA,SAASI,eAAeA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,GAAG;IAAE,GAAGA;EAAI,CAAC,GAAG,IAAI;AAClC;AACA,MAAM1H,0BAA0B,CAAC;EAC7BvE,MAAM;EACNmF,UAAU,GAAG,CAAC;EACdC,QAAQ,GAAG,CAAC;EACZ8G,iBAAiB,GAAG,IAAI;EACxBd,YAAY,GAAG,IAAI;EACnBvG,oBAAoB,GAAG,IAAI;EAC3BoD,qBAAqB,GAAG,IAAI;EAC5BhD,WAAW,GAAG,CAAC;EACfH,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3BmB,OAAO,GAAG,IAAI;EACdxB,6BAA6B,GAAG,IAAIpE,GAAG,CAAC,CAAC;EACzC+D,WAAWA,CAACrE,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,SAASqK,aAAaA,CAAChE,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,IAAI,QAAQ,EACzB,OAAO,IAAI;EACf,IAAImD,MAAM,GAAG,IAAI;EACjB,IAAIX,KAAK,CAACC,OAAO,CAACzC,MAAM,CAAC,EAAE;IACvBA,MAAM,CAAC/D,OAAO,CAAEyG,UAAU,IAAK;MAC3B,IAAIA,UAAU,YAAYhE,GAAG,IAAIgE,UAAU,CAACnI,GAAG,CAAC,QAAQ,CAAC,EAAE;QACvD,MAAMqL,GAAG,GAAGlD,UAAU;QACtBS,MAAM,GAAGtG,UAAU,CAAC+I,GAAG,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC6C,GAAG,CAAC5C,MAAM,CAAC,QAAQ,CAAC;MACxB;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAIhD,MAAM,YAAYtB,GAAG,IAAIsB,MAAM,CAACzF,GAAG,CAAC,QAAQ,CAAC,EAAE;IACpD,MAAMqL,GAAG,GAAG5F,MAAM;IAClBmD,MAAM,GAAGtG,UAAU,CAAC+I,GAAG,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC6C,GAAG,CAAC5C,MAAM,CAAC,QAAQ,CAAC;EACxB;EACA,OAAOG,MAAM;AACjB;AACA,SAASzB,kBAAkBA,CAAC3H,KAAK,EAAEJ,MAAM,EAAE;EACvC,IAAII,KAAK,CAACuG,cAAc,CAAC,UAAU,CAAC,EAAE;IAClC,OAAOvG,KAAK;EAChB;EACA,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;IAC1B,MAAMzB,QAAQ,GAAGxD,aAAa,CAACiF,KAAK,EAAEJ,MAAM,CAAC,CAACrB,QAAQ;IACtD,OAAOwN,aAAa,CAACxN,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;EACzC;EACA,MAAMyN,QAAQ,GAAGhM,KAAK;EACtB,MAAMiM,SAAS,GAAGD,QAAQ,CAAC/J,KAAK,CAAC,KAAK,CAAC,CAACiK,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAChH,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIgH,CAAC,CAAChH,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;EAC7F,IAAI8G,SAAS,EAAE;IACX,MAAM5H,GAAG,GAAG0H,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACnC1H,GAAG,CAAC+H,OAAO,GAAG,IAAI;IAClB/H,GAAG,CAAC2H,QAAQ,GAAGA,QAAQ;IACvB,OAAO3H,GAAG;EACd;EACA,MAAMuD,OAAO,GAAG7M,aAAa,CAACiR,QAAQ,EAAEpM,MAAM,CAAC;EAC/C,OAAOmM,aAAa,CAACnE,OAAO,CAACrJ,QAAQ,EAAEqJ,OAAO,CAACpJ,KAAK,EAAEoJ,OAAO,CAACnJ,MAAM,CAAC;AACzE;AACA,SAASoI,yBAAyBA,CAACf,OAAO,EAAE;EACxC,IAAIA,OAAO,EAAE;IACTA,OAAO,GAAG;MAAE,GAAGA;IAAQ,CAAC;IACxB,IAAIA,OAAO,CAAC,QAAQ,CAAC,EAAE;MACnBA,OAAO,CAAC,QAAQ,CAAC,GAAG8F,eAAe,CAAC9F,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1D;EACJ,CAAC,MACI;IACDA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,OAAOA,OAAO;AAClB;AACA,SAASiG,aAAaA,CAACxN,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC5C,OAAO;IAAEF,QAAQ;IAAEC,KAAK;IAAEC;EAAO,CAAC;AACtC;AAEA,SAAS4N,yBAAyBA,CAACrO,OAAO,EAAEM,SAAS,EAAEgO,aAAa,EAAEC,cAAc,EAAEhO,QAAQ,EAAEC,KAAK,EAAEC,MAAM,GAAG,IAAI,EAAE+N,WAAW,GAAG,KAAK,EAAE;EACvI,OAAO;IACHnN,IAAI,EAAE,CAAC,CAAC;IACRrB,OAAO;IACPM,SAAS;IACTgO,aAAa;IACbC,cAAc;IACdhO,QAAQ;IACRC,KAAK;IACLiO,SAAS,EAAElO,QAAQ,GAAGC,KAAK;IAC3BC,MAAM;IACN+N;EACJ,CAAC;AACL;AAEA,MAAME,qBAAqB,CAAC;EACxBC,IAAI,GAAG,IAAIhI,GAAG,CAAC,CAAC;EAChBqE,GAAGA,CAAChL,OAAO,EAAE;IACT,OAAO,IAAI,CAAC2O,IAAI,CAAC3D,GAAG,CAAChL,OAAO,CAAC,IAAI,EAAE;EACvC;EACA4O,MAAMA,CAAC5O,OAAO,EAAE6O,YAAY,EAAE;IAC1B,IAAIC,oBAAoB,GAAG,IAAI,CAACH,IAAI,CAAC3D,GAAG,CAAChL,OAAO,CAAC;IACjD,IAAI,CAAC8O,oBAAoB,EAAE;MACvB,IAAI,CAACH,IAAI,CAAC/H,GAAG,CAAC5G,OAAO,EAAG8O,oBAAoB,GAAG,EAAG,CAAC;IACvD;IACAA,oBAAoB,CAAClM,IAAI,CAAC,GAAGiM,YAAY,CAAC;EAC9C;EACArM,GAAGA,CAACxC,OAAO,EAAE;IACT,OAAO,IAAI,CAAC2O,IAAI,CAACnM,GAAG,CAACxC,OAAO,CAAC;EACjC;EACA+O,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACJ,IAAI,CAACI,KAAK,CAAC,CAAC;EACrB;AACJ;AAEA,MAAMC,yBAAyB,GAAG,CAAC;AACnC,MAAMC,WAAW,GAAG,QAAQ;AAC5B,MAAMC,iBAAiB,GAAG,eAAgB,IAAIzJ,MAAM,CAACwJ,WAAW,EAAE,GAAG,CAAC;AACtE,MAAME,WAAW,GAAG,QAAQ;AAC5B,MAAMC,iBAAiB,GAAG,eAAgB,IAAI3J,MAAM,CAAC0J,WAAW,EAAE,GAAG,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAAC1J,MAAM,EAAE2J,WAAW,EAAEjJ,GAAG,EAAEkJ,cAAc,EAAEC,cAAc,EAAEC,cAAc,GAAG,IAAI9I,GAAG,CAAC,CAAC,EAAE+I,WAAW,GAAG,IAAI/I,GAAG,CAAC,CAAC,EAAEmB,OAAO,EAAE6H,eAAe,EAAE/N,MAAM,GAAG,EAAE,EAAE;EACnL,OAAO,IAAIgO,+BAA+B,CAAC,CAAC,CAACC,cAAc,CAAClK,MAAM,EAAE2J,WAAW,EAAEjJ,GAAG,EAAEkJ,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE5H,OAAO,EAAE6H,eAAe,EAAE/N,MAAM,CAAC;AACxL;AACA,MAAMgO,+BAA+B,CAAC;EAClCC,cAAcA,CAAClK,MAAM,EAAE2J,WAAW,EAAEjJ,GAAG,EAAEkJ,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE5H,OAAO,EAAE6H,eAAe,EAAE/N,MAAM,GAAG,EAAE,EAAE;IACzI+N,eAAe,GAAGA,eAAe,IAAI,IAAIjB,qBAAqB,CAAC,CAAC;IAChE,MAAMxI,OAAO,GAAG,IAAI4J,wBAAwB,CAACnK,MAAM,EAAE2J,WAAW,EAAEK,eAAe,EAAEJ,cAAc,EAAEC,cAAc,EAAE5N,MAAM,EAAE,EAAE,CAAC;IAC9HsE,OAAO,CAAC4B,OAAO,GAAGA,OAAO;IACzB,MAAMtH,KAAK,GAAGsH,OAAO,CAACtH,KAAK,GAAGrD,kBAAkB,CAAC2K,OAAO,CAACtH,KAAK,CAAC,GAAG,CAAC;IACnE0F,OAAO,CAAC6J,eAAe,CAACC,aAAa,CAACxP,KAAK,CAAC;IAC5C0F,OAAO,CAAC6J,eAAe,CAAC7R,SAAS,CAAC,CAACuR,cAAc,CAAC,EAAE,IAAI,EAAEvJ,OAAO,CAACtE,MAAM,EAAEkG,OAAO,CAAC;IAClF9L,YAAY,CAAC,IAAI,EAAEqK,GAAG,EAAEH,OAAO,CAAC;IAChC;IACA,MAAM+J,SAAS,GAAG/J,OAAO,CAAC+J,SAAS,CAACjN,MAAM,CAAEkN,QAAQ,IAAKA,QAAQ,CAACC,iBAAiB,CAAC,CAAC,CAAC;IACtF;IACA;IACA;IACA;IACA,IAAIF,SAAS,CAACtN,MAAM,IAAI+M,WAAW,CAACnJ,IAAI,EAAE;MACtC,IAAI6J,gBAAgB;MACpB,KAAK,IAAI9D,CAAC,GAAG2D,SAAS,CAACtN,MAAM,GAAG,CAAC,EAAE2J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,MAAM4D,QAAQ,GAAGD,SAAS,CAAC3D,CAAC,CAAC;QAC7B,IAAI4D,QAAQ,CAAClQ,OAAO,KAAKsP,WAAW,EAAE;UAClCc,gBAAgB,GAAGF,QAAQ;UAC3B;QACJ;MACJ;MACA,IAAIE,gBAAgB,IAAI,CAACA,gBAAgB,CAACC,uBAAuB,CAAC,CAAC,EAAE;QACjED,gBAAgB,CAAClS,SAAS,CAAC,CAACwR,WAAW,CAAC,EAAE,IAAI,EAAExJ,OAAO,CAACtE,MAAM,EAAEkG,OAAO,CAAC;MAC5E;IACJ;IACA,OAAOmI,SAAS,CAACtN,MAAM,GACjBsN,SAAS,CAAC/M,GAAG,CAAEgN,QAAQ,IAAKA,QAAQ,CAACL,cAAc,CAAC,CAAC,CAAC,GACtD,CAACxB,yBAAyB,CAACiB,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE9O,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;EACnF;EACAsG,YAAYA,CAACT,GAAG,EAAEH,OAAO,EAAE;IACvB;EAAA;EAEJuB,UAAUA,CAACpB,GAAG,EAAEH,OAAO,EAAE;IACrB;EAAA;EAEJ0B,eAAeA,CAACvB,GAAG,EAAEH,OAAO,EAAE;IAC1B;EAAA;EAEJwG,iBAAiBA,CAACrG,GAAG,EAAEH,OAAO,EAAE;IAC5B,MAAMoK,mBAAmB,GAAGpK,OAAO,CAACyJ,eAAe,CAAC3E,GAAG,CAAC9E,OAAO,CAAClG,OAAO,CAAC;IACxE,IAAIsQ,mBAAmB,EAAE;MACrB,MAAMC,YAAY,GAAGrK,OAAO,CAACsK,gBAAgB,CAACnK,GAAG,CAACyB,OAAO,CAAC;MAC1D,MAAMwD,SAAS,GAAGpF,OAAO,CAAC6J,eAAe,CAAClJ,WAAW;MACrD,MAAMwE,OAAO,GAAG,IAAI,CAACoF,qBAAqB,CAACH,mBAAmB,EAAEC,YAAY,EAAEA,YAAY,CAACzI,OAAO,CAAC;MACnG,IAAIwD,SAAS,IAAID,OAAO,EAAE;QACtB;QACA;QACAnF,OAAO,CAACwK,wBAAwB,CAACrF,OAAO,CAAC;MAC7C;IACJ;IACAnF,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACAuG,eAAeA,CAACvG,GAAG,EAAEH,OAAO,EAAE;IAC1B,MAAMqK,YAAY,GAAGrK,OAAO,CAACsK,gBAAgB,CAACnK,GAAG,CAACyB,OAAO,CAAC;IAC1DyI,YAAY,CAACG,wBAAwB,CAAC,CAAC;IACvC,IAAI,CAACE,wBAAwB,CAAC,CAACvK,GAAG,CAACyB,OAAO,EAAEzB,GAAG,CAACqC,SAAS,CAACZ,OAAO,CAAC,EAAE5B,OAAO,EAAEqK,YAAY,CAAC;IAC1F,IAAI,CAAC/D,cAAc,CAACnG,GAAG,CAACqC,SAAS,EAAE6H,YAAY,CAAC;IAChDrK,OAAO,CAACwK,wBAAwB,CAACH,YAAY,CAACR,eAAe,CAAClJ,WAAW,CAAC;IAC1EX,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACAuK,wBAAwBA,CAACC,qBAAqB,EAAE3K,OAAO,EAAEqK,YAAY,EAAE;IACnE,KAAK,MAAMO,mBAAmB,IAAID,qBAAqB,EAAE;MACrD,MAAME,cAAc,GAAGD,mBAAmB,EAAEtQ,KAAK;MACjD,IAAIuQ,cAAc,EAAE;QAChB,MAAMC,mBAAmB,GAAG,OAAOD,cAAc,KAAK,QAAQ,GACxDA,cAAc,GACd5T,kBAAkB,CAACC,iBAAiB,CAAC2T,cAAc,EAAED,mBAAmB,EAAE3I,MAAM,IAAI,CAAC,CAAC,EAAEjC,OAAO,CAACtE,MAAM,CAAC,CAAC;QAC9G2O,YAAY,CAACP,aAAa,CAACgB,mBAAmB,CAAC;MACnD;IACJ;EACJ;EACAP,qBAAqBA,CAAC5B,YAAY,EAAE3I,OAAO,EAAE4B,OAAO,EAAE;IAClD,MAAMwD,SAAS,GAAGpF,OAAO,CAAC6J,eAAe,CAAClJ,WAAW;IACrD,IAAIsC,YAAY,GAAGmC,SAAS;IAC5B;IACA;IACA,MAAM/K,QAAQ,GAAGuH,OAAO,CAACvH,QAAQ,IAAI,IAAI,GAAGpD,kBAAkB,CAAC2K,OAAO,CAACvH,QAAQ,CAAC,GAAG,IAAI;IACvF,MAAMC,KAAK,GAAGsH,OAAO,CAACtH,KAAK,IAAI,IAAI,GAAGrD,kBAAkB,CAAC2K,OAAO,CAACtH,KAAK,CAAC,GAAG,IAAI;IAC9E,IAAID,QAAQ,KAAK,CAAC,EAAE;MAChBsO,YAAY,CAAC3K,OAAO,CAAE+M,WAAW,IAAK;QAClC,MAAMC,kBAAkB,GAAGhL,OAAO,CAACiL,2BAA2B,CAACF,WAAW,EAAE1Q,QAAQ,EAAEC,KAAK,CAAC;QAC5F2I,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAE+H,kBAAkB,CAAC3Q,QAAQ,GAAG2Q,kBAAkB,CAAC1Q,KAAK,CAAC;MACjG,CAAC,CAAC;IACN;IACA,OAAO2I,YAAY;EACvB;EACAqD,cAAcA,CAACnG,GAAG,EAAEH,OAAO,EAAE;IACzBA,OAAO,CAACkL,aAAa,CAAC/K,GAAG,CAACyB,OAAO,EAAE,IAAI,CAAC;IACxC9L,YAAY,CAAC,IAAI,EAAEqK,GAAG,CAACqC,SAAS,EAAExC,OAAO,CAAC;IAC1CA,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACAyC,aAAaA,CAACzC,GAAG,EAAEH,OAAO,EAAE;IACxB,MAAMmL,eAAe,GAAGnL,OAAO,CAACmL,eAAe;IAC/C,IAAIC,GAAG,GAAGpL,OAAO;IACjB,MAAM4B,OAAO,GAAGzB,GAAG,CAACyB,OAAO;IAC3B,IAAIA,OAAO,KAAKA,OAAO,CAACK,MAAM,IAAIL,OAAO,CAACtH,KAAK,CAAC,EAAE;MAC9C8Q,GAAG,GAAGpL,OAAO,CAACsK,gBAAgB,CAAC1I,OAAO,CAAC;MACvCwJ,GAAG,CAACZ,wBAAwB,CAAC,CAAC;MAC9B,IAAI5I,OAAO,CAACtH,KAAK,IAAI,IAAI,EAAE;QACvB,IAAI8Q,GAAG,CAACX,YAAY,CAACtP,IAAI,IAAI/B,qBAAqB,CAAC6L,KAAK,EAAE;UACtDmG,GAAG,CAACvB,eAAe,CAACwB,qBAAqB,CAAC,CAAC;UAC3CD,GAAG,CAACX,YAAY,GAAGa,0BAA0B;QACjD;QACA,MAAMhR,KAAK,GAAGrD,kBAAkB,CAAC2K,OAAO,CAACtH,KAAK,CAAC;QAC/C8Q,GAAG,CAACtB,aAAa,CAACxP,KAAK,CAAC;MAC5B;IACJ;IACA,IAAI6F,GAAG,CAAC2C,KAAK,CAACrG,MAAM,EAAE;MAClB0D,GAAG,CAAC2C,KAAK,CAAC9E,OAAO,CAAE+E,CAAC,IAAKjN,YAAY,CAAC,IAAI,EAAEiN,CAAC,EAAEqI,GAAG,CAAC,CAAC;MACpD;MACAA,GAAG,CAACvB,eAAe,CAAC0B,qBAAqB,CAAC,CAAC;MAC3C;MACA;MACA;MACA,IAAIH,GAAG,CAACD,eAAe,GAAGA,eAAe,EAAE;QACvCC,GAAG,CAACZ,wBAAwB,CAAC,CAAC;MAClC;IACJ;IACAxK,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACA6C,UAAUA,CAAC7C,GAAG,EAAEH,OAAO,EAAE;IACrB,MAAMwL,cAAc,GAAG,EAAE;IACzB,IAAIvI,YAAY,GAAGjD,OAAO,CAAC6J,eAAe,CAAClJ,WAAW;IACtD,MAAMrG,KAAK,GAAG6F,GAAG,CAACyB,OAAO,IAAIzB,GAAG,CAACyB,OAAO,CAACtH,KAAK,GAAGrD,kBAAkB,CAACkJ,GAAG,CAACyB,OAAO,CAACtH,KAAK,CAAC,GAAG,CAAC;IAC1F6F,GAAG,CAAC2C,KAAK,CAAC9E,OAAO,CAAE+E,CAAC,IAAK;MACrB,MAAMsH,YAAY,GAAGrK,OAAO,CAACsK,gBAAgB,CAACnK,GAAG,CAACyB,OAAO,CAAC;MAC1D,IAAItH,KAAK,EAAE;QACP+P,YAAY,CAACP,aAAa,CAACxP,KAAK,CAAC;MACrC;MACAxE,YAAY,CAAC,IAAI,EAAEiN,CAAC,EAAEsH,YAAY,CAAC;MACnCpH,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEoH,YAAY,CAACR,eAAe,CAAClJ,WAAW,CAAC;MAC/E6K,cAAc,CAAC9O,IAAI,CAAC2N,YAAY,CAACR,eAAe,CAAC;IACrD,CAAC,CAAC;IACF;IACA;IACA;IACA2B,cAAc,CAACxN,OAAO,CAAEgM,QAAQ,IAAKhK,OAAO,CAAC6J,eAAe,CAAC4B,4BAA4B,CAACzB,QAAQ,CAAC,CAAC;IACpGhK,OAAO,CAACwK,wBAAwB,CAACvH,YAAY,CAAC;IAC9CjD,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACAuL,YAAYA,CAACvL,GAAG,EAAEH,OAAO,EAAE;IACvB,IAAIG,GAAG,CAAC+H,OAAO,EAAE;MACb,MAAMJ,QAAQ,GAAG3H,GAAG,CAAC2H,QAAQ;MAC7B,MAAM6D,WAAW,GAAG3L,OAAO,CAACiC,MAAM,GAC5B/K,iBAAiB,CAAC4Q,QAAQ,EAAE9H,OAAO,CAACiC,MAAM,EAAEjC,OAAO,CAACtE,MAAM,CAAC,GAC3DoM,QAAQ;MACd,OAAOjR,aAAa,CAAC8U,WAAW,EAAE3L,OAAO,CAACtE,MAAM,CAAC;IACrD,CAAC,MACI;MACD,OAAO;QAAErB,QAAQ,EAAE8F,GAAG,CAAC9F,QAAQ;QAAEC,KAAK,EAAE6F,GAAG,CAAC7F,KAAK;QAAEC,MAAM,EAAE4F,GAAG,CAAC5F;MAAO,CAAC;IAC3E;EACJ;EACAgJ,YAAYA,CAACpD,GAAG,EAAEH,OAAO,EAAE;IACvB,MAAM0D,OAAO,GAAI1D,OAAO,CAAC2D,qBAAqB,GAAG,IAAI,CAAC+H,YAAY,CAACvL,GAAG,CAACuD,OAAO,EAAE1D,OAAO,CAAE;IACzF,MAAMgK,QAAQ,GAAGhK,OAAO,CAAC6J,eAAe;IACxC,IAAInG,OAAO,CAACpJ,KAAK,EAAE;MACf0F,OAAO,CAAC4L,aAAa,CAAClI,OAAO,CAACpJ,KAAK,CAAC;MACpC0P,QAAQ,CAACqB,qBAAqB,CAAC,CAAC;IACpC;IACA,MAAMhS,KAAK,GAAG8G,GAAG,CAAC9G,KAAK;IACvB,IAAIA,KAAK,CAAC8B,IAAI,IAAI/B,qBAAqB,CAACyK,SAAS,EAAE;MAC/C,IAAI,CAACC,cAAc,CAACzK,KAAK,EAAE2G,OAAO,CAAC;IACvC,CAAC,MACI;MACDA,OAAO,CAAC4L,aAAa,CAAClI,OAAO,CAACrJ,QAAQ,CAAC;MACvC,IAAI,CAACyH,UAAU,CAACzI,KAAK,EAAE2G,OAAO,CAAC;MAC/BgK,QAAQ,CAACuB,qBAAqB,CAAC,CAAC;IACpC;IACAvL,OAAO,CAAC2D,qBAAqB,GAAG,IAAI;IACpC3D,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACA2B,UAAUA,CAAC3B,GAAG,EAAEH,OAAO,EAAE;IACrB,MAAMgK,QAAQ,GAAGhK,OAAO,CAAC6J,eAAe;IACxC,MAAMnG,OAAO,GAAG1D,OAAO,CAAC2D,qBAAqB;IAC7C;IACA;IACA,IAAI,CAACD,OAAO,IAAIsG,QAAQ,CAAC6B,yBAAyB,CAAC,CAAC,EAAE;MAClD7B,QAAQ,CAAC8B,YAAY,CAAC,CAAC;IAC3B;IACA,MAAMvR,MAAM,GAAImJ,OAAO,IAAIA,OAAO,CAACnJ,MAAM,IAAK4F,GAAG,CAAC5F,MAAM;IACxD,IAAI4F,GAAG,CAAC+D,WAAW,EAAE;MACjB8F,QAAQ,CAAC+B,cAAc,CAACxR,MAAM,CAAC;IACnC,CAAC,MACI;MACDyP,QAAQ,CAAChS,SAAS,CAACmI,GAAG,CAAC4B,MAAM,EAAExH,MAAM,EAAEyF,OAAO,CAACtE,MAAM,EAAEsE,OAAO,CAAC4B,OAAO,CAAC;IAC3E;IACA5B,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACA2D,cAAcA,CAAC3D,GAAG,EAAEH,OAAO,EAAE;IACzB,MAAM2D,qBAAqB,GAAG3D,OAAO,CAAC2D,qBAAqB;IAC3D,MAAMyB,SAAS,GAAGpF,OAAO,CAAC6J,eAAe,CAACxP,QAAQ;IAClD,MAAMA,QAAQ,GAAGsJ,qBAAqB,CAACtJ,QAAQ;IAC/C,MAAMgQ,YAAY,GAAGrK,OAAO,CAACsK,gBAAgB,CAAC,CAAC;IAC/C,MAAM0B,aAAa,GAAG3B,YAAY,CAACR,eAAe;IAClDmC,aAAa,CAACzR,MAAM,GAAGoJ,qBAAqB,CAACpJ,MAAM;IACnD4F,GAAG,CAAC4B,MAAM,CAAC/D,OAAO,CAAEkF,IAAI,IAAK;MACzB,MAAMgC,MAAM,GAAGhC,IAAI,CAACgC,MAAM,IAAI,CAAC;MAC/B8G,aAAa,CAACC,WAAW,CAAC/G,MAAM,GAAG7K,QAAQ,CAAC;MAC5C2R,aAAa,CAAChU,SAAS,CAACkL,IAAI,CAACnB,MAAM,EAAEmB,IAAI,CAAC3I,MAAM,EAAEyF,OAAO,CAACtE,MAAM,EAAEsE,OAAO,CAAC4B,OAAO,CAAC;MAClFoK,aAAa,CAACT,qBAAqB,CAAC,CAAC;IACzC,CAAC,CAAC;IACF;IACA;IACAvL,OAAO,CAAC6J,eAAe,CAAC4B,4BAA4B,CAACO,aAAa,CAAC;IACnE;IACA;IACAhM,OAAO,CAACwK,wBAAwB,CAACpF,SAAS,GAAG/K,QAAQ,CAAC;IACtD2F,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACAyG,UAAUA,CAACzG,GAAG,EAAEH,OAAO,EAAE;IACrB;IACA;IACA,MAAMoF,SAAS,GAAGpF,OAAO,CAAC6J,eAAe,CAAClJ,WAAW;IACrD,MAAMiB,OAAO,GAAIzB,GAAG,CAACyB,OAAO,IAAI,CAAC,CAAE;IACnC,MAAMtH,KAAK,GAAGsH,OAAO,CAACtH,KAAK,GAAGrD,kBAAkB,CAAC2K,OAAO,CAACtH,KAAK,CAAC,GAAG,CAAC;IACnE,IAAIA,KAAK,KACJ0F,OAAO,CAACyK,YAAY,CAACtP,IAAI,KAAK/B,qBAAqB,CAAC6L,KAAK,IACrDG,SAAS,IAAI,CAAC,IAAIpF,OAAO,CAAC6J,eAAe,CAACgC,yBAAyB,CAAC,CAAE,CAAC,EAAE;MAC9E7L,OAAO,CAAC6J,eAAe,CAACwB,qBAAqB,CAAC,CAAC;MAC/CrL,OAAO,CAACyK,YAAY,GAAGa,0BAA0B;IACrD;IACA,IAAIrI,YAAY,GAAGmC,SAAS;IAC5B,MAAM8G,IAAI,GAAGlM,OAAO,CAACvK,WAAW,CAAC0K,GAAG,CAACnG,QAAQ,EAAEmG,GAAG,CAACgH,gBAAgB,EAAEhH,GAAG,CAAC8F,KAAK,EAAE9F,GAAG,CAAC4G,WAAW,EAAEnF,OAAO,CAACsF,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAElH,OAAO,CAACtE,MAAM,CAAC;IACjJsE,OAAO,CAACmM,iBAAiB,GAAGD,IAAI,CAACzP,MAAM;IACvC,IAAI2P,mBAAmB,GAAG,IAAI;IAC9BF,IAAI,CAAClO,OAAO,CAAC,CAAClE,OAAO,EAAEsM,CAAC,KAAK;MACzBpG,OAAO,CAACqM,iBAAiB,GAAGjG,CAAC;MAC7B,MAAMiE,YAAY,GAAGrK,OAAO,CAACsK,gBAAgB,CAACnK,GAAG,CAACyB,OAAO,EAAE9H,OAAO,CAAC;MACnE,IAAIQ,KAAK,EAAE;QACP+P,YAAY,CAACP,aAAa,CAACxP,KAAK,CAAC;MACrC;MACA,IAAIR,OAAO,KAAKkG,OAAO,CAAClG,OAAO,EAAE;QAC7BsS,mBAAmB,GAAG/B,YAAY,CAACR,eAAe;MACtD;MACA/T,YAAY,CAAC,IAAI,EAAEqK,GAAG,CAACqC,SAAS,EAAE6H,YAAY,CAAC;MAC/C;MACA;MACA;MACAA,YAAY,CAACR,eAAe,CAAC0B,qBAAqB,CAAC,CAAC;MACpD,MAAMpG,OAAO,GAAGkF,YAAY,CAACR,eAAe,CAAClJ,WAAW;MACxDsC,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEkC,OAAO,CAAC;IAClD,CAAC,CAAC;IACFnF,OAAO,CAACqM,iBAAiB,GAAG,CAAC;IAC7BrM,OAAO,CAACmM,iBAAiB,GAAG,CAAC;IAC7BnM,OAAO,CAACwK,wBAAwB,CAACvH,YAAY,CAAC;IAC9C,IAAImJ,mBAAmB,EAAE;MACrBpM,OAAO,CAAC6J,eAAe,CAAC4B,4BAA4B,CAACW,mBAAmB,CAAC;MACzEpM,OAAO,CAAC6J,eAAe,CAACwB,qBAAqB,CAAC,CAAC;IACnD;IACArL,OAAO,CAACyK,YAAY,GAAGtK,GAAG;EAC9B;EACAiH,YAAYA,CAACjH,GAAG,EAAEH,OAAO,EAAE;IACvB,MAAMsM,aAAa,GAAGtM,OAAO,CAACsM,aAAa;IAC3C,MAAMC,EAAE,GAAGvM,OAAO,CAAC6J,eAAe;IAClC,MAAMnG,OAAO,GAAGvD,GAAG,CAACuD,OAAO;IAC3B,MAAMrJ,QAAQ,GAAG+I,IAAI,CAACoJ,GAAG,CAAC9I,OAAO,CAACrJ,QAAQ,CAAC;IAC3C,MAAMoS,OAAO,GAAGpS,QAAQ,IAAI2F,OAAO,CAACmM,iBAAiB,GAAG,CAAC,CAAC;IAC1D,IAAI7R,KAAK,GAAGD,QAAQ,GAAG2F,OAAO,CAACqM,iBAAiB;IAChD,IAAIK,kBAAkB,GAAGhJ,OAAO,CAACrJ,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAGqJ,OAAO,CAACnJ,MAAM;IAC1E,QAAQmS,kBAAkB;MACtB,KAAK,SAAS;QACVpS,KAAK,GAAGmS,OAAO,GAAGnS,KAAK;QACvB;MACJ,KAAK,MAAM;QACPA,KAAK,GAAGgS,aAAa,CAACK,kBAAkB;QACxC;IACR;IACA,MAAM3C,QAAQ,GAAGhK,OAAO,CAAC6J,eAAe;IACxC,IAAIvP,KAAK,EAAE;MACP0P,QAAQ,CAACF,aAAa,CAACxP,KAAK,CAAC;IACjC;IACA,MAAMsS,YAAY,GAAG5C,QAAQ,CAACrJ,WAAW;IACzC7K,YAAY,CAAC,IAAI,EAAEqK,GAAG,CAACqC,SAAS,EAAExC,OAAO,CAAC;IAC1CA,OAAO,CAACyK,YAAY,GAAGtK,GAAG;IAC1B;IACA;IACA;IACA;IACAmM,aAAa,CAACK,kBAAkB,GAC5BJ,EAAE,CAAC5L,WAAW,GAAGiM,YAAY,IAAIL,EAAE,CAACnH,SAAS,GAAGkH,aAAa,CAACzC,eAAe,CAACzE,SAAS,CAAC;EAChG;AACJ;AACA,MAAMkG,0BAA0B,GAAG,CAAC,CAAC;AACrC,MAAM1B,wBAAwB,CAAC;EAC3B9J,OAAO;EACPhG,OAAO;EACP2P,eAAe;EACfoD,eAAe;EACfC,eAAe;EACfpR,MAAM;EACNqO,SAAS;EACTuC,aAAa,GAAG,IAAI;EACpBzC,eAAe;EACflG,qBAAqB,GAAG,IAAI;EAC5B8G,YAAY,GAAGa,0BAA0B;EACzCH,eAAe,GAAG,CAAC;EACnBvJ,OAAO,GAAG,CAAC,CAAC;EACZyK,iBAAiB,GAAG,CAAC;EACrBF,iBAAiB,GAAG,CAAC;EACrBQ,kBAAkB,GAAG,CAAC;EACtB5M,WAAWA,CAACD,OAAO,EAAEhG,OAAO,EAAE2P,eAAe,EAAEoD,eAAe,EAAEC,eAAe,EAAEpR,MAAM,EAAEqO,SAAS,EAAEgD,eAAe,EAAE;IACjH,IAAI,CAACjN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC2P,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACoD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACpR,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqO,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,eAAe,GAAGkD,eAAe,IAAI,IAAIC,eAAe,CAAC,IAAI,CAAClN,OAAO,EAAEhG,OAAO,EAAE,CAAC,CAAC;IACvFiQ,SAAS,CAACrN,IAAI,CAAC,IAAI,CAACmN,eAAe,CAAC;EACxC;EACA,IAAI5H,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,OAAO,CAACK,MAAM;EAC9B;EACAiJ,aAAaA,CAACtJ,OAAO,EAAEqL,YAAY,EAAE;IACjC,IAAI,CAACrL,OAAO,EACR;IACJ,MAAMsL,UAAU,GAAGtL,OAAO;IAC1B,IAAIuL,eAAe,GAAG,IAAI,CAACvL,OAAO;IAClC;IACA,IAAIsL,UAAU,CAAC7S,QAAQ,IAAI,IAAI,EAAE;MAC7B8S,eAAe,CAAC9S,QAAQ,GAAGpD,kBAAkB,CAACiW,UAAU,CAAC7S,QAAQ,CAAC;IACtE;IACA,IAAI6S,UAAU,CAAC5S,KAAK,IAAI,IAAI,EAAE;MAC1B6S,eAAe,CAAC7S,KAAK,GAAGrD,kBAAkB,CAACiW,UAAU,CAAC5S,KAAK,CAAC;IAChE;IACA,MAAM8S,SAAS,GAAGF,UAAU,CAACjL,MAAM;IACnC,IAAImL,SAAS,EAAE;MACX,IAAIC,cAAc,GAAGF,eAAe,CAAClL,MAAM;MAC3C,IAAI,CAACoL,cAAc,EAAE;QACjBA,cAAc,GAAG,IAAI,CAACzL,OAAO,CAACK,MAAM,GAAG,CAAC,CAAC;MAC7C;MACAyC,MAAM,CAACpE,IAAI,CAAC8M,SAAS,CAAC,CAACpP,OAAO,CAAET,IAAI,IAAK;QACrC,IAAI,CAAC0P,YAAY,IAAI,CAACI,cAAc,CAAChL,cAAc,CAAC9E,IAAI,CAAC,EAAE;UACvD8P,cAAc,CAAC9P,IAAI,CAAC,GAAGrG,iBAAiB,CAACkW,SAAS,CAAC7P,IAAI,CAAC,EAAE8P,cAAc,EAAE,IAAI,CAAC3R,MAAM,CAAC;QAC1F;MACJ,CAAC,CAAC;IACN;EACJ;EACA4R,YAAYA,CAAA,EAAG;IACX,MAAM1L,OAAO,GAAG,CAAC,CAAC;IAClB,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,MAAM2L,SAAS,GAAG,IAAI,CAAC3L,OAAO,CAACK,MAAM;MACrC,IAAIsL,SAAS,EAAE;QACX,MAAMtL,MAAM,GAAIL,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAE;QACvC8C,MAAM,CAACpE,IAAI,CAACiN,SAAS,CAAC,CAACvP,OAAO,CAAET,IAAI,IAAK;UACrC0E,MAAM,CAAC1E,IAAI,CAAC,GAAGgQ,SAAS,CAAChQ,IAAI,CAAC;QAClC,CAAC,CAAC;MACN;IACJ;IACA,OAAOqE,OAAO;EAClB;EACA0I,gBAAgBA,CAAC1I,OAAO,GAAG,IAAI,EAAE9H,OAAO,EAAE0T,OAAO,EAAE;IAC/C,MAAMC,MAAM,GAAG3T,OAAO,IAAI,IAAI,CAACA,OAAO;IACtC,MAAMkG,OAAO,GAAG,IAAI4J,wBAAwB,CAAC,IAAI,CAAC9J,OAAO,EAAE2N,MAAM,EAAE,IAAI,CAAChE,eAAe,EAAE,IAAI,CAACoD,eAAe,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACpR,MAAM,EAAE,IAAI,CAACqO,SAAS,EAAE,IAAI,CAACF,eAAe,CAAC6D,IAAI,CAACD,MAAM,EAAED,OAAO,IAAI,CAAC,CAAC,CAAC;IAClNxN,OAAO,CAACyK,YAAY,GAAG,IAAI,CAACA,YAAY;IACxCzK,OAAO,CAAC2D,qBAAqB,GAAG,IAAI,CAACA,qBAAqB;IAC1D3D,OAAO,CAAC4B,OAAO,GAAG,IAAI,CAAC0L,YAAY,CAAC,CAAC;IACrCtN,OAAO,CAACkL,aAAa,CAACtJ,OAAO,CAAC;IAC9B5B,OAAO,CAACqM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAClDrM,OAAO,CAACmM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAClDnM,OAAO,CAACsM,aAAa,GAAG,IAAI;IAC5B,IAAI,CAACnB,eAAe,EAAE;IACtB,OAAOnL,OAAO;EAClB;EACAwK,wBAAwBA,CAACgD,OAAO,EAAE;IAC9B,IAAI,CAAC/C,YAAY,GAAGa,0BAA0B;IAC9C,IAAI,CAACzB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6D,IAAI,CAAC,IAAI,CAAC5T,OAAO,EAAE0T,OAAO,CAAC;IACvE,IAAI,CAACzD,SAAS,CAACrN,IAAI,CAAC,IAAI,CAACmN,eAAe,CAAC;IACzC,OAAO,IAAI,CAACA,eAAe;EAC/B;EACAoB,2BAA2BA,CAACF,WAAW,EAAE1Q,QAAQ,EAAEC,KAAK,EAAE;IACtD,MAAMqT,cAAc,GAAG;MACnBtT,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG0Q,WAAW,CAAC1Q,QAAQ;MAC5DC,KAAK,EAAE,IAAI,CAACuP,eAAe,CAAClJ,WAAW,IAAIrG,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,CAAC,GAAGyQ,WAAW,CAACzQ,KAAK;MACzFC,MAAM,EAAE;IACZ,CAAC;IACD,MAAMqT,OAAO,GAAG,IAAIC,kBAAkB,CAAC,IAAI,CAAC/N,OAAO,EAAEiL,WAAW,CAACjR,OAAO,EAAEiR,WAAW,CAAC3Q,SAAS,EAAE2Q,WAAW,CAAC3C,aAAa,EAAE2C,WAAW,CAAC1C,cAAc,EAAEsF,cAAc,EAAE5C,WAAW,CAAC+C,uBAAuB,CAAC;IAC5M,IAAI,CAAC/D,SAAS,CAACrN,IAAI,CAACkR,OAAO,CAAC;IAC5B,OAAOD,cAAc;EACzB;EACA/B,aAAaA,CAACmC,IAAI,EAAE;IAChB,IAAI,CAAClE,eAAe,CAACoC,WAAW,CAAC,IAAI,CAACpC,eAAe,CAACxP,QAAQ,GAAG0T,IAAI,CAAC;EAC1E;EACAjE,aAAaA,CAACxP,KAAK,EAAE;IACjB;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,IAAI,CAACuP,eAAe,CAACC,aAAa,CAACxP,KAAK,CAAC;IAC7C;EACJ;EACA7E,WAAWA,CAACuE,QAAQ,EAAEmN,gBAAgB,EAAElB,KAAK,EAAEc,WAAW,EAAEG,QAAQ,EAAExL,MAAM,EAAE;IAC1E,IAAIsS,OAAO,GAAG,EAAE;IAChB,IAAIjH,WAAW,EAAE;MACbiH,OAAO,CAACtR,IAAI,CAAC,IAAI,CAAC5C,OAAO,CAAC;IAC9B;IACA,IAAIE,QAAQ,CAACyC,MAAM,GAAG,CAAC,EAAE;MACrB;MACAzC,QAAQ,GAAGA,QAAQ,CAACwN,OAAO,CAACwB,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC6D,eAAe,CAAC;MAC1E7S,QAAQ,GAAGA,QAAQ,CAACwN,OAAO,CAAC0B,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC4D,eAAe,CAAC;MAC1E,MAAM7S,KAAK,GAAGgM,KAAK,IAAI,CAAC;MACxB,IAAIgI,QAAQ,GAAG,IAAI,CAACnO,OAAO,CAAC/F,KAAK,CAAC,IAAI,CAACD,OAAO,EAAEE,QAAQ,EAAEC,KAAK,CAAC;MAChE,IAAIgM,KAAK,KAAK,CAAC,EAAE;QACbgI,QAAQ,GACJhI,KAAK,GAAG,CAAC,GACHgI,QAAQ,CAACxG,KAAK,CAACwG,QAAQ,CAACxR,MAAM,GAAGwJ,KAAK,EAAEgI,QAAQ,CAACxR,MAAM,CAAC,GACxDwR,QAAQ,CAACxG,KAAK,CAAC,CAAC,EAAExB,KAAK,CAAC;MACtC;MACA+H,OAAO,CAACtR,IAAI,CAAC,GAAGuR,QAAQ,CAAC;IAC7B;IACA,IAAI,CAAC/G,QAAQ,IAAI8G,OAAO,CAACvR,MAAM,IAAI,CAAC,EAAE;MAClCf,MAAM,CAACgB,IAAI,CAACvF,YAAY,CAACgQ,gBAAgB,CAAC,CAAC;IAC/C;IACA,OAAO6G,OAAO;EAClB;AACJ;AACA,MAAMhB,eAAe,CAAC;EAClBlN,OAAO;EACPhG,OAAO;EACPsL,SAAS;EACT8I,4BAA4B;EAC5B7T,QAAQ,GAAG,CAAC;EACZE,MAAM,GAAG,IAAI;EACb4T,iBAAiB,GAAG,IAAI1N,GAAG,CAAC,CAAC;EAC7B2N,gBAAgB,GAAG,IAAI3N,GAAG,CAAC,CAAC;EAC5B4N,UAAU,GAAG,IAAI5N,GAAG,CAAC,CAAC;EACtB6N,aAAa,GAAG,IAAI7N,GAAG,CAAC,CAAC;EACzB8N,oBAAoB,GAAG,IAAI9N,GAAG,CAAC,CAAC;EAChC+N,qBAAqB;EACrBC,cAAc,GAAG,IAAIhO,GAAG,CAAC,CAAC;EAC1BiO,SAAS,GAAG,IAAIjO,GAAG,CAAC,CAAC;EACrBkO,yBAAyB,GAAG,IAAI;EAChC5O,WAAWA,CAACD,OAAO,EAAEhG,OAAO,EAAEsL,SAAS,EAAE8I,4BAA4B,EAAE;IACnE,IAAI,CAACpO,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC8I,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAAC,IAAI,CAACA,4BAA4B,EAAE;MACpC,IAAI,CAACA,4BAA4B,GAAG,IAAIzN,GAAG,CAAC,CAAC;IACjD;IACA,IAAI,CAAC+N,qBAAqB,GAAG,IAAI,CAACN,4BAA4B,CAACpJ,GAAG,CAAChL,OAAO,CAAC;IAC3E,IAAI,CAAC,IAAI,CAAC0U,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAACD,oBAAoB;MACtD,IAAI,CAACL,4BAA4B,CAACxN,GAAG,CAAC5G,OAAO,EAAE,IAAI,CAACyU,oBAAoB,CAAC;IAC7E;IACA,IAAI,CAACK,aAAa,CAAC,CAAC;EACxB;EACA3E,iBAAiBA,CAAA,EAAG;IAChB,QAAQ,IAAI,CAACoE,UAAU,CAAChO,IAAI;MACxB,KAAK,CAAC;QACF,OAAO,KAAK;MAChB,KAAK,CAAC;QACF,OAAO,IAAI,CAACwL,yBAAyB,CAAC,CAAC;MAC3C;QACI,OAAO,IAAI;IACnB;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACuC,gBAAgB,CAAC/N,IAAI,GAAG,CAAC;EACzC;EACA,IAAIM,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACyE,SAAS,GAAG,IAAI,CAAC/K,QAAQ;EACzC;EACAyP,aAAaA,CAACxP,KAAK,EAAE;IACjB;IACA;IACA;IACA;IACA,MAAMuU,eAAe,GAAG,IAAI,CAACR,UAAU,CAAChO,IAAI,KAAK,CAAC,IAAI,IAAI,CAACoO,cAAc,CAACpO,IAAI;IAC9E,IAAI,IAAI,CAAChG,QAAQ,IAAIwU,eAAe,EAAE;MAClC,IAAI,CAAC5C,WAAW,CAAC,IAAI,CAACtL,WAAW,GAAGrG,KAAK,CAAC;MAC1C,IAAIuU,eAAe,EAAE;QACjB,IAAI,CAACxD,qBAAqB,CAAC,CAAC;MAChC;IACJ,CAAC,MACI;MACD,IAAI,CAACjG,SAAS,IAAI9K,KAAK;IAC3B;EACJ;EACAoT,IAAIA,CAAC5T,OAAO,EAAE6G,WAAW,EAAE;IACvB,IAAI,CAAC4K,qBAAqB,CAAC,CAAC;IAC5B,OAAO,IAAIyB,eAAe,CAAC,IAAI,CAAClN,OAAO,EAAEhG,OAAO,EAAE6G,WAAW,IAAI,IAAI,CAACA,WAAW,EAAE,IAAI,CAACuN,4BAA4B,CAAC;EACzH;EACAU,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACvB,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAACC,gBAAgB;IAClD;IACA,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACC,UAAU,CAACvJ,GAAG,CAAC,IAAI,CAACzK,QAAQ,CAAC;IAC1D,IAAI,CAAC,IAAI,CAAC+T,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI3N,GAAG,CAAC,CAAC;MACjC,IAAI,CAAC4N,UAAU,CAAC3N,GAAG,CAAC,IAAI,CAACrG,QAAQ,EAAE,IAAI,CAAC+T,gBAAgB,CAAC;IAC7D;EACJ;EACAtC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACzR,QAAQ,IAAIyO,yBAAyB;IAC1C,IAAI,CAAC8F,aAAa,CAAC,CAAC;EACxB;EACA3C,WAAWA,CAAC8B,IAAI,EAAE;IACd,IAAI,CAACxC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAClR,QAAQ,GAAG0T,IAAI;IACpB,IAAI,CAACa,aAAa,CAAC,CAAC;EACxB;EACAE,YAAYA,CAACnV,IAAI,EAAEmC,KAAK,EAAE;IACtB,IAAI,CAACyS,oBAAoB,CAAC7N,GAAG,CAAC/G,IAAI,EAAEmC,KAAK,CAAC;IAC1C,IAAI,CAAC0S,qBAAqB,CAAC9N,GAAG,CAAC/G,IAAI,EAAEmC,KAAK,CAAC;IAC3C,IAAI,CAACwS,aAAa,CAAC5N,GAAG,CAAC/G,IAAI,EAAE;MAAEoU,IAAI,EAAE,IAAI,CAACpN,WAAW;MAAE7E;IAAM,CAAC,CAAC;EACnE;EACAqO,uBAAuBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACwE,yBAAyB,KAAK,IAAI,CAACP,gBAAgB;EACnE;EACArC,cAAcA,CAACxR,MAAM,EAAE;IACnB,IAAIA,MAAM,EAAE;MACR,IAAI,CAAC4T,iBAAiB,CAACzN,GAAG,CAAC,QAAQ,EAAEnG,MAAM,CAAC;IAChD;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAI,CAACZ,IAAI,EAAEmC,KAAK,CAAC,IAAI,IAAI,CAAC0S,qBAAqB,EAAE;MAClD,IAAI,CAACE,SAAS,CAAChO,GAAG,CAAC/G,IAAI,EAAEmC,KAAK,IAAIxC,UAAU,CAAC;MAC7C,IAAI,CAAC8U,gBAAgB,CAAC1N,GAAG,CAAC/G,IAAI,EAAEL,UAAU,CAAC;IAC/C;IACA,IAAI,CAACqV,yBAAyB,GAAG,IAAI,CAACP,gBAAgB;EAC1D;EACApW,SAASA,CAAC+W,KAAK,EAAExU,MAAM,EAAEmB,MAAM,EAAEkG,OAAO,EAAE;IACtC,IAAIrH,MAAM,EAAE;MACR,IAAI,CAAC4T,iBAAiB,CAACzN,GAAG,CAAC,QAAQ,EAAEnG,MAAM,CAAC;IAChD;IACA,MAAM0H,MAAM,GAAIL,OAAO,IAAIA,OAAO,CAACK,MAAM,IAAK,CAAC,CAAC;IAChD,MAAMF,MAAM,GAAGiN,aAAa,CAACD,KAAK,EAAE,IAAI,CAACP,qBAAqB,CAAC;IAC/D,KAAK,IAAI,CAAC7U,IAAI,EAAEmC,KAAK,CAAC,IAAIiG,MAAM,EAAE;MAC9B,MAAMkN,GAAG,GAAG/X,iBAAiB,CAAC4E,KAAK,EAAEmG,MAAM,EAAEvG,MAAM,CAAC;MACpD,IAAI,CAAC+S,cAAc,CAAC/N,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;MAClC,IAAI,CAAC,IAAI,CAACV,oBAAoB,CAACjS,GAAG,CAAC3C,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC+U,SAAS,CAAChO,GAAG,CAAC/G,IAAI,EAAE,IAAI,CAAC6U,qBAAqB,CAAC1J,GAAG,CAACnL,IAAI,CAAC,IAAIL,UAAU,CAAC;MAChF;MACA,IAAI,CAACwV,YAAY,CAACnV,IAAI,EAAEsV,GAAG,CAAC;IAChC;EACJ;EACA1D,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACkD,cAAc,CAACpO,IAAI,IAAI,CAAC,EAC7B;IACJ,IAAI,CAACoO,cAAc,CAACzQ,OAAO,CAAC,CAACiR,GAAG,EAAEtV,IAAI,KAAK;MACvC,IAAI,CAACyU,gBAAgB,CAAC1N,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;IACxC,CAAC,CAAC;IACF,IAAI,CAACR,cAAc,CAAC5F,KAAK,CAAC,CAAC;IAC3B,IAAI,CAAC0F,oBAAoB,CAACvQ,OAAO,CAAC,CAACiR,GAAG,EAAEtV,IAAI,KAAK;MAC7C,IAAI,CAAC,IAAI,CAACyU,gBAAgB,CAAC9R,GAAG,CAAC3C,IAAI,CAAC,EAAE;QAClC,IAAI,CAACyU,gBAAgB,CAAC1N,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA5D,qBAAqBA,CAAA,EAAG;IACpB,KAAK,IAAI,CAAC1R,IAAI,EAAEsV,GAAG,CAAC,IAAI,IAAI,CAACV,oBAAoB,EAAE;MAC/C,IAAI,CAACE,cAAc,CAAC/N,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;MAClC,IAAI,CAACH,YAAY,CAACnV,IAAI,EAAEsV,GAAG,CAAC;IAChC;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACb,UAAU,CAACvJ,GAAG,CAAC,IAAI,CAACzK,QAAQ,CAAC;EAC7C;EACA,IAAI8U,UAAUA,CAAA,EAAG;IACb,MAAMA,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIxV,IAAI,IAAI,IAAI,CAACyU,gBAAgB,EAAE;MACpCe,UAAU,CAACzS,IAAI,CAAC/C,IAAI,CAAC;IACzB;IACA,OAAOwV,UAAU;EACrB;EACA1D,4BAA4BA,CAACzB,QAAQ,EAAE;IACnCA,QAAQ,CAACsE,aAAa,CAACtQ,OAAO,CAAC,CAACoR,QAAQ,EAAEzV,IAAI,KAAK;MAC/C,MAAM0V,QAAQ,GAAG,IAAI,CAACf,aAAa,CAACxJ,GAAG,CAACnL,IAAI,CAAC;MAC7C,IAAI,CAAC0V,QAAQ,IAAID,QAAQ,CAACrB,IAAI,GAAGsB,QAAQ,CAACtB,IAAI,EAAE;QAC5C,IAAI,CAACe,YAAY,CAACnV,IAAI,EAAEyV,QAAQ,CAACtT,KAAK,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACA6N,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC4B,qBAAqB,CAAC,CAAC;IAC5B,MAAMnD,aAAa,GAAG,IAAIpM,GAAG,CAAC,CAAC;IAC/B,MAAMqM,cAAc,GAAG,IAAIrM,GAAG,CAAC,CAAC;IAChC,MAAM+H,OAAO,GAAG,IAAI,CAACsK,UAAU,CAAChO,IAAI,KAAK,CAAC,IAAI,IAAI,CAAChG,QAAQ,KAAK,CAAC;IACjE,IAAIiV,cAAc,GAAG,EAAE;IACvB,IAAI,CAACjB,UAAU,CAACrQ,OAAO,CAAC,CAACuR,QAAQ,EAAExB,IAAI,KAAK;MACxC,MAAMyB,aAAa,GAAG,IAAI/O,GAAG,CAAC,CAAC,GAAG,IAAI,CAACiO,SAAS,EAAE,GAAGa,QAAQ,CAAC,CAAC;MAC/DC,aAAa,CAACxR,OAAO,CAAC,CAAClC,KAAK,EAAEnC,IAAI,KAAK;QACnC,IAAImC,KAAK,KAAKtC,UAAU,EAAE;UACtB4O,aAAa,CAAC9F,GAAG,CAAC3I,IAAI,CAAC;QAC3B,CAAC,MACI,IAAImC,KAAK,KAAKxC,UAAU,EAAE;UAC3B+O,cAAc,CAAC/F,GAAG,CAAC3I,IAAI,CAAC;QAC5B;MACJ,CAAC,CAAC;MACF,IAAI,CAACoK,OAAO,EAAE;QACVyL,aAAa,CAAC9O,GAAG,CAAC,QAAQ,EAAEqN,IAAI,GAAG,IAAI,CAAC1T,QAAQ,CAAC;MACrD;MACAiV,cAAc,CAAC5S,IAAI,CAAC8S,aAAa,CAAC;IACtC,CAAC,CAAC;IACF,MAAMC,QAAQ,GAAG,CAAC,GAAGrH,aAAa,CAAC7F,MAAM,CAAC,CAAC,CAAC;IAC5C,MAAMmN,SAAS,GAAG,CAAC,GAAGrH,cAAc,CAAC9F,MAAM,CAAC,CAAC,CAAC;IAC9C;IACA,IAAIwB,OAAO,EAAE;MACT,MAAM4L,GAAG,GAAGL,cAAc,CAAC,CAAC,CAAC;MAC7B,MAAMM,GAAG,GAAG,IAAInP,GAAG,CAACkP,GAAG,CAAC;MACxBA,GAAG,CAACjP,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;MACpBkP,GAAG,CAAClP,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;MACpB4O,cAAc,GAAG,CAACK,GAAG,EAAEC,GAAG,CAAC;IAC/B;IACA,OAAOzH,yBAAyB,CAAC,IAAI,CAACrO,OAAO,EAAEwV,cAAc,EAAEG,QAAQ,EAAEC,SAAS,EAAE,IAAI,CAACrV,QAAQ,EAAE,IAAI,CAAC+K,SAAS,EAAE,IAAI,CAAC7K,MAAM,EAAE,KAAK,CAAC;EAC1I;AACJ;AACA,MAAMsT,kBAAkB,SAASb,eAAe,CAAC;EAC7C5S,SAAS;EACTgO,aAAa;EACbC,cAAc;EACdwH,wBAAwB;EACxBnM,OAAO;EACP3D,WAAWA,CAACN,MAAM,EAAE3F,OAAO,EAAEM,SAAS,EAAEgO,aAAa,EAAEC,cAAc,EAAE3E,OAAO,EAAEmM,wBAAwB,GAAG,KAAK,EAAE;IAC9G,KAAK,CAACpQ,MAAM,EAAE3F,OAAO,EAAE4J,OAAO,CAACpJ,KAAK,CAAC;IACrC,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgO,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACwH,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACnM,OAAO,GAAG;MAAErJ,QAAQ,EAAEqJ,OAAO,CAACrJ,QAAQ;MAAEC,KAAK,EAAEoJ,OAAO,CAACpJ,KAAK;MAAEC,MAAM,EAAEmJ,OAAO,CAACnJ;IAAO,CAAC;EAC/F;EACA0P,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC7P,SAAS,CAACqC,MAAM,GAAG,CAAC;EACpC;EACAkN,cAAcA,CAAA,EAAG;IACb,IAAIvP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI;MAAEE,KAAK;MAAED,QAAQ;MAAEE;IAAO,CAAC,GAAG,IAAI,CAACmJ,OAAO;IAC9C,IAAI,IAAI,CAACmM,wBAAwB,IAAIvV,KAAK,EAAE;MACxC,MAAMwV,YAAY,GAAG,EAAE;MACvB,MAAMvH,SAAS,GAAGlO,QAAQ,GAAGC,KAAK;MAClC,MAAMyV,WAAW,GAAGzV,KAAK,GAAGiO,SAAS;MACrC;MACA,MAAMyH,gBAAgB,GAAG,IAAIvP,GAAG,CAACrG,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9C4V,gBAAgB,CAACtP,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;MACjCoP,YAAY,CAACpT,IAAI,CAACsT,gBAAgB,CAAC;MACnC,MAAMC,gBAAgB,GAAG,IAAIxP,GAAG,CAACrG,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9C6V,gBAAgB,CAACvP,GAAG,CAAC,QAAQ,EAAEwP,WAAW,CAACH,WAAW,CAAC,CAAC;MACxDD,YAAY,CAACpT,IAAI,CAACuT,gBAAgB,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAIY;MACA,MAAMhK,KAAK,GAAG7L,SAAS,CAACqC,MAAM,GAAG,CAAC;MAClC,KAAK,IAAI2J,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,KAAK,EAAEG,CAAC,EAAE,EAAE;QAC7B,IAAID,EAAE,GAAG,IAAI1F,GAAG,CAACrG,SAAS,CAACgM,CAAC,CAAC,CAAC;QAC9B,MAAM+J,SAAS,GAAGhK,EAAE,CAACrB,GAAG,CAAC,QAAQ,CAAC;QAClC,MAAMsL,cAAc,GAAG9V,KAAK,GAAG6V,SAAS,GAAG9V,QAAQ;QACnD8L,EAAE,CAACzF,GAAG,CAAC,QAAQ,EAAEwP,WAAW,CAACE,cAAc,GAAG7H,SAAS,CAAC,CAAC;QACzDuH,YAAY,CAACpT,IAAI,CAACyJ,EAAE,CAAC;MACzB;MACA;MACA9L,QAAQ,GAAGkO,SAAS;MACpBjO,KAAK,GAAG,CAAC;MACTC,MAAM,GAAG,EAAE;MACXH,SAAS,GAAG0V,YAAY;IAC5B;IACA,OAAO3H,yBAAyB,CAAC,IAAI,CAACrO,OAAO,EAAEM,SAAS,EAAE,IAAI,CAACgO,aAAa,EAAE,IAAI,CAACC,cAAc,EAAEhO,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAE,IAAI,CAAC;EACrI;AACJ;AACA,SAAS2V,WAAWA,CAAChL,MAAM,EAAEmL,aAAa,GAAG,CAAC,EAAE;EAC5C,MAAMC,IAAI,GAAGlN,IAAI,CAACmN,GAAG,CAAC,EAAE,EAAEF,aAAa,GAAG,CAAC,CAAC;EAC5C,OAAOjN,IAAI,CAACoN,KAAK,CAACtL,MAAM,GAAGoL,IAAI,CAAC,GAAGA,IAAI;AAC3C;AACA,SAAStB,aAAaA,CAACD,KAAK,EAAE0B,SAAS,EAAE;EACrC,MAAM1O,MAAM,GAAG,IAAItB,GAAG,CAAC,CAAC;EACxB,IAAIiQ,aAAa;EACjB3B,KAAK,CAAC/Q,OAAO,CAAEjD,KAAK,IAAK;IACrB,IAAIA,KAAK,KAAK,GAAG,EAAE;MACf2V,aAAa,KAAKD,SAAS,CAACnQ,IAAI,CAAC,CAAC;MAClC,KAAK,IAAI3G,IAAI,IAAI+W,aAAa,EAAE;QAC5B3O,MAAM,CAACrB,GAAG,CAAC/G,IAAI,EAAEL,UAAU,CAAC;MAChC;IACJ,CAAC,MACI;MACD,KAAK,IAAI,CAACK,IAAI,EAAEsV,GAAG,CAAC,IAAIlU,KAAK,EAAE;QAC3BgH,MAAM,CAACrB,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;MACzB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOlN,MAAM;AACjB;AAEA,SAAS4O,2BAA2BA,CAAC7W,OAAO,EAAE8W,WAAW,EAAEtS,SAAS,EAAEE,OAAO,EAAEqS,mBAAmB,EAAEC,UAAU,EAAEC,QAAQ,EAAEhH,SAAS,EAAEiH,eAAe,EAAE5I,aAAa,EAAEC,cAAc,EAAEE,SAAS,EAAE7M,MAAM,EAAE;EACpM,OAAO;IACHP,IAAI,EAAE,CAAC,CAAC;IACRrB,OAAO;IACP8W,WAAW;IACXC,mBAAmB;IACnBvS,SAAS;IACTwS,UAAU;IACVtS,OAAO;IACPuS,QAAQ;IACRhH,SAAS;IACTiH,eAAe;IACf5I,aAAa;IACbC,cAAc;IACdE,SAAS;IACT7M;EACJ,CAAC;AACL;AAEA,MAAMuV,YAAY,GAAG,CAAC,CAAC;AACvB,MAAMC,0BAA0B,CAAC;EAC7BC,YAAY;EACZhR,GAAG;EACHiR,YAAY;EACZrR,WAAWA,CAACoR,YAAY,EAAEhR,GAAG,EAAEiR,YAAY,EAAE;IACzC,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAChR,GAAG,GAAGA,GAAG;IACd,IAAI,CAACiR,YAAY,GAAGA,YAAY;EACpC;EACA5U,KAAKA,CAAC6U,YAAY,EAAEC,SAAS,EAAExX,OAAO,EAAEmI,MAAM,EAAE;IAC5C,OAAOsP,yBAAyB,CAAC,IAAI,CAACpR,GAAG,CAACsC,QAAQ,EAAE4O,YAAY,EAAEC,SAAS,EAAExX,OAAO,EAAEmI,MAAM,CAAC;EACjG;EACAuP,WAAWA,CAACC,SAAS,EAAExP,MAAM,EAAEvG,MAAM,EAAE;IACnC,IAAIgW,MAAM,GAAG,IAAI,CAACN,YAAY,CAACtM,GAAG,CAAC,GAAG,CAAC;IACvC,IAAI2M,SAAS,KAAKE,SAAS,EAAE;MACzBD,MAAM,GAAG,IAAI,CAACN,YAAY,CAACtM,GAAG,CAAC2M,SAAS,EAAErV,QAAQ,CAAC,CAAC,CAAC,IAAIsV,MAAM;IACnE;IACA,OAAOA,MAAM,GAAGA,MAAM,CAACF,WAAW,CAACvP,MAAM,EAAEvG,MAAM,CAAC,GAAG,IAAI+E,GAAG,CAAC,CAAC;EAClE;EACAb,KAAKA,CAACH,MAAM,EAAE3F,OAAO,EAAEuX,YAAY,EAAEC,SAAS,EAAEjI,cAAc,EAAEC,cAAc,EAAEsI,cAAc,EAAEC,WAAW,EAAEpI,eAAe,EAAEqI,YAAY,EAAE;IACxI,MAAMpW,MAAM,GAAG,EAAE;IACjB,MAAMqW,yBAAyB,GAAI,IAAI,CAAC5R,GAAG,CAACyB,OAAO,IAAI,IAAI,CAACzB,GAAG,CAACyB,OAAO,CAACK,MAAM,IAAKgP,YAAY;IAC/F,MAAMe,sBAAsB,GAAIJ,cAAc,IAAIA,cAAc,CAAC3P,MAAM,IAAKgP,YAAY;IACxF,MAAMgB,kBAAkB,GAAG,IAAI,CAACT,WAAW,CAACH,YAAY,EAAEW,sBAAsB,EAAEtW,MAAM,CAAC;IACzF,MAAMwW,mBAAmB,GAAIL,WAAW,IAAIA,WAAW,CAAC5P,MAAM,IAAKgP,YAAY;IAC/E,MAAMkB,eAAe,GAAG,IAAI,CAACX,WAAW,CAACF,SAAS,EAAEY,mBAAmB,EAAExW,MAAM,CAAC;IAChF,MAAMsV,eAAe,GAAG,IAAIhV,GAAG,CAAC,CAAC;IACjC,MAAMoW,WAAW,GAAG,IAAI3R,GAAG,CAAC,CAAC;IAC7B,MAAM4R,YAAY,GAAG,IAAI5R,GAAG,CAAC,CAAC;IAC9B,MAAM6R,SAAS,GAAGhB,SAAS,KAAK,MAAM;IACtC,MAAMiB,gBAAgB,GAAG;MACrBtQ,MAAM,EAAEuQ,kBAAkB,CAACN,mBAAmB,EAAEH,yBAAyB,CAAC;MAC1EzX,KAAK,EAAE,IAAI,CAAC6F,GAAG,CAACyB,OAAO,EAAEtH;IAC7B,CAAC;IACD,MAAMyP,SAAS,GAAG+H,YAAY,GACxB,EAAE,GACF3I,uBAAuB,CAAC1J,MAAM,EAAE3F,OAAO,EAAE,IAAI,CAACqG,GAAG,CAACqC,SAAS,EAAE6G,cAAc,EAAEC,cAAc,EAAE2I,kBAAkB,EAAEE,eAAe,EAAEI,gBAAgB,EAAE9I,eAAe,EAAE/N,MAAM,CAAC;IAClL,IAAI6M,SAAS,GAAG,CAAC;IACjBwB,SAAS,CAAC/L,OAAO,CAAEuO,EAAE,IAAK;MACtBhE,SAAS,GAAGnF,IAAI,CAACC,GAAG,CAACkJ,EAAE,CAAClS,QAAQ,GAAGkS,EAAE,CAACjS,KAAK,EAAEiO,SAAS,CAAC;IAC3D,CAAC,CAAC;IACF,IAAI7M,MAAM,CAACe,MAAM,EAAE;MACf,OAAOkU,2BAA2B,CAAC7W,OAAO,EAAE,IAAI,CAACqX,YAAY,EAAEE,YAAY,EAAEC,SAAS,EAAEgB,SAAS,EAAEL,kBAAkB,EAAEE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAEC,WAAW,EAAEC,YAAY,EAAE9J,SAAS,EAAE7M,MAAM,CAAC;IACjM;IACAqO,SAAS,CAAC/L,OAAO,CAAEuO,EAAE,IAAK;MACtB,MAAMkG,GAAG,GAAGlG,EAAE,CAACzS,OAAO;MACtB,MAAM2V,QAAQ,GAAG9Y,oBAAoB,CAACyb,WAAW,EAAEK,GAAG,EAAE,IAAIzW,GAAG,CAAC,CAAC,CAAC;MAClEuQ,EAAE,CAACnE,aAAa,CAACpK,OAAO,CAAErE,IAAI,IAAK8V,QAAQ,CAACnN,GAAG,CAAC3I,IAAI,CAAC,CAAC;MACtD,MAAM+V,SAAS,GAAG/Y,oBAAoB,CAAC0b,YAAY,EAAEI,GAAG,EAAE,IAAIzW,GAAG,CAAC,CAAC,CAAC;MACpEuQ,EAAE,CAAClE,cAAc,CAACrK,OAAO,CAAErE,IAAI,IAAK+V,SAAS,CAACpN,GAAG,CAAC3I,IAAI,CAAC,CAAC;MACxD,IAAI8Y,GAAG,KAAK3Y,OAAO,EAAE;QACjBkX,eAAe,CAAC1O,GAAG,CAACmQ,GAAG,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,OAAOxX,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CyX,6BAA6B,CAAC3I,SAAS,EAAE,IAAI,CAACoH,YAAY,EAAE1R,MAAM,CAAC;IACvE;IACA,OAAOkR,2BAA2B,CAAC7W,OAAO,EAAE,IAAI,CAACqX,YAAY,EAAEE,YAAY,EAAEC,SAAS,EAAEgB,SAAS,EAAEL,kBAAkB,EAAEE,eAAe,EAAEpI,SAAS,EAAE,CAAC,GAAGiH,eAAe,CAACzO,MAAM,CAAC,CAAC,CAAC,EAAE6P,WAAW,EAAEC,YAAY,EAAE9J,SAAS,CAAC;EAC3N;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmK,6BAA6BA,CAAC3I,SAAS,EAAE6G,WAAW,EAAEnR,MAAM,EAAE;EACnE,IAAI,CAACA,MAAM,CAACkT,+BAA+B,EAAE;IACzC;EACJ;EACA,MAAMC,yBAAyB,GAAG,IAAI5W,GAAG,CAAC;EACtC;EACA;EACA;EACA;EACA,QAAQ,CACX,CAAC;EACF,MAAM6W,yBAAyB,GAAG,IAAI7W,GAAG,CAAC,CAAC;EAC3C+N,SAAS,CAAC/L,OAAO,CAAC,CAAC;IAAE5D;EAAU,CAAC,KAAK;IACjC,MAAM0Y,+BAA+B,GAAG,IAAIrS,GAAG,CAAC,CAAC;IACjDrG,SAAS,CAAC4D,OAAO,CAAEuR,QAAQ,IAAK;MAC5B,MAAMwD,cAAc,GAAGxO,KAAK,CAACyO,IAAI,CAACzD,QAAQ,CAAC5K,OAAO,CAAC,CAAC,CAAC,CAAC7H,MAAM,CAAC,CAAC,CAACnD,IAAI,CAAC,KAAK,CAACiZ,yBAAyB,CAACtW,GAAG,CAAC3C,IAAI,CAAC,CAAC;MAC9G,KAAK,MAAM,CAACA,IAAI,EAAEmC,KAAK,CAAC,IAAIiX,cAAc,EAAE;QACxC,IAAI,CAACtT,MAAM,CAACkT,+BAA+B,CAAChZ,IAAI,CAAC,EAAE;UAC/C,IAAImZ,+BAA+B,CAACxW,GAAG,CAAC3C,IAAI,CAAC,IAAI,CAACkZ,yBAAyB,CAACvW,GAAG,CAAC3C,IAAI,CAAC,EAAE;YACnF,MAAMsZ,gBAAgB,GAAGH,+BAA+B,CAAChO,GAAG,CAACnL,IAAI,CAAC;YAClE,IAAIsZ,gBAAgB,KAAKnX,KAAK,EAAE;cAC5B+W,yBAAyB,CAACvQ,GAAG,CAAC3I,IAAI,CAAC;YACvC;UACJ,CAAC,MACI;YACDmZ,+BAA+B,CAACpS,GAAG,CAAC/G,IAAI,EAAEmC,KAAK,CAAC;UACpD;QACJ;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,IAAI+W,yBAAyB,CAACxS,IAAI,GAAG,CAAC,EAAE;IACpCjD,OAAO,CAACC,IAAI,CAAC,mCAAmCuT,WAAW,0CAA0C,GACjG,8BAA8B,GAC9BrM,KAAK,CAACyO,IAAI,CAACH,yBAAyB,CAAC,CAAC3V,IAAI,CAAC,IAAI,CAAC,GAChD,IAAI,GACJ,iIAAiI,CAAC;EAC1I;AACJ;AACA,SAASqU,yBAAyBA,CAAC2B,QAAQ,EAAE7B,YAAY,EAAEC,SAAS,EAAExX,OAAO,EAAEmI,MAAM,EAAE;EACnF,OAAOiR,QAAQ,CAAClL,IAAI,CAAEmL,EAAE,IAAKA,EAAE,CAAC9B,YAAY,EAAEC,SAAS,EAAExX,OAAO,EAAEmI,MAAM,CAAC,CAAC;AAC9E;AACA,SAASuQ,kBAAkBA,CAACY,UAAU,EAAEC,QAAQ,EAAE;EAC9C,MAAMjV,MAAM,GAAG;IAAE,GAAGiV;EAAS,CAAC;EAC9B3O,MAAM,CAACC,OAAO,CAACyO,UAAU,CAAC,CAACpV,OAAO,CAAC,CAAC,CAACsV,GAAG,EAAExX,KAAK,CAAC,KAAK;IACjD,IAAIA,KAAK,IAAI,IAAI,EAAE;MACfsC,MAAM,CAACkV,GAAG,CAAC,GAAGxX,KAAK;IACvB;EACJ,CAAC,CAAC;EACF,OAAOsC,MAAM;AACjB;AACA,MAAMmV,oBAAoB,CAAC;EACvBxR,MAAM;EACNyR,aAAa;EACbC,UAAU;EACV1T,WAAWA,CAACgC,MAAM,EAAEyR,aAAa,EAAEC,UAAU,EAAE;IAC3C,IAAI,CAAC1R,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACyR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAjC,WAAWA,CAACvP,MAAM,EAAEvG,MAAM,EAAE;IACxB,MAAM8N,WAAW,GAAG,IAAI/I,GAAG,CAAC,CAAC;IAC7B,MAAMiT,cAAc,GAAGlB,kBAAkB,CAACvQ,MAAM,EAAE,IAAI,CAACuR,aAAa,CAAC;IACrE,IAAI,CAACzR,MAAM,CAACA,MAAM,CAAC/D,OAAO,CAAElC,KAAK,IAAK;MAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3BA,KAAK,CAACkC,OAAO,CAAC,CAACiR,GAAG,EAAEtV,IAAI,KAAK;UACzB,IAAIsV,GAAG,EAAE;YACLA,GAAG,GAAG/X,iBAAiB,CAAC+X,GAAG,EAAEyE,cAAc,EAAEhY,MAAM,CAAC;UACxD;UACA,MAAMiY,cAAc,GAAG,IAAI,CAACF,UAAU,CAACjY,qBAAqB,CAAC7B,IAAI,EAAE+B,MAAM,CAAC;UAC1EuT,GAAG,GAAG,IAAI,CAACwE,UAAU,CAAC9X,mBAAmB,CAAChC,IAAI,EAAEga,cAAc,EAAE1E,GAAG,EAAEvT,MAAM,CAAC;UAC5E8N,WAAW,CAAC9I,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;QAC9B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,OAAOzF,WAAW;EACtB;AACJ;AAEA,SAASoK,YAAYA,CAACrW,IAAI,EAAE4C,GAAG,EAAEsT,UAAU,EAAE;EACzC,OAAO,IAAII,gBAAgB,CAACtW,IAAI,EAAE4C,GAAG,EAAEsT,UAAU,CAAC;AACtD;AACA,MAAMI,gBAAgB,CAAC;EACnBtW,IAAI;EACJ4C,GAAG;EACH2T,WAAW;EACXC,mBAAmB,GAAG,EAAE;EACxBC,kBAAkB;EAClBjT,MAAM,GAAG,IAAIN,GAAG,CAAC,CAAC;EAClBV,WAAWA,CAACxC,IAAI,EAAE4C,GAAG,EAAE2T,WAAW,EAAE;IAChC,IAAI,CAACvW,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC4C,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC2T,WAAW,GAAGA,WAAW;IAC9B3T,GAAG,CAACY,MAAM,CAAC/C,OAAO,CAAEmC,GAAG,IAAK;MACxB,MAAMqT,aAAa,GAAIrT,GAAG,CAACyB,OAAO,IAAIzB,GAAG,CAACyB,OAAO,CAACK,MAAM,IAAK,CAAC,CAAC;MAC/D,IAAI,CAAClB,MAAM,CAACL,GAAG,CAACP,GAAG,CAAC5C,IAAI,EAAE,IAAIgW,oBAAoB,CAACpT,GAAG,CAAC9G,KAAK,EAAEma,aAAa,EAAEM,WAAW,CAAC,CAAC;IAC9F,CAAC,CAAC;IACFG,iBAAiB,CAAC,IAAI,CAAClT,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC;IAC3CkT,iBAAiB,CAAC,IAAI,CAAClT,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC;IAC5CZ,GAAG,CAACa,WAAW,CAAChD,OAAO,CAAEmC,GAAG,IAAK;MAC7B,IAAI,CAAC4T,mBAAmB,CAACrX,IAAI,CAAC,IAAIwU,0BAA0B,CAAC3T,IAAI,EAAE4C,GAAG,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC;IACzF,CAAC,CAAC;IACF,IAAI,CAACiT,kBAAkB,GAAGE,wBAAwB,CAAC3W,IAAI,EAAE,IAAI,CAACwD,MAAM,CAAC;EACzE;EACA,IAAIoT,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAChU,GAAG,CAACU,UAAU,GAAG,CAAC;EAClC;EACAuT,eAAeA,CAAC/C,YAAY,EAAEC,SAAS,EAAExX,OAAO,EAAEmI,MAAM,EAAE;IACtD,MAAMoS,KAAK,GAAG,IAAI,CAACN,mBAAmB,CAACxM,IAAI,CAAE+M,CAAC,IAAKA,CAAC,CAAC9X,KAAK,CAAC6U,YAAY,EAAEC,SAAS,EAAExX,OAAO,EAAEmI,MAAM,CAAC,CAAC;IACrG,OAAOoS,KAAK,IAAI,IAAI;EACxB;EACAE,WAAWA,CAAClD,YAAY,EAAEpP,MAAM,EAAEvG,MAAM,EAAE;IACtC,OAAO,IAAI,CAACsY,kBAAkB,CAACxC,WAAW,CAACH,YAAY,EAAEpP,MAAM,EAAEvG,MAAM,CAAC;EAC5E;AACJ;AACA,SAASwY,wBAAwBA,CAACtD,WAAW,EAAE7P,MAAM,EAAE0S,UAAU,EAAE;EAC/D,MAAMhR,QAAQ,GAAG,CAAC,CAACnE,SAAS,EAAEE,OAAO,KAAK,IAAI,CAAC;EAC/C,MAAMgE,SAAS,GAAG;IAAErH,IAAI,EAAE/B,qBAAqB,CAACyJ,QAAQ;IAAEC,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAK,CAAC;EACpF,MAAMH,UAAU,GAAG;IACftG,IAAI,EAAE/B,qBAAqB,CAACoI,UAAU;IACtCgB,SAAS;IACTC,QAAQ;IACRb,OAAO,EAAE,IAAI;IACbf,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACd,CAAC;EACD,OAAO,IAAIoQ,0BAA0B,CAACN,WAAW,EAAEnP,UAAU,EAAEV,MAAM,CAAC;AAC1E;AACA,SAASkT,iBAAiBA,CAACO,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC7C,IAAIF,QAAQ,CAAClY,GAAG,CAACmY,IAAI,CAAC,EAAE;IACpB,IAAI,CAACD,QAAQ,CAAClY,GAAG,CAACoY,IAAI,CAAC,EAAE;MACrBF,QAAQ,CAAC9T,GAAG,CAACgU,IAAI,EAAEF,QAAQ,CAAC1P,GAAG,CAAC2P,IAAI,CAAC,CAAC;IAC1C;EACJ,CAAC,MACI,IAAID,QAAQ,CAAClY,GAAG,CAACoY,IAAI,CAAC,EAAE;IACzBF,QAAQ,CAAC9T,GAAG,CAAC+T,IAAI,EAAED,QAAQ,CAAC1P,GAAG,CAAC4P,IAAI,CAAC,CAAC;EAC1C;AACJ;AAEA,MAAMC,qBAAqB,GAAG,eAAgB,IAAInM,qBAAqB,CAAC,CAAC;AACzE,MAAMoM,uBAAuB,CAAC;EAC1BC,QAAQ;EACR/U,OAAO;EACPgU,WAAW;EACXgB,WAAW,GAAG,IAAIrU,GAAG,CAAC,CAAC;EACvBsU,YAAY,GAAG,IAAItU,GAAG,CAAC,CAAC;EACxBuU,OAAO,GAAG,EAAE;EACZjV,WAAWA,CAAC8U,QAAQ,EAAE/U,OAAO,EAAEgU,WAAW,EAAE;IACxC,IAAI,CAACe,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC/U,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgU,WAAW,GAAGA,WAAW;EAClC;EACAmB,QAAQA,CAACC,EAAE,EAAExV,QAAQ,EAAE;IACnB,MAAMhE,MAAM,GAAG,EAAE;IACjB,MAAMkB,QAAQ,GAAG,EAAE;IACnB,MAAMuD,GAAG,GAAGX,iBAAiB,CAAC,IAAI,CAACM,OAAO,EAAEJ,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,CAAC;IACvE,IAAIlB,MAAM,CAACe,MAAM,EAAE;MACf,MAAMrF,cAAc,CAACsE,MAAM,CAAC;IAChC,CAAC,MACI;MACD,IAAI,OAAOT,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI2B,QAAQ,CAACH,MAAM,EAAE;UACjBe,YAAY,CAACZ,QAAQ,CAAC;QAC1B;MACJ;MACA,IAAI,CAACkY,WAAW,CAACpU,GAAG,CAACwU,EAAE,EAAE/U,GAAG,CAAC;IACjC;EACJ;EACAgV,YAAYA,CAAC/O,CAAC,EAAEgP,SAAS,EAAEC,UAAU,EAAE;IACnC,MAAMvb,OAAO,GAAGsM,CAAC,CAACtM,OAAO;IACzB,MAAMM,SAAS,GAAG/C,kBAAkB,CAAC,IAAI,CAACyc,WAAW,EAAE1N,CAAC,CAAChM,SAAS,EAAEgb,SAAS,EAAEC,UAAU,CAAC;IAC1F,OAAO,IAAI,CAACvV,OAAO,CAAC3F,OAAO,CAACL,OAAO,EAAEM,SAAS,EAAEgM,CAAC,CAAC/L,QAAQ,EAAE+L,CAAC,CAAC9L,KAAK,EAAE8L,CAAC,CAAC7L,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;EAC5F;EACA+a,MAAMA,CAACJ,EAAE,EAAEpb,OAAO,EAAE8H,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,MAAMlG,MAAM,GAAG,EAAE;IACjB,MAAMyE,GAAG,GAAG,IAAI,CAAC2U,WAAW,CAAChQ,GAAG,CAACoQ,EAAE,CAAC;IACpC,IAAIvM,YAAY;IAChB,MAAM4M,aAAa,GAAG,IAAI9U,GAAG,CAAC,CAAC;IAC/B,IAAIN,GAAG,EAAE;MACLwI,YAAY,GAAGQ,uBAAuB,CAAC,IAAI,CAACrJ,OAAO,EAAEhG,OAAO,EAAEqG,GAAG,EAAE5I,eAAe,EAAED,eAAe,EAAE,IAAImJ,GAAG,CAAC,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAEmB,OAAO,EAAE+S,qBAAqB,EAAEjZ,MAAM,CAAC;MAClKiN,YAAY,CAAC3K,OAAO,CAAEwX,IAAI,IAAK;QAC3B,MAAMzT,MAAM,GAAGpL,oBAAoB,CAAC4e,aAAa,EAAEC,IAAI,CAAC1b,OAAO,EAAE,IAAI2G,GAAG,CAAC,CAAC,CAAC;QAC3E+U,IAAI,CAACnN,cAAc,CAACrK,OAAO,CAAErE,IAAI,IAAKoI,MAAM,CAACrB,GAAG,CAAC/G,IAAI,EAAE,IAAI,CAAC,CAAC;MACjE,CAAC,CAAC;IACN,CAAC,MACI;MACD+B,MAAM,CAACgB,IAAI,CAAClF,2BAA2B,CAAC,CAAC,CAAC;MAC1CmR,YAAY,GAAG,EAAE;IACrB;IACA,IAAIjN,MAAM,CAACe,MAAM,EAAE;MACf,MAAMhF,qBAAqB,CAACiE,MAAM,CAAC;IACvC;IACA6Z,aAAa,CAACvX,OAAO,CAAC,CAAC+D,MAAM,EAAEjI,OAAO,KAAK;MACvCiI,MAAM,CAAC/D,OAAO,CAAC,CAACyX,CAAC,EAAE9b,IAAI,KAAK;QACxBoI,MAAM,CAACrB,GAAG,CAAC/G,IAAI,EAAE,IAAI,CAACmG,OAAO,CAACpH,YAAY,CAACoB,OAAO,EAAEH,IAAI,EAAEL,UAAU,CAAC,CAAC;MAC1E,CAAC,CAAC;IACN,CAAC,CAAC;IACF,MAAM0b,OAAO,GAAGrM,YAAY,CAAC3L,GAAG,CAAEoJ,CAAC,IAAK;MACpC,MAAMrE,MAAM,GAAGwT,aAAa,CAACzQ,GAAG,CAACsB,CAAC,CAACtM,OAAO,CAAC;MAC3C,OAAO,IAAI,CAACqb,YAAY,CAAC/O,CAAC,EAAE,IAAI3F,GAAG,CAAC,CAAC,EAAEsB,MAAM,CAAC;IAClD,CAAC,CAAC;IACF,MAAM2T,MAAM,GAAGhe,mBAAmB,CAACsd,OAAO,CAAC;IAC3C,IAAI,CAACD,YAAY,CAACrU,GAAG,CAACwU,EAAE,EAAEQ,MAAM,CAAC;IACjCA,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,OAAO,CAACV,EAAE,CAAC,CAAC;IACxC,IAAI,CAACF,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;IACzB,OAAOA,MAAM;EACjB;EACAE,OAAOA,CAACV,EAAE,EAAE;IACR,MAAMQ,MAAM,GAAG,IAAI,CAACG,UAAU,CAACX,EAAE,CAAC;IAClCQ,MAAM,CAACE,OAAO,CAAC,CAAC;IAChB,IAAI,CAACb,YAAY,CAAChQ,MAAM,CAACmQ,EAAE,CAAC;IAC5B,MAAMY,KAAK,GAAG,IAAI,CAACd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;IAC1C,IAAII,KAAK,IAAI,CAAC,EAAE;MACZ,IAAI,CAACd,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACjC;EACJ;EACAD,UAAUA,CAACX,EAAE,EAAE;IACX,MAAMQ,MAAM,GAAG,IAAI,CAACX,YAAY,CAACjQ,GAAG,CAACoQ,EAAE,CAAC;IACxC,IAAI,CAACQ,MAAM,EAAE;MACT,MAAM/d,aAAa,CAACud,EAAE,CAAC;IAC3B;IACA,OAAOQ,MAAM;EACjB;EACAM,MAAMA,CAACd,EAAE,EAAEpb,OAAO,EAAEmc,SAAS,EAAEC,QAAQ,EAAE;IACrC;IACA,MAAMC,SAAS,GAAGte,kBAAkB,CAACiC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzDlC,cAAc,CAAC,IAAI,CAACie,UAAU,CAACX,EAAE,CAAC,EAAEe,SAAS,EAAEE,SAAS,EAAED,QAAQ,CAAC;IACnE,OAAO,MAAM,CAAE,CAAC;EACpB;EACAE,OAAOA,CAAClB,EAAE,EAAEpb,OAAO,EAAEsc,OAAO,EAAEC,IAAI,EAAE;IAChC,IAAID,OAAO,IAAI,UAAU,EAAE;MACvB,IAAI,CAACnB,QAAQ,CAACC,EAAE,EAAEmB,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1B;IACJ;IACA,IAAID,OAAO,IAAI,QAAQ,EAAE;MACrB,MAAMxU,OAAO,GAAIyU,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAE;MAC/B,IAAI,CAACf,MAAM,CAACJ,EAAE,EAAEpb,OAAO,EAAE8H,OAAO,CAAC;MACjC;IACJ;IACA,MAAM8T,MAAM,GAAG,IAAI,CAACG,UAAU,CAACX,EAAE,CAAC;IAClC,QAAQkB,OAAO;MACX,KAAK,MAAM;QACPV,MAAM,CAACY,IAAI,CAAC,CAAC;QACb;MACJ,KAAK,OAAO;QACRZ,MAAM,CAACa,KAAK,CAAC,CAAC;QACd;MACJ,KAAK,OAAO;QACRb,MAAM,CAACc,KAAK,CAAC,CAAC;QACd;MACJ,KAAK,SAAS;QACVd,MAAM,CAACe,OAAO,CAAC,CAAC;QAChB;MACJ,KAAK,QAAQ;QACTf,MAAM,CAACgB,MAAM,CAAC,CAAC;QACf;MACJ,KAAK,MAAM;QACPhB,MAAM,CAACiB,IAAI,CAAC,CAAC;QACb;MACJ,KAAK,aAAa;QACdjB,MAAM,CAACkB,WAAW,CAAChY,UAAU,CAACyX,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC;MACJ,KAAK,SAAS;QACV,IAAI,CAACT,OAAO,CAACV,EAAE,CAAC;QAChB;IACR;EACJ;AACJ;AAEA,MAAM2B,gBAAgB,GAAG,mBAAmB;AAC5C,MAAMC,eAAe,GAAG,oBAAoB;AAC5C,MAAMC,kBAAkB,GAAG,qBAAqB;AAChD,MAAMC,iBAAiB,GAAG,sBAAsB;AAChD,MAAMC,cAAc,GAAG,kBAAkB;AACzC,MAAMC,aAAa,GAAG,mBAAmB;AACzC,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,kBAAkB,GAAG;EACvBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,KAAK;EACpBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,0BAA0B,GAAG;EAC/BL,WAAW,EAAE,EAAE;EACfE,UAAU,EAAE,KAAK;EACjBD,aAAa,EAAE,KAAK;EACpBE,YAAY,EAAE,KAAK;EACnBC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAME,YAAY,GAAG,cAAc;AACnC,MAAMC,UAAU,CAAC;EACbP,WAAW;EACXvb,KAAK;EACL8F,OAAO;EACP,IAAIK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,OAAO,CAACK,MAAM;EAC9B;EACAlC,WAAWA,CAACgP,KAAK,EAAEsI,WAAW,GAAG,EAAE,EAAE;IACjC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,MAAMQ,KAAK,GAAG9I,KAAK,IAAIA,KAAK,CAAC1M,cAAc,CAAC,OAAO,CAAC;IACpD,MAAMvG,KAAK,GAAG+b,KAAK,GAAG9I,KAAK,CAAC,OAAO,CAAC,GAAGA,KAAK;IAC5C,IAAI,CAACjT,KAAK,GAAGgc,qBAAqB,CAAChc,KAAK,CAAC;IACzC,IAAI+b,KAAK,EAAE;MACP;MACA,MAAM;QAAE/b,KAAK;QAAE,GAAG8F;MAAQ,CAAC,GAAGmN,KAAK;MACnC,IAAI,CAACnN,OAAO,GAAGA,OAAO;IAC1B,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAG,CAAC,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACA,OAAO,CAACK,MAAM,EAAE;MACtB,IAAI,CAACL,OAAO,CAACK,MAAM,GAAG,CAAC,CAAC;IAC5B;EACJ;EACA8V,aAAaA,CAACnW,OAAO,EAAE;IACnB,MAAMwL,SAAS,GAAGxL,OAAO,CAACK,MAAM;IAChC,IAAImL,SAAS,EAAE;MACX,MAAMG,SAAS,GAAG,IAAI,CAAC3L,OAAO,CAACK,MAAM;MACrCyC,MAAM,CAACpE,IAAI,CAAC8M,SAAS,CAAC,CAACpP,OAAO,CAAErE,IAAI,IAAK;QACrC,IAAI4T,SAAS,CAAC5T,IAAI,CAAC,IAAI,IAAI,EAAE;UACzB4T,SAAS,CAAC5T,IAAI,CAAC,GAAGyT,SAAS,CAACzT,IAAI,CAAC;QACrC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACA,MAAMqe,UAAU,GAAG,MAAM;AACzB,MAAMC,mBAAmB,GAAG,eAAgB,IAAIL,UAAU,CAACI,UAAU,CAAC;AACtE,MAAME,4BAA4B,CAAC;EAC/BhD,EAAE;EACFiD,WAAW;EACXC,OAAO;EACPpD,OAAO,GAAG,EAAE;EACZqD,SAAS,GAAG,IAAI5X,GAAG,CAAC,CAAC;EACrB6X,MAAM,GAAG,EAAE;EACXC,iBAAiB,GAAG,IAAI9X,GAAG,CAAC,CAAC;EAC7B+X,cAAc;EACdzY,WAAWA,CAACmV,EAAE,EAAEiD,WAAW,EAAEC,OAAO,EAAE;IAClC,IAAI,CAAClD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACiD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,cAAc,GAAG,SAAS,GAAGtD,EAAE;IACpCuD,QAAQ,CAACN,WAAW,EAAE,IAAI,CAACK,cAAc,CAAC;EAC9C;EACAxC,MAAMA,CAAClc,OAAO,EAAEyD,IAAI,EAAEmb,KAAK,EAAExC,QAAQ,EAAE;IACnC,IAAI,CAAC,IAAI,CAACmC,SAAS,CAAC/b,GAAG,CAACiB,IAAI,CAAC,EAAE;MAC3B,MAAMrF,cAAc,CAACwgB,KAAK,EAAEnb,IAAI,CAAC;IACrC;IACA,IAAImb,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACjc,MAAM,IAAI,CAAC,EAAE;MACpC,MAAMtE,YAAY,CAACoF,IAAI,CAAC;IAC5B;IACA,IAAI,CAACob,mBAAmB,CAACD,KAAK,CAAC,EAAE;MAC7B,MAAMtgB,uBAAuB,CAACsgB,KAAK,EAAEnb,IAAI,CAAC;IAC9C;IACA,MAAMqb,SAAS,GAAGjiB,oBAAoB,CAAC,IAAI,CAAC4hB,iBAAiB,EAAEze,OAAO,EAAE,EAAE,CAAC;IAC3E,MAAM+e,IAAI,GAAG;MAAEtb,IAAI;MAAEmb,KAAK;MAAExC;IAAS,CAAC;IACtC0C,SAAS,CAAClc,IAAI,CAACmc,IAAI,CAAC;IACpB,MAAMC,kBAAkB,GAAGniB,oBAAoB,CAAC,IAAI,CAACyhB,OAAO,CAACW,eAAe,EAAEjf,OAAO,EAAE,IAAI2G,GAAG,CAAC,CAAC,CAAC;IACjG,IAAI,CAACqY,kBAAkB,CAACxc,GAAG,CAACiB,IAAI,CAAC,EAAE;MAC/Bkb,QAAQ,CAAC3e,OAAO,EAAExB,oBAAoB,CAAC;MACvCmgB,QAAQ,CAAC3e,OAAO,EAAExB,oBAAoB,GAAG,GAAG,GAAGiF,IAAI,CAAC;MACpDub,kBAAkB,CAACpY,GAAG,CAACnD,IAAI,EAAE0a,mBAAmB,CAAC;IACrD;IACA,OAAO,MAAM;MACT;MACA;MACA;MACA,IAAI,CAACG,OAAO,CAACY,UAAU,CAAC,MAAM;QAC1B,MAAMlD,KAAK,GAAG8C,SAAS,CAAC5T,OAAO,CAAC6T,IAAI,CAAC;QACrC,IAAI/C,KAAK,IAAI,CAAC,EAAE;UACZ8C,SAAS,CAAC7C,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QAC9B;QACA,IAAI,CAAC,IAAI,CAACuC,SAAS,CAAC/b,GAAG,CAACiB,IAAI,CAAC,EAAE;UAC3Bub,kBAAkB,CAAC/T,MAAM,CAACxH,IAAI,CAAC;QACnC;MACJ,CAAC,CAAC;IACN,CAAC;EACL;EACA0X,QAAQA,CAAC1X,IAAI,EAAE4C,GAAG,EAAE;IAChB,IAAI,IAAI,CAACkY,SAAS,CAAC/b,GAAG,CAACiB,IAAI,CAAC,EAAE;MAC1B;MACA,OAAO,KAAK;IAChB,CAAC,MACI;MACD,IAAI,CAAC8a,SAAS,CAAC3X,GAAG,CAACnD,IAAI,EAAE4C,GAAG,CAAC;MAC7B,OAAO,IAAI;IACf;EACJ;EACA8Y,WAAWA,CAAC1b,IAAI,EAAE;IACd,MAAM2b,OAAO,GAAG,IAAI,CAACb,SAAS,CAACvT,GAAG,CAACvH,IAAI,CAAC;IACxC,IAAI,CAAC2b,OAAO,EAAE;MACV,MAAM7gB,mBAAmB,CAACkF,IAAI,CAAC;IACnC;IACA,OAAO2b,OAAO;EAClB;EACAA,OAAOA,CAACpf,OAAO,EAAE8W,WAAW,EAAE9U,KAAK,EAAEqd,iBAAiB,GAAG,IAAI,EAAE;IAC3D,MAAMD,OAAO,GAAG,IAAI,CAACD,WAAW,CAACrI,WAAW,CAAC;IAC7C,MAAM8E,MAAM,GAAG,IAAI0D,yBAAyB,CAAC,IAAI,CAAClE,EAAE,EAAEtE,WAAW,EAAE9W,OAAO,CAAC;IAC3E,IAAIgf,kBAAkB,GAAG,IAAI,CAACV,OAAO,CAACW,eAAe,CAACjU,GAAG,CAAChL,OAAO,CAAC;IAClE,IAAI,CAACgf,kBAAkB,EAAE;MACrBL,QAAQ,CAAC3e,OAAO,EAAExB,oBAAoB,CAAC;MACvCmgB,QAAQ,CAAC3e,OAAO,EAAExB,oBAAoB,GAAG,GAAG,GAAGsY,WAAW,CAAC;MAC3D,IAAI,CAACwH,OAAO,CAACW,eAAe,CAACrY,GAAG,CAAC5G,OAAO,EAAGgf,kBAAkB,GAAG,IAAIrY,GAAG,CAAC,CAAE,CAAC;IAC/E;IACA,IAAInC,SAAS,GAAGwa,kBAAkB,CAAChU,GAAG,CAAC8L,WAAW,CAAC;IACnD,MAAMpS,OAAO,GAAG,IAAIoZ,UAAU,CAAC9b,KAAK,EAAE,IAAI,CAACoZ,EAAE,CAAC;IAC9C,MAAM2C,KAAK,GAAG/b,KAAK,IAAIA,KAAK,CAACuG,cAAc,CAAC,OAAO,CAAC;IACpD,IAAI,CAACwV,KAAK,IAAIvZ,SAAS,EAAE;MACrBE,OAAO,CAACuZ,aAAa,CAACzZ,SAAS,CAACsD,OAAO,CAAC;IAC5C;IACAkX,kBAAkB,CAACpY,GAAG,CAACkQ,WAAW,EAAEpS,OAAO,CAAC;IAC5C,IAAI,CAACF,SAAS,EAAE;MACZA,SAAS,GAAG2Z,mBAAmB;IACnC;IACA,MAAM3F,SAAS,GAAG9T,OAAO,CAAC1C,KAAK,KAAKkc,UAAU;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC1F,SAAS,IAAIhU,SAAS,CAACxC,KAAK,KAAK0C,OAAO,CAAC1C,KAAK,EAAE;MACjD;MACA;MACA,IAAI,CAACud,SAAS,CAAC/a,SAAS,CAAC2D,MAAM,EAAEzD,OAAO,CAACyD,MAAM,CAAC,EAAE;QAC9C,MAAMvG,MAAM,GAAG,EAAE;QACjB,MAAMoV,UAAU,GAAGoI,OAAO,CAAC3E,WAAW,CAACjW,SAAS,CAACxC,KAAK,EAAEwC,SAAS,CAAC2D,MAAM,EAAEvG,MAAM,CAAC;QACjF,MAAMqV,QAAQ,GAAGmI,OAAO,CAAC3E,WAAW,CAAC/V,OAAO,CAAC1C,KAAK,EAAE0C,OAAO,CAACyD,MAAM,EAAEvG,MAAM,CAAC;QAC3E,IAAIA,MAAM,CAACe,MAAM,EAAE;UACf,IAAI,CAAC2b,OAAO,CAACkB,WAAW,CAAC5d,MAAM,CAAC;QACpC,CAAC,MACI;UACD,IAAI,CAAC0c,OAAO,CAACY,UAAU,CAAC,MAAM;YAC1BjhB,WAAW,CAAC+B,OAAO,EAAEgX,UAAU,CAAC;YAChC9Y,SAAS,CAAC8B,OAAO,EAAEiX,QAAQ,CAAC;UAChC,CAAC,CAAC;QACN;MACJ;MACA;IACJ;IACA,MAAMwI,gBAAgB,GAAG5iB,oBAAoB,CAAC,IAAI,CAACyhB,OAAO,CAACoB,gBAAgB,EAAE1f,OAAO,EAAE,EAAE,CAAC;IACzFyf,gBAAgB,CAACvb,OAAO,CAAE0X,MAAM,IAAK;MACjC;MACA;MACA;MACA;MACA,IAAIA,MAAM,CAAC2B,WAAW,IAAI,IAAI,CAACnC,EAAE,IAAIQ,MAAM,CAAC9E,WAAW,IAAIA,WAAW,IAAI8E,MAAM,CAAC+D,MAAM,EAAE;QACrF/D,MAAM,CAACE,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;IACF,IAAInU,UAAU,GAAGyX,OAAO,CAAC9E,eAAe,CAAC9V,SAAS,CAACxC,KAAK,EAAE0C,OAAO,CAAC1C,KAAK,EAAEhC,OAAO,EAAE0E,OAAO,CAACyD,MAAM,CAAC;IACjG,IAAIyX,oBAAoB,GAAG,KAAK;IAChC,IAAI,CAACjY,UAAU,EAAE;MACb,IAAI,CAAC0X,iBAAiB,EAClB;MACJ1X,UAAU,GAAGyX,OAAO,CAAClF,kBAAkB;MACvC0F,oBAAoB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACtB,OAAO,CAACuB,kBAAkB,EAAE;IACjC,IAAI,CAACrB,MAAM,CAAC5b,IAAI,CAAC;MACb5C,OAAO;MACP8W,WAAW;MACXnP,UAAU;MACVnD,SAAS;MACTE,OAAO;MACPkX,MAAM;MACNgE;IACJ,CAAC,CAAC;IACF,IAAI,CAACA,oBAAoB,EAAE;MACvBjB,QAAQ,CAAC3e,OAAO,EAAE+c,gBAAgB,CAAC;MACnCnB,MAAM,CAACkE,OAAO,CAAC,MAAM;QACjBC,WAAW,CAAC/f,OAAO,EAAE+c,gBAAgB,CAAC;MAC1C,CAAC,CAAC;IACN;IACAnB,MAAM,CAACoE,MAAM,CAAC,MAAM;MAChB,IAAIhE,KAAK,GAAG,IAAI,CAACd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;MACxC,IAAII,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACd,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACjC;MACA,MAAMd,OAAO,GAAG,IAAI,CAACoD,OAAO,CAACoB,gBAAgB,CAAC1U,GAAG,CAAChL,OAAO,CAAC;MAC1D,IAAIkb,OAAO,EAAE;QACT,IAAIc,KAAK,GAAGd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;QACnC,IAAII,KAAK,IAAI,CAAC,EAAE;UACZd,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QAC5B;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACd,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;IACzB6D,gBAAgB,CAAC7c,IAAI,CAACgZ,MAAM,CAAC;IAC7B,OAAOA,MAAM;EACjB;EACAqE,UAAUA,CAACxc,IAAI,EAAE;IACb,IAAI,CAAC8a,SAAS,CAACtT,MAAM,CAACxH,IAAI,CAAC;IAC3B,IAAI,CAAC6a,OAAO,CAACW,eAAe,CAAC/a,OAAO,CAAEwW,QAAQ,IAAKA,QAAQ,CAACzP,MAAM,CAACxH,IAAI,CAAC,CAAC;IACzE,IAAI,CAACgb,iBAAiB,CAACva,OAAO,CAAC,CAAC4a,SAAS,EAAE9e,OAAO,KAAK;MACnD,IAAI,CAACye,iBAAiB,CAAC7X,GAAG,CAAC5G,OAAO,EAAE8e,SAAS,CAAC9b,MAAM,CAAEuX,KAAK,IAAK;QAC5D,OAAOA,KAAK,CAAC9W,IAAI,IAAIA,IAAI;MAC7B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACAyc,iBAAiBA,CAAClgB,OAAO,EAAE;IACvB,IAAI,CAACse,OAAO,CAACW,eAAe,CAAChU,MAAM,CAACjL,OAAO,CAAC;IAC5C,IAAI,CAACye,iBAAiB,CAACxT,MAAM,CAACjL,OAAO,CAAC;IACtC,MAAMmgB,cAAc,GAAG,IAAI,CAAC7B,OAAO,CAACoB,gBAAgB,CAAC1U,GAAG,CAAChL,OAAO,CAAC;IACjE,IAAImgB,cAAc,EAAE;MAChBA,cAAc,CAACjc,OAAO,CAAE0X,MAAM,IAAKA,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC;MACpD,IAAI,CAACwC,OAAO,CAACoB,gBAAgB,CAACzU,MAAM,CAACjL,OAAO,CAAC;IACjD;EACJ;EACAogB,8BAA8BA,CAAC9Q,WAAW,EAAEpJ,OAAO,EAAE;IACjD,MAAMiO,QAAQ,GAAG,IAAI,CAACmK,OAAO,CAAC3Y,MAAM,CAAC1F,KAAK,CAACqP,WAAW,EAAErS,mBAAmB,EAAE,IAAI,CAAC;IAClF;IACA;IACA;IACAkX,QAAQ,CAACjQ,OAAO,CAAEyU,GAAG,IAAK;MACtB;MACA;MACA,IAAIA,GAAG,CAACkF,YAAY,CAAC,EACjB;MACJ,MAAMwC,UAAU,GAAG,IAAI,CAAC/B,OAAO,CAACgC,wBAAwB,CAAC3H,GAAG,CAAC;MAC7D,IAAI0H,UAAU,CAAC9Z,IAAI,EAAE;QACjB8Z,UAAU,CAACnc,OAAO,CAAEqc,EAAE,IAAKA,EAAE,CAACC,qBAAqB,CAAC7H,GAAG,EAAEzS,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;MACnF,CAAC,MACI;QACD,IAAI,CAACga,iBAAiB,CAACvH,GAAG,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC2F,OAAO,CAACmC,wBAAwB,CAAC,MAAMtM,QAAQ,CAACjQ,OAAO,CAAEyU,GAAG,IAAK,IAAI,CAACuH,iBAAiB,CAACvH,GAAG,CAAC,CAAC,CAAC;EACvG;EACA6H,qBAAqBA,CAACxgB,OAAO,EAAEkG,OAAO,EAAEwa,oBAAoB,EAAErB,iBAAiB,EAAE;IAC7E,MAAMsB,aAAa,GAAG,IAAI,CAACrC,OAAO,CAACW,eAAe,CAACjU,GAAG,CAAChL,OAAO,CAAC;IAC/D,MAAM4gB,sBAAsB,GAAG,IAAIja,GAAG,CAAC,CAAC;IACxC,IAAIga,aAAa,EAAE;MACf,MAAMzF,OAAO,GAAG,EAAE;MAClByF,aAAa,CAACzc,OAAO,CAAC,CAAC2c,KAAK,EAAE/J,WAAW,KAAK;QAC1C8J,sBAAsB,CAACha,GAAG,CAACkQ,WAAW,EAAE+J,KAAK,CAAC7e,KAAK,CAAC;QACpD;QACA;QACA,IAAI,IAAI,CAACuc,SAAS,CAAC/b,GAAG,CAACsU,WAAW,CAAC,EAAE;UACjC,MAAM8E,MAAM,GAAG,IAAI,CAACwD,OAAO,CAACpf,OAAO,EAAE8W,WAAW,EAAEoH,UAAU,EAAEmB,iBAAiB,CAAC;UAChF,IAAIzD,MAAM,EAAE;YACRV,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;UACxB;QACJ;MACJ,CAAC,CAAC;MACF,IAAIV,OAAO,CAACvY,MAAM,EAAE;QAChB,IAAI,CAAC2b,OAAO,CAACwC,oBAAoB,CAAC,IAAI,CAAC1F,EAAE,EAAEpb,OAAO,EAAE,IAAI,EAAEkG,OAAO,EAAE0a,sBAAsB,CAAC;QAC1F,IAAIF,oBAAoB,EAAE;UACtB9iB,mBAAmB,CAACsd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAM,IAAI,CAAC1B,OAAO,CAACyC,gBAAgB,CAAC/gB,OAAO,CAAC,CAAC;QACrF;QACA,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACAghB,8BAA8BA,CAAChhB,OAAO,EAAE;IACpC,MAAM8e,SAAS,GAAG,IAAI,CAACL,iBAAiB,CAACzT,GAAG,CAAChL,OAAO,CAAC;IACrD,MAAMihB,aAAa,GAAG,IAAI,CAAC3C,OAAO,CAACW,eAAe,CAACjU,GAAG,CAAChL,OAAO,CAAC;IAC/D;IACA;IACA,IAAI8e,SAAS,IAAImC,aAAa,EAAE;MAC5B,MAAMC,eAAe,GAAG,IAAIhf,GAAG,CAAC,CAAC;MACjC4c,SAAS,CAAC5a,OAAO,CAAEid,QAAQ,IAAK;QAC5B,MAAMrK,WAAW,GAAGqK,QAAQ,CAAC1d,IAAI;QACjC,IAAIyd,eAAe,CAAC1e,GAAG,CAACsU,WAAW,CAAC,EAChC;QACJoK,eAAe,CAAC1Y,GAAG,CAACsO,WAAW,CAAC;QAChC,MAAMsI,OAAO,GAAG,IAAI,CAACb,SAAS,CAACvT,GAAG,CAAC8L,WAAW,CAAC;QAC/C,MAAMnP,UAAU,GAAGyX,OAAO,CAAClF,kBAAkB;QAC7C,MAAM1V,SAAS,GAAGyc,aAAa,CAACjW,GAAG,CAAC8L,WAAW,CAAC,IAAIqH,mBAAmB;QACvE,MAAMzZ,OAAO,GAAG,IAAIoZ,UAAU,CAACI,UAAU,CAAC;QAC1C,MAAMtC,MAAM,GAAG,IAAI0D,yBAAyB,CAAC,IAAI,CAAClE,EAAE,EAAEtE,WAAW,EAAE9W,OAAO,CAAC;QAC3E,IAAI,CAACse,OAAO,CAACuB,kBAAkB,EAAE;QACjC,IAAI,CAACrB,MAAM,CAAC5b,IAAI,CAAC;UACb5C,OAAO;UACP8W,WAAW;UACXnP,UAAU;UACVnD,SAAS;UACTE,OAAO;UACPkX,MAAM;UACNgE,oBAAoB,EAAE;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACAwB,UAAUA,CAACphB,OAAO,EAAEkG,OAAO,EAAE;IACzB,MAAMmb,MAAM,GAAG,IAAI,CAAC/C,OAAO;IAC3B,IAAIte,OAAO,CAACshB,iBAAiB,EAAE;MAC3B,IAAI,CAAClB,8BAA8B,CAACpgB,OAAO,EAAEkG,OAAO,CAAC;IACzD;IACA;IACA,IAAI,IAAI,CAACsa,qBAAqB,CAACxgB,OAAO,EAAEkG,OAAO,EAAE,IAAI,CAAC,EAClD;IACJ;IACA;IACA,IAAIqb,iCAAiC,GAAG,KAAK;IAC7C,IAAIF,MAAM,CAACG,eAAe,EAAE;MACxB,MAAMC,cAAc,GAAGJ,MAAM,CAACnG,OAAO,CAACvY,MAAM,GACtC0e,MAAM,CAACK,uBAAuB,CAAC1W,GAAG,CAAChL,OAAO,CAAC,GAC3C,EAAE;MACR;MACA;MACA;MACA;MACA,IAAIyhB,cAAc,IAAIA,cAAc,CAAC9e,MAAM,EAAE;QACzC4e,iCAAiC,GAAG,IAAI;MAC5C,CAAC,MACI;QACD,IAAII,MAAM,GAAG3hB,OAAO;QACpB,OAAQ2hB,MAAM,GAAGA,MAAM,CAACC,UAAU,EAAG;UACjC,MAAMC,QAAQ,GAAGR,MAAM,CAACpC,eAAe,CAACjU,GAAG,CAAC2W,MAAM,CAAC;UACnD,IAAIE,QAAQ,EAAE;YACVN,iCAAiC,GAAG,IAAI;YACxC;UACJ;QACJ;MACJ;IACJ;IACA;IACA;IACA;IACA;IACA,IAAI,CAACP,8BAA8B,CAAChhB,OAAO,CAAC;IAC5C;IACA;IACA,IAAIuhB,iCAAiC,EAAE;MACnCF,MAAM,CAACP,oBAAoB,CAAC,IAAI,CAAC1F,EAAE,EAAEpb,OAAO,EAAE,KAAK,EAAEkG,OAAO,CAAC;IACjE,CAAC,MACI;MACD,MAAM4b,WAAW,GAAG9hB,OAAO,CAAC6d,YAAY,CAAC;MACzC,IAAI,CAACiE,WAAW,IAAIA,WAAW,KAAKxE,kBAAkB,EAAE;QACpD;QACA;QACA+D,MAAM,CAACnC,UAAU,CAAC,MAAM,IAAI,CAACgB,iBAAiB,CAAClgB,OAAO,CAAC,CAAC;QACxDqhB,MAAM,CAACU,sBAAsB,CAAC/hB,OAAO,CAAC;QACtCqhB,MAAM,CAACW,kBAAkB,CAAChiB,OAAO,EAAEkG,OAAO,CAAC;MAC/C;IACJ;EACJ;EACA+b,UAAUA,CAACjiB,OAAO,EAAE2hB,MAAM,EAAE;IACxBhD,QAAQ,CAAC3e,OAAO,EAAE,IAAI,CAAC0e,cAAc,CAAC;EAC1C;EACAwD,sBAAsBA,CAACC,WAAW,EAAE;IAChC,MAAMtT,YAAY,GAAG,EAAE;IACvB,IAAI,CAAC2P,MAAM,CAACta,OAAO,CAAEqW,KAAK,IAAK;MAC3B,MAAMqB,MAAM,GAAGrB,KAAK,CAACqB,MAAM;MAC3B,IAAIA,MAAM,CAACwG,SAAS,EAChB;MACJ,MAAMpiB,OAAO,GAAGua,KAAK,CAACva,OAAO;MAC7B,MAAM8e,SAAS,GAAG,IAAI,CAACL,iBAAiB,CAACzT,GAAG,CAAChL,OAAO,CAAC;MACrD,IAAI8e,SAAS,EAAE;QACXA,SAAS,CAAC5a,OAAO,CAAEid,QAAQ,IAAK;UAC5B,IAAIA,QAAQ,CAAC1d,IAAI,IAAI8W,KAAK,CAACzD,WAAW,EAAE;YACpC,MAAMuF,SAAS,GAAGte,kBAAkB,CAACiC,OAAO,EAAEua,KAAK,CAACzD,WAAW,EAAEyD,KAAK,CAAC/V,SAAS,CAACxC,KAAK,EAAEuY,KAAK,CAAC7V,OAAO,CAAC1C,KAAK,CAAC;YAC5Gqa,SAAS,CAAC,OAAO,CAAC,GAAG8F,WAAW;YAChCrkB,cAAc,CAACyc,KAAK,CAACqB,MAAM,EAAEuF,QAAQ,CAACvC,KAAK,EAAEvC,SAAS,EAAE8E,QAAQ,CAAC/E,QAAQ,CAAC;UAC9E;QACJ,CAAC,CAAC;MACN;MACA,IAAIR,MAAM,CAACyG,gBAAgB,EAAE;QACzB,IAAI,CAAC/D,OAAO,CAACY,UAAU,CAAC,MAAM;UAC1B;UACA;UACAtD,MAAM,CAACE,OAAO,CAAC,CAAC;QACpB,CAAC,CAAC;MACN,CAAC,MACI;QACDjN,YAAY,CAACjM,IAAI,CAAC2X,KAAK,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,CAACiE,MAAM,GAAG,EAAE;IAChB,OAAO3P,YAAY,CAACyT,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B;MACA;MACA,MAAMC,EAAE,GAAGF,CAAC,CAAC5a,UAAU,CAACtB,GAAG,CAACW,QAAQ;MACpC,MAAM0b,EAAE,GAAGF,CAAC,CAAC7a,UAAU,CAACtB,GAAG,CAACW,QAAQ;MACpC,IAAIyb,EAAE,IAAI,CAAC,IAAIC,EAAE,IAAI,CAAC,EAAE;QACpB,OAAOD,EAAE,GAAGC,EAAE;MAClB;MACA,OAAO,IAAI,CAACpE,OAAO,CAAC3Y,MAAM,CAAClK,eAAe,CAAC8mB,CAAC,CAACviB,OAAO,EAAEwiB,CAAC,CAACxiB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7E,CAAC,CAAC;EACN;EACA8b,OAAOA,CAAC5V,OAAO,EAAE;IACb,IAAI,CAACgV,OAAO,CAAChX,OAAO,CAAEye,CAAC,IAAKA,CAAC,CAAC7G,OAAO,CAAC,CAAC,CAAC;IACxC,IAAI,CAACsE,8BAA8B,CAAC,IAAI,CAAC/B,WAAW,EAAEnY,OAAO,CAAC;EAClE;AACJ;AACA,MAAM0c,yBAAyB,CAAC;EAC5B7H,QAAQ;EACRpV,MAAM;EACNqU,WAAW;EACXkB,OAAO,GAAG,EAAE;EACZ2H,eAAe,GAAG,IAAIlc,GAAG,CAAC,CAAC;EAC3B+Y,gBAAgB,GAAG,IAAI/Y,GAAG,CAAC,CAAC;EAC5B+a,uBAAuB,GAAG,IAAI/a,GAAG,CAAC,CAAC;EACnCsY,eAAe,GAAG,IAAItY,GAAG,CAAC,CAAC;EAC3Bmc,aAAa,GAAG,IAAI5gB,GAAG,CAAC,CAAC;EACzBsf,eAAe,GAAG,CAAC;EACnB3B,kBAAkB,GAAG,CAAC;EACtBkD,gBAAgB,GAAG,CAAC,CAAC;EACrBC,cAAc,GAAG,EAAE;EACnBC,SAAS,GAAG,EAAE;EACdC,aAAa,GAAG,EAAE;EAClBC,uBAAuB,GAAG,IAAIxc,GAAG,CAAC,CAAC;EACnCyc,sBAAsB,GAAG,EAAE;EAC3BC,sBAAsB,GAAG,EAAE;EAC3B;EACAC,iBAAiB,GAAGA,CAACtjB,OAAO,EAAEkG,OAAO,KAAK,CAAE,CAAC;EAC7C;EACA8b,kBAAkBA,CAAChiB,OAAO,EAAEkG,OAAO,EAAE;IACjC,IAAI,CAACod,iBAAiB,CAACtjB,OAAO,EAAEkG,OAAO,CAAC;EAC5C;EACAD,WAAWA,CAAC8U,QAAQ,EAAEpV,MAAM,EAAEqU,WAAW,EAAE;IACvC,IAAI,CAACe,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACpV,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqU,WAAW,GAAGA,WAAW;EAClC;EACA,IAAIuJ,aAAaA,CAAA,EAAG;IAChB,MAAMrI,OAAO,GAAG,EAAE;IAClB,IAAI,CAAC8H,cAAc,CAAC9e,OAAO,CAAEqc,EAAE,IAAK;MAChCA,EAAE,CAACrF,OAAO,CAAChX,OAAO,CAAE0X,MAAM,IAAK;QAC3B,IAAIA,MAAM,CAAC+D,MAAM,EAAE;UACfzE,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;QACxB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOV,OAAO;EAClB;EACAsI,eAAeA,CAACjG,WAAW,EAAEc,WAAW,EAAE;IACtC,MAAMkC,EAAE,GAAG,IAAInC,4BAA4B,CAACb,WAAW,EAAEc,WAAW,EAAE,IAAI,CAAC;IAC3E,IAAI,IAAI,CAACtD,QAAQ,IAAI,IAAI,CAACpV,MAAM,CAAClK,eAAe,CAAC,IAAI,CAACsf,QAAQ,EAAEsD,WAAW,CAAC,EAAE;MAC1E,IAAI,CAACoF,qBAAqB,CAAClD,EAAE,EAAElC,WAAW,CAAC;IAC/C,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACwE,eAAe,CAACjc,GAAG,CAACyX,WAAW,EAAEkC,EAAE,CAAC;MACzC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACmD,mBAAmB,CAACrF,WAAW,CAAC;IACzC;IACA,OAAQ,IAAI,CAAC0E,gBAAgB,CAACxF,WAAW,CAAC,GAAGgD,EAAE;EACnD;EACAkD,qBAAqBA,CAAClD,EAAE,EAAElC,WAAW,EAAE;IACnC,MAAMsF,aAAa,GAAG,IAAI,CAACX,cAAc;IACzC,MAAMG,uBAAuB,GAAG,IAAI,CAACA,uBAAuB;IAC5D,MAAMhX,KAAK,GAAGwX,aAAa,CAAChhB,MAAM,GAAG,CAAC;IACtC,IAAIwJ,KAAK,IAAI,CAAC,EAAE;MACZ,IAAIyX,KAAK,GAAG,KAAK;MACjB;MACA;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACle,MAAM,CAACjK,gBAAgB,CAAC2iB,WAAW,CAAC;MACxD,OAAOwF,QAAQ,EAAE;QACb,MAAMC,UAAU,GAAGX,uBAAuB,CAACnY,GAAG,CAAC6Y,QAAQ,CAAC;QACxD,IAAIC,UAAU,EAAE;UACZ;UACA;UACA,MAAM9H,KAAK,GAAG2H,aAAa,CAACzY,OAAO,CAAC4Y,UAAU,CAAC;UAC/CH,aAAa,CAAC1H,MAAM,CAACD,KAAK,GAAG,CAAC,EAAE,CAAC,EAAEuE,EAAE,CAAC;UACtCqD,KAAK,GAAG,IAAI;UACZ;QACJ;QACAC,QAAQ,GAAG,IAAI,CAACle,MAAM,CAACjK,gBAAgB,CAACmoB,QAAQ,CAAC;MACrD;MACA,IAAI,CAACD,KAAK,EAAE;QACR;QACA;QACA;QACAD,aAAa,CAACI,OAAO,CAACxD,EAAE,CAAC;MAC7B;IACJ,CAAC,MACI;MACDoD,aAAa,CAAC/gB,IAAI,CAAC2d,EAAE,CAAC;IAC1B;IACA4C,uBAAuB,CAACvc,GAAG,CAACyX,WAAW,EAAEkC,EAAE,CAAC;IAC5C,OAAOA,EAAE;EACb;EACApF,QAAQA,CAACoC,WAAW,EAAEc,WAAW,EAAE;IAC/B,IAAIkC,EAAE,GAAG,IAAI,CAACwC,gBAAgB,CAACxF,WAAW,CAAC;IAC3C,IAAI,CAACgD,EAAE,EAAE;MACLA,EAAE,GAAG,IAAI,CAACiD,eAAe,CAACjG,WAAW,EAAEc,WAAW,CAAC;IACvD;IACA,OAAOkC,EAAE;EACb;EACAyD,eAAeA,CAACzG,WAAW,EAAE9Z,IAAI,EAAE2b,OAAO,EAAE;IACxC,IAAImB,EAAE,GAAG,IAAI,CAACwC,gBAAgB,CAACxF,WAAW,CAAC;IAC3C,IAAIgD,EAAE,IAAIA,EAAE,CAACpF,QAAQ,CAAC1X,IAAI,EAAE2b,OAAO,CAAC,EAAE;MAClC,IAAI,CAACoC,eAAe,EAAE;IAC1B;EACJ;EACA1F,OAAOA,CAACyB,WAAW,EAAErX,OAAO,EAAE;IAC1B,IAAI,CAACqX,WAAW,EACZ;IACJ,IAAI,CAAC2B,UAAU,CAAC,MAAM,CAAE,CAAC,CAAC;IAC1B,IAAI,CAACuB,wBAAwB,CAAC,MAAM;MAChC,MAAMF,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAAC1G,WAAW,CAAC;MAC5C,IAAI,CAAC4F,uBAAuB,CAAClY,MAAM,CAACsV,EAAE,CAAClC,WAAW,CAAC;MACnD,MAAMrC,KAAK,GAAG,IAAI,CAACgH,cAAc,CAAC9X,OAAO,CAACqV,EAAE,CAAC;MAC7C,IAAIvE,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACgH,cAAc,CAAC/G,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACxC;MACAuE,EAAE,CAACzE,OAAO,CAAC5V,OAAO,CAAC;MACnB,OAAO,IAAI,CAAC6c,gBAAgB,CAACxF,WAAW,CAAC;IAC7C,CAAC,CAAC;EACN;EACA0G,eAAeA,CAAC7I,EAAE,EAAE;IAChB,OAAO,IAAI,CAAC2H,gBAAgB,CAAC3H,EAAE,CAAC;EACpC;EACAkF,wBAAwBA,CAACtgB,OAAO,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA,MAAMqgB,UAAU,GAAG,IAAIne,GAAG,CAAC,CAAC;IAC5B,MAAM+e,aAAa,GAAG,IAAI,CAAChC,eAAe,CAACjU,GAAG,CAAChL,OAAO,CAAC;IACvD,IAAIihB,aAAa,EAAE;MACf,KAAK,IAAIiD,UAAU,IAAIjD,aAAa,CAACxY,MAAM,CAAC,CAAC,EAAE;QAC3C,IAAIyb,UAAU,CAAC3G,WAAW,EAAE;UACxB,MAAMgD,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAACC,UAAU,CAAC3G,WAAW,CAAC;UACvD,IAAIgD,EAAE,EAAE;YACJF,UAAU,CAAC7X,GAAG,CAAC+X,EAAE,CAAC;UACtB;QACJ;MACJ;IACJ;IACA,OAAOF,UAAU;EACrB;EACAjB,OAAOA,CAAC7B,WAAW,EAAEvd,OAAO,EAAEyD,IAAI,EAAEzB,KAAK,EAAE;IACvC,IAAImiB,aAAa,CAACnkB,OAAO,CAAC,EAAE;MACxB,MAAMugB,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAAC1G,WAAW,CAAC;MAC5C,IAAIgD,EAAE,EAAE;QACJA,EAAE,CAACnB,OAAO,CAACpf,OAAO,EAAEyD,IAAI,EAAEzB,KAAK,CAAC;QAChC,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACAigB,UAAUA,CAAC1E,WAAW,EAAEvd,OAAO,EAAE2hB,MAAM,EAAEyC,YAAY,EAAE;IACnD,IAAI,CAACD,aAAa,CAACnkB,OAAO,CAAC,EACvB;IACJ;IACA;IACA,MAAMqkB,OAAO,GAAGrkB,OAAO,CAAC6d,YAAY,CAAC;IACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC7G,aAAa,EAAE;MAClC6G,OAAO,CAAC7G,aAAa,GAAG,KAAK;MAC7B6G,OAAO,CAAC5G,UAAU,GAAG,IAAI;MACzB,MAAMzB,KAAK,GAAG,IAAI,CAACqH,sBAAsB,CAACnY,OAAO,CAAClL,OAAO,CAAC;MAC1D,IAAIgc,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACqH,sBAAsB,CAACpH,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAChD;IACJ;IACA;IACA;IACA;IACA,IAAIuB,WAAW,EAAE;MACb,MAAMgD,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAAC1G,WAAW,CAAC;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA,IAAIgD,EAAE,EAAE;QACJA,EAAE,CAAC0B,UAAU,CAACjiB,OAAO,EAAE2hB,MAAM,CAAC;MAClC;IACJ;IACA;IACA,IAAIyC,YAAY,EAAE;MACd,IAAI,CAACV,mBAAmB,CAAC1jB,OAAO,CAAC;IACrC;EACJ;EACA0jB,mBAAmBA,CAAC1jB,OAAO,EAAE;IACzB,IAAI,CAACojB,sBAAsB,CAACxgB,IAAI,CAAC5C,OAAO,CAAC;EAC7C;EACAskB,qBAAqBA,CAACtkB,OAAO,EAAEgC,KAAK,EAAE;IAClC,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC,IAAI,CAAC8gB,aAAa,CAACtgB,GAAG,CAACxC,OAAO,CAAC,EAAE;QAClC,IAAI,CAAC8iB,aAAa,CAACta,GAAG,CAACxI,OAAO,CAAC;QAC/B2e,QAAQ,CAAC3e,OAAO,EAAEid,kBAAkB,CAAC;MACzC;IACJ,CAAC,MACI,IAAI,IAAI,CAAC6F,aAAa,CAACtgB,GAAG,CAACxC,OAAO,CAAC,EAAE;MACtC,IAAI,CAAC8iB,aAAa,CAAC7X,MAAM,CAACjL,OAAO,CAAC;MAClC+f,WAAW,CAAC/f,OAAO,EAAEid,kBAAkB,CAAC;IAC5C;EACJ;EACAmE,UAAUA,CAAC7D,WAAW,EAAEvd,OAAO,EAAEkG,OAAO,EAAE;IACtC,IAAIie,aAAa,CAACnkB,OAAO,CAAC,EAAE;MACxB,MAAMugB,EAAE,GAAGhD,WAAW,GAAG,IAAI,CAAC0G,eAAe,CAAC1G,WAAW,CAAC,GAAG,IAAI;MACjE,IAAIgD,EAAE,EAAE;QACJA,EAAE,CAACa,UAAU,CAACphB,OAAO,EAAEkG,OAAO,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAAC4a,oBAAoB,CAACvD,WAAW,EAAEvd,OAAO,EAAE,KAAK,EAAEkG,OAAO,CAAC;MACnE;MACA,MAAMqe,MAAM,GAAG,IAAI,CAACpB,uBAAuB,CAACnY,GAAG,CAAChL,OAAO,CAAC;MACxD,IAAIukB,MAAM,IAAIA,MAAM,CAACnJ,EAAE,KAAKmC,WAAW,EAAE;QACrCgH,MAAM,CAACnD,UAAU,CAACphB,OAAO,EAAEkG,OAAO,CAAC;MACvC;IACJ,CAAC,MACI;MACD,IAAI,CAAC8b,kBAAkB,CAAChiB,OAAO,EAAEkG,OAAO,CAAC;IAC7C;EACJ;EACA4a,oBAAoBA,CAACvD,WAAW,EAAEvd,OAAO,EAAE0d,YAAY,EAAExX,OAAO,EAAE0a,sBAAsB,EAAE;IACtF,IAAI,CAACyC,sBAAsB,CAACzgB,IAAI,CAAC5C,OAAO,CAAC;IACzCA,OAAO,CAAC6d,YAAY,CAAC,GAAG;MACpBN,WAAW;MACXC,aAAa,EAAEtX,OAAO;MACtBwX,YAAY;MACZC,oBAAoB,EAAE,KAAK;MAC3BiD;IACJ,CAAC;EACL;EACA1E,MAAMA,CAACqB,WAAW,EAAEvd,OAAO,EAAEyD,IAAI,EAAEmb,KAAK,EAAExC,QAAQ,EAAE;IAChD,IAAI+H,aAAa,CAACnkB,OAAO,CAAC,EAAE;MACxB,OAAO,IAAI,CAACikB,eAAe,CAAC1G,WAAW,CAAC,CAACrB,MAAM,CAAClc,OAAO,EAAEyD,IAAI,EAAEmb,KAAK,EAAExC,QAAQ,CAAC;IACnF;IACA,OAAO,MAAM,CAAE,CAAC;EACpB;EACAoI,iBAAiBA,CAACjK,KAAK,EAAEkK,YAAY,EAAElV,cAAc,EAAEC,cAAc,EAAEkV,YAAY,EAAE;IACjF,OAAOnK,KAAK,CAAC5S,UAAU,CAAC7B,KAAK,CAAC,IAAI,CAACH,MAAM,EAAE4U,KAAK,CAACva,OAAO,EAAEua,KAAK,CAAC/V,SAAS,CAACxC,KAAK,EAAEuY,KAAK,CAAC7V,OAAO,CAAC1C,KAAK,EAAEuN,cAAc,EAAEC,cAAc,EAAE+K,KAAK,CAAC/V,SAAS,CAACsD,OAAO,EAAEyS,KAAK,CAAC7V,OAAO,CAACoD,OAAO,EAAE2c,YAAY,EAAEC,YAAY,CAAC;EACrN;EACA3C,sBAAsBA,CAAC4C,gBAAgB,EAAE;IACrC,IAAIxQ,QAAQ,GAAG,IAAI,CAACxO,MAAM,CAAC1F,KAAK,CAAC0kB,gBAAgB,EAAE1nB,mBAAmB,EAAE,IAAI,CAAC;IAC7EkX,QAAQ,CAACjQ,OAAO,CAAElE,OAAO,IAAK,IAAI,CAAC4kB,iCAAiC,CAAC5kB,OAAO,CAAC,CAAC;IAC9E,IAAI,IAAI,CAAC0hB,uBAAuB,CAACnb,IAAI,IAAI,CAAC,EACtC;IACJ4N,QAAQ,GAAG,IAAI,CAACxO,MAAM,CAAC1F,KAAK,CAAC0kB,gBAAgB,EAAEznB,qBAAqB,EAAE,IAAI,CAAC;IAC3EiX,QAAQ,CAACjQ,OAAO,CAAElE,OAAO,IAAK,IAAI,CAAC6kB,qCAAqC,CAAC7kB,OAAO,CAAC,CAAC;EACtF;EACA4kB,iCAAiCA,CAAC5kB,OAAO,EAAE;IACvC,MAAMkb,OAAO,GAAG,IAAI,CAACwE,gBAAgB,CAAC1U,GAAG,CAAChL,OAAO,CAAC;IAClD,IAAIkb,OAAO,EAAE;MACTA,OAAO,CAAChX,OAAO,CAAE0X,MAAM,IAAK;QACxB;QACA;QACA;QACA,IAAIA,MAAM,CAAC+D,MAAM,EAAE;UACf/D,MAAM,CAACyG,gBAAgB,GAAG,IAAI;QAClC,CAAC,MACI;UACDzG,MAAM,CAACE,OAAO,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;IACN;EACJ;EACA+I,qCAAqCA,CAAC7kB,OAAO,EAAE;IAC3C,MAAMkb,OAAO,GAAG,IAAI,CAACwG,uBAAuB,CAAC1W,GAAG,CAAChL,OAAO,CAAC;IACzD,IAAIkb,OAAO,EAAE;MACTA,OAAO,CAAChX,OAAO,CAAE0X,MAAM,IAAKA,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC;IAChD;EACJ;EACAkI,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI,IAAI,CAAC9J,OAAO,CAACvY,MAAM,EAAE;QACrB,OAAO/E,mBAAmB,CAAC,IAAI,CAACsd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAMgF,OAAO,CAAC,CAAC,CAAC;MACpE,CAAC,MACI;QACDA,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,CAAC;EACN;EACAjE,gBAAgBA,CAAC/gB,OAAO,EAAE;IACtB,MAAMqkB,OAAO,GAAGrkB,OAAO,CAAC6d,YAAY,CAAC;IACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC7G,aAAa,EAAE;MAClC;MACAxd,OAAO,CAAC6d,YAAY,CAAC,GAAGP,kBAAkB;MAC1C,IAAI+G,OAAO,CAAC9G,WAAW,EAAE;QACrB,IAAI,CAACwE,sBAAsB,CAAC/hB,OAAO,CAAC;QACpC,MAAMugB,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAACI,OAAO,CAAC9G,WAAW,CAAC;QACpD,IAAIgD,EAAE,EAAE;UACJA,EAAE,CAACL,iBAAiB,CAAClgB,OAAO,CAAC;QACjC;MACJ;MACA,IAAI,CAACgiB,kBAAkB,CAAChiB,OAAO,EAAEqkB,OAAO,CAAC7G,aAAa,CAAC;IAC3D;IACA,IAAIxd,OAAO,CAACilB,SAAS,EAAEC,QAAQ,CAACjI,kBAAkB,CAAC,EAAE;MACjD,IAAI,CAACqH,qBAAqB,CAACtkB,OAAO,EAAE,KAAK,CAAC;IAC9C;IACA,IAAI,CAAC2F,MAAM,CAAC1F,KAAK,CAACD,OAAO,EAAEkd,iBAAiB,EAAE,IAAI,CAAC,CAAChZ,OAAO,CAAEihB,IAAI,IAAK;MAClE,IAAI,CAACb,qBAAqB,CAACa,IAAI,EAAE,KAAK,CAAC;IAC3C,CAAC,CAAC;EACN;EACAC,KAAKA,CAACjD,WAAW,GAAG,CAAC,CAAC,EAAE;IACpB,IAAIjH,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC2H,eAAe,CAACtc,IAAI,EAAE;MAC3B,IAAI,CAACsc,eAAe,CAAC3e,OAAO,CAAC,CAACqc,EAAE,EAAEvgB,OAAO,KAAK,IAAI,CAACyjB,qBAAqB,CAAClD,EAAE,EAAEvgB,OAAO,CAAC,CAAC;MACtF,IAAI,CAAC6iB,eAAe,CAAC9T,KAAK,CAAC,CAAC;IAChC;IACA,IAAI,IAAI,CAACyS,eAAe,IAAI,IAAI,CAAC4B,sBAAsB,CAACzgB,MAAM,EAAE;MAC5D,KAAK,IAAI2J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8W,sBAAsB,CAACzgB,MAAM,EAAE2J,CAAC,EAAE,EAAE;QACzD,MAAMqM,GAAG,GAAG,IAAI,CAACyK,sBAAsB,CAAC9W,CAAC,CAAC;QAC1CqS,QAAQ,CAAChG,GAAG,EAAEwE,cAAc,CAAC;MACjC;IACJ;IACA,IAAI,IAAI,CAAC6F,cAAc,CAACrgB,MAAM,KACzB,IAAI,CAACkd,kBAAkB,IAAI,IAAI,CAACwD,sBAAsB,CAAC1gB,MAAM,CAAC,EAAE;MACjE,MAAM0iB,UAAU,GAAG,EAAE;MACrB,IAAI;QACAnK,OAAO,GAAG,IAAI,CAACoK,gBAAgB,CAACD,UAAU,EAAElD,WAAW,CAAC;MAC5D,CAAC,SACO;QACJ,KAAK,IAAI7V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Y,UAAU,CAAC1iB,MAAM,EAAE2J,CAAC,EAAE,EAAE;UACxC+Y,UAAU,CAAC/Y,CAAC,CAAC,CAAC,CAAC;QACnB;MACJ;IACJ,CAAC,MACI;MACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+W,sBAAsB,CAAC1gB,MAAM,EAAE2J,CAAC,EAAE,EAAE;QACzD,MAAMtM,OAAO,GAAG,IAAI,CAACqjB,sBAAsB,CAAC/W,CAAC,CAAC;QAC9C,IAAI,CAACyU,gBAAgB,CAAC/gB,OAAO,CAAC;MAClC;IACJ;IACA,IAAI,CAAC6f,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACuD,sBAAsB,CAACzgB,MAAM,GAAG,CAAC;IACtC,IAAI,CAAC0gB,sBAAsB,CAAC1gB,MAAM,GAAG,CAAC;IACtC,IAAI,CAACsgB,SAAS,CAAC/e,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC4J,SAAS,GAAG,EAAE;IACnB,IAAI,IAAI,CAACC,aAAa,CAACvgB,MAAM,EAAE;MAC3B;MACA;MACA;MACA,MAAM4iB,QAAQ,GAAG,IAAI,CAACrC,aAAa;MACnC,IAAI,CAACA,aAAa,GAAG,EAAE;MACvB,IAAIhI,OAAO,CAACvY,MAAM,EAAE;QAChB/E,mBAAmB,CAACsd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAM;UACtCuF,QAAQ,CAACrhB,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC;MACN,CAAC,MACI;QACDkM,QAAQ,CAACrhB,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MAClC;IACJ;EACJ;EACAmG,WAAWA,CAAC5d,MAAM,EAAE;IAChB,MAAM5D,wBAAwB,CAAC4D,MAAM,CAAC;EAC1C;EACA0jB,gBAAgBA,CAACD,UAAU,EAAElD,WAAW,EAAE;IACtC,MAAMsC,YAAY,GAAG,IAAI/V,qBAAqB,CAAC,CAAC;IAChD,MAAM8W,cAAc,GAAG,EAAE;IACzB,MAAMC,iBAAiB,GAAG,IAAI9e,GAAG,CAAC,CAAC;IACnC,MAAM+e,kBAAkB,GAAG,EAAE;IAC7B,MAAMxO,eAAe,GAAG,IAAIvQ,GAAG,CAAC,CAAC;IACjC,MAAMgf,mBAAmB,GAAG,IAAIhf,GAAG,CAAC,CAAC;IACrC,MAAMif,oBAAoB,GAAG,IAAIjf,GAAG,CAAC,CAAC;IACtC,MAAMkf,mBAAmB,GAAG,IAAI3jB,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC4gB,aAAa,CAAC5e,OAAO,CAAEihB,IAAI,IAAK;MACjCU,mBAAmB,CAACrd,GAAG,CAAC2c,IAAI,CAAC;MAC7B,MAAMW,oBAAoB,GAAG,IAAI,CAACngB,MAAM,CAAC1F,KAAK,CAACklB,IAAI,EAAEnI,eAAe,EAAE,IAAI,CAAC;MAC3E,KAAK,IAAI1Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwZ,oBAAoB,CAACnjB,MAAM,EAAE2J,CAAC,EAAE,EAAE;QAClDuZ,mBAAmB,CAACrd,GAAG,CAACsd,oBAAoB,CAACxZ,CAAC,CAAC,CAAC;MACpD;IACJ,CAAC,CAAC;IACF,MAAMyO,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMgL,kBAAkB,GAAGtb,KAAK,CAACyO,IAAI,CAAC,IAAI,CAAC+F,eAAe,CAACzY,IAAI,CAAC,CAAC,CAAC;IAClE,MAAMwf,YAAY,GAAGC,YAAY,CAACF,kBAAkB,EAAE,IAAI,CAAC3C,sBAAsB,CAAC;IAClF;IACA;IACA;IACA,MAAM8C,eAAe,GAAG,IAAIvf,GAAG,CAAC,CAAC;IACjC,IAAI2F,CAAC,GAAG,CAAC;IACT0Z,YAAY,CAAC9hB,OAAO,CAAC,CAACiiB,KAAK,EAAEC,IAAI,KAAK;MAClC,MAAMC,SAAS,GAAG5oB,eAAe,GAAG6O,CAAC,EAAE;MACvC4Z,eAAe,CAACtf,GAAG,CAACwf,IAAI,EAAEC,SAAS,CAAC;MACpCF,KAAK,CAACjiB,OAAO,CAAEihB,IAAI,IAAKxG,QAAQ,CAACwG,IAAI,EAAEkB,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,gBAAgB,GAAG,IAAIrkB,GAAG,CAAC,CAAC;IAClC,MAAMskB,2BAA2B,GAAG,IAAItkB,GAAG,CAAC,CAAC;IAC7C,KAAK,IAAIoK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+W,sBAAsB,CAAC1gB,MAAM,EAAE2J,CAAC,EAAE,EAAE;MACzD,MAAMtM,OAAO,GAAG,IAAI,CAACqjB,sBAAsB,CAAC/W,CAAC,CAAC;MAC9C,MAAM+X,OAAO,GAAGrkB,OAAO,CAAC6d,YAAY,CAAC;MACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC7G,aAAa,EAAE;QAClC8I,aAAa,CAAC1jB,IAAI,CAAC5C,OAAO,CAAC;QAC3BumB,gBAAgB,CAAC/d,GAAG,CAACxI,OAAO,CAAC;QAC7B,IAAIqkB,OAAO,CAAC3G,YAAY,EAAE;UACtB,IAAI,CAAC/X,MAAM,CACN1F,KAAK,CAACD,OAAO,EAAEod,aAAa,EAAE,IAAI,CAAC,CACnClZ,OAAO,CAAEyU,GAAG,IAAK4N,gBAAgB,CAAC/d,GAAG,CAACmQ,GAAG,CAAC,CAAC;QACpD,CAAC,MACI;UACD6N,2BAA2B,CAAChe,GAAG,CAACxI,OAAO,CAAC;QAC5C;MACJ;IACJ;IACA,MAAMymB,eAAe,GAAG,IAAI9f,GAAG,CAAC,CAAC;IACjC,MAAM+f,YAAY,GAAGT,YAAY,CAACF,kBAAkB,EAAEtb,KAAK,CAACyO,IAAI,CAACqN,gBAAgB,CAAC,CAAC;IACnFG,YAAY,CAACxiB,OAAO,CAAC,CAACiiB,KAAK,EAAEC,IAAI,KAAK;MAClC,MAAMC,SAAS,GAAG7oB,eAAe,GAAG8O,CAAC,EAAE;MACvCma,eAAe,CAAC7f,GAAG,CAACwf,IAAI,EAAEC,SAAS,CAAC;MACpCF,KAAK,CAACjiB,OAAO,CAAEihB,IAAI,IAAKxG,QAAQ,CAACwG,IAAI,EAAEkB,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC;IACFhB,UAAU,CAACziB,IAAI,CAAC,MAAM;MAClBojB,YAAY,CAAC9hB,OAAO,CAAC,CAACiiB,KAAK,EAAEC,IAAI,KAAK;QAClC,MAAMC,SAAS,GAAGH,eAAe,CAAClb,GAAG,CAACob,IAAI,CAAC;QAC3CD,KAAK,CAACjiB,OAAO,CAAEihB,IAAI,IAAKpF,WAAW,CAACoF,IAAI,EAAEkB,SAAS,CAAC,CAAC;MACzD,CAAC,CAAC;MACFK,YAAY,CAACxiB,OAAO,CAAC,CAACiiB,KAAK,EAAEC,IAAI,KAAK;QAClC,MAAMC,SAAS,GAAGI,eAAe,CAACzb,GAAG,CAACob,IAAI,CAAC;QAC3CD,KAAK,CAACjiB,OAAO,CAAEihB,IAAI,IAAKpF,WAAW,CAACoF,IAAI,EAAEkB,SAAS,CAAC,CAAC;MACzD,CAAC,CAAC;MACFC,aAAa,CAACpiB,OAAO,CAAElE,OAAO,IAAK;QAC/B,IAAI,CAAC+gB,gBAAgB,CAAC/gB,OAAO,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,MAAM2mB,UAAU,GAAG,EAAE;IACrB,MAAMC,oBAAoB,GAAG,EAAE;IAC/B,KAAK,IAAIta,CAAC,GAAG,IAAI,CAAC0W,cAAc,CAACrgB,MAAM,GAAG,CAAC,EAAE2J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtD,MAAMiU,EAAE,GAAG,IAAI,CAACyC,cAAc,CAAC1W,CAAC,CAAC;MACjCiU,EAAE,CAAC2B,sBAAsB,CAACC,WAAW,CAAC,CAACje,OAAO,CAAEqW,KAAK,IAAK;QACtD,MAAMqB,MAAM,GAAGrB,KAAK,CAACqB,MAAM;QAC3B,MAAM5b,OAAO,GAAGua,KAAK,CAACva,OAAO;QAC7B2mB,UAAU,CAAC/jB,IAAI,CAACgZ,MAAM,CAAC;QACvB,IAAI,IAAI,CAACwH,sBAAsB,CAACzgB,MAAM,EAAE;UACpC,MAAM0hB,OAAO,GAAGrkB,OAAO,CAAC6d,YAAY,CAAC;UACrC;UACA;UACA,IAAIwG,OAAO,IAAIA,OAAO,CAAC5G,UAAU,EAAE;YAC/B,IAAI4G,OAAO,CAACzD,sBAAsB,IAC9ByD,OAAO,CAACzD,sBAAsB,CAACpe,GAAG,CAAC+X,KAAK,CAACzD,WAAW,CAAC,EAAE;cACvD,MAAM+P,aAAa,GAAGxC,OAAO,CAACzD,sBAAsB,CAAC5V,GAAG,CAACuP,KAAK,CAACzD,WAAW,CAAC;cAC3E;cACA;cACA,MAAMkI,kBAAkB,GAAG,IAAI,CAACC,eAAe,CAACjU,GAAG,CAACuP,KAAK,CAACva,OAAO,CAAC;cAClE,IAAIgf,kBAAkB,IAAIA,kBAAkB,CAACxc,GAAG,CAAC+X,KAAK,CAACzD,WAAW,CAAC,EAAE;gBACjE,MAAM+J,KAAK,GAAG7B,kBAAkB,CAAChU,GAAG,CAACuP,KAAK,CAACzD,WAAW,CAAC;gBACvD+J,KAAK,CAAC7e,KAAK,GAAG6kB,aAAa;gBAC3B7H,kBAAkB,CAACpY,GAAG,CAAC2T,KAAK,CAACzD,WAAW,EAAE+J,KAAK,CAAC;cACpD;YACJ;YACAjF,MAAM,CAACE,OAAO,CAAC,CAAC;YAChB;UACJ;QACJ;QACA,MAAMgL,cAAc,GAAG,CAAC/L,QAAQ,IAAI,CAAC,IAAI,CAACpV,MAAM,CAAClK,eAAe,CAACsf,QAAQ,EAAE/a,OAAO,CAAC;QACnF,MAAMwP,cAAc,GAAGiX,eAAe,CAACzb,GAAG,CAAChL,OAAO,CAAC;QACnD,MAAMuP,cAAc,GAAG2W,eAAe,CAAClb,GAAG,CAAChL,OAAO,CAAC;QACnD,MAAMiR,WAAW,GAAG,IAAI,CAACuT,iBAAiB,CAACjK,KAAK,EAAEkK,YAAY,EAAElV,cAAc,EAAEC,cAAc,EAAEsX,cAAc,CAAC;QAC/G,IAAI7V,WAAW,CAACrP,MAAM,IAAIqP,WAAW,CAACrP,MAAM,CAACe,MAAM,EAAE;UACjDikB,oBAAoB,CAAChkB,IAAI,CAACqO,WAAW,CAAC;UACtC;QACJ;QACA;QACA;QACA;QACA;QACA,IAAI6V,cAAc,EAAE;UAChBlL,MAAM,CAACkE,OAAO,CAAC,MAAM7hB,WAAW,CAAC+B,OAAO,EAAEiR,WAAW,CAAC+F,UAAU,CAAC,CAAC;UAClE4E,MAAM,CAACC,SAAS,CAAC,MAAM3d,SAAS,CAAC8B,OAAO,EAAEiR,WAAW,CAACgG,QAAQ,CAAC,CAAC;UAChEuO,cAAc,CAAC5iB,IAAI,CAACgZ,MAAM,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA,IAAIrB,KAAK,CAACqF,oBAAoB,EAAE;UAC5BhE,MAAM,CAACkE,OAAO,CAAC,MAAM7hB,WAAW,CAAC+B,OAAO,EAAEiR,WAAW,CAAC+F,UAAU,CAAC,CAAC;UAClE4E,MAAM,CAACC,SAAS,CAAC,MAAM3d,SAAS,CAAC8B,OAAO,EAAEiR,WAAW,CAACgG,QAAQ,CAAC,CAAC;UAChEuO,cAAc,CAAC5iB,IAAI,CAACgZ,MAAM,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA;QACA;QACA,MAAM3L,SAAS,GAAG,EAAE;QACpBgB,WAAW,CAAChB,SAAS,CAAC/L,OAAO,CAAEuO,EAAE,IAAK;UAClCA,EAAE,CAACuB,uBAAuB,GAAG,IAAI;UACjC,IAAI,CAAC,IAAI,CAAC8O,aAAa,CAACtgB,GAAG,CAACiQ,EAAE,CAACzS,OAAO,CAAC,EAAE;YACrCiQ,SAAS,CAACrN,IAAI,CAAC6P,EAAE,CAAC;UACtB;QACJ,CAAC,CAAC;QACFxB,WAAW,CAAChB,SAAS,GAAGA,SAAS;QACjCwU,YAAY,CAAC7V,MAAM,CAAC5O,OAAO,EAAEiR,WAAW,CAAChB,SAAS,CAAC;QACnD,MAAM1E,KAAK,GAAG;UAAE0F,WAAW;UAAE2K,MAAM;UAAE5b;QAAQ,CAAC;QAC9C0lB,kBAAkB,CAAC9iB,IAAI,CAAC2I,KAAK,CAAC;QAC9B0F,WAAW,CAACiG,eAAe,CAAChT,OAAO,CAAElE,OAAO,IAAKnD,oBAAoB,CAACqa,eAAe,EAAElX,OAAO,EAAE,EAAE,CAAC,CAAC4C,IAAI,CAACgZ,MAAM,CAAC,CAAC;QACjH3K,WAAW,CAAC3C,aAAa,CAACpK,OAAO,CAAC,CAAC6iB,SAAS,EAAE/mB,OAAO,KAAK;UACtD,IAAI+mB,SAAS,CAACxgB,IAAI,EAAE;YAChB,IAAIygB,MAAM,GAAGrB,mBAAmB,CAAC3a,GAAG,CAAChL,OAAO,CAAC;YAC7C,IAAI,CAACgnB,MAAM,EAAE;cACTrB,mBAAmB,CAAC/e,GAAG,CAAC5G,OAAO,EAAGgnB,MAAM,GAAG,IAAI9kB,GAAG,CAAC,CAAE,CAAC;YAC1D;YACA6kB,SAAS,CAAC7iB,OAAO,CAAC,CAACyX,CAAC,EAAE9b,IAAI,KAAKmnB,MAAM,CAACxe,GAAG,CAAC3I,IAAI,CAAC,CAAC;UACpD;QACJ,CAAC,CAAC;QACFoR,WAAW,CAAC1C,cAAc,CAACrK,OAAO,CAAC,CAAC6iB,SAAS,EAAE/mB,OAAO,KAAK;UACvD,IAAIgnB,MAAM,GAAGpB,oBAAoB,CAAC5a,GAAG,CAAChL,OAAO,CAAC;UAC9C,IAAI,CAACgnB,MAAM,EAAE;YACTpB,oBAAoB,CAAChf,GAAG,CAAC5G,OAAO,EAAGgnB,MAAM,GAAG,IAAI9kB,GAAG,CAAC,CAAE,CAAC;UAC3D;UACA6kB,SAAS,CAAC7iB,OAAO,CAAC,CAACyX,CAAC,EAAE9b,IAAI,KAAKmnB,MAAM,CAACxe,GAAG,CAAC3I,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAI+mB,oBAAoB,CAACjkB,MAAM,EAAE;MAC7B,MAAMf,MAAM,GAAG,EAAE;MACjBglB,oBAAoB,CAAC1iB,OAAO,CAAE+M,WAAW,IAAK;QAC1CrP,MAAM,CAACgB,IAAI,CAACzE,gBAAgB,CAAC8S,WAAW,CAAC6F,WAAW,EAAE7F,WAAW,CAACrP,MAAM,CAAC,CAAC;MAC9E,CAAC,CAAC;MACF+kB,UAAU,CAACziB,OAAO,CAAE0X,MAAM,IAAKA,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC0D,WAAW,CAAC5d,MAAM,CAAC;IAC5B;IACA,MAAMqlB,qBAAqB,GAAG,IAAItgB,GAAG,CAAC,CAAC;IACvC;IACA;IACA;IACA;IACA,MAAMugB,mBAAmB,GAAG,IAAIvgB,GAAG,CAAC,CAAC;IACrC+e,kBAAkB,CAACxhB,OAAO,CAAEqW,KAAK,IAAK;MAClC,MAAMva,OAAO,GAAGua,KAAK,CAACva,OAAO;MAC7B,IAAIykB,YAAY,CAACjiB,GAAG,CAACxC,OAAO,CAAC,EAAE;QAC3BknB,mBAAmB,CAACtgB,GAAG,CAAC5G,OAAO,EAAEA,OAAO,CAAC;QACzC,IAAI,CAACmnB,qBAAqB,CAAC5M,KAAK,CAACqB,MAAM,CAAC2B,WAAW,EAAEhD,KAAK,CAACtJ,WAAW,EAAEgW,qBAAqB,CAAC;MAClG;IACJ,CAAC,CAAC;IACFzB,cAAc,CAACthB,OAAO,CAAE0X,MAAM,IAAK;MAC/B,MAAM5b,OAAO,GAAG4b,MAAM,CAAC5b,OAAO;MAC9B,MAAMU,eAAe,GAAG,IAAI,CAAC0mB,mBAAmB,CAACpnB,OAAO,EAAE,KAAK,EAAE4b,MAAM,CAAC2B,WAAW,EAAE3B,MAAM,CAAC9E,WAAW,EAAE,IAAI,CAAC;MAC9GpW,eAAe,CAACwD,OAAO,CAAEmjB,UAAU,IAAK;QACpCxqB,oBAAoB,CAACoqB,qBAAqB,EAAEjnB,OAAO,EAAE,EAAE,CAAC,CAAC4C,IAAI,CAACykB,UAAU,CAAC;QACzEA,UAAU,CAACvL,OAAO,CAAC,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMwL,YAAY,GAAGhB,aAAa,CAACtjB,MAAM,CAAEmiB,IAAI,IAAK;MAChD,OAAOoC,sBAAsB,CAACpC,IAAI,EAAEQ,mBAAmB,EAAEC,oBAAoB,CAAC;IAClF,CAAC,CAAC;IACF;IACA,MAAM4B,aAAa,GAAG,IAAI7gB,GAAG,CAAC,CAAC;IAC/B,MAAM8gB,oBAAoB,GAAGC,qBAAqB,CAACF,aAAa,EAAE,IAAI,CAAC7hB,MAAM,EAAE6gB,2BAA2B,EAAEZ,oBAAoB,EAAEpmB,UAAU,CAAC;IAC7IioB,oBAAoB,CAACvjB,OAAO,CAAEihB,IAAI,IAAK;MACnC,IAAIoC,sBAAsB,CAACpC,IAAI,EAAEQ,mBAAmB,EAAEC,oBAAoB,CAAC,EAAE;QACzE0B,YAAY,CAAC1kB,IAAI,CAACuiB,IAAI,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF;IACA,MAAMwC,YAAY,GAAG,IAAIhhB,GAAG,CAAC,CAAC;IAC9Bqf,YAAY,CAAC9hB,OAAO,CAAC,CAACiiB,KAAK,EAAEC,IAAI,KAAK;MAClCsB,qBAAqB,CAACC,YAAY,EAAE,IAAI,CAAChiB,MAAM,EAAE,IAAIzD,GAAG,CAACikB,KAAK,CAAC,EAAER,mBAAmB,EAAEjmB,UAAU,CAAC;IACrG,CAAC,CAAC;IACF4nB,YAAY,CAACpjB,OAAO,CAAEihB,IAAI,IAAK;MAC3B,MAAMyC,IAAI,GAAGJ,aAAa,CAACxc,GAAG,CAACma,IAAI,CAAC;MACpC,MAAM0C,GAAG,GAAGF,YAAY,CAAC3c,GAAG,CAACma,IAAI,CAAC;MAClCqC,aAAa,CAAC5gB,GAAG,CAACue,IAAI,EAAE,IAAIxe,GAAG,CAAC,CAAC,IAAIihB,IAAI,EAAE/c,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,IAAIgd,GAAG,EAAEhd,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7F,CAAC,CAAC;IACF,MAAMid,WAAW,GAAG,EAAE;IACtB,MAAMC,UAAU,GAAG,EAAE;IACrB,MAAMC,oCAAoC,GAAG,CAAC,CAAC;IAC/CtC,kBAAkB,CAACxhB,OAAO,CAAEqW,KAAK,IAAK;MAClC,MAAM;QAAEva,OAAO;QAAE4b,MAAM;QAAE3K;MAAY,CAAC,GAAGsJ,KAAK;MAC9C;MACA;MACA,IAAIkK,YAAY,CAACjiB,GAAG,CAACxC,OAAO,CAAC,EAAE;QAC3B,IAAI6lB,mBAAmB,CAACrjB,GAAG,CAACxC,OAAO,CAAC,EAAE;UAClC4b,MAAM,CAACC,SAAS,CAAC,MAAM3d,SAAS,CAAC8B,OAAO,EAAEiR,WAAW,CAACgG,QAAQ,CAAC,CAAC;UAChE2E,MAAM,CAACqM,QAAQ,GAAG,IAAI;UACtBrM,MAAM,CAACsM,iBAAiB,CAACjX,WAAW,CAACxC,SAAS,CAAC;UAC/C+W,cAAc,CAAC5iB,IAAI,CAACgZ,MAAM,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIuM,mBAAmB,GAAGH,oCAAoC;QAC9D,IAAId,mBAAmB,CAAC3gB,IAAI,GAAG,CAAC,EAAE;UAC9B,IAAIoS,GAAG,GAAG3Y,OAAO;UACjB,MAAMooB,YAAY,GAAG,EAAE;UACvB,OAAQzP,GAAG,GAAGA,GAAG,CAACiJ,UAAU,EAAG;YAC3B,MAAMyG,cAAc,GAAGnB,mBAAmB,CAAClc,GAAG,CAAC2N,GAAG,CAAC;YACnD,IAAI0P,cAAc,EAAE;cAChBF,mBAAmB,GAAGE,cAAc;cACpC;YACJ;YACAD,YAAY,CAACxlB,IAAI,CAAC+V,GAAG,CAAC;UAC1B;UACAyP,YAAY,CAAClkB,OAAO,CAAEyd,MAAM,IAAKuF,mBAAmB,CAACtgB,GAAG,CAAC+a,MAAM,EAAEwG,mBAAmB,CAAC,CAAC;QAC1F;QACA,MAAMG,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC3M,MAAM,CAAC2B,WAAW,EAAEtM,WAAW,EAAEgW,qBAAqB,EAAExB,iBAAiB,EAAEkC,YAAY,EAAEH,aAAa,CAAC;QAChJ5L,MAAM,CAAC4M,aAAa,CAACF,WAAW,CAAC;QACjC,IAAIH,mBAAmB,KAAKH,oCAAoC,EAAE;UAC9DF,WAAW,CAACllB,IAAI,CAACgZ,MAAM,CAAC;QAC5B,CAAC,MACI;UACD,MAAM6M,aAAa,GAAG,IAAI,CAAC/I,gBAAgB,CAAC1U,GAAG,CAACmd,mBAAmB,CAAC;UACpE,IAAIM,aAAa,IAAIA,aAAa,CAAC9lB,MAAM,EAAE;YACvCiZ,MAAM,CAAC8M,YAAY,GAAG9qB,mBAAmB,CAAC6qB,aAAa,CAAC;UAC5D;UACAjD,cAAc,CAAC5iB,IAAI,CAACgZ,MAAM,CAAC;QAC/B;MACJ,CAAC,MACI;QACD3d,WAAW,CAAC+B,OAAO,EAAEiR,WAAW,CAAC+F,UAAU,CAAC;QAC5C4E,MAAM,CAACC,SAAS,CAAC,MAAM3d,SAAS,CAAC8B,OAAO,EAAEiR,WAAW,CAACgG,QAAQ,CAAC,CAAC;QAChE;QACA;QACA;QACA8Q,UAAU,CAACnlB,IAAI,CAACgZ,MAAM,CAAC;QACvB,IAAIiK,mBAAmB,CAACrjB,GAAG,CAACxC,OAAO,CAAC,EAAE;UAClCwlB,cAAc,CAAC5iB,IAAI,CAACgZ,MAAM,CAAC;QAC/B;MACJ;IACJ,CAAC,CAAC;IACF;IACAmM,UAAU,CAAC7jB,OAAO,CAAE0X,MAAM,IAAK;MAC3B;MACA;MACA,MAAM+M,iBAAiB,GAAGlD,iBAAiB,CAACza,GAAG,CAAC4Q,MAAM,CAAC5b,OAAO,CAAC;MAC/D,IAAI2oB,iBAAiB,IAAIA,iBAAiB,CAAChmB,MAAM,EAAE;QAC/C,MAAM2lB,WAAW,GAAG1qB,mBAAmB,CAAC+qB,iBAAiB,CAAC;QAC1D/M,MAAM,CAAC4M,aAAa,CAACF,WAAW,CAAC;MACrC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA9C,cAAc,CAACthB,OAAO,CAAE0X,MAAM,IAAK;MAC/B,IAAIA,MAAM,CAAC8M,YAAY,EAAE;QACrB9M,MAAM,CAACgN,gBAAgB,CAAChN,MAAM,CAAC8M,YAAY,CAAC;MAChD,CAAC,MACI;QACD9M,MAAM,CAACE,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA,KAAK,IAAIxP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGga,aAAa,CAAC3jB,MAAM,EAAE2J,CAAC,EAAE,EAAE;MAC3C,MAAMtM,OAAO,GAAGsmB,aAAa,CAACha,CAAC,CAAC;MAChC,MAAM+X,OAAO,GAAGrkB,OAAO,CAAC6d,YAAY,CAAC;MACrCkC,WAAW,CAAC/f,OAAO,EAAExC,eAAe,CAAC;MACrC;MACA;MACA;MACA,IAAI6mB,OAAO,IAAIA,OAAO,CAAC3G,YAAY,EAC/B;MACJ,IAAIxC,OAAO,GAAG,EAAE;MAChB;MACA;MACA;MACA,IAAIhE,eAAe,CAAC3Q,IAAI,EAAE;QACtB,IAAIsiB,oBAAoB,GAAG3R,eAAe,CAAClM,GAAG,CAAChL,OAAO,CAAC;QACvD,IAAI6oB,oBAAoB,IAAIA,oBAAoB,CAAClmB,MAAM,EAAE;UACrDuY,OAAO,CAACtY,IAAI,CAAC,GAAGimB,oBAAoB,CAAC;QACzC;QACA,IAAIC,oBAAoB,GAAG,IAAI,CAACnjB,MAAM,CAAC1F,KAAK,CAACD,OAAO,EAAE9C,qBAAqB,EAAE,IAAI,CAAC;QAClF,KAAK,IAAI6rB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,oBAAoB,CAACnmB,MAAM,EAAEomB,CAAC,EAAE,EAAE;UAClD,IAAIC,cAAc,GAAG9R,eAAe,CAAClM,GAAG,CAAC8d,oBAAoB,CAACC,CAAC,CAAC,CAAC;UACjE,IAAIC,cAAc,IAAIA,cAAc,CAACrmB,MAAM,EAAE;YACzCuY,OAAO,CAACtY,IAAI,CAAC,GAAGomB,cAAc,CAAC;UACnC;QACJ;MACJ;MACA,MAAMC,aAAa,GAAG/N,OAAO,CAAClY,MAAM,CAAE2f,CAAC,IAAK,CAACA,CAAC,CAACP,SAAS,CAAC;MACzD,IAAI6G,aAAa,CAACtmB,MAAM,EAAE;QACtBumB,6BAA6B,CAAC,IAAI,EAAElpB,OAAO,EAAEipB,aAAa,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAAClI,gBAAgB,CAAC/gB,OAAO,CAAC;MAClC;IACJ;IACA;IACAsmB,aAAa,CAAC3jB,MAAM,GAAG,CAAC;IACxBmlB,WAAW,CAAC5jB,OAAO,CAAE0X,MAAM,IAAK;MAC5B,IAAI,CAACV,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;MACzBA,MAAM,CAACoE,MAAM,CAAC,MAAM;QAChBpE,MAAM,CAACE,OAAO,CAAC,CAAC;QAChB,MAAME,KAAK,GAAG,IAAI,CAACd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;QAC1C,IAAI,CAACV,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACjC,CAAC,CAAC;MACFJ,MAAM,CAACY,IAAI,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,OAAOsL,WAAW;EACtB;EACA5I,UAAUA,CAAC9C,QAAQ,EAAE;IACjB,IAAI,CAAC6G,SAAS,CAACrgB,IAAI,CAACwZ,QAAQ,CAAC;EACjC;EACAqE,wBAAwBA,CAACrE,QAAQ,EAAE;IAC/B,IAAI,CAAC8G,aAAa,CAACtgB,IAAI,CAACwZ,QAAQ,CAAC;EACrC;EACAgL,mBAAmBA,CAACpnB,OAAO,EAAEmpB,gBAAgB,EAAE5L,WAAW,EAAEzG,WAAW,EAAEsS,YAAY,EAAE;IACnF,IAAIlO,OAAO,GAAG,EAAE;IAChB,IAAIiO,gBAAgB,EAAE;MAClB,MAAME,qBAAqB,GAAG,IAAI,CAAC3H,uBAAuB,CAAC1W,GAAG,CAAChL,OAAO,CAAC;MACvE,IAAIqpB,qBAAqB,EAAE;QACvBnO,OAAO,GAAGmO,qBAAqB;MACnC;IACJ,CAAC,MACI;MACD,MAAMlJ,cAAc,GAAG,IAAI,CAACT,gBAAgB,CAAC1U,GAAG,CAAChL,OAAO,CAAC;MACzD,IAAImgB,cAAc,EAAE;QAChB,MAAMmJ,kBAAkB,GAAG,CAACF,YAAY,IAAIA,YAAY,IAAIlL,UAAU;QACtEiC,cAAc,CAACjc,OAAO,CAAE0X,MAAM,IAAK;UAC/B,IAAIA,MAAM,CAAC+D,MAAM,EACb;UACJ,IAAI,CAAC2J,kBAAkB,IAAI1N,MAAM,CAAC9E,WAAW,IAAIA,WAAW,EACxD;UACJoE,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;QACxB,CAAC,CAAC;MACN;IACJ;IACA,IAAI2B,WAAW,IAAIzG,WAAW,EAAE;MAC5BoE,OAAO,GAAGA,OAAO,CAAClY,MAAM,CAAE4Y,MAAM,IAAK;QACjC,IAAI2B,WAAW,IAAIA,WAAW,IAAI3B,MAAM,CAAC2B,WAAW,EAChD,OAAO,KAAK;QAChB,IAAIzG,WAAW,IAAIA,WAAW,IAAI8E,MAAM,CAAC9E,WAAW,EAChD,OAAO,KAAK;QAChB,OAAO,IAAI;MACf,CAAC,CAAC;IACN;IACA,OAAOoE,OAAO;EAClB;EACAiM,qBAAqBA,CAAC5J,WAAW,EAAEtM,WAAW,EAAEgW,qBAAqB,EAAE;IACnE,MAAMnQ,WAAW,GAAG7F,WAAW,CAAC6F,WAAW;IAC3C,MAAMxH,WAAW,GAAG2B,WAAW,CAACjR,OAAO;IACvC;IACA;IACA,MAAMupB,iBAAiB,GAAGtY,WAAW,CAAC8F,mBAAmB,GACnDc,SAAS,GACT0F,WAAW;IACjB,MAAMiM,iBAAiB,GAAGvY,WAAW,CAAC8F,mBAAmB,GACnDc,SAAS,GACTf,WAAW;IACjB,KAAK,MAAM2S,mBAAmB,IAAIxY,WAAW,CAAChB,SAAS,EAAE;MACrD,MAAMjQ,OAAO,GAAGypB,mBAAmB,CAACzpB,OAAO;MAC3C,MAAMmpB,gBAAgB,GAAGnpB,OAAO,KAAKsP,WAAW;MAChD,MAAM4L,OAAO,GAAGre,oBAAoB,CAACoqB,qBAAqB,EAAEjnB,OAAO,EAAE,EAAE,CAAC;MACxE,MAAMU,eAAe,GAAG,IAAI,CAAC0mB,mBAAmB,CAACpnB,OAAO,EAAEmpB,gBAAgB,EAAEI,iBAAiB,EAAEC,iBAAiB,EAAEvY,WAAW,CAACvM,OAAO,CAAC;MACtIhE,eAAe,CAACwD,OAAO,CAAE0X,MAAM,IAAK;QAChC,MAAM8N,UAAU,GAAG9N,MAAM,CAAC+N,aAAa,CAAC,CAAC;QACzC,IAAID,UAAU,CAACE,aAAa,EAAE;UAC1BF,UAAU,CAACE,aAAa,CAAC,CAAC;QAC9B;QACAhO,MAAM,CAACE,OAAO,CAAC,CAAC;QAChBZ,OAAO,CAACtY,IAAI,CAACgZ,MAAM,CAAC;MACxB,CAAC,CAAC;IACN;IACA;IACA;IACA3d,WAAW,CAACqR,WAAW,EAAE2B,WAAW,CAAC+F,UAAU,CAAC;EACpD;EACAuR,eAAeA,CAAChL,WAAW,EAAEtM,WAAW,EAAEgW,qBAAqB,EAAExB,iBAAiB,EAAEkC,YAAY,EAAEH,aAAa,EAAE;IAC7G,MAAM1Q,WAAW,GAAG7F,WAAW,CAAC6F,WAAW;IAC3C,MAAMxH,WAAW,GAAG2B,WAAW,CAACjR,OAAO;IACvC;IACA;IACA,MAAM6pB,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,mBAAmB,GAAG,IAAI5nB,GAAG,CAAC,CAAC;IACrC,MAAM6nB,cAAc,GAAG,IAAI7nB,GAAG,CAAC,CAAC;IAChC,MAAM8nB,aAAa,GAAG/Y,WAAW,CAAChB,SAAS,CAAC/M,GAAG,CAAEumB,mBAAmB,IAAK;MACrE,MAAMzpB,OAAO,GAAGypB,mBAAmB,CAACzpB,OAAO;MAC3C8pB,mBAAmB,CAACthB,GAAG,CAACxI,OAAO,CAAC;MAChC;MACA,MAAMqkB,OAAO,GAAGrkB,OAAO,CAAC6d,YAAY,CAAC;MACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC1G,oBAAoB,EACvC,OAAO,IAAIte,mBAAmB,CAACoqB,mBAAmB,CAAClpB,QAAQ,EAAEkpB,mBAAmB,CAACjpB,KAAK,CAAC;MAC3F,MAAM2oB,gBAAgB,GAAGnpB,OAAO,KAAKsP,WAAW;MAChD,MAAM5O,eAAe,GAAGupB,mBAAmB,CAAC,CAAChD,qBAAqB,CAACjc,GAAG,CAAChL,OAAO,CAAC,IAAIqd,kBAAkB,EAAEna,GAAG,CAAEyf,CAAC,IAAKA,CAAC,CAACgH,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC3mB,MAAM,CAAE2f,CAAC,IAAK;QAChJ;QACA;QACA;QACA;QACA,MAAMuH,EAAE,GAAGvH,CAAC;QACZ,OAAOuH,EAAE,CAAClqB,OAAO,GAAGkqB,EAAE,CAAClqB,OAAO,KAAKA,OAAO,GAAG,KAAK;MACtD,CAAC,CAAC;MACF,MAAMsb,SAAS,GAAGqM,YAAY,CAAC3c,GAAG,CAAChL,OAAO,CAAC;MAC3C,MAAMub,UAAU,GAAGiM,aAAa,CAACxc,GAAG,CAAChL,OAAO,CAAC;MAC7C,MAAMM,SAAS,GAAG/C,kBAAkB,CAAC,IAAI,CAACyc,WAAW,EAAEyP,mBAAmB,CAACnpB,SAAS,EAAEgb,SAAS,EAAEC,UAAU,CAAC;MAC5G,MAAMK,MAAM,GAAG,IAAI,CAACP,YAAY,CAACoO,mBAAmB,EAAEnpB,SAAS,EAAEI,eAAe,CAAC;MACjF;MACA;MACA,IAAI+oB,mBAAmB,CAACjb,WAAW,IAAIiX,iBAAiB,EAAE;QACtDsE,cAAc,CAACvhB,GAAG,CAACxI,OAAO,CAAC;MAC/B;MACA,IAAImpB,gBAAgB,EAAE;QAClB,MAAMgB,aAAa,GAAG,IAAI7K,yBAAyB,CAAC/B,WAAW,EAAEzG,WAAW,EAAE9W,OAAO,CAAC;QACtFmqB,aAAa,CAAC3B,aAAa,CAAC5M,MAAM,CAAC;QACnCiO,iBAAiB,CAACjnB,IAAI,CAACunB,aAAa,CAAC;MACzC;MACA,OAAOvO,MAAM;IACjB,CAAC,CAAC;IACFiO,iBAAiB,CAAC3lB,OAAO,CAAE0X,MAAM,IAAK;MAClC/e,oBAAoB,CAAC,IAAI,CAAC6kB,uBAAuB,EAAE9F,MAAM,CAAC5b,OAAO,EAAE,EAAE,CAAC,CAAC4C,IAAI,CAACgZ,MAAM,CAAC;MACnFA,MAAM,CAACoE,MAAM,CAAC,MAAMoK,kBAAkB,CAAC,IAAI,CAAC1I,uBAAuB,EAAE9F,MAAM,CAAC5b,OAAO,EAAE4b,MAAM,CAAC,CAAC;IACjG,CAAC,CAAC;IACFkO,mBAAmB,CAAC5lB,OAAO,CAAElE,OAAO,IAAK2e,QAAQ,CAAC3e,OAAO,EAAEvB,sBAAsB,CAAC,CAAC;IACnF,MAAMmd,MAAM,GAAGhe,mBAAmB,CAACosB,aAAa,CAAC;IACjDpO,MAAM,CAACC,SAAS,CAAC,MAAM;MACnBiO,mBAAmB,CAAC5lB,OAAO,CAAElE,OAAO,IAAK+f,WAAW,CAAC/f,OAAO,EAAEvB,sBAAsB,CAAC,CAAC;MACtFP,SAAS,CAACoR,WAAW,EAAE2B,WAAW,CAACgG,QAAQ,CAAC;IAChD,CAAC,CAAC;IACF;IACA;IACA8S,cAAc,CAAC7lB,OAAO,CAAElE,OAAO,IAAK;MAChCnD,oBAAoB,CAAC4oB,iBAAiB,EAAEzlB,OAAO,EAAE,EAAE,CAAC,CAAC4C,IAAI,CAACgZ,MAAM,CAAC;IACrE,CAAC,CAAC;IACF,OAAOA,MAAM;EACjB;EACAP,YAAYA,CAACpK,WAAW,EAAE3Q,SAAS,EAAEI,eAAe,EAAE;IAClD,IAAIJ,SAAS,CAACqC,MAAM,GAAG,CAAC,EAAE;MACtB,OAAO,IAAI,CAACgD,MAAM,CAACtF,OAAO,CAAC4Q,WAAW,CAACjR,OAAO,EAAEM,SAAS,EAAE2Q,WAAW,CAAC1Q,QAAQ,EAAE0Q,WAAW,CAACzQ,KAAK,EAAEyQ,WAAW,CAACxQ,MAAM,EAAEC,eAAe,CAAC;IAC5I;IACA;IACA;IACA,OAAO,IAAIrB,mBAAmB,CAAC4R,WAAW,CAAC1Q,QAAQ,EAAE0Q,WAAW,CAACzQ,KAAK,CAAC;EAC3E;AACJ;AACA,MAAM8e,yBAAyB,CAAC;EAC5B/B,WAAW;EACXzG,WAAW;EACX9W,OAAO;EACPqqB,OAAO,GAAG,IAAIhrB,mBAAmB,CAAC,CAAC;EACnCirB,mBAAmB,GAAG,KAAK;EAC3BC,gBAAgB,GAAG,IAAI5jB,GAAG,CAAC,CAAC;EAC5Byb,SAAS,GAAG,KAAK;EACjBsG,YAAY,GAAG,IAAI;EACnBrG,gBAAgB,GAAG,KAAK;EACxB4F,QAAQ,GAAG,KAAK;EAChBtI,MAAM,GAAG,IAAI;EACblR,SAAS,GAAG,CAAC;EACbxI,WAAWA,CAACsX,WAAW,EAAEzG,WAAW,EAAE9W,OAAO,EAAE;IAC3C,IAAI,CAACud,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACzG,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC9W,OAAO,GAAGA,OAAO;EAC1B;EACAwoB,aAAaA,CAAC5M,MAAM,EAAE;IAClB,IAAI,IAAI,CAAC0O,mBAAmB,EACxB;IACJ,IAAI,CAACD,OAAO,GAAGzO,MAAM;IACrB,IAAI,CAAC2O,gBAAgB,CAACrmB,OAAO,CAAC,CAACsmB,SAAS,EAAE5L,KAAK,KAAK;MAChD4L,SAAS,CAACtmB,OAAO,CAAEkY,QAAQ,IAAKte,cAAc,CAAC8d,MAAM,EAAEgD,KAAK,EAAE/G,SAAS,EAAEuE,QAAQ,CAAC,CAAC;IACvF,CAAC,CAAC;IACF,IAAI,CAACmO,gBAAgB,CAACxb,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACub,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACpC,iBAAiB,CAACtM,MAAM,CAACnN,SAAS,CAAC;IACxC,IAAI,CAACkR,MAAM,GAAG,KAAK;EACvB;EACAgK,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACU,OAAO;EACvB;EACAnC,iBAAiBA,CAACzZ,SAAS,EAAE;IACzB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAma,gBAAgBA,CAAChN,MAAM,EAAE;IACrB,MAAM+G,CAAC,GAAG,IAAI,CAAC0H,OAAO;IACtB,IAAI1H,CAAC,CAAC8H,eAAe,EAAE;MACnB7O,MAAM,CAACkE,OAAO,CAAC,MAAM6C,CAAC,CAAC8H,eAAe,CAAC,OAAO,CAAC,CAAC;IACpD;IACA7O,MAAM,CAACoE,MAAM,CAAC,MAAM,IAAI,CAACpD,MAAM,CAAC,CAAC,CAAC;IAClChB,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EAC1C;EACA4O,WAAWA,CAACjnB,IAAI,EAAE2Y,QAAQ,EAAE;IACxBvf,oBAAoB,CAAC,IAAI,CAAC0tB,gBAAgB,EAAE9mB,IAAI,EAAE,EAAE,CAAC,CAACb,IAAI,CAACwZ,QAAQ,CAAC;EACxE;EACA4D,MAAMA,CAAC3G,EAAE,EAAE;IACP,IAAI,IAAI,CAACsG,MAAM,EAAE;MACb,IAAI,CAAC+K,WAAW,CAAC,MAAM,EAAErR,EAAE,CAAC;IAChC;IACA,IAAI,CAACgR,OAAO,CAACrK,MAAM,CAAC3G,EAAE,CAAC;EAC3B;EACAyG,OAAOA,CAACzG,EAAE,EAAE;IACR,IAAI,IAAI,CAACsG,MAAM,EAAE;MACb,IAAI,CAAC+K,WAAW,CAAC,OAAO,EAAErR,EAAE,CAAC;IACjC;IACA,IAAI,CAACgR,OAAO,CAACvK,OAAO,CAACzG,EAAE,CAAC;EAC5B;EACAwC,SAASA,CAACxC,EAAE,EAAE;IACV,IAAI,IAAI,CAACsG,MAAM,EAAE;MACb,IAAI,CAAC+K,WAAW,CAAC,SAAS,EAAErR,EAAE,CAAC;IACnC;IACA,IAAI,CAACgR,OAAO,CAACxO,SAAS,CAACxC,EAAE,CAAC;EAC9B;EACAwD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACwN,OAAO,CAACxN,IAAI,CAAC,CAAC;EACvB;EACA8N,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAChL,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC0K,OAAO,CAACM,UAAU,CAAC,CAAC;EAC1D;EACAnO,IAAIA,CAAA,EAAG;IACH,CAAC,IAAI,CAACmD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC7N,IAAI,CAAC,CAAC;EACvC;EACAC,KAAKA,CAAA,EAAG;IACJ,CAAC,IAAI,CAACkD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC5N,KAAK,CAAC,CAAC;EACxC;EACAE,OAAOA,CAAA,EAAG;IACN,CAAC,IAAI,CAACgD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC1N,OAAO,CAAC,CAAC;EAC1C;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACyN,OAAO,CAACzN,MAAM,CAAC,CAAC;EACzB;EACAd,OAAOA,CAAA,EAAG;IACN,IAAI,CAACsG,SAAS,GAAG,IAAI;IACrB,IAAI,CAACiI,OAAO,CAACvO,OAAO,CAAC,CAAC;EAC1B;EACAY,KAAKA,CAAA,EAAG;IACJ,CAAC,IAAI,CAACiD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC3N,KAAK,CAAC,CAAC;EACxC;EACAI,WAAWA,CAAC6F,CAAC,EAAE;IACX,IAAI,CAAC,IAAI,CAAChD,MAAM,EAAE;MACd,IAAI,CAAC0K,OAAO,CAACvN,WAAW,CAAC6F,CAAC,CAAC;IAC/B;EACJ;EACAiI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjL,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC0K,OAAO,CAACO,WAAW,CAAC,CAAC;EACvD;EACA;EACAH,eAAeA,CAACI,SAAS,EAAE;IACvB,MAAMlI,CAAC,GAAG,IAAI,CAAC0H,OAAO;IACtB,IAAI1H,CAAC,CAAC8H,eAAe,EAAE;MACnB9H,CAAC,CAAC8H,eAAe,CAACI,SAAS,CAAC;IAChC;EACJ;AACJ;AACA,SAAST,kBAAkBA,CAAClnB,GAAG,EAAEsW,GAAG,EAAExX,KAAK,EAAE;EACzC,IAAI8oB,aAAa,GAAG5nB,GAAG,CAAC8H,GAAG,CAACwO,GAAG,CAAC;EAChC,IAAIsR,aAAa,EAAE;IACf,IAAIA,aAAa,CAACnoB,MAAM,EAAE;MACtB,MAAMqZ,KAAK,GAAG8O,aAAa,CAAC5f,OAAO,CAAClJ,KAAK,CAAC;MAC1C8oB,aAAa,CAAC7O,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAClC;IACA,IAAI8O,aAAa,CAACnoB,MAAM,IAAI,CAAC,EAAE;MAC3BO,GAAG,CAAC+H,MAAM,CAACuO,GAAG,CAAC;IACnB;EACJ;EACA,OAAOsR,aAAa;AACxB;AACA,SAAS9M,qBAAqBA,CAAChc,KAAK,EAAE;EAClC;EACA;EACA;EACA,OAAOA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,IAAI;AACvC;AACA,SAASmiB,aAAaA,CAACgB,IAAI,EAAE;EACzB,OAAOA,IAAI,IAAIA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACzC;AACA,SAAStG,mBAAmBA,CAAC1C,SAAS,EAAE;EACpC,OAAOA,SAAS,IAAI,OAAO,IAAIA,SAAS,IAAI,MAAM;AACtD;AACA,SAAS4O,YAAYA,CAAC/qB,OAAO,EAAEgC,KAAK,EAAE;EAClC,MAAMgpB,QAAQ,GAAGhrB,OAAO,CAACT,KAAK,CAAC0rB,OAAO;EACtCjrB,OAAO,CAACT,KAAK,CAAC0rB,OAAO,GAAGjpB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,MAAM;EACtD,OAAOgpB,QAAQ;AACnB;AACA,SAAStD,qBAAqBA,CAACwD,SAAS,EAAEvlB,MAAM,EAAEwO,QAAQ,EAAEgX,eAAe,EAAEC,YAAY,EAAE;EACvF,MAAMC,SAAS,GAAG,EAAE;EACpBlX,QAAQ,CAACjQ,OAAO,CAAElE,OAAO,IAAKqrB,SAAS,CAACzoB,IAAI,CAACmoB,YAAY,CAAC/qB,OAAO,CAAC,CAAC,CAAC;EACpE,MAAMsrB,cAAc,GAAG,EAAE;EACzBH,eAAe,CAACjnB,OAAO,CAAC,CAACN,KAAK,EAAE5D,OAAO,KAAK;IACxC,MAAMiI,MAAM,GAAG,IAAItB,GAAG,CAAC,CAAC;IACxB/C,KAAK,CAACM,OAAO,CAAErE,IAAI,IAAK;MACpB,MAAMmC,KAAK,GAAG2D,MAAM,CAAC/G,YAAY,CAACoB,OAAO,EAAEH,IAAI,EAAEurB,YAAY,CAAC;MAC9DnjB,MAAM,CAACrB,GAAG,CAAC/G,IAAI,EAAEmC,KAAK,CAAC;MACvB;MACA;MACA,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACW,MAAM,IAAI,CAAC,EAAE;QAC7B3C,OAAO,CAAC6d,YAAY,CAAC,GAAGD,0BAA0B;QAClD0N,cAAc,CAAC1oB,IAAI,CAAC5C,OAAO,CAAC;MAChC;IACJ,CAAC,CAAC;IACFkrB,SAAS,CAACtkB,GAAG,CAAC5G,OAAO,EAAEiI,MAAM,CAAC;EAClC,CAAC,CAAC;EACF;EACA;EACA,IAAIqE,CAAC,GAAG,CAAC;EACT6H,QAAQ,CAACjQ,OAAO,CAAElE,OAAO,IAAK+qB,YAAY,CAAC/qB,OAAO,EAAEqrB,SAAS,CAAC/e,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE,OAAOgf,cAAc;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrF,YAAYA,CAACsF,KAAK,EAAEpF,KAAK,EAAE;EAChC,MAAMqF,OAAO,GAAG,IAAI7kB,GAAG,CAAC,CAAC;EACzB4kB,KAAK,CAACrnB,OAAO,CAAEkiB,IAAI,IAAKoF,OAAO,CAAC5kB,GAAG,CAACwf,IAAI,EAAE,EAAE,CAAC,CAAC;EAC9C,IAAID,KAAK,CAACxjB,MAAM,IAAI,CAAC,EACjB,OAAO6oB,OAAO;EAClB,MAAMC,SAAS,GAAG,CAAC;EACnB,MAAMC,OAAO,GAAG,IAAIxpB,GAAG,CAACikB,KAAK,CAAC;EAC9B,MAAMwF,YAAY,GAAG,IAAIhlB,GAAG,CAAC,CAAC;EAC9B,SAASilB,OAAOA,CAACzG,IAAI,EAAE;IACnB,IAAI,CAACA,IAAI,EACL,OAAOsG,SAAS;IACpB,IAAIrF,IAAI,GAAGuF,YAAY,CAAC3gB,GAAG,CAACma,IAAI,CAAC;IACjC,IAAIiB,IAAI,EACJ,OAAOA,IAAI;IACf,MAAMzE,MAAM,GAAGwD,IAAI,CAACvD,UAAU;IAC9B,IAAI4J,OAAO,CAAChpB,GAAG,CAACmf,MAAM,CAAC,EAAE;MACrB;MACAyE,IAAI,GAAGzE,MAAM;IACjB,CAAC,MACI,IAAI+J,OAAO,CAAClpB,GAAG,CAACmf,MAAM,CAAC,EAAE;MAC1B;MACAyE,IAAI,GAAGqF,SAAS;IACpB,CAAC,MACI;MACD;MACArF,IAAI,GAAGwF,OAAO,CAACjK,MAAM,CAAC;IAC1B;IACAgK,YAAY,CAAC/kB,GAAG,CAACue,IAAI,EAAEiB,IAAI,CAAC;IAC5B,OAAOA,IAAI;EACf;EACAD,KAAK,CAACjiB,OAAO,CAAEihB,IAAI,IAAK;IACpB,MAAMiB,IAAI,GAAGwF,OAAO,CAACzG,IAAI,CAAC;IAC1B,IAAIiB,IAAI,KAAKqF,SAAS,EAAE;MACpBD,OAAO,CAACxgB,GAAG,CAACob,IAAI,CAAC,CAACxjB,IAAI,CAACuiB,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;EACF,OAAOqG,OAAO;AAClB;AACA,SAAS7M,QAAQA,CAAC3e,OAAO,EAAEqmB,SAAS,EAAE;EAClCrmB,OAAO,CAACilB,SAAS,EAAEzc,GAAG,CAAC6d,SAAS,CAAC;AACrC;AACA,SAAStG,WAAWA,CAAC/f,OAAO,EAAEqmB,SAAS,EAAE;EACrCrmB,OAAO,CAACilB,SAAS,EAAE4G,MAAM,CAACxF,SAAS,CAAC;AACxC;AACA,SAAS6C,6BAA6BA,CAAC7H,MAAM,EAAErhB,OAAO,EAAEkb,OAAO,EAAE;EAC7Dtd,mBAAmB,CAACsd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAMqB,MAAM,CAACN,gBAAgB,CAAC/gB,OAAO,CAAC,CAAC;AAC/E;AACA,SAASiqB,mBAAmBA,CAAC/O,OAAO,EAAE;EAClC,MAAM4Q,YAAY,GAAG,EAAE;EACvBC,yBAAyB,CAAC7Q,OAAO,EAAE4Q,YAAY,CAAC;EAChD,OAAOA,YAAY;AACvB;AACA,SAASC,yBAAyBA,CAAC7Q,OAAO,EAAE4Q,YAAY,EAAE;EACtD,KAAK,IAAIxf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4O,OAAO,CAACvY,MAAM,EAAE2J,CAAC,EAAE,EAAE;IACrC,MAAMsP,MAAM,GAAGV,OAAO,CAAC5O,CAAC,CAAC;IACzB,IAAIsP,MAAM,YAAYjc,oBAAoB,EAAE;MACxCosB,yBAAyB,CAACnQ,MAAM,CAACV,OAAO,EAAE4Q,YAAY,CAAC;IAC3D,CAAC,MACI;MACDA,YAAY,CAAClpB,IAAI,CAACgZ,MAAM,CAAC;IAC7B;EACJ;AACJ;AACA,SAAS2D,SAASA,CAACgD,CAAC,EAAEC,CAAC,EAAE;EACrB,MAAMwJ,EAAE,GAAGphB,MAAM,CAACpE,IAAI,CAAC+b,CAAC,CAAC;EACzB,MAAM0J,EAAE,GAAGrhB,MAAM,CAACpE,IAAI,CAACgc,CAAC,CAAC;EACzB,IAAIwJ,EAAE,CAACrpB,MAAM,IAAIspB,EAAE,CAACtpB,MAAM,EACtB,OAAO,KAAK;EAChB,KAAK,IAAI2J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0f,EAAE,CAACrpB,MAAM,EAAE2J,CAAC,EAAE,EAAE;IAChC,MAAMzM,IAAI,GAAGmsB,EAAE,CAAC1f,CAAC,CAAC;IAClB,IAAI,CAACkW,CAAC,CAACja,cAAc,CAAC1I,IAAI,CAAC,IAAI0iB,CAAC,CAAC1iB,IAAI,CAAC,KAAK2iB,CAAC,CAAC3iB,IAAI,CAAC,EAC9C,OAAO,KAAK;EACpB;EACA,OAAO,IAAI;AACf;AACA,SAAS0nB,sBAAsBA,CAACvnB,OAAO,EAAE2lB,mBAAmB,EAAEC,oBAAoB,EAAE;EAChF,MAAMsG,SAAS,GAAGtG,oBAAoB,CAAC5a,GAAG,CAAChL,OAAO,CAAC;EACnD,IAAI,CAACksB,SAAS,EACV,OAAO,KAAK;EAChB,IAAIC,QAAQ,GAAGxG,mBAAmB,CAAC3a,GAAG,CAAChL,OAAO,CAAC;EAC/C,IAAImsB,QAAQ,EAAE;IACVD,SAAS,CAAChoB,OAAO,CAAE6a,IAAI,IAAKoN,QAAQ,CAAC3jB,GAAG,CAACuW,IAAI,CAAC,CAAC;EACnD,CAAC,MACI;IACD4G,mBAAmB,CAAC/e,GAAG,CAAC5G,OAAO,EAAEksB,SAAS,CAAC;EAC/C;EACAtG,oBAAoB,CAAC3a,MAAM,CAACjL,OAAO,CAAC;EACpC,OAAO,IAAI;AACf;AAEA,MAAMosB,eAAe,CAAC;EAClBpmB,OAAO;EACPgU,WAAW;EACXqS,iBAAiB;EACjBC,eAAe;EACfC,aAAa,GAAG,CAAC,CAAC;EAClB;EACAjJ,iBAAiB,GAAGA,CAACtjB,OAAO,EAAEkG,OAAO,KAAK,CAAE,CAAC;EAC7CD,WAAWA,CAACumB,GAAG,EAAExmB,OAAO,EAAEgU,WAAW,EAAE;IACnC,IAAI,CAAChU,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgU,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqS,iBAAiB,GAAG,IAAIzJ,yBAAyB,CAAC4J,GAAG,CAACC,IAAI,EAAEzmB,OAAO,EAAEgU,WAAW,CAAC;IACtF,IAAI,CAACsS,eAAe,GAAG,IAAIxR,uBAAuB,CAAC0R,GAAG,CAACC,IAAI,EAAEzmB,OAAO,EAAEgU,WAAW,CAAC;IAClF,IAAI,CAACqS,iBAAiB,CAAC/I,iBAAiB,GAAG,CAACtjB,OAAO,EAAEkG,OAAO,KAAK,IAAI,CAACod,iBAAiB,CAACtjB,OAAO,EAAEkG,OAAO,CAAC;EAC7G;EACA8d,eAAeA,CAAC0I,WAAW,EAAEnP,WAAW,EAAEc,WAAW,EAAE5a,IAAI,EAAEmC,QAAQ,EAAE;IACnE,MAAM+mB,QAAQ,GAAGD,WAAW,GAAG,GAAG,GAAGjpB,IAAI;IACzC,IAAI2b,OAAO,GAAG,IAAI,CAACmN,aAAa,CAACI,QAAQ,CAAC;IAC1C,IAAI,CAACvN,OAAO,EAAE;MACV,MAAMxd,MAAM,GAAG,EAAE;MACjB,MAAMkB,QAAQ,GAAG,EAAE;MACnB,MAAMuD,GAAG,GAAGX,iBAAiB,CAAC,IAAI,CAACM,OAAO,EAAEJ,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,CAAC;MACvE,IAAIlB,MAAM,CAACe,MAAM,EAAE;QACf,MAAMjE,kBAAkB,CAAC+E,IAAI,EAAE7B,MAAM,CAAC;MAC1C;MACA,IAAI,OAAOT,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI2B,QAAQ,CAACH,MAAM,EAAE;UACjBa,gBAAgB,CAACC,IAAI,EAAEX,QAAQ,CAAC;QACpC;MACJ;MACAsc,OAAO,GAAGtF,YAAY,CAACrW,IAAI,EAAE4C,GAAG,EAAE,IAAI,CAAC2T,WAAW,CAAC;MACnD,IAAI,CAACuS,aAAa,CAACI,QAAQ,CAAC,GAAGvN,OAAO;IAC1C;IACA,IAAI,CAACiN,iBAAiB,CAACrI,eAAe,CAACzG,WAAW,EAAE9Z,IAAI,EAAE2b,OAAO,CAAC;EACtE;EACAjE,QAAQA,CAACoC,WAAW,EAAEc,WAAW,EAAE;IAC/B,IAAI,CAACgO,iBAAiB,CAAClR,QAAQ,CAACoC,WAAW,EAAEc,WAAW,CAAC;EAC7D;EACAvC,OAAOA,CAACyB,WAAW,EAAErX,OAAO,EAAE;IAC1B,IAAI,CAACmmB,iBAAiB,CAACvQ,OAAO,CAACyB,WAAW,EAAErX,OAAO,CAAC;EACxD;EACA0mB,QAAQA,CAACrP,WAAW,EAAEvd,OAAO,EAAE2hB,MAAM,EAAEyC,YAAY,EAAE;IACjD,IAAI,CAACiI,iBAAiB,CAACpK,UAAU,CAAC1E,WAAW,EAAEvd,OAAO,EAAE2hB,MAAM,EAAEyC,YAAY,CAAC;EACjF;EACAyI,QAAQA,CAACtP,WAAW,EAAEvd,OAAO,EAAEkG,OAAO,EAAE;IACpC,IAAI,CAACmmB,iBAAiB,CAACjL,UAAU,CAAC7D,WAAW,EAAEvd,OAAO,EAAEkG,OAAO,CAAC;EACpE;EACA4mB,iBAAiBA,CAAC9sB,OAAO,EAAE+sB,OAAO,EAAE;IAChC,IAAI,CAACV,iBAAiB,CAAC/H,qBAAqB,CAACtkB,OAAO,EAAE+sB,OAAO,CAAC;EAClE;EACAC,OAAOA,CAACzP,WAAW,EAAEvd,OAAO,EAAEitB,QAAQ,EAAEjrB,KAAK,EAAE;IAC3C,IAAIirB,QAAQ,CAAC9lB,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAC3B,MAAM,CAACiU,EAAE,EAAE8R,MAAM,CAAC,GAAGvuB,oBAAoB,CAACsuB,QAAQ,CAAC;MACnD,MAAM1Q,IAAI,GAAGva,KAAK;MAClB,IAAI,CAACsqB,eAAe,CAAChQ,OAAO,CAAClB,EAAE,EAAEpb,OAAO,EAAEktB,MAAM,EAAE3Q,IAAI,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAAC8P,iBAAiB,CAACjN,OAAO,CAAC7B,WAAW,EAAEvd,OAAO,EAAEitB,QAAQ,EAAEjrB,KAAK,CAAC;IACzE;EACJ;EACAka,MAAMA,CAACqB,WAAW,EAAEvd,OAAO,EAAEmc,SAAS,EAAEgR,UAAU,EAAE/Q,QAAQ,EAAE;IAC1D;IACA,IAAID,SAAS,CAAChV,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAC5B,MAAM,CAACiU,EAAE,EAAE8R,MAAM,CAAC,GAAGvuB,oBAAoB,CAACwd,SAAS,CAAC;MACpD,OAAO,IAAI,CAACmQ,eAAe,CAACpQ,MAAM,CAACd,EAAE,EAAEpb,OAAO,EAAEktB,MAAM,EAAE9Q,QAAQ,CAAC;IACrE;IACA,OAAO,IAAI,CAACiQ,iBAAiB,CAACnQ,MAAM,CAACqB,WAAW,EAAEvd,OAAO,EAAEmc,SAAS,EAAEgR,UAAU,EAAE/Q,QAAQ,CAAC;EAC/F;EACAgJ,KAAKA,CAACjD,WAAW,GAAG,CAAC,CAAC,EAAE;IACpB,IAAI,CAACkK,iBAAiB,CAACjH,KAAK,CAACjD,WAAW,CAAC;EAC7C;EACA,IAAIjH,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,GAAG,IAAI,CAACmR,iBAAiB,CAACnR,OAAO,EAAE,GAAG,IAAI,CAACoR,eAAe,CAACpR,OAAO,CAAC;EAC/E;EACA4J,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACuH,iBAAiB,CAACvH,iBAAiB,CAAC,CAAC;EACrD;EACArE,wBAAwBA,CAAC2M,EAAE,EAAE;IACzB,IAAI,CAACf,iBAAiB,CAAC5L,wBAAwB,CAAC2M,EAAE,CAAC;EACvD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACrtB,OAAO,EAAEiI,MAAM,EAAE;EACjD,IAAIqlB,WAAW,GAAG,IAAI;EACtB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAI9iB,KAAK,CAACC,OAAO,CAACzC,MAAM,CAAC,IAAIA,MAAM,CAACtF,MAAM,EAAE;IACxC2qB,WAAW,GAAGE,yBAAyB,CAACvlB,MAAM,CAAC,CAAC,CAAC,CAAC;IAClD,IAAIA,MAAM,CAACtF,MAAM,GAAG,CAAC,EAAE;MACnB4qB,SAAS,GAAGC,yBAAyB,CAACvlB,MAAM,CAACA,MAAM,CAACtF,MAAM,GAAG,CAAC,CAAC,CAAC;IACpE;EACJ,CAAC,MACI,IAAIsF,MAAM,YAAYtB,GAAG,EAAE;IAC5B2mB,WAAW,GAAGE,yBAAyB,CAACvlB,MAAM,CAAC;EACnD;EACA,OAAOqlB,WAAW,IAAIC,SAAS,GAAG,IAAIE,kBAAkB,CAACztB,OAAO,EAAEstB,WAAW,EAAEC,SAAS,CAAC,GAAG,IAAI;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,kBAAkB,CAAC;EACrBC,QAAQ;EACRC,YAAY;EACZC,UAAU;EACV,OAAOC,sBAAsB,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC;EAC7DC,MAAM,GAAG,CAAC,CAAC;EACXC,cAAc;EACd/nB,WAAWA,CAACynB,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAE;IAC5C,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAIK,aAAa,GAAGR,kBAAkB,CAACI,sBAAsB,CAAC7iB,GAAG,CAAC0iB,QAAQ,CAAC;IAC3E,IAAI,CAACO,aAAa,EAAE;MAChBR,kBAAkB,CAACI,sBAAsB,CAACjnB,GAAG,CAAC8mB,QAAQ,EAAGO,aAAa,GAAG,IAAItnB,GAAG,CAAC,CAAE,CAAC;IACxF;IACA,IAAI,CAACqnB,cAAc,GAAGC,aAAa;EACvC;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,uCAAuC;MACvD,IAAI,IAAI,CAACJ,YAAY,EAAE;QACnBzvB,SAAS,CAAC,IAAI,CAACwvB,QAAQ,EAAE,IAAI,CAACC,YAAY,EAAE,IAAI,CAACK,cAAc,CAAC;MACpE;MACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;IACpB;EACJ;EACAnR,MAAMA,CAAA,EAAG;IACL,IAAI,CAACsR,KAAK,CAAC,CAAC;IACZ,IAAI,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,wCAAwC;MACxD7vB,SAAS,CAAC,IAAI,CAACwvB,QAAQ,EAAE,IAAI,CAACM,cAAc,CAAC;MAC7C,IAAI,IAAI,CAACJ,UAAU,EAAE;QACjB1vB,SAAS,CAAC,IAAI,CAACwvB,QAAQ,EAAE,IAAI,CAACE,UAAU,CAAC;QACzC,IAAI,CAACA,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;IACpB;EACJ;EACAjS,OAAOA,CAAA,EAAG;IACN,IAAI,CAACc,MAAM,CAAC,CAAC;IACb,IAAI,IAAI,CAACmR,MAAM,GAAG,CAAC,CAAC,yCAAyC;MACzDN,kBAAkB,CAACI,sBAAsB,CAAC5iB,MAAM,CAAC,IAAI,CAACyiB,QAAQ,CAAC;MAC/D,IAAI,IAAI,CAACC,YAAY,EAAE;QACnB1vB,WAAW,CAAC,IAAI,CAACyvB,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;QAC7C,IAAI,CAACC,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI,IAAI,CAACA,UAAU,EAAE;QACjB3vB,WAAW,CAAC,IAAI,CAACyvB,QAAQ,EAAE,IAAI,CAACE,UAAU,CAAC;QAC3C,IAAI,CAACA,UAAU,GAAG,IAAI;MAC1B;MACA1vB,SAAS,CAAC,IAAI,CAACwvB,QAAQ,EAAE,IAAI,CAACM,cAAc,CAAC;MAC7C,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;IACpB;EACJ;AACJ;AACA,SAASP,yBAAyBA,CAACvlB,MAAM,EAAE;EACvC,IAAI3D,MAAM,GAAG,IAAI;EACjB2D,MAAM,CAAC/D,OAAO,CAAC,CAACiR,GAAG,EAAEtV,IAAI,KAAK;IAC1B,IAAIsuB,oBAAoB,CAACtuB,IAAI,CAAC,EAAE;MAC5ByE,MAAM,GAAGA,MAAM,IAAI,IAAIqC,GAAG,CAAC,CAAC;MAC5BrC,MAAM,CAACsC,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC;IACzB;EACJ,CAAC,CAAC;EACF,OAAO7Q,MAAM;AACjB;AACA,SAAS6pB,oBAAoBA,CAACtuB,IAAI,EAAE;EAChC,OAAOA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,UAAU;AACpD;AAEA,MAAMuuB,mBAAmB,CAAC;EACtBpuB,OAAO;EACPM,SAAS;EACTwH,OAAO;EACPumB,cAAc;EACdC,UAAU,GAAG,EAAE;EACfC,WAAW,GAAG,EAAE;EAChBC,aAAa,GAAG,EAAE;EAClBC,SAAS;EACTC,MAAM;EACNC,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG,KAAK;EACjBC,QAAQ,GAAG,KAAK;EAChBC,UAAU,GAAG,KAAK;EAClBC,cAAc;EACd;EACA;EACA;EACAC,kBAAkB,GAAG,EAAE;EACvBC,mBAAmB,GAAG,EAAE;EACxB;EACAC,SAAS;EACTjb,IAAI,GAAG,CAAC;EACRyU,YAAY,GAAG,IAAI;EACnByG,eAAe,GAAG,IAAIxoB,GAAG,CAAC,CAAC;EAC3BV,WAAWA,CAACjG,OAAO,EAAEM,SAAS,EAAEwH,OAAO,EAAEumB,cAAc,EAAE;IACrD,IAAI,CAACruB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACwH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACumB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACI,SAAS,GAAG3mB,OAAO,CAAC,UAAU,CAAC;IACpC,IAAI,CAAC4mB,MAAM,GAAG5mB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;IACnC,IAAI,CAACmM,IAAI,GAAG,IAAI,CAACwa,SAAS,GAAG,IAAI,CAACC,MAAM;EAC5C;EACAU,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACR,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACN,UAAU,CAACpqB,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACrC,IAAI,CAACiV,UAAU,GAAG,EAAE;IACxB;EACJ;EACAzR,IAAIA,CAAA,EAAG;IACH,IAAI,CAACxB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACgU,yBAAyB,CAAC,CAAC;EACpC;EACAhU,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACsT,YAAY,EACjB;IACJ,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,MAAMruB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA,IAAI,CAAC4uB,SAAS,GAAG,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAACtvB,OAAO,EAAEM,SAAS,EAAE,IAAI,CAACwH,OAAO,CAAC;IACjF,IAAI,CAACinB,cAAc,GAAGzuB,SAAS,CAACqC,MAAM,GAAGrC,SAAS,CAACA,SAAS,CAACqC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIgE,GAAG,CAAC,CAAC;IACpF,MAAM4oB,QAAQ,GAAGA,CAAA,KAAM,IAAI,CAACH,SAAS,CAAC,CAAC;IACvC,IAAI,CAACF,SAAS,CAACM,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;IACnD,IAAI,CAAC1T,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACqT,SAAS,CAACO,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC1D,CAAC,CAAC;EACN;EACAF,yBAAyBA,CAAA,EAAG;IACxB;IACA,IAAI,IAAI,CAACX,MAAM,EAAE;MACb,IAAI,CAACgB,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACR,SAAS,CAACzS,KAAK,CAAC,CAAC;IAC1B;EACJ;EACAkT,yBAAyBA,CAACrvB,SAAS,EAAE;IACjC,MAAMsvB,GAAG,GAAG,EAAE;IACdtvB,SAAS,CAAC4D,OAAO,CAAE2rB,KAAK,IAAK;MACzBD,GAAG,CAAChtB,IAAI,CAACgI,MAAM,CAACklB,WAAW,CAACD,KAAK,CAAC,CAAC;IACvC,CAAC,CAAC;IACF,OAAOD,GAAG;EACd;EACA;EACAN,oBAAoBA,CAACtvB,OAAO,EAAEM,SAAS,EAAEwH,OAAO,EAAE;IAC9C,OAAO9H,OAAO,CAACK,OAAO,CAAC,IAAI,CAACsvB,yBAAyB,CAACrvB,SAAS,CAAC,EAAEwH,OAAO,CAAC;EAC9E;EACAgY,OAAOA,CAACzG,EAAE,EAAE;IACR,IAAI,CAAC4V,mBAAmB,CAACrsB,IAAI,CAACyW,EAAE,CAAC;IACjC,IAAI,CAACkV,WAAW,CAAC3rB,IAAI,CAACyW,EAAE,CAAC;EAC7B;EACA2G,MAAMA,CAAC3G,EAAE,EAAE;IACP,IAAI,CAAC2V,kBAAkB,CAACpsB,IAAI,CAACyW,EAAE,CAAC;IAChC,IAAI,CAACiV,UAAU,CAAC1rB,IAAI,CAACyW,EAAE,CAAC;EAC5B;EACAwC,SAASA,CAACxC,EAAE,EAAE;IACV,IAAI,CAACmV,aAAa,CAAC5rB,IAAI,CAACyW,EAAE,CAAC;EAC/B;EACAmD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnB,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC,IAAI,CAACsP,UAAU,CAAC,CAAC,EAAE;MACpB,IAAI,CAAC4D,WAAW,CAACrqB,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACtC,IAAI,CAACkV,WAAW,GAAG,EAAE;MACrB,IAAI,CAACM,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACR,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAACH,KAAK,CAAC,CAAC;MAC/B;IACJ;IACA,IAAI,CAACgB,SAAS,CAAC1S,IAAI,CAAC,CAAC;EACzB;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACI,IAAI,CAAC,CAAC;IACX,IAAI,CAACqS,SAAS,CAACzS,KAAK,CAAC,CAAC;EAC1B;EACAG,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACwR,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACzR,MAAM,CAAC,CAAC;IAChC;IACA,IAAI,CAACwS,SAAS,CAAC,CAAC;IAChB,IAAI,CAACF,SAAS,CAACtS,MAAM,CAAC,CAAC;EAC3B;EACAF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACgT,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACZ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACN,WAAW,GAAG,IAAI,CAACU,mBAAmB;IAC3C,IAAI,CAACX,UAAU,GAAG,IAAI,CAACU,kBAAkB;EAC7C;EACAU,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACa,MAAM,CAAC,CAAC;IAC3B;EACJ;EACApT,OAAOA,CAAA,EAAG;IACN,IAAI,CAACD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACF,IAAI,CAAC,CAAC;EACf;EACAmO,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACkE,QAAQ;EACxB;EACA/S,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACgT,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACY,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACN,SAAS,CAAC,CAAC;MAChB,IAAI,IAAI,CAACf,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAACvS,OAAO,CAAC,CAAC;MACjC;MACA,IAAI,CAAC0S,aAAa,CAACtqB,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACxC,IAAI,CAACmV,aAAa,GAAG,EAAE;IAC3B;EACJ;EACA1R,WAAWA,CAAC6F,CAAC,EAAE;IACX,IAAI,IAAI,CAACuM,SAAS,KAAKrX,SAAS,EAAE;MAC9B,IAAI,CAACgF,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACqS,SAAS,CAACroB,WAAW,GAAG8b,CAAC,GAAG,IAAI,CAAC1O,IAAI;EAC9C;EACA2W,WAAWA,CAAA,EAAG;IACV;IACA,OAAO,EAAE,IAAI,CAACsE,SAAS,CAACroB,WAAW,IAAI,CAAC,CAAC,GAAG,IAAI,CAACoN,IAAI;EACzD;EACA,IAAIxF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACigB,MAAM,GAAG,IAAI,CAACD,SAAS;EACvC;EACA7E,aAAaA,CAAA,EAAG;IACZ,MAAM3hB,MAAM,GAAG,IAAItB,GAAG,CAAC,CAAC;IACxB,IAAI,IAAI,CAACgkB,UAAU,CAAC,CAAC,EAAE;MACnB;MACA;MACA;MACA,MAAMjV,aAAa,GAAG,IAAI,CAACqZ,cAAc;MACzCrZ,aAAa,CAACxR,OAAO,CAAC,CAACiR,GAAG,EAAEtV,IAAI,KAAK;QACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;UACnBoI,MAAM,CAACrB,GAAG,CAAC/G,IAAI,EAAE,IAAI,CAAC+uB,SAAS,GAAGzZ,GAAG,GAAGvW,YAAY,CAAC,IAAI,CAACoB,OAAO,EAAEH,IAAI,CAAC,CAAC;QAC7E;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACsvB,eAAe,GAAGlnB,MAAM;EACjC;EACA;EACAwiB,eAAeA,CAACI,SAAS,EAAE;IACvB,MAAMmF,OAAO,GAAGnF,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC0D,WAAW,GAAG,IAAI,CAACD,UAAU;IAC1E0B,OAAO,CAAC9rB,OAAO,CAAEmV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IAC7B2W,OAAO,CAACrtB,MAAM,GAAG,CAAC;EACtB;AACJ;AAEA,MAAMstB,mBAAmB,CAAC;EACtBz0B,qBAAqBA,CAACqE,IAAI,EAAE;IACxB;IACA,IAAI,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,OAAO3F,qBAAqB,CAACqE,IAAI,CAAC;IACtC;IACA,OAAO,IAAI;EACf;EACAgZ,+BAA+BA,CAAChZ,IAAI,EAAE;IAClC;IACA,IAAI,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAM+uB,OAAO,GAAGrxB,mBAAmB,CAACgB,IAAI,CAAC;MACzC,OAAOf,kCAAkC,CAACoxB,OAAO,CAAC;IACtD;IACA,OAAO,IAAI;EACf;EACAz0B,eAAeA,CAACqE,IAAI,EAAEC,IAAI,EAAE;IACxB,OAAOtE,eAAe,CAACqE,IAAI,EAAEC,IAAI,CAAC;EACtC;EACArE,gBAAgBA,CAACsE,OAAO,EAAE;IACtB,OAAOtE,gBAAgB,CAACsE,OAAO,CAAC;EACpC;EACAC,KAAKA,CAACD,OAAO,EAAEE,QAAQ,EAAEC,KAAK,EAAE;IAC5B,OAAOxE,WAAW,CAACqE,OAAO,EAAEE,QAAQ,EAAEC,KAAK,CAAC;EAChD;EACAvB,YAAYA,CAACoB,OAAO,EAAEH,IAAI,EAAEO,YAAY,EAAE;IACtC,OAAOxB,YAAY,CAACoB,OAAO,EAAEH,IAAI,CAAC;EACtC;EACAQ,OAAOA,CAACL,OAAO,EAAEM,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,GAAG,EAAE,EAAE;IACvE,MAAMyvB,IAAI,GAAG3vB,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU;IAC7C,MAAM4vB,aAAa,GAAG;MAAE7vB,QAAQ;MAAEC,KAAK;MAAE2vB;IAAK,CAAC;IAC/C;IACA;IACA,IAAI1vB,MAAM,EAAE;MACR2vB,aAAa,CAAC,QAAQ,CAAC,GAAG3vB,MAAM;IACpC;IACA,MAAM4vB,cAAc,GAAG,IAAI1pB,GAAG,CAAC,CAAC;IAChC,MAAM2pB,2BAA2B,GAAI5vB,eAAe,CAACsC,MAAM,CAAE4Y,MAAM,IAAKA,MAAM,YAAYwS,mBAAmB,CAAE;IAC/G,IAAIrvB,8BAA8B,CAACwB,QAAQ,EAAEC,KAAK,CAAC,EAAE;MACjD8vB,2BAA2B,CAACpsB,OAAO,CAAE0X,MAAM,IAAK;QAC5CA,MAAM,CAACuT,eAAe,CAACjrB,OAAO,CAAC,CAACiR,GAAG,EAAEtV,IAAI,KAAKwwB,cAAc,CAACzpB,GAAG,CAAC/G,IAAI,EAAEsV,GAAG,CAAC,CAAC;MAChF,CAAC,CAAC;IACN;IACA,IAAIZ,UAAU,GAAGvV,oBAAoB,CAACsB,SAAS,CAAC,CAAC4C,GAAG,CAAE+E,MAAM,IAAK,IAAItB,GAAG,CAACsB,MAAM,CAAC,CAAC;IACjFsM,UAAU,GAAGtV,kCAAkC,CAACe,OAAO,EAAEuU,UAAU,EAAE8b,cAAc,CAAC;IACpF,MAAME,aAAa,GAAGlD,0BAA0B,CAACrtB,OAAO,EAAEuU,UAAU,CAAC;IACrE,OAAO,IAAI6Z,mBAAmB,CAACpuB,OAAO,EAAEuU,UAAU,EAAE6b,aAAa,EAAEG,aAAa,CAAC;EACrF;AACJ;AAEA,SAASC,YAAYA,CAACnvB,IAAI,EAAEmrB,GAAG,EAAE;EAC7B;EACA,IAAInrB,IAAI,KAAK,MAAM,EAAE;IACjB,OAAO,IAAI+qB,eAAe,CAACI,GAAG,EAAE,IAAI5sB,mBAAmB,CAAC,CAAC,EAAE,IAAI6B,4BAA4B,CAAC,CAAC,CAAC;EAClG;EACA,OAAO,IAAI2qB,eAAe,CAACI,GAAG,EAAE,IAAIyD,mBAAmB,CAAC,CAAC,EAAE,IAAI9tB,4BAA4B,CAAC,CAAC,CAAC;AAClG;AAEA,MAAMsuB,SAAS,CAAC;EACZzqB,OAAO;EACP0qB,aAAa;EACbzqB,WAAWA,CAACD,OAAO,EAAEiP,KAAK,EAAE;IACxB,IAAI,CAACjP,OAAO,GAAGA,OAAO;IACtB,MAAMpE,MAAM,GAAG,EAAE;IACjB,MAAMkB,QAAQ,GAAG,EAAE;IACnB,MAAMuD,GAAG,GAAGX,iBAAiB,CAACM,OAAO,EAAEiP,KAAK,EAAErT,MAAM,EAAEkB,QAAQ,CAAC;IAC/D,IAAIlB,MAAM,CAACe,MAAM,EAAE;MACf,MAAMzD,gBAAgB,CAAC0C,MAAM,CAAC;IAClC;IACA,IAAI,OAAOT,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI2B,QAAQ,CAACH,MAAM,EAAE;QACjBU,cAAc,CAACP,QAAQ,CAAC;MAC5B;IACJ;IACA,IAAI,CAAC4tB,aAAa,GAAGrqB,GAAG;EAC5B;EACAsqB,cAAcA,CAAC3wB,OAAO,EAAEyP,cAAc,EAAEmhB,iBAAiB,EAAE9oB,OAAO,EAAE6H,eAAe,EAAE;IACjF,MAAMue,KAAK,GAAGzjB,KAAK,CAACC,OAAO,CAAC+E,cAAc,CAAC,GACrCtQ,eAAe,CAACsQ,cAAc,CAAC,GAC/BA,cAAc;IACpB,MAAMohB,IAAI,GAAGpmB,KAAK,CAACC,OAAO,CAACkmB,iBAAiB,CAAC,GACvCzxB,eAAe,CAACyxB,iBAAiB,CAAC,GAClCA,iBAAiB;IACvB,MAAMhvB,MAAM,GAAG,EAAE;IACjB+N,eAAe,GAAGA,eAAe,IAAI,IAAIjB,qBAAqB,CAAC,CAAC;IAChE,MAAMpK,MAAM,GAAG+K,uBAAuB,CAAC,IAAI,CAACrJ,OAAO,EAAEhG,OAAO,EAAE,IAAI,CAAC0wB,aAAa,EAAEjzB,eAAe,EAAED,eAAe,EAAE0wB,KAAK,EAAE2C,IAAI,EAAE/oB,OAAO,EAAE6H,eAAe,EAAE/N,MAAM,CAAC;IAClK,IAAIA,MAAM,CAACe,MAAM,EAAE;MACf,MAAMvD,cAAc,CAACwC,MAAM,CAAC;IAChC;IACA,OAAO0C,MAAM;EACjB;AACJ;AAEA,MAAMwsB,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,uBAAuB,GAAG,YAAY;AAC5C,MAAMC,qBAAqB,CAAC;EACxBzT,WAAW;EACX0T,QAAQ;EACR5P,MAAM;EACN6P,UAAU;EACV;EACA;EACAC,KAAK,GAAG,CAAC,CAAC;EACVlrB,WAAWA,CAACsX,WAAW,EAAE0T,QAAQ,EAAE5P,MAAM,EAAE6P,UAAU,EAAE;IACnD,IAAI,CAAC3T,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC0T,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5P,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6P,UAAU,GAAGA,UAAU;EAChC;EACA,IAAInS,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACkS,QAAQ,CAAClS,IAAI;EAC7B;EACAqS,WAAWA,CAACjM,IAAI,EAAE;IACd,IAAI,CAAC8L,QAAQ,CAACG,WAAW,GAAGjM,IAAI,CAAC;EACrC;EACArJ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuF,MAAM,CAACvF,OAAO,CAAC,IAAI,CAACyB,WAAW,EAAE,IAAI,CAAC0T,QAAQ,CAAC;IACpD,IAAI,CAAC5P,MAAM,CAACZ,wBAAwB,CAAC,MAAM;MACvC;MACA;MACA4Q,cAAc,CAAC,MAAM;QACjB,IAAI,CAACJ,QAAQ,CAACnV,OAAO,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACoV,UAAU,GAAG,CAAC;EACvB;EACAI,aAAaA,CAAC7tB,IAAI,EAAE8tB,SAAS,EAAE;IAC3B,OAAO,IAAI,CAACN,QAAQ,CAACK,aAAa,CAAC7tB,IAAI,EAAE8tB,SAAS,CAAC;EACvD;EACAC,aAAaA,CAACxvB,KAAK,EAAE;IACjB,OAAO,IAAI,CAACivB,QAAQ,CAACO,aAAa,CAACxvB,KAAK,CAAC;EAC7C;EACAyvB,UAAUA,CAACzvB,KAAK,EAAE;IACd,OAAO,IAAI,CAACivB,QAAQ,CAACQ,UAAU,CAACzvB,KAAK,CAAC;EAC1C;EACA0vB,WAAWA,CAAC/P,MAAM,EAAEgQ,QAAQ,EAAE;IAC1B,IAAI,CAACV,QAAQ,CAACS,WAAW,CAAC/P,MAAM,EAAEgQ,QAAQ,CAAC;IAC3C,IAAI,CAACtQ,MAAM,CAACuL,QAAQ,CAAC,IAAI,CAACrP,WAAW,EAAEoU,QAAQ,EAAEhQ,MAAM,EAAE,KAAK,CAAC;EACnE;EACAyC,YAAYA,CAACzC,MAAM,EAAEgQ,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,GAAG,IAAI,EAAE;IACpD,IAAI,CAACZ,QAAQ,CAAC7M,YAAY,CAACzC,MAAM,EAAEgQ,QAAQ,EAAEC,QAAQ,CAAC;IACtD;IACA,IAAI,CAACvQ,MAAM,CAACuL,QAAQ,CAAC,IAAI,CAACrP,WAAW,EAAEoU,QAAQ,EAAEhQ,MAAM,EAAEkQ,MAAM,CAAC;EACpE;EACAC,WAAWA,CAACnQ,MAAM,EAAEoQ,QAAQ,EAAEC,aAAa,EAAE;IACzC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACpQ,UAAU,CAACmQ,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAAC1Q,MAAM,CAACwL,QAAQ,CAAC,IAAI,CAACtP,WAAW,EAAEwU,QAAQ,EAAE,IAAI,CAACd,QAAQ,CAAC;IACnE;EACJ;EACAgB,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,OAAO,IAAI,CAAClB,QAAQ,CAACgB,iBAAiB,CAACC,cAAc,EAAEC,eAAe,CAAC;EAC3E;EACAvQ,UAAUA,CAACuD,IAAI,EAAE;IACb,OAAO,IAAI,CAAC8L,QAAQ,CAACrP,UAAU,CAACuD,IAAI,CAAC;EACzC;EACAiN,WAAWA,CAACjN,IAAI,EAAE;IACd,OAAO,IAAI,CAAC8L,QAAQ,CAACmB,WAAW,CAACjN,IAAI,CAAC;EAC1C;EACAkN,YAAYA,CAACC,EAAE,EAAE7uB,IAAI,EAAEzB,KAAK,EAAEuvB,SAAS,EAAE;IACrC,IAAI,CAACN,QAAQ,CAACoB,YAAY,CAACC,EAAE,EAAE7uB,IAAI,EAAEzB,KAAK,EAAEuvB,SAAS,CAAC;EAC1D;EACAgB,eAAeA,CAACD,EAAE,EAAE7uB,IAAI,EAAE8tB,SAAS,EAAE;IACjC,IAAI,CAACN,QAAQ,CAACsB,eAAe,CAACD,EAAE,EAAE7uB,IAAI,EAAE8tB,SAAS,CAAC;EACtD;EACA5S,QAAQA,CAAC2T,EAAE,EAAE7uB,IAAI,EAAE;IACf,IAAI,CAACwtB,QAAQ,CAACtS,QAAQ,CAAC2T,EAAE,EAAE7uB,IAAI,CAAC;EACpC;EACAsc,WAAWA,CAACuS,EAAE,EAAE7uB,IAAI,EAAE;IAClB,IAAI,CAACwtB,QAAQ,CAAClR,WAAW,CAACuS,EAAE,EAAE7uB,IAAI,CAAC;EACvC;EACA+uB,QAAQA,CAACF,EAAE,EAAE/yB,KAAK,EAAEyC,KAAK,EAAEywB,KAAK,EAAE;IAC9B,IAAI,CAACxB,QAAQ,CAACuB,QAAQ,CAACF,EAAE,EAAE/yB,KAAK,EAAEyC,KAAK,EAAEywB,KAAK,CAAC;EACnD;EACAC,WAAWA,CAACJ,EAAE,EAAE/yB,KAAK,EAAEkzB,KAAK,EAAE;IAC1B,IAAI,CAACxB,QAAQ,CAACyB,WAAW,CAACJ,EAAE,EAAE/yB,KAAK,EAAEkzB,KAAK,CAAC;EAC/C;EACAE,WAAWA,CAACL,EAAE,EAAE7uB,IAAI,EAAEzB,KAAK,EAAE;IACzB,IAAIyB,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI2pB,gBAAgB,IAAIrtB,IAAI,IAAIstB,uBAAuB,EAAE;MACvE,IAAI,CAACjE,iBAAiB,CAACwF,EAAE,EAAE,CAAC,CAACtwB,KAAK,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAACivB,QAAQ,CAAC0B,WAAW,CAACL,EAAE,EAAE7uB,IAAI,EAAEzB,KAAK,CAAC;IAC9C;EACJ;EACA4wB,QAAQA,CAACzN,IAAI,EAAEnjB,KAAK,EAAE;IAClB,IAAI,CAACivB,QAAQ,CAAC2B,QAAQ,CAACzN,IAAI,EAAEnjB,KAAK,CAAC;EACvC;EACAka,MAAMA,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,EAAE;IACzC,OAAO,IAAI,CAACmpB,QAAQ,CAAC/U,MAAM,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,CAAC;EACrE;EACAglB,iBAAiBA,CAAC9sB,OAAO,EAAEgC,KAAK,EAAE;IAC9B,IAAI,CAACqf,MAAM,CAACyL,iBAAiB,CAAC9sB,OAAO,EAAEgC,KAAK,CAAC;EACjD;AACJ;AACA,MAAM6wB,iBAAiB,SAAS7B,qBAAqB,CAAC;EAClD9vB,OAAO;EACP+E,WAAWA,CAAC/E,OAAO,EAAEqc,WAAW,EAAE0T,QAAQ,EAAE5P,MAAM,EAAExF,SAAS,EAAE;IAC3D,KAAK,CAAC0B,WAAW,EAAE0T,QAAQ,EAAE5P,MAAM,EAAExF,SAAS,CAAC;IAC/C,IAAI,CAAC3a,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqc,WAAW,GAAGA,WAAW;EAClC;EACAoV,WAAWA,CAACL,EAAE,EAAE7uB,IAAI,EAAEzB,KAAK,EAAE;IACzB,IAAIyB,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI2pB,gBAAgB,EAAE;MACpC,IAAIrtB,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI1D,IAAI,IAAIstB,uBAAuB,EAAE;QAC1D/uB,KAAK,GAAGA,KAAK,KAAK6V,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC7V,KAAK;QAC5C,IAAI,CAAC8qB,iBAAiB,CAACwF,EAAE,EAAEtwB,KAAK,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACqf,MAAM,CAAC2L,OAAO,CAAC,IAAI,CAACzP,WAAW,EAAE+U,EAAE,EAAE7uB,IAAI,CAACkK,KAAK,CAAC,CAAC,CAAC,EAAE3L,KAAK,CAAC;MACnE;IACJ,CAAC,MACI;MACD,IAAI,CAACivB,QAAQ,CAAC0B,WAAW,CAACL,EAAE,EAAE7uB,IAAI,EAAEzB,KAAK,CAAC;IAC9C;EACJ;EACAka,MAAMA,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,EAAE;IACzC,IAAIqU,SAAS,CAAChV,MAAM,CAAC,CAAC,CAAC,IAAI2pB,gBAAgB,EAAE;MACzC,MAAM9wB,OAAO,GAAG8yB,wBAAwB,CAACnf,MAAM,CAAC;MAChD,IAAIlQ,IAAI,GAAG0Y,SAAS,CAACxO,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAIiR,KAAK,GAAG,EAAE;MACd;MACA;MACA,IAAInb,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI2pB,gBAAgB,EAAE;QACpC,CAACrtB,IAAI,EAAEmb,KAAK,CAAC,GAAGmU,wBAAwB,CAACtvB,IAAI,CAAC;MAClD;MACA,OAAO,IAAI,CAAC4d,MAAM,CAACnF,MAAM,CAAC,IAAI,CAACqB,WAAW,EAAEvd,OAAO,EAAEyD,IAAI,EAAEmb,KAAK,EAAGoU,KAAK,IAAK;QACzE,MAAMC,OAAO,GAAGD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC9xB,OAAO,CAACgyB,wBAAwB,CAACD,OAAO,EAAE7W,QAAQ,EAAE4W,KAAK,CAAC;MACnE,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAC/B,QAAQ,CAAC/U,MAAM,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,CAAC;EACrE;AACJ;AACA,SAASgrB,wBAAwBA,CAACnf,MAAM,EAAE;EACtC,QAAQA,MAAM;IACV,KAAK,MAAM;MACP,OAAOwf,QAAQ,CAAC1G,IAAI;IACxB,KAAK,UAAU;MACX,OAAO0G,QAAQ;IACnB,KAAK,QAAQ;MACT,OAAOC,MAAM;IACjB;MACI,OAAOzf,MAAM;EACrB;AACJ;AACA,SAASof,wBAAwBA,CAACjc,WAAW,EAAE;EAC3C,MAAMuc,QAAQ,GAAGvc,WAAW,CAAC5L,OAAO,CAAC,GAAG,CAAC;EACzC,MAAMkU,OAAO,GAAGtI,WAAW,CAACwc,SAAS,CAAC,CAAC,EAAED,QAAQ,CAAC;EAClD,MAAMzU,KAAK,GAAG9H,WAAW,CAACnJ,KAAK,CAAC0lB,QAAQ,GAAG,CAAC,CAAC;EAC7C,OAAO,CAACjU,OAAO,EAAER,KAAK,CAAC;AAC3B;AAEA,MAAM2U,wBAAwB,CAAC;EAC3BtC,QAAQ;EACR5P,MAAM;EACNmS,KAAK;EACLC,UAAU,GAAG,CAAC;EACdC,YAAY,GAAG,CAAC;EAChBC,yBAAyB,GAAG,EAAE;EAC9BC,cAAc,GAAG,IAAIjtB,GAAG,CAAC,CAAC;EAC1BktB,aAAa,GAAG,CAAC;EACjB5tB,WAAWA,CAACgrB,QAAQ,EAAE5P,MAAM,EAAEmS,KAAK,EAAE;IACjC,IAAI,CAACvC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5P,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACmS,KAAK,GAAGA,KAAK;IAClBnS,MAAM,CAACiC,iBAAiB,GAAG,CAACtjB,OAAO,EAAEixB,QAAQ,KAAK;MAC9CA,QAAQ,EAAEa,WAAW,CAAC,IAAI,EAAE9xB,OAAO,CAAC;IACxC,CAAC;EACL;EACA8zB,cAAcA,CAACzV,WAAW,EAAEhd,IAAI,EAAE;IAC9B,MAAM0yB,kBAAkB,GAAG,EAAE;IAC7B;IACA;IACA,MAAM9C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC6C,cAAc,CAACzV,WAAW,EAAEhd,IAAI,CAAC;IAChE,IAAI,CAACgd,WAAW,IAAI,CAAChd,IAAI,EAAE0d,IAAI,GAAG,WAAW,CAAC,EAAE;MAC5C,MAAMiV,KAAK,GAAG,IAAI,CAACJ,cAAc;MACjC,IAAIK,QAAQ,GAAGD,KAAK,CAAChpB,GAAG,CAACimB,QAAQ,CAAC;MAClC,IAAI,CAACgD,QAAQ,EAAE;QACX;QACA;QACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAMF,KAAK,CAAC/oB,MAAM,CAACgmB,QAAQ,CAAC;QACtDgD,QAAQ,GAAG,IAAIjD,qBAAqB,CAAC+C,kBAAkB,EAAE9C,QAAQ,EAAE,IAAI,CAAC5P,MAAM,EAAE6S,iBAAiB,CAAC;QAClG;QACAF,KAAK,CAACptB,GAAG,CAACqqB,QAAQ,EAAEgD,QAAQ,CAAC;MACjC;MACA,OAAOA,QAAQ;IACnB;IACA,MAAMvH,WAAW,GAAGrrB,IAAI,CAAC+Z,EAAE;IAC3B,MAAMmC,WAAW,GAAGlc,IAAI,CAAC+Z,EAAE,GAAG,GAAG,GAAG,IAAI,CAACqY,UAAU;IACnD,IAAI,CAACA,UAAU,EAAE;IACjB,IAAI,CAACpS,MAAM,CAAClG,QAAQ,CAACoC,WAAW,EAAEc,WAAW,CAAC;IAC9C,MAAM2F,eAAe,GAAI5E,OAAO,IAAK;MACjC,IAAI3U,KAAK,CAACC,OAAO,CAAC0U,OAAO,CAAC,EAAE;QACxBA,OAAO,CAAClb,OAAO,CAAC8f,eAAe,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAAC3C,MAAM,CAAC2C,eAAe,CAAC0I,WAAW,EAAEnP,WAAW,EAAEc,WAAW,EAAEe,OAAO,CAAC3b,IAAI,EAAE2b,OAAO,CAAC;MAC7F;IACJ,CAAC;IACD,MAAM+U,iBAAiB,GAAG9yB,IAAI,CAAC0d,IAAI,CAAC,WAAW,CAAC;IAChDoV,iBAAiB,CAACjwB,OAAO,CAAC8f,eAAe,CAAC;IAC1C,OAAO,IAAI6O,iBAAiB,CAAC,IAAI,EAAEtV,WAAW,EAAE0T,QAAQ,EAAE,IAAI,CAAC5P,MAAM,CAAC;EAC1E;EACA+S,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACP,aAAa,EAAE;IACpB,IAAI,IAAI,CAAC5C,QAAQ,CAACmD,KAAK,EAAE;MACrB,IAAI,CAACnD,QAAQ,CAACmD,KAAK,CAAC,CAAC;IACzB;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjBhD,cAAc,CAAC,MAAM;MACjB,IAAI,CAACqC,YAAY,EAAE;IACvB,CAAC,CAAC;EACN;EACA;EACAR,wBAAwBA,CAACoB,KAAK,EAAEjb,EAAE,EAAE0F,IAAI,EAAE;IACtC,IAAIuV,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACZ,YAAY,EAAE;MACzC,IAAI,CAACF,KAAK,CAACe,GAAG,CAAC,MAAMlb,EAAE,CAAC0F,IAAI,CAAC,CAAC;MAC9B;IACJ;IACA,MAAMyV,wBAAwB,GAAG,IAAI,CAACb,yBAAyB;IAC/D,IAAIa,wBAAwB,CAAC7xB,MAAM,IAAI,CAAC,EAAE;MACtC0uB,cAAc,CAAC,MAAM;QACjB,IAAI,CAACmC,KAAK,CAACe,GAAG,CAAC,MAAM;UACjBC,wBAAwB,CAACtwB,OAAO,CAAEqH,KAAK,IAAK;YACxC,MAAM,CAAC8N,EAAE,EAAE0F,IAAI,CAAC,GAAGxT,KAAK;YACxB8N,EAAE,CAAC0F,IAAI,CAAC;UACZ,CAAC,CAAC;UACF,IAAI,CAAC4U,yBAAyB,GAAG,EAAE;QACvC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACAa,wBAAwB,CAAC5xB,IAAI,CAAC,CAACyW,EAAE,EAAE0F,IAAI,CAAC,CAAC;EAC7C;EACA0V,GAAGA,CAAA,EAAG;IACF,IAAI,CAACZ,aAAa,EAAE;IACpB;IACA;IACA,IAAI,IAAI,CAACA,aAAa,IAAI,CAAC,EAAE;MACzB,IAAI,CAACL,KAAK,CAACkB,iBAAiB,CAAC,MAAM;QAC/B,IAAI,CAACL,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAAChT,MAAM,CAAC+D,KAAK,CAAC,IAAI,CAACsO,YAAY,CAAC;MACxC,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACzC,QAAQ,CAACwD,GAAG,EAAE;MACnB,IAAI,CAACxD,QAAQ,CAACwD,GAAG,CAAC,CAAC;IACvB;EACJ;EACA3P,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzD,MAAM,CAACyD,iBAAiB,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI6P,iBAAiBA,CAACjI,WAAW,EAAE;IAC3B;IACA,IAAI,CAACrL,MAAM,CAAC+D,KAAK,CAAC,CAAC;IACnB,IAAI,CAAC6L,QAAQ,CAAC0D,iBAAiB,GAAGjI,WAAW,CAAC;EAClD;AACJ;AAEA,SAASprB,eAAe,EAAE1B,mBAAmB,EAAE6wB,SAAS,IAAImE,UAAU,EAAExI,eAAe,IAAIyI,gBAAgB,EAAEhC,iBAAiB,IAAIiC,kBAAkB,EAAEvB,wBAAwB,IAAIwB,yBAAyB,EAAEvzB,wBAAwB,IAAIwzB,yBAAyB,EAAEhE,qBAAqB,IAAIiE,sBAAsB,EAAEx3B,eAAe,IAAIy3B,gBAAgB,EAAE13B,eAAe,IAAI23B,gBAAgB,EAAE1zB,4BAA4B,IAAI2zB,6BAA6B,EAAE9V,yBAAyB,IAAI+V,0BAA0B,EAAEpF,mBAAmB,IAAIqF,oBAAoB,EAAElH,mBAAmB,IAAImH,oBAAoB,EAAEpzB,4BAA4B,IAAIqzB,6BAA6B,EAAEz2B,8BAA8B,IAAI02B,+BAA+B,EAAE52B,mBAAmB,IAAI62B,oBAAoB,EAAEj6B,eAAe,IAAIk6B,gBAAgB,EAAEnF,YAAY,IAAIoF,aAAa,EAAEl6B,gBAAgB,IAAIm6B,iBAAiB,EAAEl6B,WAAW,IAAIm6B,YAAY,EAAE92B,oBAAoB,IAAI+2B,mBAAmB,EAAEv6B,qBAAqB,IAAIw6B,sBAAsB,EAAEl3B,kCAAkC,IAAIm3B,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}