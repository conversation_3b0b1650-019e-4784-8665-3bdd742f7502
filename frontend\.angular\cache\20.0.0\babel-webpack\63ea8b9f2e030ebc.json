{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/two-factor.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/select\";\nfunction Disable2FADialogComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-icon\", 23);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"All your recovery codes have been used. You can request to disable 2FA via email verification.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Disable2FADialogComponent_mat_icon_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Disable2FADialogComponent_mat_icon_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"email\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class Disable2FADialogComponent {\n  constructor(formBuilder, twoFactorService, snackBar, dialogRef, data) {\n    this.formBuilder = formBuilder;\n    this.twoFactorService = twoFactorService;\n    this.snackBar = snackBar;\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.loading = false;\n    this.disableForm = this.formBuilder.group({\n      email: [{\n        value: data.email,\n        disabled: true\n      }, [Validators.required, Validators.email]],\n      reason: [data.allCodesUsed ? 'recovery_codes_exhausted' : 'lost_device', [Validators.required]]\n    });\n  }\n  onSubmit() {\n    if (this.disableForm.invalid) {\n      return;\n    }\n    this.loading = true;\n    const formValue = this.disableForm.getRawValue();\n    this.twoFactorService.requestDisable2FA(formValue.email, formValue.reason).subscribe({\n      next: response => {\n        this.snackBar.open('Disable confirmation email sent! Check your inbox and click the link to confirm.', 'Close', {\n          duration: 8000,\n          panelClass: ['success-snackbar']\n        });\n        this.dialogRef.close({\n          success: true,\n          response\n        });\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Failed to request 2FA disable:', error);\n        this.snackBar.open(error.error?.message || 'Failed to send disable email. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onCancel() {\n    this.dialogRef.close({\n      success: false\n    });\n  }\n  static #_ = this.ɵfac = function Disable2FADialogComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Disable2FADialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: Disable2FADialogComponent,\n    selectors: [[\"app-disable-2fa-dialog\"]],\n    standalone: false,\n    decls: 65,\n    vars: 7,\n    consts: [[1, \"disable-2fa-dialog\"], [\"mat-dialog-title\", \"\", 1, \"dialog-title\"], [1, \"warning-icon\"], [1, \"dialog-content\"], [1, \"security-warning\"], [1, \"security-icon\"], [1, \"warning-text\"], [\"class\", \"status-info\", 4, \"ngIf\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"readonly\", \"\"], [\"matSuffix\", \"\"], [\"formControlName\", \"reason\", \"required\", \"\"], [\"value\", \"recovery_codes_exhausted\"], [\"value\", \"lost_device\"], [\"value\", \"other\"], [1, \"alternatives-section\"], [1, \"process-info\"], [1, \"dialog-actions\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"status-info\"], [1, \"info-icon\"]],\n    template: function Disable2FADialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" Disable Two-Factor Authentication \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-dialog-content\", 3)(6, \"div\", 4)(7, \"mat-icon\", 5);\n        i0.ɵɵtext(8, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"h3\");\n        i0.ɵɵtext(11, \"Security Warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"Disabling 2FA will make your account less secure. Are you sure you want to proceed?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(14, Disable2FADialogComponent_div_14_Template, 5, 0, \"div\", 7);\n        i0.ɵɵelementStart(15, \"form\", 8);\n        i0.ɵɵlistener(\"ngSubmit\", function Disable2FADialogComponent_Template_form_ngSubmit_15_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(16, \"mat-form-field\", 9)(17, \"mat-label\");\n        i0.ɵɵtext(18, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"input\", 10);\n        i0.ɵɵelementStart(20, \"mat-icon\", 11);\n        i0.ɵɵtext(21, \"email\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"mat-form-field\", 9)(23, \"mat-label\");\n        i0.ɵɵtext(24, \"Reason for Disabling\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"mat-select\", 12)(26, \"mat-option\", 13);\n        i0.ɵɵtext(27, \"All recovery codes used\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"mat-option\", 14);\n        i0.ɵɵtext(29, \"Lost access to authenticator device\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"mat-option\", 15);\n        i0.ɵɵtext(31, \"Other reason\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"mat-icon\", 11);\n        i0.ɵɵtext(33, \"help_outline\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 16)(35, \"h4\")(36, \"mat-icon\");\n        i0.ɵɵtext(37, \"lightbulb\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(38, \" Consider These Alternatives:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"ul\")(40, \"li\");\n        i0.ɵɵtext(41, \"Generate new recovery codes if you still have access to your authenticator\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"li\");\n        i0.ɵɵtext(43, \"Set up a new authenticator app on a different device\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"li\");\n        i0.ɵɵtext(45, \"Use email-based 2FA verification instead\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(46, \"div\", 17)(47, \"h4\")(48, \"mat-icon\");\n        i0.ɵɵtext(49, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(50, \" Email Verification Process:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"ol\")(52, \"li\");\n        i0.ɵɵtext(53, \"We'll send a secure link to your email address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"li\");\n        i0.ɵɵtext(55, \"Click the link to confirm disabling 2FA\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"li\");\n        i0.ɵɵtext(57, \"The link expires in 1 hour for security\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(58, \"mat-dialog-actions\", 18)(59, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function Disable2FADialogComponent_Template_button_click_59_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵtext(60, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function Disable2FADialogComponent_Template_button_click_61_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(62, Disable2FADialogComponent_mat_icon_62_Template, 2, 0, \"mat-icon\", 21)(63, Disable2FADialogComponent_mat_icon_63_Template, 2, 0, \"mat-icon\", 21);\n        i0.ɵɵtext(64);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngIf\", ctx.data.allCodesUsed);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.disableForm);\n        i0.ɵɵadvance(44);\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.disableForm.invalid || ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Sending...\" : \"Send Disable Email\", \" \");\n      }\n    },\n    dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatFormField, i6.MatLabel, i6.MatSuffix, i7.MatInput, i8.MatButton, i9.MatIcon, i4.MatDialogTitle, i4.MatDialogActions, i4.MatDialogContent, i10.MatSelect, i10.MatOption],\n    styles: [\".disable-2fa-dialog[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  width: 100%;\\n}\\n\\n.dialog-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  color: #f57c00;\\n  margin-bottom: 0;\\n}\\n\\n.warning-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.dialog-content[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\n.security-warning[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  background: #fff3e0;\\n  border: 1px solid #ffcc02;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n\\n.security-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  flex-shrink: 0;\\n}\\n\\n.warning-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #f57c00;\\n  font-size: 16px;\\n}\\n\\n.warning-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #e65100;\\n}\\n\\n.status-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: #e3f2fd;\\n  border: 1px solid #2196f3;\\n  border-radius: 8px;\\n  padding: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.info-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n.alternatives-section[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin: 16px 0;\\n}\\n\\n.alternatives-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 12px 0;\\n  color: #1976d2;\\n  font-size: 14px;\\n}\\n\\n.alternatives-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.alternatives-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #424242;\\n}\\n\\n.process-info[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.process-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #424242;\\n}\\n\\n.dialog-actions[_ngcontent-%COMP%] {\\n  padding: 16px 0 0 0;\\n  justify-content: flex-end;\\n}\\n\\n.dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "MAT_DIALOG_DATA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "Disable2FADialogComponent", "constructor", "formBuilder", "twoFactorService", "snackBar", "dialogRef", "data", "loading", "disableForm", "group", "email", "value", "disabled", "required", "reason", "allCodesUsed", "onSubmit", "invalid", "formValue", "getRawValue", "requestDisable2FA", "subscribe", "next", "response", "open", "duration", "panelClass", "close", "success", "error", "console", "message", "onCancel", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "TwoFactorService", "i3", "MatSnackBar", "i4", "MatDialogRef", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "Disable2FADialogComponent_Template", "rf", "ctx", "ɵɵtemplate", "Disable2FADialogComponent_div_14_Template", "ɵɵlistener", "Disable2FADialogComponent_Template_form_ngSubmit_15_listener", "ɵɵelement", "Disable2FADialogComponent_Template_button_click_59_listener", "Disable2FADialogComponent_Template_button_click_61_listener", "Disable2FADialogComponent_mat_icon_62_Template", "Disable2FADialogComponent_mat_icon_63_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\disable-2fa-dialog\\disable-2fa-dialog.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\disable-2fa-dialog\\disable-2fa-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { TwoFactorService } from '../../../services/two-factor.service';\r\n\r\nexport interface DisableDialogData {\r\n  email: string;\r\n  allCodesUsed?: boolean;\r\n  source: 'recovery' | 'login' | 'profile';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-disable-2fa-dialog',\r\n  templateUrl: './disable-2fa-dialog.component.html',\r\n  styleUrls: ['./disable-2fa-dialog.component.scss'],\r\n  standalone: false\r\n})\r\nexport class Disable2FADialogComponent {\r\n  disableForm: FormGroup;\r\n  loading = false;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private twoFactorService: TwoFactorService,\r\n    private snackBar: MatSnackBar,\r\n    public dialogRef: MatDialogRef<Disable2FADialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DisableDialogData\r\n  ) {\r\n    this.disableForm = this.formBuilder.group({\r\n      email: [{ value: data.email, disabled: true }, [Validators.required, Validators.email]],\r\n      reason: [\r\n        data.allCodesUsed ? 'recovery_codes_exhausted' : 'lost_device', \r\n        [Validators.required]\r\n      ]\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.disableForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const formValue = this.disableForm.getRawValue();\r\n\r\n    this.twoFactorService.requestDisable2FA(formValue.email, formValue.reason).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open(\r\n          'Disable confirmation email sent! Check your inbox and click the link to confirm.',\r\n          'Close',\r\n          { duration: 8000, panelClass: ['success-snackbar'] }\r\n        );\r\n        this.dialogRef.close({ success: true, response });\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to request 2FA disable:', error);\r\n        this.snackBar.open(\r\n          error.error?.message || 'Failed to send disable email. Please try again.',\r\n          'Close',\r\n          { duration: 5000, panelClass: ['error-snackbar'] }\r\n        );\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.dialogRef.close({ success: false });\r\n  }\r\n}\r\n", "<div class=\"disable-2fa-dialog\">\r\n  <h2 mat-dialog-title class=\"dialog-title\">\r\n    <mat-icon class=\"warning-icon\">warning</mat-icon>\r\n    Disable Two-Factor Authentication\r\n  </h2>\r\n\r\n  <mat-dialog-content class=\"dialog-content\">\r\n    <!-- Security Warning -->\r\n    <div class=\"security-warning\">\r\n      <mat-icon class=\"security-icon\">security</mat-icon>\r\n      <div class=\"warning-text\">\r\n        <h3>Security Warning</h3>\r\n        <p>Disabling 2FA will make your account less secure. Are you sure you want to proceed?</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Current Status -->\r\n    <div class=\"status-info\" *ngIf=\"data.allCodesUsed\">\r\n      <mat-icon class=\"info-icon\">info</mat-icon>\r\n      <p>All your recovery codes have been used. You can request to disable 2FA via email verification.</p>\r\n    </div>\r\n\r\n    <!-- Form -->\r\n    <form [formGroup]=\"disableForm\" (ngSubmit)=\"onSubmit()\">\r\n      <!-- Email (readonly) -->\r\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Email Address</mat-label>\r\n        <input matInput formControlName=\"email\" readonly>\r\n        <mat-icon matSuffix>email</mat-icon>\r\n      </mat-form-field>\r\n\r\n      <!-- Reason Selection -->\r\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Reason for Disabling</mat-label>\r\n        <mat-select formControlName=\"reason\" required>\r\n          <mat-option value=\"recovery_codes_exhausted\">All recovery codes used</mat-option>\r\n          <mat-option value=\"lost_device\">Lost access to authenticator device</mat-option>\r\n          <mat-option value=\"other\">Other reason</mat-option>\r\n        </mat-select>\r\n        <mat-icon matSuffix>help_outline</mat-icon>\r\n      </mat-form-field>\r\n\r\n      <!-- Alternative Options -->\r\n      <div class=\"alternatives-section\">\r\n        <h4><mat-icon>lightbulb</mat-icon> Consider These Alternatives:</h4>\r\n        <ul>\r\n          <li>Generate new recovery codes if you still have access to your authenticator</li>\r\n          <li>Set up a new authenticator app on a different device</li>\r\n          <li>Use email-based 2FA verification instead</li>\r\n        </ul>\r\n      </div>\r\n\r\n      <!-- Process Info -->\r\n      <div class=\"process-info\">\r\n        <h4><mat-icon>email</mat-icon> Email Verification Process:</h4>\r\n        <ol>\r\n          <li>We'll send a secure link to your email address</li>\r\n          <li>Click the link to confirm disabling 2FA</li>\r\n          <li>The link expires in 1 hour for security</li>\r\n        </ol>\r\n      </div>\r\n    </form>\r\n  </mat-dialog-content>\r\n\r\n  <mat-dialog-actions class=\"dialog-actions\">\r\n    <button mat-button (click)=\"onCancel()\" [disabled]=\"loading\">\r\n      Cancel\r\n    </button>\r\n    <button \r\n      mat-raised-button \r\n      color=\"warn\" \r\n      (click)=\"onSubmit()\" \r\n      [disabled]=\"disableForm.invalid || loading\">\r\n      <mat-icon *ngIf=\"loading\">hourglass_empty</mat-icon>\r\n      <mat-icon *ngIf=\"!loading\">email</mat-icon>\r\n      {{ loading ? 'Sending...' : 'Send Disable Email' }}\r\n    </button>\r\n  </mat-dialog-actions>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAAuBC,eAAe,QAAQ,0BAA0B;;;;;;;;;;;;;;ICgBlEC,EADF,CAAAC,cAAA,cAAmD,mBACrB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qGAA8F;IACnGF,EADmG,CAAAG,YAAA,EAAI,EACjG;;;;;IAqDJH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACpDH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;ADxDjD,OAAM,MAAOC,yBAAyB;EAIpCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,QAAqB,EACtBC,SAAkD,EACzBC,IAAuB;IAJ/C,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAPtC,KAAAC,OAAO,GAAG,KAAK;IASb,IAAI,CAACC,WAAW,GAAG,IAAI,CAACN,WAAW,CAACO,KAAK,CAAC;MACxCC,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAEL,IAAI,CAACI,KAAK;QAAEE,QAAQ,EAAE;MAAI,CAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACgB,KAAK,CAAC,CAAC;MACvFI,MAAM,EAAE,CACNR,IAAI,CAACS,YAAY,GAAG,0BAA0B,GAAG,aAAa,EAC9D,CAACrB,UAAU,CAACmB,QAAQ,CAAC;KAExB,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACR,WAAW,CAACS,OAAO,EAAE;MAC5B;IACF;IAEA,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,MAAMW,SAAS,GAAG,IAAI,CAACV,WAAW,CAACW,WAAW,EAAE;IAEhD,IAAI,CAAChB,gBAAgB,CAACiB,iBAAiB,CAACF,SAAS,CAACR,KAAK,EAAEQ,SAAS,CAACJ,MAAM,CAAC,CAACO,SAAS,CAAC;MACnFC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAChB,kFAAkF,EAClF,OAAO,EACP;UAAEC,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,kBAAkB;QAAC,CAAE,CACrD;QACD,IAAI,CAACrB,SAAS,CAACsB,KAAK,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAEL;QAAQ,CAAE,CAAC;QACjD,IAAI,CAAChB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACzB,QAAQ,CAACoB,IAAI,CAChBK,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAI,iDAAiD,EACzE,OAAO,EACP;UAAEN,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,gBAAgB;QAAC,CAAE,CACnD;QACD,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAyB,QAAQA,CAAA;IACN,IAAI,CAAC3B,SAAS,CAACsB,KAAK,CAAC;MAAEC,OAAO,EAAE;IAAK,CAAE,CAAC;EAC1C;EAAC,QAAAK,CAAA,G;qCApDUjC,yBAAyB,EAAAJ,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1C,EAAA,CAAAsC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAsC,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA9C,EAAA,CAAAsC,iBAAA,CAS1BvC,eAAe;EAAA;EAAA,QAAAgD,EAAA,G;UATd3C,yBAAyB;IAAA4C,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBlCvD,EAFJ,CAAAC,cAAA,aAAgC,YACY,kBACT;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACjDH,EAAA,CAAAE,MAAA,0CACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAKDH,EAHJ,CAAAC,cAAA,4BAA2C,aAEX,kBACI;QAAAD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEjDH,EADF,CAAAC,cAAA,aAA0B,UACpB;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACzBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,2FAAmF;QAE1FF,EAF0F,CAAAG,YAAA,EAAI,EACtF,EACF;QAGNH,EAAA,CAAAyD,UAAA,KAAAC,yCAAA,iBAAmD;QAMnD1D,EAAA,CAAAC,cAAA,eAAwD;QAAxBD,EAAA,CAAA2D,UAAA,sBAAAC,6DAAA;UAAA,OAAYJ,GAAA,CAAApC,QAAA,EAAU;QAAA,EAAC;QAGnDpB,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAA6D,SAAA,iBAAiD;QACjD7D,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAC3BF,EAD2B,CAAAG,YAAA,EAAW,EACrB;QAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;QAAAD,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAEzCH,EADF,CAAAC,cAAA,sBAA8C,sBACC;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACjFH,EAAA,CAAAC,cAAA,sBAAgC;QAAAD,EAAA,CAAAE,MAAA,2CAAmC;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChFH,EAAA,CAAAC,cAAA,sBAA0B;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QACxCF,EADwC,CAAAG,YAAA,EAAa,EACxC;QACbH,EAAA,CAAAC,cAAA,oBAAoB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAClCF,EADkC,CAAAG,YAAA,EAAW,EAC5B;QAIXH,EADN,CAAAC,cAAA,eAAkC,UAC5B,gBAAU;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,qCAA4B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAElEH,EADF,CAAAC,cAAA,UAAI,UACE;QAAAD,EAAA,CAAAE,MAAA,kFAA0E;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnFH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,4DAAoD;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7DH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,gDAAwC;QAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACD;QAIAH,EADN,CAAAC,cAAA,eAA0B,UACpB,gBAAU;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAACH,EAAA,CAAAE,MAAA,oCAA2B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE7DH,EADF,CAAAC,cAAA,UAAI,UACE;QAAAD,EAAA,CAAAE,MAAA,sDAA8C;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvDH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,+CAAuC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChDH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,+CAAuC;QAInDF,EAJmD,CAAAG,YAAA,EAAK,EAC7C,EACD,EACD,EACY;QAGnBH,EADF,CAAAC,cAAA,8BAA2C,kBACoB;QAA1CD,EAAA,CAAA2D,UAAA,mBAAAG,4DAAA;UAAA,OAASN,GAAA,CAAApB,QAAA,EAAU;QAAA,EAAC;QACrCpC,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAI8C;QAD5CD,EAAA,CAAA2D,UAAA,mBAAAI,4DAAA;UAAA,OAASP,GAAA,CAAApC,QAAA,EAAU;QAAA,EAAC;QAGpBpB,EADA,CAAAyD,UAAA,KAAAO,8CAAA,uBAA0B,KAAAC,8CAAA,uBACC;QAC3BjE,EAAA,CAAAE,MAAA,IACF;QAEJF,EAFI,CAAAG,YAAA,EAAS,EACU,EACjB;;;QA7DwBH,EAAA,CAAAkE,SAAA,IAAuB;QAAvBlE,EAAA,CAAAmE,UAAA,SAAAX,GAAA,CAAA9C,IAAA,CAAAS,YAAA,CAAuB;QAM3CnB,EAAA,CAAAkE,SAAA,EAAyB;QAAzBlE,EAAA,CAAAmE,UAAA,cAAAX,GAAA,CAAA5C,WAAA,CAAyB;QA0CSZ,EAAA,CAAAkE,SAAA,IAAoB;QAApBlE,EAAA,CAAAmE,UAAA,aAAAX,GAAA,CAAA7C,OAAA,CAAoB;QAO1DX,EAAA,CAAAkE,SAAA,GAA2C;QAA3ClE,EAAA,CAAAmE,UAAA,aAAAX,GAAA,CAAA5C,WAAA,CAAAS,OAAA,IAAAmC,GAAA,CAAA7C,OAAA,CAA2C;QAChCX,EAAA,CAAAkE,SAAA,EAAa;QAAblE,EAAA,CAAAmE,UAAA,SAAAX,GAAA,CAAA7C,OAAA,CAAa;QACbX,EAAA,CAAAkE,SAAA,EAAc;QAAdlE,EAAA,CAAAmE,UAAA,UAAAX,GAAA,CAAA7C,OAAA,CAAc;QACzBX,EAAA,CAAAkE,SAAA,EACF;QADElE,EAAA,CAAAoE,kBAAA,MAAAZ,GAAA,CAAA7C,OAAA,4CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}