#!/usr/bin/env python3
"""
Comprehensive CORS Test for All Endpoints
Tests that all endpoints return proper CORS headers
"""

import requests
import json
import time

BASE_URL = "http://localhost:3002"
ORIGIN = "http://localhost:4200"

def test_cors_headers(method, endpoint, data=None, headers=None):
    """Test if an endpoint returns proper CORS headers"""
    
    if headers is None:
        headers = {'Origin': ORIGIN}
    else:
        headers.update({'Origin': ORIGIN})
    
    try:
        if method.upper() == 'GET':
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(f"{BASE_URL}{endpoint}", json=data, headers=headers, timeout=10)
        elif method.upper() == 'OPTIONS':
            response = requests.options(f"{BASE_URL}{endpoint}", headers=headers, timeout=10)
        elif method.upper() == 'DELETE':
            response = requests.delete(f"{BASE_URL}{endpoint}", headers=headers, timeout=10)
        elif method.upper() == 'PATCH':
            response = requests.patch(f"{BASE_URL}{endpoint}", json=data, headers=headers, timeout=10)
        else:
            return False, f"Unsupported method: {method}"
        
        # Check for CORS headers
        cors_origin = response.headers.get('Access-Control-Allow-Origin')
        cors_methods = response.headers.get('Access-Control-Allow-Methods')
        cors_headers = response.headers.get('Access-Control-Allow-Headers')
        cors_credentials = response.headers.get('Access-Control-Allow-Credentials')
        
        has_cors = bool(cors_origin)
        
        return has_cors, {
            'status': response.status_code,
            'cors_origin': cors_origin,
            'cors_methods': cors_methods,
            'cors_headers': cors_headers,
            'cors_credentials': cors_credentials,
            'has_cors': has_cors
        }
        
    except Exception as e:
        return False, f"Error: {str(e)}"

def main():
    """Test CORS on all major endpoints"""
    
    print("🧪 Testing CORS Headers on All Endpoints...")
    print("=" * 60)
    
    # Test endpoints
    endpoints_to_test = [
        # Authentication endpoints
        ('POST', '/api/auth/signup', {'firstName': 'Test', 'lastName': 'User', 'email': '<EMAIL>', 'password': 'TestPass123!', 'password_confirmation': 'TestPass123!'}),
        ('POST', '/api/auth/login', {'email': '<EMAIL>', 'password': 'TestPass123!'}),
        ('GET', '/api/auth/profile'),
        ('GET', '/api/auth/me'),
        
        # 2FA endpoints
        ('POST', '/api/2fa/setup'),
        ('GET', '/api/2fa/status'),
        
        # OAuth endpoints
        ('GET', '/api/auth/oauth/providers'),
        ('GET', '/api/auth/oauth/google/url'),
        
        # Payment endpoints
        ('GET', '/api/payments/test'),
        ('POST', '/api/payments/create-order', {'amount': 100, 'currency': 'INR'}),
        ('GET', '/api/payments/my-payments'),
        
        # OTP endpoints
        ('POST', '/api/otp/send', {'email': '<EMAIL>'}),
        ('GET', '/api/otp/status'),
        
        # Security endpoints
        ('GET', '/api/security/dashboard'),
        ('GET', '/api/security/events'),
        
        # Account endpoints
        ('POST', '/api/account/request-deletion'),
        ('GET', '/api/account/deletion-status'),
        
        # Notification endpoints
        ('GET', '/api/notifications'),
        
        # Queue endpoints
        ('GET', '/api/queue/status'),
        ('GET', '/api/queue/stats'),
    ]
    
    results = []
    total_tests = len(endpoints_to_test)
    passed_tests = 0
    
    for method, endpoint, *data in endpoints_to_test:
        test_data = data[0] if data else None
        
        print(f"🔍 Testing {method} {endpoint}")
        
        has_cors, result = test_cors_headers(method, endpoint, test_data)
        
        if has_cors:
            print(f"   ✅ CORS headers present")
            passed_tests += 1
        else:
            print(f"   ❌ CORS headers missing")
            print(f"   📄 Details: {result}")
        
        results.append({
            'method': method,
            'endpoint': endpoint,
            'has_cors': has_cors,
            'result': result
        })
        
        time.sleep(0.5)  # Small delay between requests
    
    print("=" * 60)
    print(f"📊 CORS Test Summary:")
    print(f"   Total Endpoints: {total_tests}")
    print(f"   ✅ With CORS: {passed_tests}")
    print(f"   ❌ Without CORS: {total_tests - passed_tests}")
    print(f"   📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 ALL ENDPOINTS HAVE CORS HEADERS!")
    else:
        print("⚠️  Some endpoints are missing CORS headers")
    
    # Save detailed results
    with open('cors_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"📄 Detailed results saved to: cors_test_results.json")

if __name__ == "__main__":
    main()
