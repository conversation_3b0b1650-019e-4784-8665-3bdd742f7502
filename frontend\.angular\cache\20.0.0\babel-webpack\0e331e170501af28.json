{"ast": null, "code": "import { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { createBlockScrollStrategy, createGlobalPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, inject, Injector, Injectable, ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nfunction MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nclass MatDialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Custom class for the overlay pane. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Custom class for the backdrop. */\n  backdropClass = '';\n  /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n  disableClose = false;\n  /** Function used to determine whether the dialog is allowed to close. */\n  closePredicate;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Position overrides. */\n  position;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Aria label to assign to the dialog element. */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the\n   * previously-focused element, after it's closed.\n   */\n  restoreFocus = true;\n  /** Whether to wait for the opening animation to finish before trapping focus. */\n  delayFocusTrap = true;\n  /** Scroll strategy to be used for the dialog. */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  closeOnNavigation = true;\n  /**\n   * Duration of the enter animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  enterAnimationDuration;\n  /**\n   * Duration of the exit animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  exitAnimationDuration;\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n  /** Emits when an animation state changes. */\n  _animationStateChanged = new EventEmitter();\n  /** Whether animations are enabled. */\n  _animationsEnabled = !_animationsDisabled();\n  /** Number of actions projected in the dialog. */\n  _actionSectionCount = 0;\n  /** Host element of the dialog container component. */\n  _hostElement = this._elementRef.nativeElement;\n  /** Duration of the dialog open animation. */\n  _enterAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION : 0;\n  /** Duration of the dialog close animation. */\n  _exitAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION : 0;\n  /** Current timer for dialog animations. */\n  _animationTimer = null;\n  _contentAttached() {\n    // Delegate to the original dialog-container initialization (i.e. saving the\n    // previous element, setting up the focus trap and moving focus to the container).\n    super._contentAttached();\n    // Note: Usually we would be able to use the MDC dialog foundation here to handle\n    // the dialog animation for us, but there are a few reasons why we just leverage\n    // their styles and not use the runtime foundation code:\n    //   1. Foundation does not allow us to disable animations.\n    //   2. Foundation contains unnecessary features we don't need and aren't\n    //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n    this._startOpenAnimation();\n  }\n  /** Starts the dialog open animation if enabled. */\n  _startOpenAnimation() {\n    this._animationStateChanged.emit({\n      state: 'opening',\n      totalTime: this._enterAnimationDuration\n    });\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      // One would expect that the open class is added once the animation finished, but MDC\n      // uses the open class in combination with the opening class to start the animation.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n      this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n    } else {\n      this._hostElement.classList.add(OPEN_CLASS);\n      // Note: We could immediately finish the dialog opening here with noop animations,\n      // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n      // Executing this immediately would mean that `afterOpened` emits synchronously\n      // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n      Promise.resolve().then(() => this._finishDialogOpen());\n    }\n  }\n  /**\n   * Starts the exit animation of the dialog if enabled. This method is\n   * called by the dialog ref.\n   */\n  _startExitAnimation() {\n    this._animationStateChanged.emit({\n      state: 'closing',\n      totalTime: this._exitAnimationDuration\n    });\n    this._hostElement.classList.remove(OPEN_CLASS);\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n      this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n    } else {\n      // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n      // set up before any user can subscribe to the backdrop click. The subscription triggers\n      // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n      // animation state event if animations are disabled, the overlay would be disposed\n      // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n      // skipped. We work around this by waiting with the dialog close until the next tick when\n      // all subscriptions have been fired as expected. This is not an ideal solution, but\n      // there doesn't seem to be any other good way. Alternatives that have been considered:\n      //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n      //      Also this issue is specific to the MDC implementation where the dialog could\n      //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n      //      and closing always takes at least a tick.\n      //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n      //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n      //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n      // Based on the fact that this is specific to the MDC-based implementation of the dialog\n      // animations, the defer is applied here.\n      Promise.resolve().then(() => this._finishDialogClose());\n    }\n  }\n  /**\n   * Updates the number action sections.\n   * @param delta Increase/decrease in the number of sections.\n   */\n  _updateActionSectionCount(delta) {\n    this._actionSectionCount += delta;\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Completes the dialog open by clearing potential animation classes, trapping\n   * focus and emitting an opened event.\n   */\n  _finishDialogOpen = () => {\n    this._clearAnimationClasses();\n    this._openAnimationDone(this._enterAnimationDuration);\n  };\n  /**\n   * Completes the dialog close by clearing potential animation classes, restoring\n   * focus and emitting a closed event.\n   */\n  _finishDialogClose = () => {\n    this._clearAnimationClasses();\n    this._animationStateChanged.emit({\n      state: 'closed',\n      totalTime: this._exitAnimationDuration\n    });\n  };\n  /** Clears all dialog animation classes. */\n  _clearAnimationClasses() {\n    this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n  }\n  _waitForAnimationToComplete(duration, callback) {\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n    // Note that we want this timer to run inside the NgZone, because we want\n    // the related events like `afterClosed` to be inside the zone as well.\n    this._animationTimer = setTimeout(callback, duration);\n  }\n  /** Runs a callback in `requestAnimationFrame`, if available. */\n  _requestAnimationFrame(callback) {\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(callback);\n      } else {\n        callback();\n      }\n    });\n  }\n  _captureInitialFocus() {\n    if (!this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Callback for when the open dialog animation has finished. Intended to\n   * be called by sub-classes that use different animation implementations.\n   */\n  _openAnimationDone(totalTime) {\n    if (this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n    this._animationStateChanged.next({\n      state: 'opened',\n      totalTime\n    });\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n  }\n  attachComponentPortal(portal) {\n    // When a component is passed into the dialog, the host element interrupts\n    // the `display:flex` from affecting the dialog title, content, and\n    // actions. To fix this, we make the component host `display: contents` by\n    // marking its host with the `mat-mdc-dialog-component-host` class.\n    //\n    // Note that this problem does not exist when a template ref is used since\n    // the title, contents, and actions are then nested directly under the\n    // dialog surface.\n    const ref = super.attachComponentPortal(portal);\n    ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n    return ref;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogContainer_BaseFactory;\n    return function MatDialogContainer_Factory(__ngFactoryType__) {\n      return (ɵMatDialogContainer_BaseFactory || (ɵMatDialogContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogContainer)))(__ngFactoryType__ || MatDialogContainer);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDialogContainer,\n    selectors: [[\"mat-dialog-container\"]],\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n    hostVars: 10,\n    hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx._config.id);\n        i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled)(\"mat-mdc-dialog-container-with-actions\", ctx._actionSectionCount > 0);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"mat-mdc-dialog-inner-container\", \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mat-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mat-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mat-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mat-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mat-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mat-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mat-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mat-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mat-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mat-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mat-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mat-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mat-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;box-sizing:border-box;min-height:52px;margin:0;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mat-mdc-dialog-container mdc-dialog',\n        'tabindex': '-1',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[id]': '_config.id',\n        '[attr.role]': '_config.role',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n        '[class._mat-animation-noopable]': '!_animationsEnabled',\n        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0'\n      },\n      template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mat-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mat-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mat-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mat-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mat-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mat-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mat-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mat-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mat-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mat-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mat-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mat-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mat-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;box-sizing:border-box;min-height:52px;margin:0;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\nvar MatDialogState;\n(function (MatDialogState) {\n  MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n  MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n  MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  _ref;\n  _config;\n  _containerInstance;\n  /** The instance of component opened into the dialog. */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subject for notifying the user that the dialog has finished opening. */\n  _afterOpened = new Subject();\n  /** Subject for notifying the user that the dialog has started closing. */\n  _beforeClosed = new Subject();\n  /** Result to be passed to afterClosed. */\n  _result;\n  /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n  _closeFallbackTimeout;\n  /** Current state of the dialog. */\n  _state = MatDialogState.OPEN;\n  // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n  // already has a second `options` parameter that we can use. The problem is that internal tests\n  // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n  // because it'll be called with two arguments by things like `MatDialogClose`.\n  /** Interaction that caused the dialog to close. */\n  _closeInteractionType;\n  constructor(_ref, _config, _containerInstance) {\n    this._ref = _ref;\n    this._config = _config;\n    this._containerInstance = _containerInstance;\n    this.disableClose = _config.disableClose;\n    this.id = _ref.id;\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    const closePredicate = this._config.closePredicate;\n    if (closePredicate && !closePredicate(dialogResult, this._config, this.componentInstance)) {\n      return;\n    }\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createBlockScrollStrategy(injector);\n  }\n});\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n  _defaultOptions = inject(MAT_DIALOG_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _scrollStrategy = inject(MAT_DIALOG_SCROLL_STRATEGY);\n  _parentDialog = inject(MatDialog, {\n    optional: true,\n    skipSelf: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  _injector = inject(Injector);\n  _dialog = inject(Dialog);\n  _animationsDisabled = _animationsDisabled();\n  _openDialogsAtThisLevel = [];\n  _afterAllClosedAtThisLevel = new Subject();\n  _afterOpenedAtThisLevel = new Subject();\n  dialogConfigClass = MatDialogConfig;\n  _dialogRefConstructor;\n  _dialogContainerType;\n  _dialogDataToken;\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n  constructor() {\n    this._dialogRefConstructor = MatDialogRef;\n    this._dialogContainerType = MatDialogContainer;\n    this._dialogDataToken = MAT_DIALOG_DATA;\n  }\n  open(componentOrTemplateRef, config) {\n    let dialogRef;\n    config = {\n      ...(this._defaultOptions || new MatDialogConfig()),\n      ...config\n    };\n    config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n    config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n    const cdkRef = this._dialog.open(componentOrTemplateRef, {\n      ...config,\n      positionStrategy: createGlobalPositionStrategy(this._injector).centerHorizontally().centerVertically(),\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Closing is tied to our animation so the close predicate has to be implemented separately.\n      closePredicate: undefined,\n      // Disable closing on destroy, because this service cleans up its open dialogs as well.\n      // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n      // the dialogs immediately whereas we want it to wait for the animations to finish.\n      closeOnDestroy: false,\n      // Disable closing on detachments so that we can sync up the animation.\n      // The Material dialog ref handles this manually.\n      closeOnOverlayDetachments: false,\n      disableAnimations: this._animationsDisabled || config.enterAnimationDuration?.toLocaleString() === '0' || config.exitAnimationDuration?.toString() === '0',\n      container: {\n        type: this._dialogContainerType,\n        providers: () => [\n        // Provide our config as the CDK config as well since it has the same interface as the\n        // CDK one, but it contains the actual values passed in by the user for things like\n        // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n        {\n          provide: this.dialogConfigClass,\n          useValue: config\n        }, {\n          provide: DialogConfig,\n          useValue: config\n        }]\n      },\n      templateContext: () => ({\n        dialogRef\n      }),\n      providers: (ref, cdkConfig, dialogContainer) => {\n        dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n        dialogRef.updatePosition(config?.position);\n        return [{\n          provide: this._dialogContainerType,\n          useValue: dialogContainer\n        }, {\n          provide: this._dialogDataToken,\n          useValue: cdkConfig.data\n        }, {\n          provide: this._dialogRefConstructor,\n          useValue: dialogRef\n        }];\n      }\n    });\n    // This can't be assigned in the `providers` callback, because\n    // the instance hasn't been assigned to the CDK ref yet.\n    dialogRef.componentRef = cdkRef.componentRef;\n    dialogRef.componentInstance = cdkRef.componentInstance;\n    this.openDialogs.push(dialogRef);\n    this.afterOpened.next(dialogRef);\n    dialogRef.afterClosed().subscribe(() => {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        if (!this.openDialogs.length) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    });\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    this._closeDialogs(this.openDialogs);\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Only close the dialogs at this level on destroy\n    // since the parent service may still be active.\n    this._closeDialogs(this._openDialogsAtThisLevel);\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n  }\n  _closeDialogs(dialogs) {\n    let i = dialogs.length;\n    while (i--) {\n      dialogs[i].close();\n    }\n  }\n  static ɵfac = function MatDialog_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialog)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatDialog,\n    factory: MatDialog.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n  dialogRef = inject(MatDialogRef, {\n    optional: true\n  });\n  _elementRef = inject(ElementRef);\n  _dialog = inject(MatDialog);\n  /** Screen-reader label for the button. */\n  ariaLabel;\n  /** Default to \"button\" to prevents accidental form submits. */\n  type = 'button';\n  /** Dialog close input. */\n  dialogResult;\n  _matDialogClose;\n  constructor() {}\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n  }\n  ngOnChanges(changes) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n  _onButtonClick(event) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n  }\n  static ɵfac = function MatDialogClose_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogClose)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogClose,\n    selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n    hostVars: 2,\n    hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n          return ctx._onButtonClick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      type: \"type\",\n      dialogResult: [0, \"mat-dialog-close\", \"dialogResult\"],\n      _matDialogClose: [0, \"matDialogClose\", \"_matDialogClose\"]\n    },\n    exportAs: [\"matDialogClose\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogClose, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-close], [matDialogClose]',\n      exportAs: 'matDialogClose',\n      host: {\n        '(click)': '_onButtonClick($event)',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.type]': 'type'\n      }\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    type: [{\n      type: Input\n    }],\n    dialogResult: [{\n      type: Input,\n      args: ['mat-dialog-close']\n    }],\n    _matDialogClose: [{\n      type: Input,\n      args: ['matDialogClose']\n    }]\n  });\n})();\nclass MatDialogLayoutSection {\n  _dialogRef = inject(MatDialogRef, {\n    optional: true\n  });\n  _elementRef = inject(ElementRef);\n  _dialog = inject(MatDialog);\n  constructor() {}\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        this._onAdd();\n      });\n    }\n  }\n  ngOnDestroy() {\n    // Note: we null check because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    const instance = this._dialogRef?._containerInstance;\n    if (instance) {\n      Promise.resolve().then(() => {\n        this._onRemove();\n      });\n    }\n  }\n  static ɵfac = function MatDialogLayoutSection_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogLayoutSection)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogLayoutSection\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogLayoutSection, [{\n    type: Directive\n  }], () => [], null);\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n  id = inject(_IdGenerator).getId('mat-mdc-dialog-title-');\n  _onAdd() {\n    // Note: we null check the queue, because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n  }\n  _onRemove() {\n    this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogTitle_BaseFactory;\n    return function MatDialogTitle_Factory(__ngFactoryType__) {\n      return (ɵMatDialogTitle_BaseFactory || (ɵMatDialogTitle_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogTitle)))(__ngFactoryType__ || MatDialogTitle);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogTitle,\n    selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n    hostVars: 1,\n    hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matDialogTitle\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-title], [matDialogTitle]',\n      exportAs: 'matDialogTitle',\n      host: {\n        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n        '[id]': 'id'\n      }\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n  static ɵfac = function MatDialogContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogContent,\n    selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"],\n    features: [i0.ɵɵHostDirectivesFeature([i1.CdkScrollable])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n      host: {\n        'class': 'mat-mdc-dialog-content mdc-dialog__content'\n      },\n      hostDirectives: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n  /**\n   * Horizontal alignment of action buttons.\n   */\n  align;\n  _onAdd() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n  }\n  _onRemove() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogActions_BaseFactory;\n    return function MatDialogActions_Factory(__ngFactoryType__) {\n      return (ɵMatDialogActions_BaseFactory || (ɵMatDialogActions_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogActions)))(__ngFactoryType__ || MatDialogActions);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogActions,\n    selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n    hostVars: 6,\n    hostBindings: function MatDialogActions_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-start\", ctx.align === \"start\")(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n      }\n    },\n    inputs: {\n      align: \"align\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogActions, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n      host: {\n        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nconst DIRECTIVES = [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent];\nclass MatDialogModule {\n  static ɵfac = function MatDialogModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDialogModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatDialog],\n    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatDialog]\n    }]\n  }], null, null);\n})();\nexport { MatDialogActions as M, _closeDialogVia as _, MatDialogClose as a, MatDialogTitle as b, MatDialogContent as c, MatDialogContainer as d, MAT_DIALOG_DATA as e, MAT_DIALOG_DEFAULT_OPTIONS as f, MAT_DIALOG_SCROLL_STRATEGY as g, MatDialog as h, MatDialogConfig as i, MatDialogState as j, MatDialogRef as k, MatDialogModule as l };", "map": {"version": 3, "names": ["CdkDialogContainer", "Dialog", "DialogConfig", "DialogModule", "createBlockScrollStrategy", "createGlobalPositionStrategy", "OverlayModule", "CdkPortalOutlet", "PortalModule", "i0", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "InjectionToken", "inject", "Injector", "Injectable", "ElementRef", "Directive", "Input", "NgModule", "coerceNumberProperty", "_", "_animationsDisabled", "Subject", "merge", "defer", "filter", "take", "startWith", "ESCAPE", "hasModifierKey", "_IdGenerator", "i1", "CdkScrollable", "M", "MatCommonModule", "MatDialogContainer_ng_template_2_Template", "rf", "ctx", "MatDialogConfig", "viewContainerRef", "injector", "id", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "closePredicate", "width", "height", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "position", "data", "direction", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "delayFocusTrap", "scrollStrategy", "closeOnNavigation", "enterAnimationDuration", "exitAnimationDuration", "OPEN_CLASS", "OPENING_CLASS", "CLOSING_CLASS", "OPEN_ANIMATION_DURATION", "CLOSE_ANIMATION_DURATION", "MatDialogContainer", "_animationStateChanged", "_animationsEnabled", "_actionSectionCount", "_hostElement", "_elementRef", "nativeElement", "_enterAnimationDuration", "parseCssTime", "_config", "_exitAnimationDuration", "_animationTimer", "_contentAttached", "_startOpenAnimation", "emit", "state", "totalTime", "style", "setProperty", "TRANSITION_DURATION_PROPERTY", "_requestAnimationFrame", "classList", "add", "_waitForAnimationToComplete", "_finishDialogOpen", "Promise", "resolve", "then", "_startExitAnimation", "remove", "_finishDialogClose", "_updateActionSectionCount", "delta", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_clearAnimationClasses", "_openAnimationDone", "duration", "callback", "clearTimeout", "setTimeout", "_ngZone", "runOutsideAngular", "requestAnimationFrame", "_captureInitialFocus", "_trapFocus", "next", "ngOnDestroy", "attachComponentPortal", "portal", "ref", "location", "ɵfac", "ɵMatDialogContainer_BaseFactory", "MatDialogContainer_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatDialogContainer_HostBindings", "ɵɵdomProperty", "ɵɵattribute", "_ariaLabelledByQueue", "ɵɵclassProp", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "MatDialogContainer_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "dependencies", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "time", "endsWith", "substring", "length", "MatDialogState", "MatDialogRef", "_ref", "_containerInstance", "componentInstance", "componentRef", "_afterOpened", "_beforeClosed", "_result", "_closeFallbackTimeout", "_state", "OPEN", "_closeInteractionType", "constructor", "addPanelClass", "pipe", "event", "subscribe", "complete", "overlayRef", "detachments", "backdropClick", "keydownEvents", "keyCode", "preventDefault", "_closeDialogVia", "close", "dialogResult", "detachBackdrop", "CLOSING", "afterOpened", "afterClosed", "closed", "beforeClosed", "updatePosition", "strategy", "config", "positionStrategy", "left", "right", "centerHorizontally", "top", "bottom", "centerVertically", "updateSize", "classes", "removePanelClass", "getState", "CLOSED", "<PERSON><PERSON><PERSON><PERSON>", "interactionType", "result", "MAT_DIALOG_DATA", "MAT_DIALOG_DEFAULT_OPTIONS", "MAT_DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "MatDialog", "_defaultOptions", "optional", "_scrollStrategy", "_parentDialog", "skipSelf", "_idGenerator", "_injector", "_dialog", "_openDialogsAtThisLevel", "_afterAllClosedAtThisLevel", "_afterOpenedAtThisLevel", "dialogConfigClass", "_dialogRefConstructor", "_dialogContainerType", "_dialogDataToken", "openDialogs", "_getAfterAllClosed", "parent", "afterAllClosed", "undefined", "open", "componentOrTemplateRef", "dialogRef", "getId", "cdkRef", "closeOnDestroy", "closeOnOverlayDetachments", "disableAnimations", "toLocaleString", "toString", "container", "providers", "provide", "useValue", "templateContext", "cdkConfig", "dialogContainer", "push", "index", "indexOf", "splice", "closeAll", "_closeDialogs", "getDialogById", "find", "dialog", "dialogs", "i", "MatDialog_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "MatDialogClose", "_matDialogClose", "ngOnInit", "getClosestDialog", "ngOnChanges", "changes", "proxied<PERSON><PERSON>e", "currentValue", "_onButtonClick", "screenX", "screenY", "MatDialogClose_Factory", "ɵdir", "ɵɵdefineDirective", "MatDialogClose_HostBindings", "ɵɵlistener", "MatDialogClose_click_HostBindingHandler", "$event", "inputs", "exportAs", "ɵɵNgOnChangesFeature", "MatDialogLayoutSection", "_dialogRef", "_onAdd", "instance", "_onRemove", "MatDialogLayoutSection_Factory", "MatDialogTitle", "_addAriaLabelledBy", "_removeAriaLabelledBy", "ɵMatDialogTitle_BaseFactory", "MatDialogTitle_Factory", "MatDialogTitle_HostBindings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogContent_Factory", "ɵɵHostDirectivesFeature", "hostDirectives", "MatDialogActions", "align", "ɵMatDialogActions_BaseFactory", "MatDialogActions_Factory", "MatDialogActions_HostBindings", "element", "parentElement", "contains", "DIRECTIVES", "MatDialogModule", "MatDialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "l"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/material/fesm2022/module-Ce6F7TNm.mjs"], "sourcesContent": ["import { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { createBlockScrollStrategy, createGlobalPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, inject, Injector, Injectable, ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nclass MatDialogConfig {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    viewContainerRef;\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    injector;\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    id;\n    /** The ARIA role of the dialog element. */\n    role = 'dialog';\n    /** Custom class for the overlay pane. */\n    panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    hasBackdrop = true;\n    /** Custom class for the backdrop. */\n    backdropClass = '';\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n    disableClose = false;\n    /** Function used to determine whether the dialog is allowed to close. */\n    closePredicate;\n    /** Width of the dialog. */\n    width = '';\n    /** Height of the dialog. */\n    height = '';\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    minWidth;\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    minHeight;\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    maxWidth;\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    maxHeight;\n    /** Position overrides. */\n    position;\n    /** Data being injected into the child component. */\n    data = null;\n    /** Layout direction for the dialog's content. */\n    direction;\n    /** ID of the element that describes the dialog. */\n    ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    ariaLabelledBy = null;\n    /** Aria label to assign to the dialog element. */\n    ariaLabel = null;\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    ariaModal = false;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n    restoreFocus = true;\n    /** Whether to wait for the opening animation to finish before trapping focus. */\n    delayFocusTrap = true;\n    /** Scroll strategy to be used for the dialog. */\n    scrollStrategy;\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    closeOnNavigation = true;\n    /**\n     * Duration of the enter animation in ms.\n     * Should be a number, string type is deprecated.\n     * @breaking-change 17.0.0 Remove string signature.\n     */\n    enterAnimationDuration;\n    /**\n     * Duration of the exit animation in ms.\n     * Should be a number, string type is deprecated.\n     * @breaking-change 17.0.0 Remove string signature.\n     */\n    exitAnimationDuration;\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n    /** Emits when an animation state changes. */\n    _animationStateChanged = new EventEmitter();\n    /** Whether animations are enabled. */\n    _animationsEnabled = !_animationsDisabled();\n    /** Number of actions projected in the dialog. */\n    _actionSectionCount = 0;\n    /** Host element of the dialog container component. */\n    _hostElement = this._elementRef.nativeElement;\n    /** Duration of the dialog open animation. */\n    _enterAnimationDuration = this._animationsEnabled\n        ? (parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION)\n        : 0;\n    /** Duration of the dialog close animation. */\n    _exitAnimationDuration = this._animationsEnabled\n        ? (parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION)\n        : 0;\n    /** Current timer for dialog animations. */\n    _animationTimer = null;\n    _contentAttached() {\n        // Delegate to the original dialog-container initialization (i.e. saving the\n        // previous element, setting up the focus trap and moving focus to the container).\n        super._contentAttached();\n        // Note: Usually we would be able to use the MDC dialog foundation here to handle\n        // the dialog animation for us, but there are a few reasons why we just leverage\n        // their styles and not use the runtime foundation code:\n        //   1. Foundation does not allow us to disable animations.\n        //   2. Foundation contains unnecessary features we don't need and aren't\n        //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n        this._startOpenAnimation();\n    }\n    /** Starts the dialog open animation if enabled. */\n    _startOpenAnimation() {\n        this._animationStateChanged.emit({ state: 'opening', totalTime: this._enterAnimationDuration });\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            // One would expect that the open class is added once the animation finished, but MDC\n            // uses the open class in combination with the opening class to start the animation.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n            this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n        }\n        else {\n            this._hostElement.classList.add(OPEN_CLASS);\n            // Note: We could immediately finish the dialog opening here with noop animations,\n            // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n            // Executing this immediately would mean that `afterOpened` emits synchronously\n            // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n            Promise.resolve().then(() => this._finishDialogOpen());\n        }\n    }\n    /**\n     * Starts the exit animation of the dialog if enabled. This method is\n     * called by the dialog ref.\n     */\n    _startExitAnimation() {\n        this._animationStateChanged.emit({ state: 'closing', totalTime: this._exitAnimationDuration });\n        this._hostElement.classList.remove(OPEN_CLASS);\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n            this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n        }\n        else {\n            // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n            // set up before any user can subscribe to the backdrop click. The subscription triggers\n            // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n            // animation state event if animations are disabled, the overlay would be disposed\n            // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n            // skipped. We work around this by waiting with the dialog close until the next tick when\n            // all subscriptions have been fired as expected. This is not an ideal solution, but\n            // there doesn't seem to be any other good way. Alternatives that have been considered:\n            //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n            //      Also this issue is specific to the MDC implementation where the dialog could\n            //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n            //      and closing always takes at least a tick.\n            //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n            //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n            //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n            // Based on the fact that this is specific to the MDC-based implementation of the dialog\n            // animations, the defer is applied here.\n            Promise.resolve().then(() => this._finishDialogClose());\n        }\n    }\n    /**\n     * Updates the number action sections.\n     * @param delta Increase/decrease in the number of sections.\n     */\n    _updateActionSectionCount(delta) {\n        this._actionSectionCount += delta;\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Completes the dialog open by clearing potential animation classes, trapping\n     * focus and emitting an opened event.\n     */\n    _finishDialogOpen = () => {\n        this._clearAnimationClasses();\n        this._openAnimationDone(this._enterAnimationDuration);\n    };\n    /**\n     * Completes the dialog close by clearing potential animation classes, restoring\n     * focus and emitting a closed event.\n     */\n    _finishDialogClose = () => {\n        this._clearAnimationClasses();\n        this._animationStateChanged.emit({ state: 'closed', totalTime: this._exitAnimationDuration });\n    };\n    /** Clears all dialog animation classes. */\n    _clearAnimationClasses() {\n        this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n    }\n    _waitForAnimationToComplete(duration, callback) {\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n        // Note that we want this timer to run inside the NgZone, because we want\n        // the related events like `afterClosed` to be inside the zone as well.\n        this._animationTimer = setTimeout(callback, duration);\n    }\n    /** Runs a callback in `requestAnimationFrame`, if available. */\n    _requestAnimationFrame(callback) {\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame === 'function') {\n                requestAnimationFrame(callback);\n            }\n            else {\n                callback();\n            }\n        });\n    }\n    _captureInitialFocus() {\n        if (!this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Callback for when the open dialog animation has finished. Intended to\n     * be called by sub-classes that use different animation implementations.\n     */\n    _openAnimationDone(totalTime) {\n        if (this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n        this._animationStateChanged.next({ state: 'opened', totalTime });\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n    }\n    attachComponentPortal(portal) {\n        // When a component is passed into the dialog, the host element interrupts\n        // the `display:flex` from affecting the dialog title, content, and\n        // actions. To fix this, we make the component host `display: contents` by\n        // marking its host with the `mat-mdc-dialog-component-host` class.\n        //\n        // Note that this problem does not exist when a template ref is used since\n        // the title, contents, and actions are then nested directly under the\n        // dialog surface.\n        const ref = super.attachComponentPortal(portal);\n        ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n        return ref;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogContainer, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDialogContainer, isStandalone: true, selector: \"mat-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.aria-modal\": \"_config.ariaModal\", \"id\": \"_config.id\", \"attr.role\": \"_config.role\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\", \"class._mat-animation-noopable\": \"!_animationsEnabled\", \"class.mat-mdc-dialog-container-with-actions\": \"_actionSectionCount > 0\" }, classAttribute: \"mat-mdc-dialog-container mdc-dialog\" }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mat-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mat-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mat-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mat-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mat-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mat-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mat-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mat-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mat-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mat-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mat-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mat-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mat-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;box-sizing:border-box;min-height:52px;margin:0;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [CdkPortalOutlet], host: {\n                        'class': 'mat-mdc-dialog-container mdc-dialog',\n                        'tabindex': '-1',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[id]': '_config.id',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                        '[class._mat-animation-noopable]': '!_animationsEnabled',\n                        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0',\n                    }, template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mat-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mat-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mat-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mat-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mat-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mat-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mat-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mat-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mat-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mat-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mat-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mat-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mat-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;box-sizing:border-box;min-height:52px;margin:0;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"] }]\n        }] });\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n    if (time == null) {\n        return null;\n    }\n    if (typeof time === 'number') {\n        return time;\n    }\n    if (time.endsWith('ms')) {\n        return coerceNumberProperty(time.substring(0, time.length - 2));\n    }\n    if (time.endsWith('s')) {\n        return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n    }\n    if (time === '0') {\n        return 0;\n    }\n    return null; // anything else is invalid.\n}\n\nvar MatDialogState;\n(function (MatDialogState) {\n    MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n    MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n    MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n    _ref;\n    _config;\n    _containerInstance;\n    /** The instance of component opened into the dialog. */\n    componentInstance;\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentRef;\n    /** Whether the user is allowed to close the dialog. */\n    disableClose;\n    /** Unique ID for the dialog. */\n    id;\n    /** Subject for notifying the user that the dialog has finished opening. */\n    _afterOpened = new Subject();\n    /** Subject for notifying the user that the dialog has started closing. */\n    _beforeClosed = new Subject();\n    /** Result to be passed to afterClosed. */\n    _result;\n    /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n    _closeFallbackTimeout;\n    /** Current state of the dialog. */\n    _state = MatDialogState.OPEN;\n    // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n    // already has a second `options` parameter that we can use. The problem is that internal tests\n    // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n    // because it'll be called with two arguments by things like `MatDialogClose`.\n    /** Interaction that caused the dialog to close. */\n    _closeInteractionType;\n    constructor(_ref, _config, _containerInstance) {\n        this._ref = _ref;\n        this._config = _config;\n        this._containerInstance = _containerInstance;\n        this.disableClose = _config.disableClose;\n        this.id = _ref.id;\n        // Used to target panels specifically tied to dialogs.\n        _ref.addPanelClass('mat-mdc-dialog-panel');\n        // Emit when opening animation completes\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'opened'), take(1))\n            .subscribe(() => {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        });\n        // Dispose overlay when closing animation is complete\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closed'), take(1))\n            .subscribe(() => {\n            clearTimeout(this._closeFallbackTimeout);\n            this._finishDialogClose();\n        });\n        _ref.overlayRef.detachments().subscribe(() => {\n            this._beforeClosed.next(this._result);\n            this._beforeClosed.complete();\n            this._finishDialogClose();\n        });\n        merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n            if (!this.disableClose) {\n                event.preventDefault();\n                _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param dialogResult Optional result to return to the dialog opener.\n     */\n    close(dialogResult) {\n        const closePredicate = this._config.closePredicate;\n        if (closePredicate && !closePredicate(dialogResult, this._config, this.componentInstance)) {\n            return;\n        }\n        this._result = dialogResult;\n        // Transition the backdrop in parallel to the dialog.\n        this._containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closing'), take(1))\n            .subscribe(event => {\n            this._beforeClosed.next(dialogResult);\n            this._beforeClosed.complete();\n            this._ref.overlayRef.detachBackdrop();\n            // The logic that disposes of the overlay depends on the exit animation completing, however\n            // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n            // timeout which will clean everything up if the animation hasn't fired within the specified\n            // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n            // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n            this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n        });\n        this._state = MatDialogState.CLOSING;\n        this._containerInstance._startExitAnimation();\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished opening.\n     */\n    afterOpened() {\n        return this._afterOpened;\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished closing.\n     */\n    afterClosed() {\n        return this._ref.closed;\n    }\n    /**\n     * Gets an observable that is notified when the dialog has started closing.\n     */\n    beforeClosed() {\n        return this._beforeClosed;\n    }\n    /**\n     * Gets an observable that emits when the overlay's backdrop has been clicked.\n     */\n    backdropClick() {\n        return this._ref.backdropClick;\n    }\n    /**\n     * Gets an observable that emits when keydown events are targeted on the overlay.\n     */\n    keydownEvents() {\n        return this._ref.keydownEvents;\n    }\n    /**\n     * Updates the dialog's position.\n     * @param position New dialog position.\n     */\n    updatePosition(position) {\n        let strategy = this._ref.config.positionStrategy;\n        if (position && (position.left || position.right)) {\n            position.left ? strategy.left(position.left) : strategy.right(position.right);\n        }\n        else {\n            strategy.centerHorizontally();\n        }\n        if (position && (position.top || position.bottom)) {\n            position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n        }\n        else {\n            strategy.centerVertically();\n        }\n        this._ref.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this._ref.updateSize(width, height);\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this._ref.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this._ref.removePanelClass(classes);\n        return this;\n    }\n    /** Gets the current state of the dialog's lifecycle. */\n    getState() {\n        return this._state;\n    }\n    /**\n     * Finishes the dialog close by updating the state of the dialog\n     * and disposing the overlay.\n     */\n    _finishDialogClose() {\n        this._state = MatDialogState.CLOSED;\n        this._ref.close(this._result, { focusOrigin: this._closeInteractionType });\n        this.componentInstance = null;\n    }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n    ref._closeInteractionType = interactionType;\n    return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createBlockScrollStrategy(injector);\n    },\n});\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n    _defaultOptions = inject(MAT_DIALOG_DEFAULT_OPTIONS, { optional: true });\n    _scrollStrategy = inject(MAT_DIALOG_SCROLL_STRATEGY);\n    _parentDialog = inject(MatDialog, { optional: true, skipSelf: true });\n    _idGenerator = inject(_IdGenerator);\n    _injector = inject(Injector);\n    _dialog = inject(Dialog);\n    _animationsDisabled = _animationsDisabled();\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    dialogConfigClass = MatDialogConfig;\n    _dialogRefConstructor;\n    _dialogContainerType;\n    _dialogDataToken;\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length\n        ? this._getAfterAllClosed()\n        : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() {\n        this._dialogRefConstructor = MatDialogRef;\n        this._dialogContainerType = MatDialogContainer;\n        this._dialogDataToken = MAT_DIALOG_DATA;\n    }\n    open(componentOrTemplateRef, config) {\n        let dialogRef;\n        config = { ...(this._defaultOptions || new MatDialogConfig()), ...config };\n        config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n        config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n        const cdkRef = this._dialog.open(componentOrTemplateRef, {\n            ...config,\n            positionStrategy: createGlobalPositionStrategy(this._injector)\n                .centerHorizontally()\n                .centerVertically(),\n            // Disable closing since we need to sync it up to the animation ourselves.\n            disableClose: true,\n            // Closing is tied to our animation so the close predicate has to be implemented separately.\n            closePredicate: undefined,\n            // Disable closing on destroy, because this service cleans up its open dialogs as well.\n            // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n            // the dialogs immediately whereas we want it to wait for the animations to finish.\n            closeOnDestroy: false,\n            // Disable closing on detachments so that we can sync up the animation.\n            // The Material dialog ref handles this manually.\n            closeOnOverlayDetachments: false,\n            disableAnimations: this._animationsDisabled ||\n                config.enterAnimationDuration?.toLocaleString() === '0' ||\n                config.exitAnimationDuration?.toString() === '0',\n            container: {\n                type: this._dialogContainerType,\n                providers: () => [\n                    // Provide our config as the CDK config as well since it has the same interface as the\n                    // CDK one, but it contains the actual values passed in by the user for things like\n                    // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n                    { provide: this.dialogConfigClass, useValue: config },\n                    { provide: DialogConfig, useValue: config },\n                ],\n            },\n            templateContext: () => ({ dialogRef }),\n            providers: (ref, cdkConfig, dialogContainer) => {\n                dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n                dialogRef.updatePosition(config?.position);\n                return [\n                    { provide: this._dialogContainerType, useValue: dialogContainer },\n                    { provide: this._dialogDataToken, useValue: cdkConfig.data },\n                    { provide: this._dialogRefConstructor, useValue: dialogRef },\n                ];\n            },\n        });\n        // This can't be assigned in the `providers` callback, because\n        // the instance hasn't been assigned to the CDK ref yet.\n        dialogRef.componentRef = cdkRef.componentRef;\n        dialogRef.componentInstance = cdkRef.componentInstance;\n        this.openDialogs.push(dialogRef);\n        this.afterOpened.next(dialogRef);\n        dialogRef.afterClosed().subscribe(() => {\n            const index = this.openDialogs.indexOf(dialogRef);\n            if (index > -1) {\n                this.openDialogs.splice(index, 1);\n                if (!this.openDialogs.length) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        });\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Only close the dialogs at this level on destroy\n        // since the parent service may still be active.\n        this._closeDialogs(this._openDialogsAtThisLevel);\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n    }\n    _closeDialogs(dialogs) {\n        let i = dialogs.length;\n        while (i--) {\n            dialogs[i].close();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialog, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialog, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n    dialogRef = inject(MatDialogRef, { optional: true });\n    _elementRef = inject(ElementRef);\n    _dialog = inject(MatDialog);\n    /** Screen-reader label for the button. */\n    ariaLabel;\n    /** Default to \"button\" to prevents accidental form submits. */\n    type = 'button';\n    /** Dialog close input. */\n    dialogResult;\n    _matDialogClose;\n    constructor() { }\n    ngOnInit() {\n        if (!this.dialogRef) {\n            // When this directive is included in a dialog via TemplateRef (rather than being\n            // in a Component), the DialogRef isn't available via injection because embedded\n            // views cannot be given a custom injector. Instead, we look up the DialogRef by\n            // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n            // be resolved at constructor time.\n            this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n    }\n    ngOnChanges(changes) {\n        const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n        if (proxiedChange) {\n            this.dialogResult = proxiedChange.currentValue;\n        }\n    }\n    _onButtonClick(event) {\n        // Determinate the focus origin using the click event, because using the FocusMonitor will\n        // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n        // dialog, and therefore clicking the button won't result in a focus change. This means that\n        // the FocusMonitor won't detect any origin change, and will always output `program`.\n        _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogClose, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDialogClose, isStandalone: true, selector: \"[mat-dialog-close], [matDialogClose]\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], type: \"type\", dialogResult: [\"mat-dialog-close\", \"dialogResult\"], _matDialogClose: [\"matDialogClose\", \"_matDialogClose\"] }, host: { listeners: { \"click\": \"_onButtonClick($event)\" }, properties: { \"attr.aria-label\": \"ariaLabel || null\", \"attr.type\": \"type\" } }, exportAs: [\"matDialogClose\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogClose, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-close], [matDialogClose]',\n                    exportAs: 'matDialogClose',\n                    host: {\n                        '(click)': '_onButtonClick($event)',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.type]': 'type',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], type: [{\n                type: Input\n            }], dialogResult: [{\n                type: Input,\n                args: ['mat-dialog-close']\n            }], _matDialogClose: [{\n                type: Input,\n                args: ['matDialogClose']\n            }] } });\nclass MatDialogLayoutSection {\n    _dialogRef = inject(MatDialogRef, { optional: true });\n    _elementRef = inject(ElementRef);\n    _dialog = inject(MatDialog);\n    constructor() { }\n    ngOnInit() {\n        if (!this._dialogRef) {\n            this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n        if (this._dialogRef) {\n            Promise.resolve().then(() => {\n                this._onAdd();\n            });\n        }\n    }\n    ngOnDestroy() {\n        // Note: we null check because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        const instance = this._dialogRef?._containerInstance;\n        if (instance) {\n            Promise.resolve().then(() => {\n                this._onRemove();\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogLayoutSection, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDialogLayoutSection, isStandalone: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogLayoutSection, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n    id = inject(_IdGenerator).getId('mat-mdc-dialog-title-');\n    _onAdd() {\n        // Note: we null check the queue, because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n    }\n    _onRemove() {\n        this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogTitle, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDialogTitle, isStandalone: true, selector: \"[mat-dialog-title], [matDialogTitle]\", inputs: { id: \"id\" }, host: { properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-dialog-title mdc-dialog__title\" }, exportAs: [\"matDialogTitle\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-title], [matDialogTitle]',\n                    exportAs: 'matDialogTitle',\n                    host: {\n                        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n                        '[id]': 'id',\n                    },\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDialogContent, isStandalone: true, selector: \"[mat-dialog-content], mat-dialog-content, [matDialogContent]\", host: { classAttribute: \"mat-mdc-dialog-content mdc-dialog__content\" }, hostDirectives: [{ directive: i1.CdkScrollable }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n                    host: { 'class': 'mat-mdc-dialog-content mdc-dialog__content' },\n                    hostDirectives: [CdkScrollable],\n                }]\n        }] });\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n    /**\n     * Horizontal alignment of action buttons.\n     */\n    align;\n    _onAdd() {\n        this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n    }\n    _onRemove() {\n        this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogActions, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDialogActions, isStandalone: true, selector: \"[mat-dialog-actions], mat-dialog-actions, [matDialogActions]\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-dialog-actions-align-start\": \"align === \\\"start\\\"\", \"class.mat-mdc-dialog-actions-align-center\": \"align === \\\"center\\\"\", \"class.mat-mdc-dialog-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-dialog-actions mdc-dialog__actions\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n                    host: {\n                        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n                        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n                        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n                        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n    let parent = element.nativeElement.parentElement;\n    while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n        parent = parent.parentElement;\n    }\n    return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\n\nconst DIRECTIVES = [\n    MatDialogContainer,\n    MatDialogClose,\n    MatDialogTitle,\n    MatDialogActions,\n    MatDialogContent,\n];\nclass MatDialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogModule, imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent], exports: [MatCommonModule, MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogModule, providers: [MatDialog], imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatDialog],\n                }]\n        }] });\n\nexport { MatDialogActions as M, _closeDialogVia as _, MatDialogClose as a, MatDialogTitle as b, MatDialogContent as c, MatDialogContainer as d, MAT_DIALOG_DATA as e, MAT_DIALOG_DEFAULT_OPTIONS as f, MAT_DIALOG_SCROLL_STRATEGY as g, MatDialog as h, MatDialogConfig as i, MatDialogState as j, MatDialogRef as k, MatDialogModule as l };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,QAAQ,qBAAqB;AAC5F,SAASC,yBAAyB,EAAEC,4BAA4B,EAAEC,aAAa,QAAQ,sBAAsB;AAC7G,SAASC,eAAe,EAAEC,YAAY,QAAQ,qBAAqB;AACnE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACzL,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;;AAEnE;AACA;AACA;AAFA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AAGA,MAAMC,eAAe,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;EACAC,EAAE;EACF;EACAC,IAAI,GAAG,QAAQ;EACf;EACAC,UAAU,GAAG,EAAE;EACf;EACAC,WAAW,GAAG,IAAI;EAClB;EACAC,aAAa,GAAG,EAAE;EAClB;EACAC,YAAY,GAAG,KAAK;EACpB;EACAC,cAAc;EACd;EACAC,KAAK,GAAG,EAAE;EACV;EACAC,MAAM,GAAG,EAAE;EACX;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,IAAI,GAAG,IAAI;EACX;EACAC,SAAS;EACT;EACAC,eAAe,GAAG,IAAI;EACtB;EACAC,cAAc,GAAG,IAAI;EACrB;EACAC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,gBAAgB;EAC5B;AACJ;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;EACAC,cAAc,GAAG,IAAI;EACrB;EACAC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,GAAG,IAAI;EACxB;AACJ;AACA;AACA;AACA;EACIC,sBAAsB;EACtB;AACJ;AACA;AACA;AACA;EACIC,qBAAqB;AACzB;;AAEA;AACA,MAAMC,UAAU,GAAG,kBAAkB;AACrC;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,kBAAkB,SAAS5E,kBAAkB,CAAC;EAChD;EACA6E,sBAAsB,GAAG,IAAInE,YAAY,CAAC,CAAC;EAC3C;EACAoE,kBAAkB,GAAG,CAACtD,mBAAmB,CAAC,CAAC;EAC3C;EACAuD,mBAAmB,GAAG,CAAC;EACvB;EACAC,YAAY,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa;EAC7C;EACAC,uBAAuB,GAAG,IAAI,CAACL,kBAAkB,GAC1CM,YAAY,CAAC,IAAI,CAACC,OAAO,CAAChB,sBAAsB,CAAC,IAAIK,uBAAuB,GAC7E,CAAC;EACP;EACAY,sBAAsB,GAAG,IAAI,CAACR,kBAAkB,GACzCM,YAAY,CAAC,IAAI,CAACC,OAAO,CAACf,qBAAqB,CAAC,IAAIK,wBAAwB,GAC7E,CAAC;EACP;EACAY,eAAe,GAAG,IAAI;EACtBC,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA,KAAK,CAACA,gBAAgB,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAA,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACZ,sBAAsB,CAACa,IAAI,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACT;IAAwB,CAAC,CAAC;IAC/F,IAAI,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAACE,YAAY,CAACa,KAAK,CAACC,WAAW,CAACC,4BAA4B,EAAE,GAAG,IAAI,CAACZ,uBAAuB,IAAI,CAAC;MACtG;MACA;MACA;MACA,IAAI,CAACa,sBAAsB,CAAC,MAAM,IAAI,CAAChB,YAAY,CAACiB,SAAS,CAACC,GAAG,CAAC1B,aAAa,EAAED,UAAU,CAAC,CAAC;MAC7F,IAAI,CAAC4B,2BAA2B,CAAC,IAAI,CAAChB,uBAAuB,EAAE,IAAI,CAACiB,iBAAiB,CAAC;IAC1F,CAAC,MACI;MACD,IAAI,CAACpB,YAAY,CAACiB,SAAS,CAACC,GAAG,CAAC3B,UAAU,CAAC;MAC3C;MACA;MACA;MACA;MACA8B,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACH,iBAAiB,CAAC,CAAC,CAAC;IAC1D;EACJ;EACA;AACJ;AACA;AACA;EACII,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC3B,sBAAsB,CAACa,IAAI,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACN;IAAuB,CAAC,CAAC;IAC9F,IAAI,CAACN,YAAY,CAACiB,SAAS,CAACQ,MAAM,CAAClC,UAAU,CAAC;IAC9C,IAAI,IAAI,CAACO,kBAAkB,EAAE;MACzB,IAAI,CAACE,YAAY,CAACa,KAAK,CAACC,WAAW,CAACC,4BAA4B,EAAE,GAAG,IAAI,CAACT,sBAAsB,IAAI,CAAC;MACrG;MACA,IAAI,CAACU,sBAAsB,CAAC,MAAM,IAAI,CAAChB,YAAY,CAACiB,SAAS,CAACC,GAAG,CAACzB,aAAa,CAAC,CAAC;MACjF,IAAI,CAAC0B,2BAA2B,CAAC,IAAI,CAACb,sBAAsB,EAAE,IAAI,CAACoB,kBAAkB,CAAC;IAC1F,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACG,kBAAkB,CAAC,CAAC,CAAC;IAC3D;EACJ;EACA;AACJ;AACA;AACA;EACIC,yBAAyBA,CAACC,KAAK,EAAE;IAC7B,IAAI,CAAC7B,mBAAmB,IAAI6B,KAAK;IACjC,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIV,iBAAiB,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACW,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC7B,uBAAuB,CAAC;EACzD,CAAC;EACD;AACJ;AACA;AACA;EACIuB,kBAAkB,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACK,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAClC,sBAAsB,CAACa,IAAI,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,SAAS,EAAE,IAAI,CAACN;IAAuB,CAAC,CAAC;EACjG,CAAC;EACD;EACAyB,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC/B,YAAY,CAACiB,SAAS,CAACQ,MAAM,CAACjC,aAAa,EAAEC,aAAa,CAAC;EACpE;EACA0B,2BAA2BA,CAACc,QAAQ,EAAEC,QAAQ,EAAE;IAC5C,IAAI,IAAI,CAAC3B,eAAe,KAAK,IAAI,EAAE;MAC/B4B,YAAY,CAAC,IAAI,CAAC5B,eAAe,CAAC;IACtC;IACA;IACA;IACA,IAAI,CAACA,eAAe,GAAG6B,UAAU,CAACF,QAAQ,EAAED,QAAQ,CAAC;EACzD;EACA;EACAjB,sBAAsBA,CAACkB,QAAQ,EAAE;IAC7B,IAAI,CAACG,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOC,qBAAqB,KAAK,UAAU,EAAE;QAC7CA,qBAAqB,CAACL,QAAQ,CAAC;MACnC,CAAC,MACI;QACDA,QAAQ,CAAC,CAAC;MACd;IACJ,CAAC,CAAC;EACN;EACAM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACnC,OAAO,CAACnB,cAAc,EAAE;MAC9B,IAAI,CAACuD,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;EACIT,kBAAkBA,CAACpB,SAAS,EAAE;IAC1B,IAAI,IAAI,CAACP,OAAO,CAACnB,cAAc,EAAE;MAC7B,IAAI,CAACuD,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,CAAC5C,sBAAsB,CAAC6C,IAAI,CAAC;MAAE/B,KAAK,EAAE,QAAQ;MAAEC;IAAU,CAAC,CAAC;EACpE;EACA+B,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAACpC,eAAe,KAAK,IAAI,EAAE;MAC/B4B,YAAY,CAAC,IAAI,CAAC5B,eAAe,CAAC;IACtC;EACJ;EACAqC,qBAAqBA,CAACC,MAAM,EAAE;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,GAAG,GAAG,KAAK,CAACF,qBAAqB,CAACC,MAAM,CAAC;IAC/CC,GAAG,CAACC,QAAQ,CAAC7C,aAAa,CAACe,SAAS,CAACC,GAAG,CAAC,+BAA+B,CAAC;IACzE,OAAO4B,GAAG;EACd;EACA,OAAOE,IAAI;IAAA,IAAAC,+BAAA;IAAA,gBAAAC,2BAAAC,iBAAA;MAAA,QAAAF,+BAAA,KAAAA,+BAAA,GAA8ExH,EAAE,CAAA2H,qBAAA,CAAQxD,kBAAkB,IAAAuD,iBAAA,IAAlBvD,kBAAkB;IAAA;EAAA;EACrH,OAAOyD,IAAI,kBAD8E5H,EAAE,CAAA6H,iBAAA;IAAAC,IAAA,EACJ3D,kBAAkB;IAAA4D,SAAA;IAAAC,SAAA,eAA0F,IAAI;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAArG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD9G9B,EAAE,CAAAoI,aAAA,OAAArG,GAAA,CAAA6C,OAAA,CAAAzC,EACa,CAAC;QADhBnC,EAAE,CAAAqI,WAAA,eAAAtG,GAAA,CAAA6C,OAAA,CAAAtB,SAAA,UAAAvB,GAAA,CAAA6C,OAAA,CAAAxC,IAAA,qBAAAL,GAAA,CAAA6C,OAAA,CAAAvB,SAAA,GACgB,IAAI,GAAAtB,GAAA,CAAAuG,oBAAA,CAAwB,CAAC,iBAAAvG,GAAA,CAAA6C,OAAA,CAAAvB,SAAA,sBAAAtB,GAAA,CAAA6C,OAAA,CAAAzB,eAAA,IAAtB,IAAI;QAD7BnD,EAAE,CAAAuI,WAAA,6BAAAxG,GAAA,CAAAsC,kBACa,CAAC,0CAAAtC,GAAA,CAAAuC,mBAAA,GAAI,CAAL,CAAC;MAAA;IAAA;IAAAkE,QAAA,GADhBxI,EAAE,CAAAyI,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAhH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF9B,EAAE,CAAA+I,cAAA,YACyrB,CAAC,YAA6D,CAAC;QAD1vB/I,EAAE,CAAAgJ,UAAA,IAAAnH,yCAAA,wBAC4xB,CAAC;QAD/xB7B,EAAE,CAAAiJ,YAAA,CACsyB,CAAC,CAAO,CAAC;MAAA;IAAA;IAAAC,YAAA,GAAmnKpJ,eAAe;IAAAqJ,MAAA;IAAAC,aAAA;EAAA;AAChhM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FrJ,EAAE,CAAAsJ,iBAAA,CAGJnF,kBAAkB,EAAc,CAAC;IAChH2D,IAAI,EAAE5H,SAAS;IACfqJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEJ,aAAa,EAAEjJ,iBAAiB,CAACsJ,IAAI;MAAEC,eAAe,EAAEtJ,uBAAuB,CAACuJ,OAAO;MAAEC,OAAO,EAAE,CAAC9J,eAAe,CAAC;MAAE+J,IAAI,EAAE;QAC1J,OAAO,EAAE,qCAAqC;QAC9C,UAAU,EAAE,IAAI;QAChB,mBAAmB,EAAE,mBAAmB;QACxC,MAAM,EAAE,YAAY;QACpB,aAAa,EAAE,cAAc;QAC7B,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE,iCAAiC;QAC5D,iCAAiC,EAAE,qBAAqB;QACxD,+CAA+C,EAAE;MACrD,CAAC;MAAEhB,QAAQ,EAAE,6LAA6L;MAAEM,MAAM,EAAE,CAAC,wjKAAwjK;IAAE,CAAC;EAC5xK,CAAC,CAAC;AAAA;AACV,MAAM7D,4BAA4B,GAAG,kCAAkC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASX,YAAYA,CAACmF,IAAI,EAAE;EACxB,IAAIA,IAAI,IAAI,IAAI,EAAE;IACd,OAAO,IAAI;EACf;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAIA,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrB,OAAOlJ,oBAAoB,CAACiJ,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;EACnE;EACA,IAAIH,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACpB,OAAOlJ,oBAAoB,CAACiJ,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAC1E;EACA,IAAIH,IAAI,KAAK,GAAG,EAAE;IACd,OAAO,CAAC;EACZ;EACA,OAAO,IAAI,CAAC,CAAC;AACjB;AAEA,IAAII,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzDA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC3D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,IAAI;EACJxF,OAAO;EACPyF,kBAAkB;EAClB;EACAC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,YAAY;EACZ;EACA/H,YAAY;EACZ;EACAL,EAAE;EACF;EACAqI,YAAY,GAAG,IAAIxJ,OAAO,CAAC,CAAC;EAC5B;EACAyJ,aAAa,GAAG,IAAIzJ,OAAO,CAAC,CAAC;EAC7B;EACA0J,OAAO;EACP;EACAC,qBAAqB;EACrB;EACAC,MAAM,GAAGV,cAAc,CAACW,IAAI;EAC5B;EACA;EACA;EACA;EACA;EACAC,qBAAqB;EACrBC,WAAWA,CAACX,IAAI,EAAExF,OAAO,EAAEyF,kBAAkB,EAAE;IAC3C,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACxF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACyF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC7H,YAAY,GAAGoC,OAAO,CAACpC,YAAY;IACxC,IAAI,CAACL,EAAE,GAAGiI,IAAI,CAACjI,EAAE;IACjB;IACAiI,IAAI,CAACY,aAAa,CAAC,sBAAsB,CAAC;IAC1C;IACAX,kBAAkB,CAACjG,sBAAsB,CACpC6G,IAAI,CAAC9J,MAAM,CAAC+J,KAAK,IAAIA,KAAK,CAAChG,KAAK,KAAK,QAAQ,CAAC,EAAE9D,IAAI,CAAC,CAAC,CAAC,CAAC,CACxD+J,SAAS,CAAC,MAAM;MACjB,IAAI,CAACX,YAAY,CAACvD,IAAI,CAAC,CAAC;MACxB,IAAI,CAACuD,YAAY,CAACY,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC;IACF;IACAf,kBAAkB,CAACjG,sBAAsB,CACpC6G,IAAI,CAAC9J,MAAM,CAAC+J,KAAK,IAAIA,KAAK,CAAChG,KAAK,KAAK,QAAQ,CAAC,EAAE9D,IAAI,CAAC,CAAC,CAAC,CAAC,CACxD+J,SAAS,CAAC,MAAM;MACjBzE,YAAY,CAAC,IAAI,CAACiE,qBAAqB,CAAC;MACxC,IAAI,CAAC1E,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACFmE,IAAI,CAACiB,UAAU,CAACC,WAAW,CAAC,CAAC,CAACH,SAAS,CAAC,MAAM;MAC1C,IAAI,CAACV,aAAa,CAACxD,IAAI,CAAC,IAAI,CAACyD,OAAO,CAAC;MACrC,IAAI,CAACD,aAAa,CAACW,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACnF,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACFhF,KAAK,CAAC,IAAI,CAACsK,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAACP,IAAI,CAAC9J,MAAM,CAAC+J,KAAK,IAAIA,KAAK,CAACO,OAAO,KAAKnK,MAAM,IAAI,CAAC,IAAI,CAACkB,YAAY,IAAI,CAACjB,cAAc,CAAC2J,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAACD,KAAK,IAAI;MACjK,IAAI,CAAC,IAAI,CAAC1I,YAAY,EAAE;QACpB0I,KAAK,CAACQ,cAAc,CAAC,CAAC;QACtBC,eAAe,CAAC,IAAI,EAAET,KAAK,CAACpD,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;MAC1E;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI8D,KAAKA,CAACC,YAAY,EAAE;IAChB,MAAMpJ,cAAc,GAAG,IAAI,CAACmC,OAAO,CAACnC,cAAc;IAClD,IAAIA,cAAc,IAAI,CAACA,cAAc,CAACoJ,YAAY,EAAE,IAAI,CAACjH,OAAO,EAAE,IAAI,CAAC0F,iBAAiB,CAAC,EAAE;MACvF;IACJ;IACA,IAAI,CAACI,OAAO,GAAGmB,YAAY;IAC3B;IACA,IAAI,CAACxB,kBAAkB,CAACjG,sBAAsB,CACzC6G,IAAI,CAAC9J,MAAM,CAAC+J,KAAK,IAAIA,KAAK,CAAChG,KAAK,KAAK,SAAS,CAAC,EAAE9D,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD+J,SAAS,CAACD,KAAK,IAAI;MACpB,IAAI,CAACT,aAAa,CAACxD,IAAI,CAAC4E,YAAY,CAAC;MACrC,IAAI,CAACpB,aAAa,CAACW,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAChB,IAAI,CAACiB,UAAU,CAACS,cAAc,CAAC,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACnB,qBAAqB,GAAGhE,UAAU,CAAC,MAAM,IAAI,CAACV,kBAAkB,CAAC,CAAC,EAAEiF,KAAK,CAAC/F,SAAS,GAAG,GAAG,CAAC;IACnG,CAAC,CAAC;IACF,IAAI,CAACyF,MAAM,GAAGV,cAAc,CAAC6B,OAAO;IACpC,IAAI,CAAC1B,kBAAkB,CAACtE,mBAAmB,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIiG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACxB,YAAY;EAC5B;EACA;AACJ;AACA;EACIyB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7B,IAAI,CAAC8B,MAAM;EAC3B;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1B,aAAa;EAC7B;EACA;AACJ;AACA;EACIc,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnB,IAAI,CAACmB,aAAa;EAClC;EACA;AACJ;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpB,IAAI,CAACoB,aAAa;EAClC;EACA;AACJ;AACA;AACA;EACIY,cAAcA,CAACpJ,QAAQ,EAAE;IACrB,IAAIqJ,QAAQ,GAAG,IAAI,CAACjC,IAAI,CAACkC,MAAM,CAACC,gBAAgB;IAChD,IAAIvJ,QAAQ,KAAKA,QAAQ,CAACwJ,IAAI,IAAIxJ,QAAQ,CAACyJ,KAAK,CAAC,EAAE;MAC/CzJ,QAAQ,CAACwJ,IAAI,GAAGH,QAAQ,CAACG,IAAI,CAACxJ,QAAQ,CAACwJ,IAAI,CAAC,GAAGH,QAAQ,CAACI,KAAK,CAACzJ,QAAQ,CAACyJ,KAAK,CAAC;IACjF,CAAC,MACI;MACDJ,QAAQ,CAACK,kBAAkB,CAAC,CAAC;IACjC;IACA,IAAI1J,QAAQ,KAAKA,QAAQ,CAAC2J,GAAG,IAAI3J,QAAQ,CAAC4J,MAAM,CAAC,EAAE;MAC/C5J,QAAQ,CAAC2J,GAAG,GAAGN,QAAQ,CAACM,GAAG,CAAC3J,QAAQ,CAAC2J,GAAG,CAAC,GAAGN,QAAQ,CAACO,MAAM,CAAC5J,QAAQ,CAAC4J,MAAM,CAAC;IAChF,CAAC,MACI;MACDP,QAAQ,CAACQ,gBAAgB,CAAC,CAAC;IAC/B;IACA,IAAI,CAACzC,IAAI,CAACgC,cAAc,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIU,UAAUA,CAACpK,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACyH,IAAI,CAAC0C,UAAU,CAACpK,KAAK,EAAEC,MAAM,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACAqI,aAAaA,CAAC+B,OAAO,EAAE;IACnB,IAAI,CAAC3C,IAAI,CAACY,aAAa,CAAC+B,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAAC3C,IAAI,CAAC4C,gBAAgB,CAACD,OAAO,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACrC,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACI3E,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC2E,MAAM,GAAGV,cAAc,CAACgD,MAAM;IACnC,IAAI,CAAC9C,IAAI,CAACwB,KAAK,CAAC,IAAI,CAAClB,OAAO,EAAE;MAAEyC,WAAW,EAAE,IAAI,CAACrC;IAAsB,CAAC,CAAC;IAC1E,IAAI,CAACR,iBAAiB,GAAG,IAAI;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,eAAeA,CAACtE,GAAG,EAAE+F,eAAe,EAAEC,MAAM,EAAE;EACnDhG,GAAG,CAACyD,qBAAqB,GAAGsC,eAAe;EAC3C,OAAO/F,GAAG,CAACuE,KAAK,CAACyB,MAAM,CAAC;AAC5B;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIjN,cAAc,CAAC,kBAAkB,CAAC;AAC9D;AACA,MAAMkN,0BAA0B,GAAG,IAAIlN,cAAc,CAAC,gCAAgC,CAAC;AACvF;AACA,MAAMmN,0BAA0B,GAAG,IAAInN,cAAc,CAAC,gCAAgC,EAAE;EACpFoN,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMxL,QAAQ,GAAG5B,MAAM,CAACC,QAAQ,CAAC;IACjC,OAAO,MAAMZ,yBAAyB,CAACuC,QAAQ,CAAC;EACpD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMyL,SAAS,CAAC;EACZC,eAAe,GAAGtN,MAAM,CAACiN,0BAA0B,EAAE;IAAEM,QAAQ,EAAE;EAAK,CAAC,CAAC;EACxEC,eAAe,GAAGxN,MAAM,CAACkN,0BAA0B,CAAC;EACpDO,aAAa,GAAGzN,MAAM,CAACqN,SAAS,EAAE;IAAEE,QAAQ,EAAE,IAAI;IAAEG,QAAQ,EAAE;EAAK,CAAC,CAAC;EACrEC,YAAY,GAAG3N,MAAM,CAACkB,YAAY,CAAC;EACnC0M,SAAS,GAAG5N,MAAM,CAACC,QAAQ,CAAC;EAC5B4N,OAAO,GAAG7N,MAAM,CAACd,MAAM,CAAC;EACxBuB,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3CqN,uBAAuB,GAAG,EAAE;EAC5BC,0BAA0B,GAAG,IAAIrN,OAAO,CAAC,CAAC;EAC1CsN,uBAAuB,GAAG,IAAItN,OAAO,CAAC,CAAC;EACvCuN,iBAAiB,GAAGvM,eAAe;EACnCwM,qBAAqB;EACrBC,oBAAoB;EACpBC,gBAAgB;EAChB;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACZ,aAAa,GAAG,IAAI,CAACA,aAAa,CAACY,WAAW,GAAG,IAAI,CAACP,uBAAuB;EAC7F;EACA;EACA,IAAIpC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC+B,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC/B,WAAW,GAAG,IAAI,CAACsC,uBAAuB;EAC7F;EACAM,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,MAAM,GAAG,IAAI,CAACd,aAAa;IACjC,OAAOc,MAAM,GAAGA,MAAM,CAACD,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACP,0BAA0B;EACjF;EACA;AACJ;AACA;AACA;EACIS,cAAc,GAAG5N,KAAK,CAAC,MAAM,IAAI,CAACyN,WAAW,CAAC1E,MAAM,GAC9C,IAAI,CAAC2E,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC3D,IAAI,CAAC5J,SAAS,CAAC0N,SAAS,CAAC,CAAC,CAAC;EAC3DhE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyD,qBAAqB,GAAGrE,YAAY;IACzC,IAAI,CAACsE,oBAAoB,GAAGtK,kBAAkB;IAC9C,IAAI,CAACuK,gBAAgB,GAAGpB,eAAe;EAC3C;EACA0B,IAAIA,CAACC,sBAAsB,EAAE3C,MAAM,EAAE;IACjC,IAAI4C,SAAS;IACb5C,MAAM,GAAG;MAAE,IAAI,IAAI,CAACsB,eAAe,IAAI,IAAI5L,eAAe,CAAC,CAAC,CAAC;MAAE,GAAGsK;IAAO,CAAC;IAC1EA,MAAM,CAACnK,EAAE,GAAGmK,MAAM,CAACnK,EAAE,IAAI,IAAI,CAAC8L,YAAY,CAACkB,KAAK,CAAC,iBAAiB,CAAC;IACnE7C,MAAM,CAAC5I,cAAc,GAAG4I,MAAM,CAAC5I,cAAc,IAAI,IAAI,CAACoK,eAAe,CAAC,CAAC;IACvE,MAAMsB,MAAM,GAAG,IAAI,CAACjB,OAAO,CAACa,IAAI,CAACC,sBAAsB,EAAE;MACrD,GAAG3C,MAAM;MACTC,gBAAgB,EAAE3M,4BAA4B,CAAC,IAAI,CAACsO,SAAS,CAAC,CACzDxB,kBAAkB,CAAC,CAAC,CACpBG,gBAAgB,CAAC,CAAC;MACvB;MACArK,YAAY,EAAE,IAAI;MAClB;MACAC,cAAc,EAAEsM,SAAS;MACzB;MACA;MACA;MACAM,cAAc,EAAE,KAAK;MACrB;MACA;MACAC,yBAAyB,EAAE,KAAK;MAChCC,iBAAiB,EAAE,IAAI,CAACxO,mBAAmB,IACvCuL,MAAM,CAAC1I,sBAAsB,EAAE4L,cAAc,CAAC,CAAC,KAAK,GAAG,IACvDlD,MAAM,CAACzI,qBAAqB,EAAE4L,QAAQ,CAAC,CAAC,KAAK,GAAG;MACpDC,SAAS,EAAE;QACP5H,IAAI,EAAE,IAAI,CAAC2G,oBAAoB;QAC/BkB,SAAS,EAAEA,CAAA,KAAM;QACb;QACA;QACA;QACA;UAAEC,OAAO,EAAE,IAAI,CAACrB,iBAAiB;UAAEsB,QAAQ,EAAEvD;QAAO,CAAC,EACrD;UAAEsD,OAAO,EAAEnQ,YAAY;UAAEoQ,QAAQ,EAAEvD;QAAO,CAAC;MAEnD,CAAC;MACDwD,eAAe,EAAEA,CAAA,MAAO;QAAEZ;MAAU,CAAC,CAAC;MACtCS,SAAS,EAAEA,CAACtI,GAAG,EAAE0I,SAAS,EAAEC,eAAe,KAAK;QAC5Cd,SAAS,GAAG,IAAI,IAAI,CAACV,qBAAqB,CAACnH,GAAG,EAAEiF,MAAM,EAAE0D,eAAe,CAAC;QACxEd,SAAS,CAAC9C,cAAc,CAACE,MAAM,EAAEtJ,QAAQ,CAAC;QAC1C,OAAO,CACH;UAAE4M,OAAO,EAAE,IAAI,CAACnB,oBAAoB;UAAEoB,QAAQ,EAAEG;QAAgB,CAAC,EACjE;UAAEJ,OAAO,EAAE,IAAI,CAAClB,gBAAgB;UAAEmB,QAAQ,EAAEE,SAAS,CAAC9M;QAAK,CAAC,EAC5D;UAAE2M,OAAO,EAAE,IAAI,CAACpB,qBAAqB;UAAEqB,QAAQ,EAAEX;QAAU,CAAC,CAC/D;MACL;IACJ,CAAC,CAAC;IACF;IACA;IACAA,SAAS,CAAC3E,YAAY,GAAG6E,MAAM,CAAC7E,YAAY;IAC5C2E,SAAS,CAAC5E,iBAAiB,GAAG8E,MAAM,CAAC9E,iBAAiB;IACtD,IAAI,CAACqE,WAAW,CAACsB,IAAI,CAACf,SAAS,CAAC;IAChC,IAAI,CAAClD,WAAW,CAAC/E,IAAI,CAACiI,SAAS,CAAC;IAChCA,SAAS,CAACjD,WAAW,CAAC,CAAC,CAACd,SAAS,CAAC,MAAM;MACpC,MAAM+E,KAAK,GAAG,IAAI,CAACvB,WAAW,CAACwB,OAAO,CAACjB,SAAS,CAAC;MACjD,IAAIgB,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACvB,WAAW,CAACyB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAACvB,WAAW,CAAC1E,MAAM,EAAE;UAC1B,IAAI,CAAC2E,kBAAkB,CAAC,CAAC,CAAC3H,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;IACF,OAAOiI,SAAS;EACpB;EACA;AACJ;AACA;EACImB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC3B,WAAW,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACI4B,aAAaA,CAACpO,EAAE,EAAE;IACd,OAAO,IAAI,CAACwM,WAAW,CAAC6B,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACtO,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACA+E,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACoJ,aAAa,CAAC,IAAI,CAAClC,uBAAuB,CAAC;IAChD,IAAI,CAACC,0BAA0B,CAACjD,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACkD,uBAAuB,CAAClD,QAAQ,CAAC,CAAC;EAC3C;EACAkF,aAAaA,CAACI,OAAO,EAAE;IACnB,IAAIC,CAAC,GAAGD,OAAO,CAACzG,MAAM;IACtB,OAAO0G,CAAC,EAAE,EAAE;MACRD,OAAO,CAACC,CAAC,CAAC,CAAC/E,KAAK,CAAC,CAAC;IACtB;EACJ;EACA,OAAOrE,IAAI,YAAAqJ,kBAAAlJ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiG,SAAS;EAAA;EAC5G,OAAOkD,KAAK,kBA/X6E7Q,EAAE,CAAA8Q,kBAAA;IAAAC,KAAA,EA+XYpD,SAAS;IAAAD,OAAA,EAATC,SAAS,CAAApG,IAAA;IAAAkG,UAAA,EAAc;EAAM;AACxI;AACA;EAAA,QAAApE,SAAA,oBAAAA,SAAA,KAjY6FrJ,EAAE,CAAAsJ,iBAAA,CAiYJqE,SAAS,EAAc,CAAC;IACvG7F,IAAI,EAAEtH,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEkE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,MAAMuD,cAAc,CAAC;EACjB9B,SAAS,GAAG5O,MAAM,CAAC6J,YAAY,EAAE;IAAE0D,QAAQ,EAAE;EAAK,CAAC,CAAC;EACpDrJ,WAAW,GAAGlE,MAAM,CAACG,UAAU,CAAC;EAChC0N,OAAO,GAAG7N,MAAM,CAACqN,SAAS,CAAC;EAC3B;EACAtK,SAAS;EACT;EACAyE,IAAI,GAAG,QAAQ;EACf;EACA+D,YAAY;EACZoF,eAAe;EACflG,WAAWA,CAAA,EAAG,CAAE;EAChBmG,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAChC,SAAS,EAAE;MACjB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACA,SAAS,GAAGiC,gBAAgB,CAAC,IAAI,CAAC3M,WAAW,EAAE,IAAI,CAAC2J,OAAO,CAACQ,WAAW,CAAC;IACjF;EACJ;EACAyC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC,IAAIA,OAAO,CAAC,uBAAuB,CAAC;IACpF,IAAIC,aAAa,EAAE;MACf,IAAI,CAACzF,YAAY,GAAGyF,aAAa,CAACC,YAAY;IAClD;EACJ;EACAC,cAAcA,CAACtG,KAAK,EAAE;IAClB;IACA;IACA;IACA;IACAS,eAAe,CAAC,IAAI,CAACuD,SAAS,EAAEhE,KAAK,CAACuG,OAAO,KAAK,CAAC,IAAIvG,KAAK,CAACwG,OAAO,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,IAAI,CAAC7F,YAAY,CAAC;EACzH;EACA,OAAOtE,IAAI,YAAAoK,uBAAAjK,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsJ,cAAc;EAAA;EACjH,OAAOY,IAAI,kBA7a8E5R,EAAE,CAAA6R,iBAAA;IAAA/J,IAAA,EA6aJkJ,cAAc;IAAAjJ,SAAA;IAAAE,QAAA;IAAAC,YAAA,WAAA4J,4BAAAhQ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7aZ9B,EAAE,CAAA+R,UAAA,mBAAAC,wCAAAC,MAAA;UAAA,OA6aJlQ,GAAA,CAAAyP,cAAA,CAAAS,MAAqB,CAAC;QAAA,CAAT,CAAC;MAAA;MAAA,IAAAnQ,EAAA;QA7aZ9B,EAAE,CAAAqI,WAAA,eAAAtG,GAAA,CAAAsB,SAAA,IA6aS,IAAI,UAAAtB,GAAA,CAAA+F,IAAA;MAAA;IAAA;IAAAoK,MAAA;MAAA7O,SAAA;MAAAyE,IAAA;MAAA+D,YAAA;MAAAoF,eAAA;IAAA;IAAAkB,QAAA;IAAA3J,QAAA,GA7afxI,EAAE,CAAAoS,oBAAA;EAAA;AA8a/F;AACA;EAAA,QAAA/I,SAAA,oBAAAA,SAAA,KA/a6FrJ,EAAE,CAAAsJ,iBAAA,CA+aJ0H,cAAc,EAAc,CAAC;IAC5GlJ,IAAI,EAAEpH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChD2I,QAAQ,EAAE,gBAAgB;MAC1BtI,IAAI,EAAE;QACF,SAAS,EAAE,wBAAwB;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,aAAa,EAAE;MACnB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExG,SAAS,EAAE,CAAC;MACpDyE,IAAI,EAAEnH,KAAK;MACX4I,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEzB,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEnH;IACV,CAAC,CAAC;IAAEkL,YAAY,EAAE,CAAC;MACf/D,IAAI,EAAEnH,KAAK;MACX4I,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE0H,eAAe,EAAE,CAAC;MAClBnJ,IAAI,EAAEnH,KAAK;MACX4I,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8I,sBAAsB,CAAC;EACzBC,UAAU,GAAGhS,MAAM,CAAC6J,YAAY,EAAE;IAAE0D,QAAQ,EAAE;EAAK,CAAC,CAAC;EACrDrJ,WAAW,GAAGlE,MAAM,CAACG,UAAU,CAAC;EAChC0N,OAAO,GAAG7N,MAAM,CAACqN,SAAS,CAAC;EAC3B5C,WAAWA,CAAA,EAAG,CAAE;EAChBmG,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACoB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAGnB,gBAAgB,CAAC,IAAI,CAAC3M,WAAW,EAAE,IAAI,CAAC2J,OAAO,CAACQ,WAAW,CAAC;IAClF;IACA,IAAI,IAAI,CAAC2D,UAAU,EAAE;MACjB1M,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACyM,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACN;EACJ;EACArL,WAAWA,CAAA,EAAG;IACV;IACA;IACA,MAAMsL,QAAQ,GAAG,IAAI,CAACF,UAAU,EAAEjI,kBAAkB;IACpD,IAAImI,QAAQ,EAAE;MACV5M,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC2M,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC;IACN;EACJ;EACA,OAAOlL,IAAI,YAAAmL,+BAAAhL,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2K,sBAAsB;EAAA;EACzH,OAAOT,IAAI,kBAhe8E5R,EAAE,CAAA6R,iBAAA;IAAA/J,IAAA,EAgeJuK;EAAsB;AACjH;AACA;EAAA,QAAAhJ,SAAA,oBAAAA,SAAA,KAle6FrJ,EAAE,CAAAsJ,iBAAA,CAkeJ+I,sBAAsB,EAAc,CAAC;IACpHvK,IAAI,EAAEpH;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA,MAAMiS,cAAc,SAASN,sBAAsB,CAAC;EAChDlQ,EAAE,GAAG7B,MAAM,CAACkB,YAAY,CAAC,CAAC2N,KAAK,CAAC,uBAAuB,CAAC;EACxDoD,MAAMA,CAAA,EAAG;IACL;IACA;IACA,IAAI,CAACD,UAAU,CAACjI,kBAAkB,EAAEuI,kBAAkB,GAAG,IAAI,CAACzQ,EAAE,CAAC;EACrE;EACAsQ,SAASA,CAAA,EAAG;IACR,IAAI,CAACH,UAAU,EAAEjI,kBAAkB,EAAEwI,qBAAqB,GAAG,IAAI,CAAC1Q,EAAE,CAAC;EACzE;EACA,OAAOoF,IAAI;IAAA,IAAAuL,2BAAA;IAAA,gBAAAC,uBAAArL,iBAAA;MAAA,QAAAoL,2BAAA,KAAAA,2BAAA,GAlf8E9S,EAAE,CAAA2H,qBAAA,CAkfQgL,cAAc,IAAAjL,iBAAA,IAAdiL,cAAc;IAAA;EAAA;EACjH,OAAOf,IAAI,kBAnf8E5R,EAAE,CAAA6R,iBAAA;IAAA/J,IAAA,EAmfJ6K,cAAc;IAAA5K,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA8K,4BAAAlR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAnfZ9B,EAAE,CAAAoI,aAAA,OAAArG,GAAA,CAAAI,EAmfS,CAAC;MAAA;IAAA;IAAA+P,MAAA;MAAA/P,EAAA;IAAA;IAAAgQ,QAAA;IAAA3J,QAAA,GAnfZxI,EAAE,CAAAyI,0BAAA;EAAA;AAof/F;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KArf6FrJ,EAAE,CAAAsJ,iBAAA,CAqfJqJ,cAAc,EAAc,CAAC;IAC5G7K,IAAI,EAAEpH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChD2I,QAAQ,EAAE,gBAAgB;MAC1BtI,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1H,EAAE,EAAE,CAAC;MACnB2F,IAAI,EAAEnH;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMsS,gBAAgB,CAAC;EACnB,OAAO1L,IAAI,YAAA2L,yBAAAxL,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuL,gBAAgB;EAAA;EACnH,OAAOrB,IAAI,kBAvgB8E5R,EAAE,CAAA6R,iBAAA;IAAA/J,IAAA,EAugBJmL,gBAAgB;IAAAlL,SAAA;IAAAC,SAAA;IAAAQ,QAAA,GAvgBdxI,EAAE,CAAAmT,uBAAA,EAugBkN1R,EAAE,CAACC,aAAa;EAAA;AACjU;AACA;EAAA,QAAA2H,SAAA,oBAAAA,SAAA,KAzgB6FrJ,EAAE,CAAAsJ,iBAAA,CAygBJ2J,gBAAgB,EAAc,CAAC;IAC9GnL,IAAI,EAAEpH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8DAA8D;MACxEK,IAAI,EAAE;QAAE,OAAO,EAAE;MAA6C,CAAC;MAC/DuJ,cAAc,EAAE,CAAC1R,aAAa;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM2R,gBAAgB,SAAShB,sBAAsB,CAAC;EAClD;AACJ;AACA;EACIiB,KAAK;EACLf,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,UAAU,CAACjI,kBAAkB,EAAEnE,yBAAyB,GAAG,CAAC,CAAC;EACtE;EACAuM,SAASA,CAAA,EAAG;IACR,IAAI,CAACH,UAAU,CAACjI,kBAAkB,EAAEnE,yBAAyB,GAAG,CAAC,CAAC,CAAC;EACvE;EACA,OAAOqB,IAAI;IAAA,IAAAgM,6BAAA;IAAA,gBAAAC,yBAAA9L,iBAAA;MAAA,QAAA6L,6BAAA,KAAAA,6BAAA,GAhiB8EvT,EAAE,CAAA2H,qBAAA,CAgiBQ0L,gBAAgB,IAAA3L,iBAAA,IAAhB2L,gBAAgB;IAAA;EAAA;EACnH,OAAOzB,IAAI,kBAjiB8E5R,EAAE,CAAA6R,iBAAA;IAAA/J,IAAA,EAiiBJuL,gBAAgB;IAAAtL,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAuL,8BAAA3R,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAjiBd9B,EAAE,CAAAuI,WAAA,uCAAAxG,GAAA,CAAAuR,KAAA,KAiiBM,OAAK,CAAC,wCAAAvR,GAAA,CAAAuR,KAAA,KAAN,QAAK,CAAC,qCAAAvR,GAAA,CAAAuR,KAAA,KAAN,KAAK,CAAC;MAAA;IAAA;IAAApB,MAAA;MAAAoB,KAAA;IAAA;IAAA9K,QAAA,GAjiBdxI,EAAE,CAAAyI,0BAAA;EAAA;AAkiB/F;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAniB6FrJ,EAAE,CAAAsJ,iBAAA,CAmiBJ+J,gBAAgB,EAAc,CAAC;IAC9GvL,IAAI,EAAEpH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8DAA8D;MACxEK,IAAI,EAAE;QACF,OAAO,EAAE,4CAA4C;QACrD,4CAA4C,EAAE,mBAAmB;QACjE,6CAA6C,EAAE,oBAAoB;QACnE,0CAA0C,EAAE;MAChD;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEyJ,KAAK,EAAE,CAAC;MACtBxL,IAAI,EAAEnH;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAASwQ,gBAAgBA,CAACuC,OAAO,EAAE/E,WAAW,EAAE;EAC5C,IAAIE,MAAM,GAAG6E,OAAO,CAACjP,aAAa,CAACkP,aAAa;EAChD,OAAO9E,MAAM,IAAI,CAACA,MAAM,CAACrJ,SAAS,CAACoO,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IACrE/E,MAAM,GAAGA,MAAM,CAAC8E,aAAa;EACjC;EACA,OAAO9E,MAAM,GAAGF,WAAW,CAAC6B,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACtO,EAAE,KAAK0M,MAAM,CAAC1M,EAAE,CAAC,GAAG,IAAI;AAC9E;AAEA,MAAM0R,UAAU,GAAG,CACf1P,kBAAkB,EAClB6M,cAAc,EACd2B,cAAc,EACdU,gBAAgB,EAChBJ,gBAAgB,CACnB;AACD,MAAMa,eAAe,CAAC;EAClB,OAAOvM,IAAI,YAAAwM,wBAAArM,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoM,eAAe;EAAA;EAClH,OAAOE,IAAI,kBAvkB8EhU,EAAE,CAAAiU,gBAAA;IAAAnM,IAAA,EAukBSgM;EAAe;EASnH,OAAOI,IAAI,kBAhlB8ElU,EAAE,CAAAmU,gBAAA;IAAAxE,SAAA,EAglBqC,CAAChC,SAAS,CAAC;IAAA/D,OAAA,GAAYlK,YAAY,EAAEG,aAAa,EAAEE,YAAY,EAAE6B,eAAe,EAAEA,eAAe;EAAA;AACtO;AACA;EAAA,QAAAyH,SAAA,oBAAAA,SAAA,KAllB6FrJ,EAAE,CAAAsJ,iBAAA,CAklBJwK,eAAe,EAAc,CAAC;IAC7GhM,IAAI,EAAElH,QAAQ;IACd2I,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAAClK,YAAY,EAAEG,aAAa,EAAEE,YAAY,EAAE6B,eAAe,EAAE,GAAGiS,UAAU,CAAC;MACpFO,OAAO,EAAE,CAACxS,eAAe,EAAE,GAAGiS,UAAU,CAAC;MACzClE,SAAS,EAAE,CAAChC,SAAS;IACzB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAS0F,gBAAgB,IAAI1R,CAAC,EAAEgK,eAAe,IAAI7K,CAAC,EAAEkQ,cAAc,IAAIqD,CAAC,EAAE1B,cAAc,IAAI2B,CAAC,EAAErB,gBAAgB,IAAIsB,CAAC,EAAEpQ,kBAAkB,IAAIqQ,CAAC,EAAElH,eAAe,IAAImH,CAAC,EAAElH,0BAA0B,IAAImH,CAAC,EAAElH,0BAA0B,IAAImH,CAAC,EAAEhH,SAAS,IAAIiH,CAAC,EAAE5S,eAAe,IAAI2O,CAAC,EAAEzG,cAAc,IAAI2K,CAAC,EAAE1K,YAAY,IAAI2K,CAAC,EAAEhB,eAAe,IAAIiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}