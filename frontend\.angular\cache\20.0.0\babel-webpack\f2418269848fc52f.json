{"ast": null, "code": "export const performanceTimestampProvider = {\n  now() {\n    return (performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["performanceTimestampProvider", "now", "delegate", "performance", "undefined"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/rxjs/dist/esm/internal/scheduler/performanceTimestampProvider.js"], "sourcesContent": ["export const performanceTimestampProvider = {\n    now() {\n        return (performanceTimestampProvider.delegate || performance).now();\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,4BAA4B,GAAG;EACxCC,GAAGA,CAAA,EAAG;IACF,OAAO,CAACD,4BAA4B,CAACE,QAAQ,IAAIC,WAAW,EAAEF,GAAG,CAAC,CAAC;EACvE,CAAC;EACDC,QAAQ,EAAEE;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}