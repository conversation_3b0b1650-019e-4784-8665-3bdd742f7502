{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/payment.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nfunction DashboardComponent_mat_card_60_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"mat-icon\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 34);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const payment_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + payment_r1.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getStatusIcon(payment_r1.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(payment_r1.description || \"Payment\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, payment_r1.createdAt, \"medium\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatCurrency(payment_r1.amount, payment_r1.currency), \" \");\n  }\n}\nfunction DashboardComponent_mat_card_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 25)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Recent Payments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5, \"Your latest transactions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 26);\n    i0.ɵɵtemplate(8, DashboardComponent_mat_card_60_div_8_Template, 12, 8, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"button\", 29);\n    i0.ɵɵtext(11, \" View All Payments \");\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentPayments);\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, paymentService) {\n    this.authService = authService;\n    this.paymentService = paymentService;\n    this.currentUser = null;\n    this.recentPayments = [];\n    this.stats = {\n      totalPayments: 0,\n      successfulPayments: 0,\n      totalAmount: 0\n    };\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.currentUserValue;\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    this.paymentService.getMyPayments().subscribe({\n      next: response => {\n        this.recentPayments = response.payments.slice(0, 5);\n        this.calculateStats(response.payments);\n      },\n      error: error => {\n        console.error('Failed to load payments:', error);\n      }\n    });\n  }\n  calculateStats(payments) {\n    this.stats.totalPayments = payments.length;\n    this.stats.successfulPayments = payments.filter(p => p.status === 'paid').length;\n    this.stats.totalAmount = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);\n  }\n  formatCurrency(amount, currency) {\n    return this.paymentService.formatCurrency(amount, currency);\n  }\n  getStatusIcon(status) {\n    return this.paymentService.getStatusIcon(status);\n  }\n  static #_ = this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.PaymentService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DashboardComponent,\n    selectors: [[\"app-dashboard\"]],\n    standalone: false,\n    decls: 88,\n    vars: 13,\n    consts: [[1, \"dashboard-container\"], [1, \"container\"], [1, \"welcome-section\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-icon\", \"success\"], [1, \"stat-icon\", \"primary\"], [1, \"actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/payment/test\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/profile\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/profile\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/payment\"], [\"class\", \"payments-card\", 4, \"ngIf\"], [1, \"security-card\"], [1, \"security-items\"], [1, \"security-item\"], [3, \"ngClass\"], [1, \"status\", 3, \"ngClass\"], [1, \"security-actions\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/profile\"], [1, \"payments-card\"], [1, \"payment-list\"], [\"class\", \"payment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/payment\"], [1, \"payment-item\"], [1, \"payment-icon\"], [1, \"payment-details\"], [1, \"payment-description\"], [1, \"payment-date\"], [1, \"payment-amount\"]],\n    template: function DashboardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Here's an overview of your account activity\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-card\", 4)(9, \"mat-card-content\")(10, \"div\", 5)(11, \"mat-icon\");\n        i0.ɵɵtext(12, \"payment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 6)(14, \"div\", 7);\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 8);\n        i0.ɵɵtext(17, \"Total Payments\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(18, \"mat-card\", 4)(19, \"mat-card-content\")(20, \"div\", 9)(21, \"mat-icon\");\n        i0.ɵɵtext(22, \"check_circle\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 6)(24, \"div\", 7);\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\", 8);\n        i0.ɵɵtext(27, \"Successful\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(28, \"mat-card\", 4)(29, \"mat-card-content\")(30, \"div\", 10)(31, \"mat-icon\");\n        i0.ɵɵtext(32, \"account_balance_wallet\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 6)(34, \"div\", 7);\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 8);\n        i0.ɵɵtext(37, \"Total Amount\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(38, \"mat-card\", 11)(39, \"mat-card-header\")(40, \"mat-card-title\");\n        i0.ɵɵtext(41, \"Quick Actions\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"mat-card-content\")(43, \"div\", 12)(44, \"button\", 13)(45, \"mat-icon\");\n        i0.ɵɵtext(46, \"payment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(47, \" Test Payment \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"button\", 14)(49, \"mat-icon\");\n        i0.ɵɵtext(50, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(51, \" Setup 2FA \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"button\", 15)(53, \"mat-icon\");\n        i0.ɵɵtext(54, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(55, \" Edit Profile \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"button\", 16)(57, \"mat-icon\");\n        i0.ɵɵtext(58, \"history\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(59, \" Payment History \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(60, DashboardComponent_mat_card_60_Template, 14, 1, \"mat-card\", 17);\n        i0.ɵɵelementStart(61, \"mat-card\", 18)(62, \"mat-card-header\")(63, \"mat-card-title\")(64, \"mat-icon\");\n        i0.ɵɵtext(65, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(66, \" Security Status \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(67, \"mat-card-content\")(68, \"div\", 19)(69, \"div\", 20)(70, \"mat-icon\", 21);\n        i0.ɵɵtext(71);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"span\");\n        i0.ɵɵtext(73, \"Email Verification\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"span\", 22);\n        i0.ɵɵtext(75);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(76, \"div\", 20)(77, \"mat-icon\", 21);\n        i0.ɵɵtext(78);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"span\");\n        i0.ɵɵtext(80, \"Two-Factor Authentication\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"span\", 22);\n        i0.ɵɵtext(82);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(83, \"div\", 23)(84, \"button\", 24)(85, \"mat-icon\");\n        i0.ɵɵtext(86, \"settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(87, \" Manage Security \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \"!\");\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate(ctx.stats.totalPayments);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.stats.successfulPayments);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate1(\"\\u20B9\", ctx.stats.totalAmount);\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"ngIf\", ctx.recentPayments.length > 0);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\", \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Pending\", \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"verified\" : \"unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"security\" : \"warning\", \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"verified\" : \"unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"Enabled\" : \"Disabled\", \" \");\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatButton, i7.MatIcon, i3.DatePipe],\n    styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: #f5f5f5;\\n  padding: 2rem;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n  font-size: 2rem;\\n  font-weight: 400;\\n}\\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1.5rem;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #e3f2fd;\\n  color: #2196f3;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .stat-icon.success[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #4caf50;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .stat-icon.primary[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  color: #9c27b0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n  line-height: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.875rem;\\n  margin-top: 0.25rem;\\n}\\n\\n.actions-card[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n}\\n.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 56px;\\n  font-size: 1rem;\\n}\\n.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.payments-card[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #f5f5f5;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]   mat-icon.status-paid[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]   mat-icon.status-pending[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]   mat-icon.status-failed[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .payment-description[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .payment-date[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]   .payment-amount[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 1.1rem;\\n}\\n.payments-card[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   mat-icon.verified[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   mat-icon.unverified[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  flex: 1;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-weight: 500;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .status.verified[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #4caf50;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .status.unverified[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  color: #ff9800;\\n}\\n.security-card[_ngcontent-%COMP%]   .security-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "payment_r1", "status", "ɵɵtextInterpolate", "ctx_r1", "getStatusIcon", "description", "ɵɵpipeBind2", "createdAt", "ɵɵtextInterpolate1", "formatCurrency", "amount", "currency", "ɵɵtemplate", "DashboardComponent_mat_card_60_div_8_Template", "recentPayments", "DashboardComponent", "constructor", "authService", "paymentService", "currentUser", "stats", "totalPayments", "successfulPayments", "totalAmount", "ngOnInit", "currentUserValue", "loadDashboardData", "getMyPayments", "subscribe", "next", "response", "payments", "slice", "calculateStats", "error", "console", "length", "filter", "p", "reduce", "sum", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "PaymentService", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_mat_card_60_Template", "firstName", "emailVerified", "twoFactorEnabled"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\modules\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\modules\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../services/auth.service';\nimport { PaymentService } from '../../services/payment.service';\nimport { User, Payment } from '../../models/index';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss'],\n  standalone: false\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: User | null = null;\n  recentPayments: Payment[] = [];\n  stats = {\n    totalPayments: 0,\n    successfulPayments: 0,\n    totalAmount: 0\n  };\n\n  constructor(\n    private authService: AuthService,\n    private paymentService: PaymentService\n  ) {}\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.currentUserValue;\n    this.loadDashboardData();\n  }\n\n  loadDashboardData(): void {\n    this.paymentService.getMyPayments().subscribe({\n      next: (response) => {\n        this.recentPayments = response.payments.slice(0, 5);\n        this.calculateStats(response.payments);\n      },\n      error: (error) => {\n        console.error('Failed to load payments:', error);\n      }\n    });\n  }\n\n  calculateStats(payments: Payment[]): void {\n    this.stats.totalPayments = payments.length;\n    this.stats.successfulPayments = payments.filter(p => p.status === 'paid').length;\n    this.stats.totalAmount = payments\n      .filter(p => p.status === 'paid')\n      .reduce((sum, p) => sum + p.amount, 0);\n  }\n\n  formatCurrency(amount: number, currency: string): string {\n    return this.paymentService.formatCurrency(amount, currency);\n  }\n\n  getStatusIcon(status: string): string {\n    return this.paymentService.getStatusIcon(status);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <div class=\"container\">\n    <!-- Welcome Section -->\n    <div class=\"welcome-section\">\n      <h1>Welcome back, {{ currentUser?.firstName }}!</h1>\n      <p>Here's an overview of your account activity</p>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"stats-grid\">\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-icon\">\n            <mat-icon>payment</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <div class=\"stat-value\">{{ stats.totalPayments }}</div>\n            <div class=\"stat-label\">Total Payments</div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-icon success\">\n            <mat-icon>check_circle</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <div class=\"stat-value\">{{ stats.successfulPayments }}</div>\n            <div class=\"stat-label\">Successful</div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-icon primary\">\n            <mat-icon>account_balance_wallet</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <div class=\"stat-value\">₹{{ stats.totalAmount }}</div>\n            <div class=\"stat-label\">Total Amount</div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Quick Actions -->\n    <mat-card class=\"actions-card\">\n      <mat-card-header>\n        <mat-card-title>Quick Actions</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"actions-grid\">\n          <button mat-raised-button color=\"primary\" routerLink=\"/payment/test\">\n            <mat-icon>payment</mat-icon>\n            Test Payment\n          </button>\n          \n          <button mat-raised-button color=\"accent\" routerLink=\"/profile\">\n            <mat-icon>security</mat-icon>\n            Setup 2FA\n          </button>\n          \n          <button mat-raised-button routerLink=\"/profile\">\n            <mat-icon>person</mat-icon>\n            Edit Profile\n          </button>\n          \n          <button mat-raised-button routerLink=\"/payment\">\n            <mat-icon>history</mat-icon>\n            Payment History\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Recent Payments -->\n    <mat-card class=\"payments-card\" *ngIf=\"recentPayments.length > 0\">\n      <mat-card-header>\n        <mat-card-title>Recent Payments</mat-card-title>\n        <mat-card-subtitle>Your latest transactions</mat-card-subtitle>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"payment-list\">\n          <div *ngFor=\"let payment of recentPayments\" class=\"payment-item\">\n            <div class=\"payment-icon\">\n              <mat-icon [ngClass]=\"'status-' + payment.status\">{{ getStatusIcon(payment.status) }}</mat-icon>\n            </div>\n            <div class=\"payment-details\">\n              <div class=\"payment-description\">{{ payment.description || 'Payment' }}</div>\n              <div class=\"payment-date\">{{ payment.createdAt | date:'medium' }}</div>\n            </div>\n            <div class=\"payment-amount\">\n              {{ formatCurrency(payment.amount, payment.currency) }}\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"view-all\">\n          <button mat-button color=\"primary\" routerLink=\"/payment\">\n            View All Payments\n            <mat-icon>arrow_forward</mat-icon>\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Security Status -->\n    <mat-card class=\"security-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>security</mat-icon>\n          Security Status\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"security-items\">\n          <div class=\"security-item\">\n            <mat-icon [ngClass]=\"currentUser?.emailVerified ? 'verified' : 'unverified'\">\n              {{ currentUser?.emailVerified ? 'verified' : 'warning' }}\n            </mat-icon>\n            <span>Email Verification</span>\n            <span class=\"status\" [ngClass]=\"currentUser?.emailVerified ? 'verified' : 'unverified'\">\n              {{ currentUser?.emailVerified ? 'Verified' : 'Pending' }}\n            </span>\n          </div>\n          \n          <div class=\"security-item\">\n            <mat-icon [ngClass]=\"currentUser?.twoFactorEnabled ? 'verified' : 'unverified'\">\n              {{ currentUser?.twoFactorEnabled ? 'security' : 'warning' }}\n            </mat-icon>\n            <span>Two-Factor Authentication</span>\n            <span class=\"status\" [ngClass]=\"currentUser?.twoFactorEnabled ? 'verified' : 'unverified'\">\n              {{ currentUser?.twoFactorEnabled ? 'Enabled' : 'Disabled' }}\n            </span>\n          </div>\n        </div>\n        \n        <div class=\"security-actions\">\n          <button mat-button color=\"primary\" routerLink=\"/profile\">\n            <mat-icon>settings</mat-icon>\n            Manage Security\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;ICuFcA,EAFJ,CAAAC,cAAA,cAAiE,cACrC,mBACyB;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACtFF,EADsF,CAAAG,YAAA,EAAW,EAC3F;IAEJH,EADF,CAAAC,cAAA,cAA6B,cACM;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAuC;;IACnEF,EADmE,CAAAG,YAAA,EAAM,EACnE;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IATQH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,UAAA,wBAAAC,UAAA,CAAAC,MAAA,CAAsC;IAACP,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAJ,UAAA,CAAAC,MAAA,EAAmC;IAGnDP,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAQ,iBAAA,CAAAF,UAAA,CAAAK,WAAA,cAAsC;IAC7CX,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAY,WAAA,OAAAN,UAAA,CAAAO,SAAA,YAAuC;IAGjEb,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,cAAA,CAAAT,UAAA,CAAAU,MAAA,EAAAV,UAAA,CAAAW,QAAA,OACF;;;;;IAfJjB,EAFJ,CAAAC,cAAA,mBAAkE,sBAC/C,qBACC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAChDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAC7CF,EAD6C,CAAAG,YAAA,EAAoB,EAC/C;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,cACU;IACxBD,EAAA,CAAAkB,UAAA,IAAAC,6CAAA,mBAAiE;IAYnEnB,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAAsB,kBACqC;IACvDD,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAI/BF,EAJ+B,CAAAG,YAAA,EAAW,EAC3B,EACL,EACW,EACV;;;;IArBoBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,UAAA,YAAAI,MAAA,CAAAW,cAAA,CAAiB;;;AD1EpD,OAAM,MAAOC,kBAAkB;EAS7BC,YACUC,WAAwB,EACxBC,cAA8B;IAD9B,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IAVxB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAL,cAAc,GAAc,EAAE;IAC9B,KAAAM,KAAK,GAAG;MACNC,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE,CAAC;MACrBC,WAAW,EAAE;KACd;EAKE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACL,WAAW,GAAG,IAAI,CAACF,WAAW,CAACQ,gBAAgB;IACpD,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACR,cAAc,CAACS,aAAa,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAChB,cAAc,GAAGgB,QAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,QAAQ,CAAC;MACxC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACJ;EAEAD,cAAcA,CAACF,QAAmB;IAChC,IAAI,CAACX,KAAK,CAACC,aAAa,GAAGU,QAAQ,CAACK,MAAM;IAC1C,IAAI,CAAChB,KAAK,CAACE,kBAAkB,GAAGS,QAAQ,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,MAAM,CAAC,CAACmC,MAAM;IAChF,IAAI,CAAChB,KAAK,CAACG,WAAW,GAAGQ,QAAQ,CAC9BM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,MAAM,CAAC,CAChCsC,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAAC5B,MAAM,EAAE,CAAC,CAAC;EAC1C;EAEAD,cAAcA,CAACC,MAAc,EAAEC,QAAgB;IAC7C,OAAO,IAAI,CAACO,cAAc,CAACT,cAAc,CAACC,MAAM,EAAEC,QAAQ,CAAC;EAC7D;EAEAP,aAAaA,CAACH,MAAc;IAC1B,OAAO,IAAI,CAACiB,cAAc,CAACd,aAAa,CAACH,MAAM,CAAC;EAClD;EAAC,QAAAwC,CAAA,G;qCA7CU1B,kBAAkB,EAAArB,EAAA,CAAAgD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlD,EAAA,CAAAgD,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBhC,kBAAkB;IAAAiC,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPzB7D,EAJN,CAAAC,cAAA,aAAiC,aACR,aAEQ,SACvB;QAAAD,EAAA,CAAAE,MAAA,GAA2C;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpDH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,kDAA2C;QAChDF,EADgD,CAAAG,YAAA,EAAI,EAC9C;QAOEH,EAJR,CAAAC,cAAA,aAAwB,kBACM,uBACR,cACO,gBACX;QAAAD,EAAA,CAAAE,MAAA,eAAO;QACnBF,EADmB,CAAAG,YAAA,EAAW,EACxB;QAEJH,EADF,CAAAC,cAAA,cAAuB,cACG;QAAAD,EAAA,CAAAE,MAAA,IAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACvDH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAG5CF,EAH4C,CAAAG,YAAA,EAAM,EACxC,EACW,EACV;QAKLH,EAHN,CAAAC,cAAA,mBAA4B,wBACR,cACe,gBACnB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;QAEJH,EADF,CAAAC,cAAA,cAAuB,cACG;QAAAD,EAAA,CAAAE,MAAA,IAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC5DH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAGxCF,EAHwC,CAAAG,YAAA,EAAM,EACpC,EACW,EACV;QAKLH,EAHN,CAAAC,cAAA,mBAA4B,wBACR,eACe,gBACnB;QAAAD,EAAA,CAAAE,MAAA,8BAAsB;QAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;QAEJH,EADF,CAAAC,cAAA,cAAuB,cACG;QAAAD,EAAA,CAAAE,MAAA,IAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACtDH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAI5CF,EAJ4C,CAAAG,YAAA,EAAM,EACtC,EACW,EACV,EACP;QAKFH,EAFJ,CAAAC,cAAA,oBAA+B,uBACZ,sBACC;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAC/BF,EAD+B,CAAAG,YAAA,EAAiB,EAC9B;QAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACU,kBAC6C,gBACzD;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5BH,EAAA,CAAAE,MAAA,sBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGPH,EADF,CAAAC,cAAA,kBAA+D,gBACnD;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,mBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGPH,EADF,CAAAC,cAAA,kBAAgD,gBACpC;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,sBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGPH,EADF,CAAAC,cAAA,kBAAgD,gBACpC;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5BH,EAAA,CAAAE,MAAA,yBACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;QAGXH,EAAA,CAAAkB,UAAA,KAAA6C,uCAAA,wBAAkE;QAkC5D/D,EAHN,CAAAC,cAAA,oBAAgC,uBACb,sBACC,gBACJ;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,yBACF;QACFF,EADE,CAAAG,YAAA,EAAiB,EACD;QAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACY,eACC,oBACoD;QAC3ED,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACXH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC/BH,EAAA,CAAAC,cAAA,gBAAwF;QACtFD,EAAA,CAAAE,MAAA,IACF;QACFF,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,eAA2B,oBACuD;QAC9ED,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACXH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,iCAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACtCH,EAAA,CAAAC,cAAA,gBAA2F;QACzFD,EAAA,CAAAE,MAAA,IACF;QAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;QAIFH,EAFJ,CAAAC,cAAA,eAA8B,kBAC6B,gBAC7C;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,yBACF;QAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP,EACF;;;QAhJIH,EAAA,CAAAI,SAAA,GAA2C;QAA3CJ,EAAA,CAAAc,kBAAA,mBAAAgD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAuC,SAAA,MAA2C;QAYjBhE,EAAA,CAAAI,SAAA,IAAyB;QAAzBJ,EAAA,CAAAQ,iBAAA,CAAAsD,GAAA,CAAApC,KAAA,CAAAC,aAAA,CAAyB;QAYzB3B,EAAA,CAAAI,SAAA,IAA8B;QAA9BJ,EAAA,CAAAQ,iBAAA,CAAAsD,GAAA,CAAApC,KAAA,CAAAE,kBAAA,CAA8B;QAY9B5B,EAAA,CAAAI,SAAA,IAAwB;QAAxBJ,EAAA,CAAAc,kBAAA,WAAAgD,GAAA,CAAApC,KAAA,CAAAG,WAAA,CAAwB;QAsCvB7B,EAAA,CAAAI,SAAA,IAA+B;QAA/BJ,EAAA,CAAAK,UAAA,SAAAyD,GAAA,CAAA1C,cAAA,CAAAsB,MAAA,KAA+B;QAyC9C1C,EAAA,CAAAI,SAAA,IAAkE;QAAlEJ,EAAA,CAAAK,UAAA,aAAAyD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAwC,aAAA,8BAAkE;QAC1EjE,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAc,kBAAA,OAAAgD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAwC,aAAA,gCACF;QAEqBjE,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAK,UAAA,aAAAyD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAwC,aAAA,8BAAkE;QACrFjE,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAc,kBAAA,OAAAgD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAwC,aAAA,gCACF;QAIUjE,EAAA,CAAAI,SAAA,GAAqE;QAArEJ,EAAA,CAAAK,UAAA,aAAAyD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAyC,gBAAA,8BAAqE;QAC7ElE,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAc,kBAAA,OAAAgD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAyC,gBAAA,gCACF;QAEqBlE,EAAA,CAAAI,SAAA,GAAqE;QAArEJ,EAAA,CAAAK,UAAA,aAAAyD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAyC,gBAAA,8BAAqE;QACxFlE,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAc,kBAAA,OAAAgD,GAAA,CAAArC,WAAA,kBAAAqC,GAAA,CAAArC,WAAA,CAAAyC,gBAAA,gCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}