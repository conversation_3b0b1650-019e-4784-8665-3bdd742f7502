{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction ForgotPasswordComponent_div_10_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_10_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_10_mat_spinner_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"diameter\", 20);\n  }\n}\nfunction ForgotPasswordComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_div_10_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"mat-form-field\", 6)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 7);\n    i0.ɵɵelementStart(6, \"mat-icon\", 8);\n    i0.ɵɵtext(7, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ForgotPasswordComponent_div_10_mat_error_8_Template, 2, 0, \"mat-error\", 2)(9, ForgotPasswordComponent_div_10_mat_error_9_Template, 2, 0, \"mat-error\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 9);\n    i0.ɵɵtemplate(11, ForgotPasswordComponent_div_10_mat_spinner_11_Template, 1, 1, \"mat-spinner\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.forgotPasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_3_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_4_0.hasError(\"email\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.forgotPasswordForm.invalid || ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoading ? \"Sending...\" : \"Send Reset Instructions\", \" \");\n  }\n}\nfunction ForgotPasswordComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\", 13);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Check Your Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \" We've sent password reset instructions to your email address. Please check your inbox and follow the link to reset your password. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 14)(8, \"strong\");\n    i0.ɵɵtext(9, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" If you don't see the email in your inbox, please check your spam or junk folder. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ForgotPasswordComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.isEmailSent = false;\n  }\n  ngOnInit() {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  onSubmit() {\n    if (this.forgotPasswordForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      const email = this.forgotPasswordForm.get('email')?.value;\n      this.authService.forgotPassword(email).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.isEmailSent = true;\n          this.snackBar.open('Password reset instructions have been sent to your email address', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n        },\n        error: error => {\n          this.isLoading = false;\n          const errorMessage = error?.error?.message || 'Failed to send reset email. Please try again.';\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n  }\n  goBackToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static #_ = this.ɵfac = function ForgotPasswordComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ForgotPasswordComponent,\n    selectors: [[\"app-forgot-password\"]],\n    standalone: false,\n    decls: 17,\n    vars: 2,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\"], [4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"accent\", 1, \"full-width\", 3, \"click\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email address\"], [\"matSuffix\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"submit-btn\", 3, \"disabled\"], [\"style\", \"display: inline-block; margin-right: 8px;\", 3, \"diameter\", 4, \"ngIf\"], [2, \"display\", \"inline-block\", \"margin-right\", \"8px\", 3, \"diameter\"], [1, \"success-message\"], [1, \"success-icon\"], [1, \"note\"]],\n    template: function ForgotPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n        i0.ɵɵtext(5, \"lock_reset\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Forgot Password \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n        i0.ɵɵtext(8, \" Enter your email address to receive password reset instructions \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"mat-card-content\");\n        i0.ɵɵtemplate(10, ForgotPasswordComponent_div_10_Template, 13, 8, \"div\", 2)(11, ForgotPasswordComponent_div_11_Template, 11, 0, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"mat-card-actions\")(13, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ForgotPasswordComponent_Template_button_click_13_listener() {\n          return ctx.goBackToLogin();\n        });\n        i0.ɵɵelementStart(14, \"mat-icon\");\n        i0.ɵɵtext(15, \"arrow_back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(16, \" Back to Login \");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEmailSent);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isEmailSent);\n      }\n    },\n    dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i10.MatIcon, i11.MatProgressSpinner],\n    styles: [\".auth-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  border-radius: 12px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.auth-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 10px;\\n}\\n\\n.success-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px 0;\\n}\\n.success-message[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #4caf50;\\n  margin-bottom: 16px;\\n}\\n.success-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 16px;\\n  font-weight: 500;\\n}\\n.success-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.5;\\n  margin-bottom: 12px;\\n}\\n.success-message[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  background-color: #f5f5f5;\\n  padding: 12px;\\n  border-radius: 6px;\\n  border-left: 4px solid #2196f3;\\n}\\n\\nmat-card-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-bottom: 0;\\n}\\nmat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  font-size: 24px;\\n  color: #333;\\n}\\nmat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\nmat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-top: 8px;\\n  font-size: 14px;\\n}\\n\\nmat-card-actions[_ngcontent-%COMP%] {\\n  padding-top: 0;\\n}\\n\\n.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #f44336;\\n}\\n\\n@media (max-width: 600px) {\\n  .auth-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .auth-card[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵproperty", "ɵɵlistener", "ForgotPasswordComponent_div_10_Template_form_ngSubmit_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵtemplate", "ForgotPasswordComponent_div_10_mat_error_8_Template", "ForgotPasswordComponent_div_10_mat_error_9_Template", "ForgotPasswordComponent_div_10_mat_spinner_11_Template", "ɵɵadvance", "forgotPasswordForm", "ɵɵclassProp", "tmp_2_0", "get", "invalid", "touched", "tmp_3_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_4_0", "isLoading", "ɵɵtextInterpolate1", "ForgotPasswordComponent", "constructor", "fb", "authService", "router", "snackBar", "isEmailSent", "ngOnInit", "group", "email", "required", "valid", "value", "forgotPassword", "subscribe", "next", "response", "open", "duration", "panelClass", "error", "errorMessage", "message", "goBackToLogin", "navigate", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ForgotPasswordComponent_div_10_Template", "ForgotPasswordComponent_div_11_Template", "ForgotPasswordComponent_Template_button_click_13_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\forgot-password\\forgot-password.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-forgot-password',\r\n  templateUrl: './forgot-password.component.html',\r\n  styleUrls: ['./forgot-password.component.scss'],\r\n  standalone: false\r\n})\r\nexport class ForgotPasswordComponent implements OnInit {\r\n  forgotPasswordForm!: FormGroup;\r\n  isLoading = false;\r\n  isEmailSent = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.forgotPasswordForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]]\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.forgotPasswordForm.valid && !this.isLoading) {\r\n      this.isLoading = true;\r\n      const email = this.forgotPasswordForm.get('email')?.value;\r\n\r\n      this.authService.forgotPassword(email).subscribe({\r\n        next: (response) => {\r\n          this.isLoading = false;\r\n          this.isEmailSent = true;\r\n          this.snackBar.open(\r\n            'Password reset instructions have been sent to your email address',\r\n            'Close',\r\n            { duration: 5000, panelClass: ['success-snackbar'] }\r\n          );\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          const errorMessage = error?.error?.message || 'Failed to send reset email. Please try again.';\r\n          this.snackBar.open(\r\n            errorMessage,\r\n            'Close',\r\n            { duration: 5000, panelClass: ['error-snackbar'] }\r\n          );\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  goBackToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n}\r\n", "<div class=\"auth-container\">\r\n  <mat-card class=\"auth-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>lock_reset</mat-icon>\r\n        Forgot Password\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Enter your email address to receive password reset instructions\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <div *ngIf=\"!isEmailSent\">\r\n        <form [formGroup]=\"forgotPasswordForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Email Address</mat-label>\r\n            <input \r\n              matInput \r\n              type=\"email\" \r\n              formControlName=\"email\"\r\n              placeholder=\"Enter your email address\"\r\n              [class.is-invalid]=\"forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched\">\r\n            <mat-icon matSuffix>email</mat-icon>\r\n            <mat-error *ngIf=\"forgotPasswordForm.get('email')?.hasError('required')\">\r\n              Email is required\r\n            </mat-error>\r\n            <mat-error *ngIf=\"forgotPasswordForm.get('email')?.hasError('email')\">\r\n              Please enter a valid email address\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <button \r\n            mat-raised-button \r\n            color=\"primary\" \r\n            type=\"submit\"\r\n            class=\"full-width submit-btn\"\r\n            [disabled]=\"forgotPasswordForm.invalid || isLoading\">\r\n            <mat-spinner \r\n              *ngIf=\"isLoading\" \r\n              [diameter]=\"20\" \r\n              style=\"display: inline-block; margin-right: 8px;\">\r\n            </mat-spinner>\r\n            {{ isLoading ? 'Sending...' : 'Send Reset Instructions' }}\r\n          </button>\r\n        </form>\r\n      </div>\r\n\r\n      <div *ngIf=\"isEmailSent\" class=\"success-message\">\r\n        <mat-icon class=\"success-icon\">check_circle</mat-icon>\r\n        <h3>Check Your Email</h3>\r\n        <p>\r\n          We've sent password reset instructions to your email address. \r\n          Please check your inbox and follow the link to reset your password.\r\n        </p>\r\n        <p class=\"note\">\r\n          <strong>Note:</strong> If you don't see the email in your inbox, \r\n          please check your spam or junk folder.\r\n        </p>\r\n      </div>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions>\r\n      <button \r\n        mat-button \r\n        color=\"accent\" \r\n        (click)=\"goBackToLogin()\"\r\n        class=\"full-width\">\r\n        <mat-icon>arrow_back</mat-icon>\r\n        Back to Login\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICuBvDC,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASZH,EAAA,CAAAI,SAAA,sBAIc;;;IAFZJ,EAAA,CAAAK,UAAA,gBAAe;;;;;;IA1BrBL,EADF,CAAAC,cAAA,UAA0B,cACyD;IAA1CD,EAAA,CAAAM,UAAA,sBAAAC,iEAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAYF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAE1Db,EADF,CAAAC,cAAA,wBAAwD,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAI,SAAA,eAK4G;IAC5GJ,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAIpCH,EAHA,CAAAc,UAAA,IAAAC,mDAAA,uBAAyE,IAAAC,mDAAA,uBAGH;IAGxEhB,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAC,cAAA,iBAKuD;IACrDD,EAAA,CAAAc,UAAA,KAAAG,sDAAA,0BAGoD;IAEpDjB,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;;;;;;;IAhCEH,EAAA,CAAAkB,SAAA,EAAgC;IAAhClB,EAAA,CAAAK,UAAA,cAAAK,MAAA,CAAAS,kBAAA,CAAgC;IAQhCnB,EAAA,CAAAkB,SAAA,GAAyG;IAAzGlB,EAAA,CAAAoB,WAAA,iBAAAC,OAAA,GAAAX,MAAA,CAAAS,kBAAA,CAAAG,GAAA,4BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAX,MAAA,CAAAS,kBAAA,CAAAG,GAAA,4BAAAD,OAAA,CAAAG,OAAA,EAAyG;IAE/FxB,EAAA,CAAAkB,SAAA,GAA2D;IAA3DlB,EAAA,CAAAK,UAAA,UAAAoB,OAAA,GAAAf,MAAA,CAAAS,kBAAA,CAAAG,GAAA,4BAAAG,OAAA,CAAAC,QAAA,aAA2D;IAG3D1B,EAAA,CAAAkB,SAAA,EAAwD;IAAxDlB,EAAA,CAAAK,UAAA,UAAAsB,OAAA,GAAAjB,MAAA,CAAAS,kBAAA,CAAAG,GAAA,4BAAAK,OAAA,CAAAD,QAAA,UAAwD;IAUpE1B,EAAA,CAAAkB,SAAA,EAAoD;IAApDlB,EAAA,CAAAK,UAAA,aAAAK,MAAA,CAAAS,kBAAA,CAAAI,OAAA,IAAAb,MAAA,CAAAkB,SAAA,CAAoD;IAEjD5B,EAAA,CAAAkB,SAAA,EAAe;IAAflB,EAAA,CAAAK,UAAA,SAAAK,MAAA,CAAAkB,SAAA,CAAe;IAIlB5B,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAA6B,kBAAA,MAAAnB,MAAA,CAAAkB,SAAA,iDACF;;;;;IAKF5B,EADF,CAAAC,cAAA,cAAiD,mBAChB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,0IAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,YAAgB,aACN;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,0FAEzB;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;;;AD/CZ,OAAM,MAAO2B,uBAAuB;EAKlCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAP,SAAS,GAAG,KAAK;IACjB,KAAAQ,WAAW,GAAG,KAAK;EAOhB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAClB,kBAAkB,GAAG,IAAI,CAACa,EAAE,CAACM,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACyC,QAAQ,EAAEzC,UAAU,CAACwC,KAAK,CAAC;KACpD,CAAC;EACJ;EAEA1B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACM,kBAAkB,CAACsB,KAAK,IAAI,CAAC,IAAI,CAACb,SAAS,EAAE;MACpD,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,MAAMW,KAAK,GAAG,IAAI,CAACpB,kBAAkB,CAACG,GAAG,CAAC,OAAO,CAAC,EAAEoB,KAAK;MAEzD,IAAI,CAACT,WAAW,CAACU,cAAc,CAACJ,KAAK,CAAC,CAACK,SAAS,CAAC;QAC/CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACQ,WAAW,GAAG,IAAI;UACvB,IAAI,CAACD,QAAQ,CAACY,IAAI,CAChB,kEAAkE,EAClE,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,kBAAkB;UAAC,CAAE,CACrD;QACH,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,MAAMuB,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEE,OAAO,IAAI,+CAA+C;UAC7F,IAAI,CAACjB,QAAQ,CAACY,IAAI,CAChBI,YAAY,EACZ,OAAO,EACP;YAAEH,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,gBAAgB;UAAC,CAAE,CACnD;QACH;OACD,CAAC;IACJ;EACF;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAAC,QAAAC,CAAA,G;qCAhDUzB,uBAAuB,EAAA9B,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1D,EAAA,CAAAwD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5D,EAAA,CAAAwD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9D,EAAA,CAAAwD,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBnC,uBAAuB;IAAAoC,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR5BzE,EAJR,CAAAC,cAAA,aAA4B,kBACE,sBACT,qBACC,eACJ;QAAAD,EAAA,CAAAE,MAAA,iBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAE,MAAA,wBACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,wBAAmB;QACjBD,EAAA,CAAAE,MAAA,wEACF;QACFF,EADE,CAAAG,YAAA,EAAoB,EACJ;QAElBH,EAAA,CAAAC,cAAA,uBAAkB;QAoChBD,EAnCA,CAAAc,UAAA,KAAA6D,uCAAA,kBAA0B,KAAAC,uCAAA,kBAmCuB;QAYnD5E,EAAA,CAAAG,YAAA,EAAmB;QAGjBH,EADF,CAAAC,cAAA,wBAAkB,iBAKK;QADnBD,EAAA,CAAAM,UAAA,mBAAAuE,0DAAA;UAAA,OAASH,GAAA,CAAArB,aAAA,EAAe;QAAA,EAAC;QAEzBrD,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAE,MAAA,uBACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACQ,EACV,EACP;;;QA5DMH,EAAA,CAAAkB,SAAA,IAAkB;QAAlBlB,EAAA,CAAAK,UAAA,UAAAqE,GAAA,CAAAtC,WAAA,CAAkB;QAmClBpC,EAAA,CAAAkB,SAAA,EAAiB;QAAjBlB,EAAA,CAAAK,UAAA,SAAAqE,GAAA,CAAAtC,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}