{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, booleanAttribute, Directive, Input, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** Injection token that can be used to provide the default options the button component. */\nconst _c0 = [\"mat-icon-button\", \"\"];\nconst _c1 = [\"*\"];\nconst MAT_BUTTON_CONFIG = new InjectionToken('MAT_BUTTON_CONFIG');\nfunction transformTabIndex(value) {\n  return value == null ? undefined : numberAttribute(value);\n}\n/** Base class for all buttons. */\nclass MatButtonBase {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _animationsDisabled = _animationsDisabled();\n  _config = inject(MAT_BUTTON_CONFIG, {\n    optional: true\n  });\n  _focusMonitor = inject(FocusMonitor);\n  _cleanupClick;\n  _renderer = inject(Renderer2);\n  /**\n   * Handles the lazy creation of the MatButton ripple.\n   * Used to improve initial load time of large applications.\n   */\n  _rippleLoader = inject(MatRippleLoader);\n  /** Whether the button is set on an anchor node. */\n  _isAnchor;\n  /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n  _isFab = false;\n  /**\n   * Theme color of the button. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = value;\n    this._updateRippleDisabled();\n  }\n  _disableRipple = false;\n  /** Whether the button is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._updateRippleDisabled();\n  }\n  _disabled = false;\n  /** `aria-disabled` value of the button. */\n  ariaDisabled;\n  /**\n   * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n   * In some scenarios this might not be desirable, because it can prevent users from finding out\n   * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n   * become disabled when activated, which would cause focus to be transferred to the document\n   * body instead of remaining on the button.\n   *\n   * Enabling this input will change the button so that it is styled to be disabled and will be\n   * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n   *\n   * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n   * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n   */\n  disabledInteractive;\n  /** Tab index for the button. */\n  tabIndex;\n  /**\n   * Backwards-compatibility input that handles pre-existing `[tabindex]` bindings.\n   * @docs-private\n   */\n  set _tabindex(value) {\n    this.tabIndex = value;\n  }\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const element = this._elementRef.nativeElement;\n    this._isAnchor = element.tagName === 'A';\n    this.disabledInteractive = this._config?.disabledInteractive ?? false;\n    this.color = this._config?.color ?? null;\n    this._rippleLoader?.configureRipple(element, {\n      className: 'mat-mdc-button-ripple'\n    });\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n    // Some internal tests depend on the timing of this,\n    // otherwise we could bind it in the constructor.\n    if (this._isAnchor) {\n      this._setupAsAnchor();\n    }\n  }\n  ngOnDestroy() {\n    this._cleanupClick?.();\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n  }\n  /** Focuses the button. */\n  focus(origin = 'program', options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n  _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n    if (this._isAnchor) {\n      return this.disabled || null;\n    }\n    return this.disabled && this.disabledInteractive ? true : null;\n  }\n  _getDisabledAttribute() {\n    return this.disabledInteractive || !this.disabled ? null : true;\n  }\n  _updateRippleDisabled() {\n    this._rippleLoader?.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n  }\n  _getTabIndex() {\n    if (this._isAnchor) {\n      return this.disabled && !this.disabledInteractive ? -1 : this.tabIndex;\n    }\n    return this.tabIndex;\n  }\n  _setupAsAnchor() {\n    this._cleanupClick = this._ngZone.runOutsideAngular(() => this._renderer.listen(this._elementRef.nativeElement, 'click', event => {\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n      }\n    }));\n  }\n  static ɵfac = function MatButtonBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatButtonBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatButtonBase,\n    hostAttrs: [1, \"mat-mdc-button-base\"],\n    hostVars: 13,\n    hostBindings: function MatButtonBase_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled())(\"tabindex\", ctx._getTabIndex());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"mat-unthemed\", !ctx.color)(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      color: \"color\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      ariaDisabled: [2, \"aria-disabled\", \"ariaDisabled\", booleanAttribute],\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", transformTabIndex],\n      _tabindex: [2, \"tabindex\", \"_tabindex\", transformTabIndex]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        // Add a class that applies to all buttons. This makes it easier to target if somebody\n        // wants to target all Material buttons.\n        'class': 'mat-mdc-button-base',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[attr.disabled]': '_getDisabledAttribute()',\n        '[attr.aria-disabled]': '_getAriaDisabled()',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[class.mat-mdc-button-disabled]': 'disabled',\n        '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n        '[class.mat-unthemed]': '!color',\n        '[class._mat-animation-noopable]': '_animationsDisabled'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute,\n        alias: 'aria-disabled'\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: transformTabIndex\n      }]\n    }],\n    _tabindex: [{\n      type: Input,\n      args: [{\n        alias: 'tabindex',\n        transform: transformTabIndex\n      }]\n    }]\n  });\n})();\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconButton extends MatButtonBase {\n  constructor() {\n    super();\n    this._rippleLoader.configureRipple(this._elementRef.nativeElement, {\n      centered: true\n    });\n  }\n  static ɵfac = function MatIconButton_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIconButton)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatIconButton,\n    selectors: [[\"button\", \"mat-icon-button\", \"\"], [\"a\", \"mat-icon-button\", \"\"], [\"button\", \"matIconButton\", \"\"], [\"a\", \"matIconButton\", \"\"]],\n    hostAttrs: [1, \"mdc-icon-button\", \"mat-mdc-icon-button\"],\n    exportAs: [\"matButton\", \"matAnchor\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 0,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatIconButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n      }\n    },\n    styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%));flex-shrink:0;text-align:center;width:var(--mat-icon-button-state-layer-size, 40px);height:var(--mat-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mat-icon-button-state-layer-size, 40px) - var(--mat-icon-button-icon-size, 24px)) / 2);font-size:var(--mat-icon-button-icon-size, 24px);color:var(--mat-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-icon-button-touch-target-display, block);left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mat-icon-button-icon-size, 24px);height:var(--mat-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%))}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconButton, [{\n    type: Component,\n    args: [{\n      selector: `button[mat-icon-button], a[mat-icon-button], button[matIconButton], a[matIconButton]`,\n      host: {\n        'class': 'mdc-icon-button mat-mdc-icon-button'\n      },\n      exportAs: 'matButton, matAnchor',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%));flex-shrink:0;text-align:center;width:var(--mat-icon-button-state-layer-size, 40px);height:var(--mat-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mat-icon-button-state-layer-size, 40px) - var(--mat-icon-button-icon-size, 24px)) / 2);font-size:var(--mat-icon-button-icon-size, 24px);color:var(--mat-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-icon-button-touch-target-display, block);left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mat-icon-button-icon-size, 24px);height:var(--mat-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%))}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], () => [], null);\n})();\n// tslint:disable:variable-name\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nconst MatIconAnchor = MatIconButton;\nexport { MatIconButton as M, MatButtonBase as a, MAT_BUTTON_CONFIG as b, MatIconAnchor as c };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "NgZone", "Renderer2", "booleanAttribute", "Directive", "Input", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "FocusMonitor", "_CdkPrivateStyleLoader", "M", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "_StructuralStylesLoader", "_animationsDisabled", "_c0", "_c1", "MAT_BUTTON_CONFIG", "transformTabIndex", "value", "undefined", "MatButtonBase", "_elementRef", "_ngZone", "_config", "optional", "_focusMonitor", "_cleanupClick", "_renderer", "_ripple<PERSON><PERSON>der", "_isAnchor", "_isFab", "color", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "_updateRippleDisabled", "disabled", "_disabled", "ariaDisabled", "disabledInteractive", "tabIndex", "_tabindex", "constructor", "load", "element", "nativeElement", "tagName", "configureRipple", "className", "ngAfterViewInit", "monitor", "_setupAsAnchor", "ngOnDestroy", "stopMonitoring", "destroyRipple", "focus", "origin", "options", "focusVia", "_getAriaDisabled", "_getDisabledAttribute", "setDisabled", "_getTabIndex", "runOutsideAngular", "listen", "event", "preventDefault", "stopImmediatePropagation", "ɵfac", "MatButtonBase_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "hostAttrs", "hostVars", "hostBindings", "MatButtonBase_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "host", "transform", "alias", "MatIconButton", "centered", "MatIconButton_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "exportAs", "features", "ɵɵInheritDefinitionFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatIconButton_Template", "ɵɵprojectionDef", "ɵɵelement", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "selector", "None", "OnPush", "MatIconAnchor", "a", "b", "c"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/material/fesm2022/icon-button-DxiIc1ex.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, booleanAttribute, Directive, Input, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** Injection token that can be used to provide the default options the button component. */\nconst MAT_BUTTON_CONFIG = new InjectionToken('MAT_BUTTON_CONFIG');\nfunction transformTabIndex(value) {\n    return value == null ? undefined : numberAttribute(value);\n}\n/** Base class for all buttons. */\nclass MatButtonBase {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _animationsDisabled = _animationsDisabled();\n    _config = inject(MAT_BUTTON_CONFIG, { optional: true });\n    _focusMonitor = inject(FocusMonitor);\n    _cleanupClick;\n    _renderer = inject(Renderer2);\n    /**\n     * Handles the lazy creation of the MatButton ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _rippleLoader = inject(MatRippleLoader);\n    /** Whether the button is set on an anchor node. */\n    _isAnchor;\n    /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n    _isFab = false;\n    /**\n     * Theme color of the button. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = value;\n        this._updateRippleDisabled();\n    }\n    _disableRipple = false;\n    /** Whether the button is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._updateRippleDisabled();\n    }\n    _disabled = false;\n    /** `aria-disabled` value of the button. */\n    ariaDisabled;\n    /**\n     * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n     * In some scenarios this might not be desirable, because it can prevent users from finding out\n     * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n     * become disabled when activated, which would cause focus to be transferred to the document\n     * body instead of remaining on the button.\n     *\n     * Enabling this input will change the button so that it is styled to be disabled and will be\n     * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n     *\n     * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n     * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n     */\n    disabledInteractive;\n    /** Tab index for the button. */\n    tabIndex;\n    /**\n     * Backwards-compatibility input that handles pre-existing `[tabindex]` bindings.\n     * @docs-private\n     */\n    set _tabindex(value) {\n        this.tabIndex = value;\n    }\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const element = this._elementRef.nativeElement;\n        this._isAnchor = element.tagName === 'A';\n        this.disabledInteractive = this._config?.disabledInteractive ?? false;\n        this.color = this._config?.color ?? null;\n        this._rippleLoader?.configureRipple(element, { className: 'mat-mdc-button-ripple' });\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n        // Some internal tests depend on the timing of this,\n        // otherwise we could bind it in the constructor.\n        if (this._isAnchor) {\n            this._setupAsAnchor();\n        }\n    }\n    ngOnDestroy() {\n        this._cleanupClick?.();\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n    }\n    /** Focuses the button. */\n    focus(origin = 'program', options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n        }\n        else {\n            this._elementRef.nativeElement.focus(options);\n        }\n    }\n    _getAriaDisabled() {\n        if (this.ariaDisabled != null) {\n            return this.ariaDisabled;\n        }\n        if (this._isAnchor) {\n            return this.disabled || null;\n        }\n        return this.disabled && this.disabledInteractive ? true : null;\n    }\n    _getDisabledAttribute() {\n        return this.disabledInteractive || !this.disabled ? null : true;\n    }\n    _updateRippleDisabled() {\n        this._rippleLoader?.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n    }\n    _getTabIndex() {\n        if (this._isAnchor) {\n            return this.disabled && !this.disabledInteractive ? -1 : this.tabIndex;\n        }\n        return this.tabIndex;\n    }\n    _setupAsAnchor() {\n        this._cleanupClick = this._ngZone.runOutsideAngular(() => this._renderer.listen(this._elementRef.nativeElement, 'click', (event) => {\n            if (this.disabled) {\n                event.preventDefault();\n                event.stopImmediatePropagation();\n            }\n        }));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatButtonBase, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatButtonBase, isStandalone: true, inputs: { color: \"color\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], ariaDisabled: [\"aria-disabled\", \"ariaDisabled\", booleanAttribute], disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", transformTabIndex], _tabindex: [\"tabindex\", \"_tabindex\", transformTabIndex] }, host: { properties: { \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\", \"attr.disabled\": \"_getDisabledAttribute()\", \"attr.aria-disabled\": \"_getAriaDisabled()\", \"attr.tabindex\": \"_getTabIndex()\", \"class.mat-mdc-button-disabled\": \"disabled\", \"class.mat-mdc-button-disabled-interactive\": \"disabledInteractive\", \"class.mat-unthemed\": \"!color\", \"class._mat-animation-noopable\": \"_animationsDisabled\" }, classAttribute: \"mat-mdc-button-base\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatButtonBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        // Add a class that applies to all buttons. This makes it easier to target if somebody\n                        // wants to target all Material buttons.\n                        'class': 'mat-mdc-button-base',\n                        '[class]': 'color ? \"mat-\" + color : \"\"',\n                        '[attr.disabled]': '_getDisabledAttribute()',\n                        '[attr.aria-disabled]': '_getAriaDisabled()',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[class.mat-mdc-button-disabled]': 'disabled',\n                        '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n                        '[class.mat-unthemed]': '!color',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], ariaDisabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute, alias: 'aria-disabled' }]\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{ transform: transformTabIndex }]\n            }], _tabindex: [{\n                type: Input,\n                args: [{ alias: 'tabindex', transform: transformTabIndex }]\n            }] } });\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconButton extends MatButtonBase {\n    constructor() {\n        super();\n        this._rippleLoader.configureRipple(this._elementRef.nativeElement, { centered: true });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconButton, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatIconButton, isStandalone: true, selector: \"button[mat-icon-button], a[mat-icon-button], button[matIconButton], a[matIconButton]\", host: { classAttribute: \"mdc-icon-button mat-mdc-icon-button\" }, exportAs: [\"matButton\", \"matAnchor\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\", styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%));flex-shrink:0;text-align:center;width:var(--mat-icon-button-state-layer-size, 40px);height:var(--mat-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mat-icon-button-state-layer-size, 40px) - var(--mat-icon-button-icon-size, 24px)) / 2);font-size:var(--mat-icon-button-icon-size, 24px);color:var(--mat-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-icon-button-touch-target-display, block);left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mat-icon-button-icon-size, 24px);height:var(--mat-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%))}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconButton, decorators: [{\n            type: Component,\n            args: [{ selector: `button[mat-icon-button], a[mat-icon-button], button[matIconButton], a[matIconButton]`, host: {\n                        'class': 'mdc-icon-button mat-mdc-icon-button',\n                    }, exportAs: 'matButton, matAnchor', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\", styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%));flex-shrink:0;text-align:center;width:var(--mat-icon-button-state-layer-size, 40px);height:var(--mat-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mat-icon-button-state-layer-size, 40px) - var(--mat-icon-button-icon-size, 24px)) / 2);font-size:var(--mat-icon-button-icon-size, 24px);color:var(--mat-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-icon-button-touch-target-display, block);left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mat-icon-button-icon-size, 24px);height:var(--mat-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%))}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"] }]\n        }], ctorParameters: () => [] });\n// tslint:disable:variable-name\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nconst MatIconAnchor = MatIconButton;\n\nexport { MatIconButton as M, MatButtonBase as a, MAT_BUTTON_CONFIG as b, MatIconAnchor as c };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;AACjM,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASD,CAAC,IAAIE,mBAAmB,QAAQ,0BAA0B;;AAEnE;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,iBAAiB,GAAG,IAAIrB,cAAc,CAAC,mBAAmB,CAAC;AACjE,SAASsB,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,IAAI,IAAI,GAAGC,SAAS,GAAGhB,eAAe,CAACe,KAAK,CAAC;AAC7D;AACA;AACA,MAAME,aAAa,CAAC;EAChBC,WAAW,GAAGzB,MAAM,CAACC,UAAU,CAAC;EAChCyB,OAAO,GAAG1B,MAAM,CAACE,MAAM,CAAC;EACxBe,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3CU,OAAO,GAAG3B,MAAM,CAACoB,iBAAiB,EAAE;IAAEQ,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvDC,aAAa,GAAG7B,MAAM,CAACW,YAAY,CAAC;EACpCmB,aAAa;EACbC,SAAS,GAAG/B,MAAM,CAACG,SAAS,CAAC;EAC7B;AACJ;AACA;AACA;EACI6B,aAAa,GAAGhC,MAAM,CAACc,eAAe,CAAC;EACvC;EACAmB,SAAS;EACT;EACAC,MAAM,GAAG,KAAK;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,KAAK;EACL;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACd,KAAK,EAAE;IACrB,IAAI,CAACe,cAAc,GAAGf,KAAK;IAC3B,IAAI,CAACgB,qBAAqB,CAAC,CAAC;EAChC;EACAD,cAAc,GAAG,KAAK;EACtB;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACjB,KAAK,EAAE;IAChB,IAAI,CAACkB,SAAS,GAAGlB,KAAK;IACtB,IAAI,CAACgB,qBAAqB,CAAC,CAAC;EAChC;EACAE,SAAS,GAAG,KAAK;EACjB;EACAC,YAAY;EACZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,mBAAmB;EACnB;EACAC,QAAQ;EACR;AACJ;AACA;AACA;EACI,IAAIC,SAASA,CAACtB,KAAK,EAAE;IACjB,IAAI,CAACqB,QAAQ,GAAGrB,KAAK;EACzB;EACAuB,WAAWA,CAAA,EAAG;IACV7C,MAAM,CAACY,sBAAsB,CAAC,CAACkC,IAAI,CAAC9B,uBAAuB,CAAC;IAC5D,MAAM+B,OAAO,GAAG,IAAI,CAACtB,WAAW,CAACuB,aAAa;IAC9C,IAAI,CAACf,SAAS,GAAGc,OAAO,CAACE,OAAO,KAAK,GAAG;IACxC,IAAI,CAACP,mBAAmB,GAAG,IAAI,CAACf,OAAO,EAAEe,mBAAmB,IAAI,KAAK;IACrE,IAAI,CAACP,KAAK,GAAG,IAAI,CAACR,OAAO,EAAEQ,KAAK,IAAI,IAAI;IACxC,IAAI,CAACH,aAAa,EAAEkB,eAAe,CAACH,OAAO,EAAE;MAAEI,SAAS,EAAE;IAAwB,CAAC,CAAC;EACxF;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACvB,aAAa,CAACwB,OAAO,CAAC,IAAI,CAAC5B,WAAW,EAAE,IAAI,CAAC;IAClD;IACA;IACA,IAAI,IAAI,CAACQ,SAAS,EAAE;MAChB,IAAI,CAACqB,cAAc,CAAC,CAAC;IACzB;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzB,aAAa,GAAG,CAAC;IACtB,IAAI,CAACD,aAAa,CAAC2B,cAAc,CAAC,IAAI,CAAC/B,WAAW,CAAC;IACnD,IAAI,CAACO,aAAa,EAAEyB,aAAa,CAAC,IAAI,CAAChC,WAAW,CAACuB,aAAa,CAAC;EACrE;EACA;EACAU,KAAKA,CAACC,MAAM,GAAG,SAAS,EAAEC,OAAO,EAAE;IAC/B,IAAID,MAAM,EAAE;MACR,IAAI,CAAC9B,aAAa,CAACgC,QAAQ,CAAC,IAAI,CAACpC,WAAW,CAACuB,aAAa,EAAEW,MAAM,EAAEC,OAAO,CAAC;IAChF,CAAC,MACI;MACD,IAAI,CAACnC,WAAW,CAACuB,aAAa,CAACU,KAAK,CAACE,OAAO,CAAC;IACjD;EACJ;EACAE,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACrB,YAAY,IAAI,IAAI,EAAE;MAC3B,OAAO,IAAI,CAACA,YAAY;IAC5B;IACA,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,OAAO,IAAI,CAACM,QAAQ,IAAI,IAAI;IAChC;IACA,OAAO,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACG,mBAAmB,GAAG,IAAI,GAAG,IAAI;EAClE;EACAqB,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACrB,mBAAmB,IAAI,CAAC,IAAI,CAACH,QAAQ,GAAG,IAAI,GAAG,IAAI;EACnE;EACAD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACN,aAAa,EAAEgC,WAAW,CAAC,IAAI,CAACvC,WAAW,CAACuB,aAAa,EAAE,IAAI,CAACZ,aAAa,IAAI,IAAI,CAACG,QAAQ,CAAC;EACxG;EACA0B,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAChC,SAAS,EAAE;MAChB,OAAO,IAAI,CAACM,QAAQ,IAAI,CAAC,IAAI,CAACG,mBAAmB,GAAG,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ;IAC1E;IACA,OAAO,IAAI,CAACA,QAAQ;EACxB;EACAW,cAAcA,CAAA,EAAG;IACb,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACJ,OAAO,CAACwC,iBAAiB,CAAC,MAAM,IAAI,CAACnC,SAAS,CAACoC,MAAM,CAAC,IAAI,CAAC1C,WAAW,CAACuB,aAAa,EAAE,OAAO,EAAGoB,KAAK,IAAK;MAChI,IAAI,IAAI,CAAC7B,QAAQ,EAAE;QACf6B,KAAK,CAACC,cAAc,CAAC,CAAC;QACtBD,KAAK,CAACE,wBAAwB,CAAC,CAAC;MACpC;IACJ,CAAC,CAAC,CAAC;EACP;EACA,OAAOC,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFjD,aAAa;EAAA;EAChH,OAAOkD,IAAI,kBAD8E5E,EAAE,CAAA6E,iBAAA;IAAAC,IAAA,EACJpD,aAAa;IAAAqD,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADXnF,EAAE,CAAAqF,WAAA,aACJD,GAAA,CAAAnB,qBAAA,CAAsB,CAAC,mBAAvBmB,GAAA,CAAApB,gBAAA,CAAiB,CAAC,cAAlBoB,GAAA,CAAAjB,YAAA,CAAa,CAAC;QADZnE,EAAE,CAAAsF,UAAA,CAAAF,GAAA,CAAA/C,KAAA,GACI,MAAM,GAAA+C,GAAA,CAAA/C,KAAA,GAAW,EAAb,CAAC;QADXrC,EAAE,CAAAuF,WAAA,4BAAAH,GAAA,CAAA3C,QACQ,CAAC,wCAAA2C,GAAA,CAAAxC,mBAAD,CAAC,kBAAAwC,GAAA,CAAA/C,KAAD,CAAC,4BAAA+C,GAAA,CAAAjE,mBAAD,CAAC;MAAA;IAAA;IAAAqE,MAAA;MAAAnD,KAAA;MAAAC,aAAA,wCAAkGhC,gBAAgB;MAAAmC,QAAA,8BAAsCnC,gBAAgB;MAAAqC,YAAA,uCAAmDrC,gBAAgB;MAAAsC,mBAAA,oDAAuEtC,gBAAgB;MAAAuC,QAAA,8BAAsCtB,iBAAiB;MAAAuB,SAAA,+BAAwCvB,iBAAiB;IAAA;EAAA;AAC1hB;AACA;EAAA,QAAAkE,SAAA,oBAAAA,SAAA,KAH6FzF,EAAE,CAAA0F,iBAAA,CAGJhE,aAAa,EAAc,CAAC;IAC3GoD,IAAI,EAAEvE,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,IAAI,EAAE;QACF;QACA;QACA,OAAO,EAAE,qBAAqB;QAC9B,SAAS,EAAE,6BAA6B;QACxC,iBAAiB,EAAE,yBAAyB;QAC5C,sBAAsB,EAAE,oBAAoB;QAC5C,iBAAiB,EAAE,gBAAgB;QACnC,iCAAiC,EAAE,UAAU;QAC7C,6CAA6C,EAAE,qBAAqB;QACpE,sBAAsB,EAAE,QAAQ;QAChC,iCAAiC,EAAE;MACvC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEvD,KAAK,EAAE,CAAC;MAChDyC,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAE8B,aAAa,EAAE,CAAC;MAChBwC,IAAI,EAAEtE,KAAK;MACXmF,IAAI,EAAE,CAAC;QAAEE,SAAS,EAAEvF;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmC,QAAQ,EAAE,CAAC;MACXqC,IAAI,EAAEtE,KAAK;MACXmF,IAAI,EAAE,CAAC;QAAEE,SAAS,EAAEvF;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqC,YAAY,EAAE,CAAC;MACfmC,IAAI,EAAEtE,KAAK;MACXmF,IAAI,EAAE,CAAC;QAAEE,SAAS,EAAEvF,gBAAgB;QAAEwF,KAAK,EAAE;MAAgB,CAAC;IAClE,CAAC,CAAC;IAAElD,mBAAmB,EAAE,CAAC;MACtBkC,IAAI,EAAEtE,KAAK;MACXmF,IAAI,EAAE,CAAC;QAAEE,SAAS,EAAEvF;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuC,QAAQ,EAAE,CAAC;MACXiC,IAAI,EAAEtE,KAAK;MACXmF,IAAI,EAAE,CAAC;QAAEE,SAAS,EAAEtE;MAAkB,CAAC;IAC3C,CAAC,CAAC;IAAEuB,SAAS,EAAE,CAAC;MACZgC,IAAI,EAAEtE,KAAK;MACXmF,IAAI,EAAE,CAAC;QAAEG,KAAK,EAAE,UAAU;QAAED,SAAS,EAAEtE;MAAkB,CAAC;IAC9D,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMwE,aAAa,SAASrE,aAAa,CAAC;EACtCqB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACb,aAAa,CAACkB,eAAe,CAAC,IAAI,CAACzB,WAAW,CAACuB,aAAa,EAAE;MAAE8C,QAAQ,EAAE;IAAK,CAAC,CAAC;EAC1F;EACA,OAAOvB,IAAI,YAAAwB,sBAAAtB,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoB,aAAa;EAAA;EAChH,OAAOG,IAAI,kBArD8ElG,EAAE,CAAAmG,iBAAA;IAAArB,IAAA,EAqDJiB,aAAa;IAAAK,SAAA;IAAArB,SAAA;IAAAsB,QAAA;IAAAC,QAAA,GArDXtG,EAAE,CAAAuG,0BAAA;IAAAC,KAAA,EAAApF,GAAA;IAAAqF,kBAAA,EAAApF,GAAA;IAAAqF,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAA3B,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnF,EAAE,CAAA+G,eAAA;QAAF/G,EAAE,CAAAgH,SAAA,aAqDuW,CAAC;QArD1WhH,EAAE,CAAAiH,YAAA,EAqDoY,CAAC;QArDvYjH,EAAE,CAAAgH,SAAA,aAqD8nB,CAAC,aAAsD,CAAC;MAAA;IAAA;IAAAE,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACrxB;AACA;EAAA,QAAA3B,SAAA,oBAAAA,SAAA,KAvD6FzF,EAAE,CAAA0F,iBAAA,CAuDJK,aAAa,EAAc,CAAC;IAC3GjB,IAAI,EAAEpE,SAAS;IACfiF,IAAI,EAAE,CAAC;MAAE0B,QAAQ,EAAE,sFAAsF;MAAEzB,IAAI,EAAE;QACrG,OAAO,EAAE;MACb,CAAC;MAAES,QAAQ,EAAE,sBAAsB;MAAEc,aAAa,EAAExG,iBAAiB,CAAC2G,IAAI;MAAEF,eAAe,EAAExG,uBAAuB,CAAC2G,MAAM;MAAEV,QAAQ,EAAE,kaAAka;MAAEK,MAAM,EAAE,CAAC,06HAA06H,EAAE,wXAAwX;IAAE,CAAC;EACv2J,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAGzB,aAAa;AAEnC,SAASA,aAAa,IAAIhF,CAAC,EAAEW,aAAa,IAAI+F,CAAC,EAAEnG,iBAAiB,IAAIoG,CAAC,EAAEF,aAAa,IAAIG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}