{"ast": null, "code": "import { HighContrastModeDetector } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgModule } from '@angular/core';\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nclass MatCommonModule {\n  constructor() {\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function MatCommonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCommonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCommonModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BidiModule, BidiModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], () => [], null);\n})();\nexport { MatCommonModule as M, MATERIAL_SANITY_CHECKS as a };", "map": {"version": 3, "names": ["HighContrastModeDetector", "BidiModule", "i0", "InjectionToken", "inject", "NgModule", "MATERIAL_SANITY_CHECKS", "providedIn", "factory", "MatCommonModule", "constructor", "_applyBodyHighContrastModeCssClasses", "ɵfac", "MatCommonModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "exports", "M", "a"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/material/fesm2022/common-module-cKSwHniA.mjs"], "sourcesContent": ["import { HighContrastModeDetector } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgModule } from '@angular/core';\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n    providedIn: 'root',\n    factory: () => true,\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nclass MatCommonModule {\n    constructor() {\n        // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n        // in MatCommonModule.\n        inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCommonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule], exports: [BidiModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule, BidiModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule],\n                    exports: [BidiModule],\n                }]\n        }], ctorParameters: () => [] });\n\nexport { MatCommonModule as M, MATERIAL_SANITY_CHECKS as a };\n"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,mBAAmB;AAC5D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;;AAEhE;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,IAAIH,cAAc,CAAC,mBAAmB,EAAE;EACnEI,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA;IACAN,MAAM,CAACJ,wBAAwB,CAAC,CAACW,oCAAoC,CAAC,CAAC;EAC3E;EACA,OAAOC,IAAI,YAAAC,wBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFL,eAAe;EAAA;EAClH,OAAOM,IAAI,kBAD8Eb,EAAE,CAAAc,gBAAA;IAAAC,IAAA,EACSR;EAAe;EACnH,OAAOS,IAAI,kBAF8EhB,EAAE,CAAAiB,gBAAA;IAAAC,OAAA,GAEoCnB,UAAU,EAAEA,UAAU;EAAA;AACzJ;AACA;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAJ6FnB,EAAE,CAAAoB,iBAAA,CAIJb,eAAe,EAAc,CAAC;IAC7GQ,IAAI,EAAEZ,QAAQ;IACdkB,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CAACnB,UAAU,CAAC;MACrBuB,OAAO,EAAE,CAACvB,UAAU;IACxB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASQ,eAAe,IAAIgB,CAAC,EAAEnB,sBAAsB,IAAIoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}