{"ast": null, "code": "import { HttpEventType } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, finalize, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/loading.service\";\nimport * as i4 from \"../services/rate-limit.service\";\nexport class AuthInterceptor {\n  constructor(authService, router, loadingService, rateLimitService) {\n    this.authService = authService;\n    this.router = router;\n    this.loadingService = loadingService;\n    this.rateLimitService = rateLimitService;\n  }\n  intercept(request, next) {\n    // Show loading indicator\n    this.loadingService.show();\n    // Add auth token if available\n    const token = this.authService.getToken();\n    if (token && !this.authService.isTokenExpired()) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        }\n      });\n    }\n    // Add CSRF protection\n    request = request.clone({\n      setHeaders: {\n        'X-CSRF-Token': this.generateCSRFToken()\n      }\n    });\n    return next.handle(request).pipe(tap(event => {\n      // Handle successful responses and extract rate limit headers\n      if (event.type === HttpEventType.Response) {\n        const response = event;\n        if (response.headers) {\n          this.rateLimitService.handleRateLimitResponse(this.extractHeaders(response.headers));\n        }\n      }\n    }), catchError(error => {\n      this.handleError(error);\n      return throwError(() => error);\n    }), finalize(() => {\n      // Hide loading indicator\n      this.loadingService.hide();\n    }));\n  }\n  handleError(error) {\n    console.log('🚨 HTTP Error intercepted:', error);\n    console.log('🔍 Error details:', {\n      status: error.status,\n      message: error.message,\n      url: error.url,\n      statusText: error.statusText,\n      error: error.error\n    });\n    // Enhanced rate limit detection for all error types (must be first)\n    if (this.detectRateLimitError(error)) {\n      console.warn('🚦 Rate limit detected via enhanced analysis - triggering popup');\n      this.handleRateLimitError(error);\n      return; // Don't continue with other error handling\n    } else {\n      console.log('❌ Rate limit NOT detected - continuing with normal error handling');\n    }\n    switch (error.status) {\n      case 401:\n        // Check if this is an account lockout vs. token expiry\n        const errorMessage = error.error?.error?.message || error.error?.message || '';\n        if (errorMessage.includes('temporarily locked') || errorMessage.includes('multiple failed login attempts')) {\n          // This is an account lockout - don't auto-logout, let the login component handle it\n          console.log('Account lockout detected - allowing login component to handle');\n          return;\n        }\n        // Regular unauthorized - token expired or invalid\n        this.authService.logout();\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            message: 'Session expired. Please login again.'\n          }\n        });\n        break;\n      case 403:\n        // Forbidden - insufficient permissions\n        this.router.navigate(['/unauthorized']);\n        break;\n      case 429:\n        // Too many requests - should already be handled by enhanced detection above\n        console.warn('🚦 Direct 429 status - already handled by enhanced detection');\n        break;\n      case 0:\n        // Network error - could be rate limit with CORS issues (handled by enhanced detection)\n        console.error('🌐 Network error. Please check your connection.');\n        break;\n      default:\n        console.error('❌ HTTP Error:', error);\n    }\n  }\n  /**\n   * Enhanced rate limit detection from various error patterns\n   */\n  detectRateLimitError(error) {\n    // Direct 429 status\n    if (error.status === 429) return true;\n    // Check error message for rate limit indicators\n    const errorMessage = error.message?.toLowerCase() || '';\n    const errorText = error.error?.toString?.()?.toLowerCase() || '';\n    const errorDetails = error.error?.error?.message?.toLowerCase() || '';\n    const statusText = error.statusText?.toLowerCase() || '';\n    const url = error.url?.toLowerCase() || '';\n    const rateLimitIndicators = ['too many requests', '(too many requests)',\n    // Your specific console error format\n    'rate limit', 'ratelimit', '429', 'net::err_failed 429', 'net::err_failed',\n    // Network failures on auth endpoints\n    'quota exceeded', 'throttled', 'exceeded', 'unknown error' // CORS-blocked rate limit responses\n    ];\n    // Check if error contains rate limit indicators in any field\n    const hasRateLimitPattern = rateLimitIndicators.some(indicator => errorMessage.includes(indicator) || errorText.includes(indicator) || errorDetails.includes(indicator) || statusText.includes(indicator));\n    // Check for commonly rate-limited endpoints (now includes all auth endpoints)\n    const rateLimitedEndpoints = ['/auth/login', '/auth/register', '/auth/reset', '/auth/verify', '/auth/verify-otp', '/auth/resend-otp', '/auth/profile',\n    // Profile endpoint \n    '/auth/update', '/auth/change-password', '/two-factor', '/otp', '/api/auth' // Generic auth API\n    ];\n    const isRateLimitedEndpoint = rateLimitedEndpoints.some(endpoint => url.includes(endpoint));\n    const isNetworkErrorOnRateLimitedEndpoint = error.status === 0 && isRateLimitedEndpoint;\n    // Enhanced check for net::ERR_FAILED patterns\n    const hasNetworkFailure = errorMessage.includes('net::err_failed') || errorMessage.includes('unknown error') || error.status === 0 && errorMessage.includes('http failure response');\n    // Log detection for debugging\n    if (hasRateLimitPattern || isNetworkErrorOnRateLimitedEndpoint || hasNetworkFailure && isRateLimitedEndpoint) {\n      console.log('🚦 Rate limit detected:', {\n        status: error.status,\n        message: errorMessage,\n        text: errorText,\n        details: errorDetails,\n        statusText: statusText,\n        url: url,\n        pattern: hasRateLimitPattern,\n        endpoint: isNetworkErrorOnRateLimitedEndpoint,\n        networkFailure: hasNetworkFailure && isRateLimitedEndpoint\n      });\n    }\n    return hasRateLimitPattern || isNetworkErrorOnRateLimitedEndpoint || hasNetworkFailure && isRateLimitedEndpoint;\n  }\n  /**\n   * Handle rate limit error with smart fallback data\n   */\n  handleRateLimitError(error) {\n    console.log('🚦 Handling rate limit error:', error);\n    // Try to extract headers if available\n    let headers = {};\n    if (error.headers) {\n      headers = this.extractHeaders(error.headers);\n    }\n    // If no retry-after header, provide smart fallback values based on endpoint\n    if (!headers['retry-after'] && !headers['x-ratelimit-retryafter']) {\n      headers = this.generateSmartRateLimitHeaders(error.url || '');\n      console.log('🔄 Using smart fallback rate limit headers:', headers);\n    }\n    // Handle the rate limit response\n    this.rateLimitService.handleRateLimitResponse(headers, error);\n  }\n  /**\n   * Generate smart fallback rate limit headers based on URL patterns\n   */\n  generateSmartRateLimitHeaders(url) {\n    const now = Math.floor(Date.now() / 1000);\n    // Match backend configuration: RATE_LIMIT_WINDOW=1 minute, RATE_LIMIT_MAX=2\n    let retryAfter = 60; // 1 minute to match backend\n    let limit = 2; // Match backend RATE_LIMIT_MAX=2\n    if (url.includes('/auth/login')) {\n      retryAfter = 60; // 1 minute for login attempts (match backend)\n      limit = 2;\n    } else if (url.includes('/auth/register')) {\n      retryAfter = 60; // 1 minute for registration\n      limit = 2;\n    } else if (url.includes('/auth/reset') || url.includes('/auth/forgot')) {\n      retryAfter = 60; // 1 minute for password reset\n      limit = 2;\n    } else if (url.includes('/auth/verify')) {\n      retryAfter = 60; // 1 minute for verification codes\n      limit = 2;\n    } else if (url.includes('/auth/')) {\n      retryAfter = 60; // 1 minute for other auth endpoints\n      limit = 2;\n    }\n    return {\n      'retry-after': retryAfter.toString(),\n      'x-ratelimit-limit': limit.toString(),\n      'x-ratelimit-remaining': '0',\n      'x-ratelimit-reset': (now + retryAfter).toString(),\n      'x-ratelimit-totalrequests': limit.toString()\n    };\n  }\n  generateCSRFToken() {\n    // Simple CSRF token generation\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Extract headers from HttpHeaders object\n   */\n  extractHeaders(httpHeaders) {\n    const headers = {};\n    if (httpHeaders && httpHeaders.keys) {\n      httpHeaders.keys().forEach(key => {\n        headers[key.toLowerCase()] = httpHeaders.get(key);\n      });\n    }\n    return headers;\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.LoadingService), i0.ɵɵinject(i4.RateLimitService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpEventType", "throwError", "catchError", "finalize", "tap", "AuthInterceptor", "constructor", "authService", "router", "loadingService", "rateLimitService", "intercept", "request", "next", "show", "token", "getToken", "isTokenExpired", "clone", "setHeaders", "Authorization", "generateCSRFToken", "handle", "pipe", "event", "type", "Response", "response", "headers", "handleRateLimitResponse", "extractHeaders", "error", "handleError", "hide", "console", "log", "status", "message", "url", "statusText", "detectRateLimitError", "warn", "handleRateLimitError", "errorMessage", "includes", "logout", "navigate", "queryParams", "toLowerCase", "errorText", "toString", "errorDetails", "rateLimitIndicators", "hasRateLimitPattern", "some", "indicator", "rateLimitedEndpoints", "isRateLimitedEndpoint", "endpoint", "isNetworkErrorOnRateLimitedEndpoint", "hasNetworkFailure", "text", "details", "pattern", "networkFailure", "generateSmartRateLimitHeaders", "now", "Math", "floor", "Date", "retryAfter", "limit", "random", "substring", "httpHeaders", "keys", "for<PERSON>ach", "key", "get", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "i3", "LoadingService", "i4", "RateLimitService", "_2", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpResponse, HttpEventType } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, finalize, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nimport { LoadingService } from '../services/loading.service';\nimport { RateLimitService } from '../services/rate-limit.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private loadingService: LoadingService,\n    private rateLimitService: RateLimitService\n  ) {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Show loading indicator\n    this.loadingService.show();\n\n    // Add auth token if available\n    const token = this.authService.getToken();\n    if (token && !this.authService.isTokenExpired()) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        }\n      });\n    }\n\n    // Add CSRF protection\n    request = request.clone({\n      setHeaders: {\n        'X-CSRF-Token': this.generateCSRFToken()\n      }\n    });\n\n    return next.handle(request).pipe(\n      tap((event: HttpEvent<any>) => {\n        // Handle successful responses and extract rate limit headers\n        if (event.type === HttpEventType.Response) {\n          const response = event as HttpResponse<any>;\n          if (response.headers) {\n            this.rateLimitService.handleRateLimitResponse(this.extractHeaders(response.headers));\n          }\n        }\n      }),\n      catchError((error: HttpErrorResponse) => {\n        this.handleError(error);\n        return throwError(() => error);\n      }),\n      finalize(() => {\n        // Hide loading indicator\n        this.loadingService.hide();\n      })\n    );\n  }\n\n  private handleError(error: HttpErrorResponse): void {\n    console.log('🚨 HTTP Error intercepted:', error);\n    console.log('🔍 Error details:', {\n      status: error.status,\n      message: error.message,\n      url: error.url,\n      statusText: error.statusText,\n      error: error.error\n    });\n    \n    // Enhanced rate limit detection for all error types (must be first)\n    if (this.detectRateLimitError(error)) {\n      console.warn('🚦 Rate limit detected via enhanced analysis - triggering popup');\n      this.handleRateLimitError(error);\n      return; // Don't continue with other error handling\n    } else {\n      console.log('❌ Rate limit NOT detected - continuing with normal error handling');\n    }\n\n    switch (error.status) {\n      case 401:\n        // Check if this is an account lockout vs. token expiry\n        const errorMessage = error.error?.error?.message || error.error?.message || '';\n        \n        if (errorMessage.includes('temporarily locked') || \n            errorMessage.includes('multiple failed login attempts')) {\n          // This is an account lockout - don't auto-logout, let the login component handle it\n          console.log('Account lockout detected - allowing login component to handle');\n          return;\n        }\n        \n        // Regular unauthorized - token expired or invalid\n        this.authService.logout();\n        this.router.navigate(['/auth/login'], {\n          queryParams: { message: 'Session expired. Please login again.' }\n        });\n        break;\n      case 403:\n        // Forbidden - insufficient permissions\n        this.router.navigate(['/unauthorized']);\n        break;\n      case 429:\n        // Too many requests - should already be handled by enhanced detection above\n        console.warn('🚦 Direct 429 status - already handled by enhanced detection');\n        break;\n      case 0:\n        // Network error - could be rate limit with CORS issues (handled by enhanced detection)\n        console.error('🌐 Network error. Please check your connection.');\n        break;\n      default:\n        console.error('❌ HTTP Error:', error);\n    }\n  }\n\n  /**\n   * Enhanced rate limit detection from various error patterns\n   */\n  private detectRateLimitError(error: HttpErrorResponse): boolean {\n    // Direct 429 status\n    if (error.status === 429) return true;\n    \n    // Check error message for rate limit indicators\n    const errorMessage = error.message?.toLowerCase() || '';\n    const errorText = error.error?.toString?.()?.toLowerCase() || '';\n    const errorDetails = error.error?.error?.message?.toLowerCase() || '';\n    const statusText = error.statusText?.toLowerCase() || '';\n    const url = error.url?.toLowerCase() || '';\n    \n    const rateLimitIndicators = [\n      'too many requests',\n      '(too many requests)', // Your specific console error format\n      'rate limit',\n      'ratelimit',\n      '429',\n      'net::err_failed 429',\n      'net::err_failed',  // Network failures on auth endpoints\n      'quota exceeded',\n      'throttled',\n      'exceeded',\n      'unknown error'  // CORS-blocked rate limit responses\n    ];\n    \n    // Check if error contains rate limit indicators in any field\n    const hasRateLimitPattern = rateLimitIndicators.some(indicator => \n      errorMessage.includes(indicator) || \n      errorText.includes(indicator) || \n      errorDetails.includes(indicator) ||\n      statusText.includes(indicator)\n    );\n    \n    // Check for commonly rate-limited endpoints (now includes all auth endpoints)\n    const rateLimitedEndpoints = [\n      '/auth/login', \n      '/auth/register', \n      '/auth/reset', \n      '/auth/verify',\n      '/auth/verify-otp',\n      '/auth/resend-otp',\n      '/auth/profile',  // Profile endpoint \n      '/auth/update',\n      '/auth/change-password',\n      '/two-factor',\n      '/otp',\n      '/api/auth'  // Generic auth API\n    ];\n    const isRateLimitedEndpoint = rateLimitedEndpoints.some(endpoint => url.includes(endpoint));\n    const isNetworkErrorOnRateLimitedEndpoint = error.status === 0 && isRateLimitedEndpoint;\n    \n    // Enhanced check for net::ERR_FAILED patterns\n    const hasNetworkFailure = errorMessage.includes('net::err_failed') || \n                             errorMessage.includes('unknown error') ||\n                             (error.status === 0 && errorMessage.includes('http failure response'));\n    \n    // Log detection for debugging\n    if (hasRateLimitPattern || isNetworkErrorOnRateLimitedEndpoint || (hasNetworkFailure && isRateLimitedEndpoint)) {\n      console.log('🚦 Rate limit detected:', {\n        status: error.status,\n        message: errorMessage,\n        text: errorText,\n        details: errorDetails,\n        statusText: statusText,\n        url: url,\n        pattern: hasRateLimitPattern,\n        endpoint: isNetworkErrorOnRateLimitedEndpoint,\n        networkFailure: hasNetworkFailure && isRateLimitedEndpoint\n      });\n    }\n    \n    return hasRateLimitPattern || isNetworkErrorOnRateLimitedEndpoint || (hasNetworkFailure && isRateLimitedEndpoint);\n  }\n\n  /**\n   * Handle rate limit error with smart fallback data\n   */\n  private handleRateLimitError(error: HttpErrorResponse): void {\n    console.log('🚦 Handling rate limit error:', error);\n    \n    // Try to extract headers if available\n    let headers: {[key: string]: string} = {};\n    \n    if (error.headers) {\n      headers = this.extractHeaders(error.headers);\n    }\n    \n    // If no retry-after header, provide smart fallback values based on endpoint\n    if (!headers['retry-after'] && !headers['x-ratelimit-retryafter']) {\n      headers = this.generateSmartRateLimitHeaders(error.url || '');\n      console.log('🔄 Using smart fallback rate limit headers:', headers);\n    }\n    \n    // Handle the rate limit response\n    this.rateLimitService.handleRateLimitResponse(headers, error);\n  }\n\n  /**\n   * Generate smart fallback rate limit headers based on URL patterns\n   */\n  private generateSmartRateLimitHeaders(url: string): {[key: string]: string} {\n    const now = Math.floor(Date.now() / 1000);\n    \n    // Match backend configuration: RATE_LIMIT_WINDOW=1 minute, RATE_LIMIT_MAX=2\n    let retryAfter = 60; // 1 minute to match backend\n    let limit = 2; // Match backend RATE_LIMIT_MAX=2\n    \n    if (url.includes('/auth/login')) {\n      retryAfter = 60; // 1 minute for login attempts (match backend)\n      limit = 2;\n    } else if (url.includes('/auth/register')) {\n      retryAfter = 60; // 1 minute for registration\n      limit = 2;\n    } else if (url.includes('/auth/reset') || url.includes('/auth/forgot')) {\n      retryAfter = 60; // 1 minute for password reset\n      limit = 2;\n    } else if (url.includes('/auth/verify')) {\n      retryAfter = 60; // 1 minute for verification codes\n      limit = 2;\n    } else if (url.includes('/auth/')) {\n      retryAfter = 60; // 1 minute for other auth endpoints\n      limit = 2;\n    }\n    \n    return {\n      'retry-after': retryAfter.toString(),\n      'x-ratelimit-limit': limit.toString(),\n      'x-ratelimit-remaining': '0',\n      'x-ratelimit-reset': (now + retryAfter).toString(),\n      'x-ratelimit-totalrequests': limit.toString()\n    };\n  }\n\n  private generateCSRFToken(): string {\n    // Simple CSRF token generation\n    return Math.random().toString(36).substring(2, 15) + \n           Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Extract headers from HttpHeaders object\n   */\n  private extractHeaders(httpHeaders: any): {[key: string]: string} {\n    const headers: {[key: string]: string} = {};\n    \n    if (httpHeaders && httpHeaders.keys) {\n      httpHeaders.keys().forEach((key: string) => {\n        headers[key.toLowerCase()] = httpHeaders.get(key);\n      });\n    }\n    \n    return headers;\n  }\n}\n"], "mappings": "AACA,SAAgGA,aAAa,QAAQ,sBAAsB;AAC3I,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,gBAAgB;;;;;;AAO1D,OAAM,MAAOC,eAAe;EAC1BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,cAA8B,EAC9BC,gBAAkC;IAHlC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACvB;EAEHC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,IAAI,CAACJ,cAAc,CAACK,IAAI,EAAE;IAE1B;IACA,MAAMC,KAAK,GAAG,IAAI,CAACR,WAAW,CAACS,QAAQ,EAAE;IACzC,IAAID,KAAK,IAAI,CAAC,IAAI,CAACR,WAAW,CAACU,cAAc,EAAE,EAAE;MAC/CL,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC;QACtBC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUL,KAAK,EAAE;UAChC,cAAc,EAAE,kBAAkB;UAClC,kBAAkB,EAAE;;OAEvB,CAAC;IACJ;IAEA;IACAH,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC;MACtBC,UAAU,EAAE;QACV,cAAc,EAAE,IAAI,CAACE,iBAAiB;;KAEzC,CAAC;IAEF,OAAOR,IAAI,CAACS,MAAM,CAACV,OAAO,CAAC,CAACW,IAAI,CAC9BnB,GAAG,CAAEoB,KAAqB,IAAI;MAC5B;MACA,IAAIA,KAAK,CAACC,IAAI,KAAKzB,aAAa,CAAC0B,QAAQ,EAAE;QACzC,MAAMC,QAAQ,GAAGH,KAA0B;QAC3C,IAAIG,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAClB,gBAAgB,CAACmB,uBAAuB,CAAC,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,OAAO,CAAC,CAAC;QACtF;MACF;IACF,CAAC,CAAC,EACF1B,UAAU,CAAE6B,KAAwB,IAAI;MACtC,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;MACvB,OAAO9B,UAAU,CAAC,MAAM8B,KAAK,CAAC;IAChC,CAAC,CAAC,EACF5B,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACM,cAAc,CAACwB,IAAI,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQD,WAAWA,CAACD,KAAwB;IAC1CG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEJ,KAAK,CAAC;IAChDG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BC,MAAM,EAAEL,KAAK,CAACK,MAAM;MACpBC,OAAO,EAAEN,KAAK,CAACM,OAAO;MACtBC,GAAG,EAAEP,KAAK,CAACO,GAAG;MACdC,UAAU,EAAER,KAAK,CAACQ,UAAU;MAC5BR,KAAK,EAAEA,KAAK,CAACA;KACd,CAAC;IAEF;IACA,IAAI,IAAI,CAACS,oBAAoB,CAACT,KAAK,CAAC,EAAE;MACpCG,OAAO,CAACO,IAAI,CAAC,iEAAiE,CAAC;MAC/E,IAAI,CAACC,oBAAoB,CAACX,KAAK,CAAC;MAChC,OAAO,CAAC;IACV,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;IAClF;IAEA,QAAQJ,KAAK,CAACK,MAAM;MAClB,KAAK,GAAG;QACN;QACA,MAAMO,YAAY,GAAGZ,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEM,OAAO,IAAIN,KAAK,CAACA,KAAK,EAAEM,OAAO,IAAI,EAAE;QAE9E,IAAIM,YAAY,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAC3CD,YAAY,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAAE;UAC3D;UACAV,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;UAC5E;QACF;QAEA;QACA,IAAI,CAAC5B,WAAW,CAACsC,MAAM,EAAE;QACzB,IAAI,CAACrC,MAAM,CAACsC,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;UACpCC,WAAW,EAAE;YAAEV,OAAO,EAAE;UAAsC;SAC/D,CAAC;QACF;MACF,KAAK,GAAG;QACN;QACA,IAAI,CAAC7B,MAAM,CAACsC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC;MACF,KAAK,GAAG;QACN;QACAZ,OAAO,CAACO,IAAI,CAAC,8DAA8D,CAAC;QAC5E;MACF,KAAK,CAAC;QACJ;QACAP,OAAO,CAACH,KAAK,CAAC,iDAAiD,CAAC;QAChE;MACF;QACEG,OAAO,CAACH,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACzC;EACF;EAEA;;;EAGQS,oBAAoBA,CAACT,KAAwB;IACnD;IACA,IAAIA,KAAK,CAACK,MAAM,KAAK,GAAG,EAAE,OAAO,IAAI;IAErC;IACA,MAAMO,YAAY,GAAGZ,KAAK,CAACM,OAAO,EAAEW,WAAW,EAAE,IAAI,EAAE;IACvD,MAAMC,SAAS,GAAGlB,KAAK,CAACA,KAAK,EAAEmB,QAAQ,GAAE,CAAE,EAAEF,WAAW,EAAE,IAAI,EAAE;IAChE,MAAMG,YAAY,GAAGpB,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEM,OAAO,EAAEW,WAAW,EAAE,IAAI,EAAE;IACrE,MAAMT,UAAU,GAAGR,KAAK,CAACQ,UAAU,EAAES,WAAW,EAAE,IAAI,EAAE;IACxD,MAAMV,GAAG,GAAGP,KAAK,CAACO,GAAG,EAAEU,WAAW,EAAE,IAAI,EAAE;IAE1C,MAAMI,mBAAmB,GAAG,CAC1B,mBAAmB,EACnB,qBAAqB;IAAE;IACvB,YAAY,EACZ,WAAW,EACX,KAAK,EACL,qBAAqB,EACrB,iBAAiB;IAAG;IACpB,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,eAAe,CAAE;IAAA,CAClB;IAED;IACA,MAAMC,mBAAmB,GAAGD,mBAAmB,CAACE,IAAI,CAACC,SAAS,IAC5DZ,YAAY,CAACC,QAAQ,CAACW,SAAS,CAAC,IAChCN,SAAS,CAACL,QAAQ,CAACW,SAAS,CAAC,IAC7BJ,YAAY,CAACP,QAAQ,CAACW,SAAS,CAAC,IAChChB,UAAU,CAACK,QAAQ,CAACW,SAAS,CAAC,CAC/B;IAED;IACA,MAAMC,oBAAoB,GAAG,CAC3B,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,eAAe;IAAG;IAClB,cAAc,EACd,uBAAuB,EACvB,aAAa,EACb,MAAM,EACN,WAAW,CAAE;IAAA,CACd;IACD,MAAMC,qBAAqB,GAAGD,oBAAoB,CAACF,IAAI,CAACI,QAAQ,IAAIpB,GAAG,CAACM,QAAQ,CAACc,QAAQ,CAAC,CAAC;IAC3F,MAAMC,mCAAmC,GAAG5B,KAAK,CAACK,MAAM,KAAK,CAAC,IAAIqB,qBAAqB;IAEvF;IACA,MAAMG,iBAAiB,GAAGjB,YAAY,CAACC,QAAQ,CAAC,iBAAiB,CAAC,IACzCD,YAAY,CAACC,QAAQ,CAAC,eAAe,CAAC,IACrCb,KAAK,CAACK,MAAM,KAAK,CAAC,IAAIO,YAAY,CAACC,QAAQ,CAAC,uBAAuB,CAAE;IAE/F;IACA,IAAIS,mBAAmB,IAAIM,mCAAmC,IAAKC,iBAAiB,IAAIH,qBAAsB,EAAE;MAC9GvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;QACrCC,MAAM,EAAEL,KAAK,CAACK,MAAM;QACpBC,OAAO,EAAEM,YAAY;QACrBkB,IAAI,EAAEZ,SAAS;QACfa,OAAO,EAAEX,YAAY;QACrBZ,UAAU,EAAEA,UAAU;QACtBD,GAAG,EAAEA,GAAG;QACRyB,OAAO,EAAEV,mBAAmB;QAC5BK,QAAQ,EAAEC,mCAAmC;QAC7CK,cAAc,EAAEJ,iBAAiB,IAAIH;OACtC,CAAC;IACJ;IAEA,OAAOJ,mBAAmB,IAAIM,mCAAmC,IAAKC,iBAAiB,IAAIH,qBAAsB;EACnH;EAEA;;;EAGQf,oBAAoBA,CAACX,KAAwB;IACnDG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,KAAK,CAAC;IAEnD;IACA,IAAIH,OAAO,GAA4B,EAAE;IAEzC,IAAIG,KAAK,CAACH,OAAO,EAAE;MACjBA,OAAO,GAAG,IAAI,CAACE,cAAc,CAACC,KAAK,CAACH,OAAO,CAAC;IAC9C;IAEA;IACA,IAAI,CAACA,OAAO,CAAC,aAAa,CAAC,IAAI,CAACA,OAAO,CAAC,wBAAwB,CAAC,EAAE;MACjEA,OAAO,GAAG,IAAI,CAACqC,6BAA6B,CAAClC,KAAK,CAACO,GAAG,IAAI,EAAE,CAAC;MAC7DJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEP,OAAO,CAAC;IACrE;IAEA;IACA,IAAI,CAAClB,gBAAgB,CAACmB,uBAAuB,CAACD,OAAO,EAAEG,KAAK,CAAC;EAC/D;EAEA;;;EAGQkC,6BAA6BA,CAAC3B,GAAW;IAC/C,MAAM4B,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,GAAG,EAAE,GAAG,IAAI,CAAC;IAEzC;IACA,IAAII,UAAU,GAAG,EAAE,CAAC,CAAC;IACrB,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;IAEf,IAAIjC,GAAG,CAACM,QAAQ,CAAC,aAAa,CAAC,EAAE;MAC/B0B,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIjC,GAAG,CAACM,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MACzC0B,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIjC,GAAG,CAACM,QAAQ,CAAC,aAAa,CAAC,IAAIN,GAAG,CAACM,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtE0B,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIjC,GAAG,CAACM,QAAQ,CAAC,cAAc,CAAC,EAAE;MACvC0B,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIjC,GAAG,CAACM,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACjC0B,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX;IAEA,OAAO;MACL,aAAa,EAAED,UAAU,CAACpB,QAAQ,EAAE;MACpC,mBAAmB,EAAEqB,KAAK,CAACrB,QAAQ,EAAE;MACrC,uBAAuB,EAAE,GAAG;MAC5B,mBAAmB,EAAE,CAACgB,GAAG,GAAGI,UAAU,EAAEpB,QAAQ,EAAE;MAClD,2BAA2B,EAAEqB,KAAK,CAACrB,QAAQ;KAC5C;EACH;EAEQ7B,iBAAiBA,CAAA;IACvB;IACA,OAAO8C,IAAI,CAACK,MAAM,EAAE,CAACtB,QAAQ,CAAC,EAAE,CAAC,CAACuB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAC3CN,IAAI,CAACK,MAAM,EAAE,CAACtB,QAAQ,CAAC,EAAE,CAAC,CAACuB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACpD;EAEA;;;EAGQ3C,cAAcA,CAAC4C,WAAgB;IACrC,MAAM9C,OAAO,GAA4B,EAAE;IAE3C,IAAI8C,WAAW,IAAIA,WAAW,CAACC,IAAI,EAAE;MACnCD,WAAW,CAACC,IAAI,EAAE,CAACC,OAAO,CAAEC,GAAW,IAAI;QACzCjD,OAAO,CAACiD,GAAG,CAAC7B,WAAW,EAAE,CAAC,GAAG0B,WAAW,CAACI,GAAG,CAACD,GAAG,CAAC;MACnD,CAAC,CAAC;IACJ;IAEA,OAAOjD,OAAO;EAChB;EAAC,QAAAmD,CAAA,G;qCArQU1E,eAAe,EAAA2E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfrF,eAAe;IAAAsF,OAAA,EAAftF,eAAe,CAAAuF;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}