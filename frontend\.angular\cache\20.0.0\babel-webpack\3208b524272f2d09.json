{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, DOCUMENT, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenuItem_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [\"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closed.emit(\"click\"));\n    })(\"animationstart\", function MatMenu_ng_template_0_Template_div_animationstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationStart($event.animationName));\n    })(\"animationend\", function MatMenu_ng_template_0_Template_div_animationend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    })(\"animationcancel\", function MatMenu_ng_template_0_Template_div_animationcancel_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-menu-panel-animations-disabled\", ctx_r1._animationsDisabled)(\"mat-menu-panel-exit-animation\", ctx_r1._panelAnimationState === \"void\")(\"mat-menu-panel-animating\", ctx_r1._isAnimating);\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1.ariaLabelledby || null)(\"aria-describedby\", ctx_r1.ariaDescribedby || null);\n  }\n}\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n  _elementRef = inject(ElementRef);\n  _document = inject(DOCUMENT);\n  _focusMonitor = inject(FocusMonitor);\n  _parentMenu = inject(MAT_MENU_PANEL, {\n    optional: true\n  });\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  /** ARIA role for the menu item. */\n  role = 'menuitem';\n  /** Whether the menu item is disabled. */\n  disabled = false;\n  /** Whether ripples are disabled on the menu item. */\n  disableRipple = false;\n  /** Stream that emits when the menu item is hovered. */\n  _hovered = new Subject();\n  /** Stream that emits when the menu item is focused. */\n  _focused = new Subject();\n  /** Whether the menu item is highlighted. */\n  _highlighted = false;\n  /** Whether the menu item acts as a trigger for a sub-menu. */\n  _triggersSubmenu = false;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    this._parentMenu?.addItem?.(this);\n  }\n  /** Focuses the menu item. */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n    this._focused.next(this);\n  }\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n    this._hovered.complete();\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n    return clone.textContent?.trim() || '';\n  }\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef.markForCheck();\n  }\n  _setTriggersSubmenu(triggersSubmenu) {\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef.markForCheck();\n  }\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n  static ɵfac = function MatMenuItem_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMenuItem)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatMenuItem,\n    selectors: [[\"\", \"mat-menu-item\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-focus-indicator\"],\n    hostVars: 8,\n    hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n          return ctx._checkDisabled($event);\n        })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n          return ctx._handleMouseEnter();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n        i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n      }\n    },\n    inputs: {\n      role: \"role\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n    },\n    exportAs: [\"matMenuItem\"],\n    attrs: _c0,\n    ngContentSelectors: _c2,\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n    template: function MatMenuItem_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"span\", 0);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(3, \"div\", 1);\n        i0.ɵɵconditionalCreate(4, MatMenuItem_Conditional_4_Template, 2, 0, \":svg:svg\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._triggersSubmenu ? 4 : -1);\n      }\n    },\n    dependencies: [MatRipple],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      host: {\n        '[attr.role]': 'role',\n        'class': 'mat-mdc-menu-item mat-focus-indicator',\n        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': 'disabled || null',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [MatRipple],\n      template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\"\n    }]\n  }], () => [], {\n    role: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n  _template = inject(TemplateRef);\n  _appRef = inject(ApplicationRef);\n  _injector = inject(Injector);\n  _viewContainerRef = inject(ViewContainerRef);\n  _document = inject(DOCUMENT);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _portal;\n  _outlet;\n  /** Emits when the menu content has been attached. */\n  _attached = new Subject();\n  constructor() {}\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n    this.detach();\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._appRef, this._injector);\n    }\n    const element = this._template.elementRef.nativeElement;\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode.insertBefore(this._outlet.outletElement, element);\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    this._changeDetectorRef.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal?.isAttached) {\n      this._portal.detach();\n    }\n  }\n  ngOnDestroy() {\n    this.detach();\n    this._outlet?.dispose();\n  }\n  static ɵfac = function MatMenuContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMenuContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatMenuContent,\n    selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_MENU_CONTENT,\n      useExisting: MatMenuContent\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nclass MatMenu {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _injector = inject(Injector);\n  _keyManager;\n  _xPosition;\n  _yPosition;\n  _firstItemFocusRef;\n  _exitFallbackTimeout;\n  /** Whether animations are currently disabled. */\n  _animationsDisabled = _animationsDisabled();\n  /** All items inside the menu. Includes items nested inside another menu. */\n  _allItems;\n  /** Only the direct descendant menu items. */\n  _directDescendantItems = new QueryList();\n  /** Classes to be applied to the menu panel. */\n  _classList = {};\n  /** Current state of the panel animation. */\n  _panelAnimationState = 'void';\n  /** Emits whenever an animation on the menu completes. */\n  _animationDone = new Subject();\n  /** Whether the menu is animating. */\n  _isAnimating = false;\n  /** Parent menu of the current menu panel. */\n  parentMenu;\n  /** Layout direction of the menu. */\n  direction;\n  /** Class or list of classes to be added to the overlay panel. */\n  overlayPanelClass;\n  /** Class to be added to the backdrop element. */\n  backdropClass;\n  /** aria-label for the menu panel. */\n  ariaLabel;\n  /** aria-labelledby for the menu panel. */\n  ariaLabelledby;\n  /** aria-describedby for the menu panel. */\n  ariaDescribedby;\n  /** Position of the menu in the X axis. */\n  get xPosition() {\n    return this._xPosition;\n  }\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n  get yPosition() {\n    return this._yPosition;\n  }\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /** @docs-private */\n  templateRef;\n  /**\n   * List of the items inside of a menu.\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  items;\n  /**\n   * Menu content that will be rendered lazily.\n   * @docs-private\n   */\n  lazyContent;\n  /** Whether the menu should overlap its trigger. */\n  overlapTrigger;\n  /** Whether the menu has a backdrop. */\n  hasBackdrop;\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n    const newClassList = {\n      ...this._classList\n    };\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        newClassList[className] = false;\n      });\n    }\n    this._previousPanelClass = classes;\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        newClassList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n    this._classList = newClassList;\n  }\n  _previousPanelClass;\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  get classList() {\n    return this.panelClass;\n  }\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n  /** Event emitted when the menu is closed. */\n  closed = new EventEmitter();\n  /**\n   * Event emitted when the menu is closed.\n   * @deprecated Switch to `closed` instead\n   * @breaking-change 8.0.0\n   */\n  close = this.closed;\n  panelId = inject(_IdGenerator).getId('mat-menu-panel-');\n  constructor() {\n    const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this.overlapTrigger = defaultOptions.overlapTrigger;\n    this.hasBackdrop = defaultOptions.hasBackdrop;\n  }\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusRef?.destroy();\n    clearTimeout(this._exitFallbackTimeout);\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n        manager.onKeydown(event);\n        return;\n    }\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin = 'program') {\n    // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusRef?.destroy();\n    this._firstItemFocusRef = afterNextRender(() => {\n      const menuPanel = this._resolvePanel();\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * @deprecated No longer used and will be removed.\n   * @breaking-change 21.0.0\n   */\n  setElevation(_depth) {}\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    this._classList = {\n      ...this._classList,\n      ['mat-menu-before']: posX === 'before',\n      ['mat-menu-after']: posX === 'after',\n      ['mat-menu-above']: posY === 'above',\n      ['mat-menu-below']: posY === 'below'\n    };\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(state) {\n    const isExit = state === EXIT_ANIMATION;\n    if (isExit || state === ENTER_ANIMATION) {\n      if (isExit) {\n        clearTimeout(this._exitFallbackTimeout);\n        this._exitFallbackTimeout = undefined;\n      }\n      this._animationDone.next(isExit ? 'void' : 'enter');\n      this._isAnimating = false;\n    }\n  }\n  _onAnimationStart(state) {\n    if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n      this._isAnimating = true;\n    }\n  }\n  _setIsOpen(isOpen) {\n    this._panelAnimationState = isOpen ? 'enter' : 'void';\n    if (isOpen) {\n      if (this._keyManager.activeItemIndex === 0) {\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        const menuPanel = this._resolvePanel();\n        if (menuPanel) {\n          menuPanel.scrollTop = 0;\n        }\n      }\n    } else if (!this._animationsDisabled) {\n      // Some apps do `* { animation: none !important; }` in tests which will prevent the\n      // `animationend` event from firing. Since the exit animation is loading-bearing for\n      // removing the content from the DOM, add a fallback timer.\n      this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n    }\n    // Animation events won't fire when animations are disabled so we simulate them.\n    if (this._animationsDisabled) {\n      setTimeout(() => {\n        this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n      });\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n  /** Gets the menu panel DOM node. */\n  _resolvePanel() {\n    let menuPanel = null;\n    if (this._directDescendantItems.length) {\n      // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n      // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n      // because the panel is inside an `ng-template`. We work around it by starting from one of\n      // the items and walking up the DOM.\n      menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n    }\n    return menuPanel;\n  }\n  static ɵfac = function MatMenu_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMenu)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatMenu,\n    selectors: [[\"mat-menu\"]],\n    contentQueries: function MatMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n      }\n    },\n    viewQuery: function MatMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n      }\n    },\n    hostVars: 3,\n    hostBindings: function MatMenu_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n      }\n    },\n    inputs: {\n      backdropClass: \"backdropClass\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n      xPosition: \"xPosition\",\n      yPosition: \"yPosition\",\n      overlapTrigger: [2, \"overlapTrigger\", \"overlapTrigger\", booleanAttribute],\n      hasBackdrop: [2, \"hasBackdrop\", \"hasBackdrop\", value => value == null ? null : booleanAttribute(value)],\n      panelClass: [0, \"class\", \"panelClass\"],\n      classList: \"classList\"\n    },\n    outputs: {\n      closed: \"closed\",\n      close: \"close\"\n    },\n    exportAs: [\"matMenu\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_MENU_PANEL,\n      useExisting: MatMenu\n    }])],\n    ngContentSelectors: _c3,\n    decls: 1,\n    vars: 0,\n    consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", 3, \"click\", \"animationstart\", \"animationend\", \"animationcancel\", \"id\"], [1, \"mat-mdc-menu-content\"]],\n    template: function MatMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 12, \"ng-template\");\n      }\n    },\n    styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null'\n      },\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"]\n    }]\n  }], () => [], {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? null : booleanAttribute(value)\n      }]\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(_overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n  _element = inject(ElementRef);\n  _viewContainerRef = inject(ViewContainerRef);\n  _menuItemInstance = inject(MatMenuItem, {\n    optional: true,\n    self: true\n  });\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _focusMonitor = inject(FocusMonitor);\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _animationsDisabled = _animationsDisabled();\n  _cleanupTouchstart;\n  _portal;\n  _overlayRef = null;\n  _menuOpen = false;\n  _closingActionsSubscription = Subscription.EMPTY;\n  _hoverSubscription = Subscription.EMPTY;\n  _menuCloseSubscription = Subscription.EMPTY;\n  _pendingRemoval;\n  /**\n   * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n   * interface lacks some functionality around nested menus and animations.\n   */\n  _parentMaterialMenu;\n  /**\n   * Cached value of the padding of the parent menu panel.\n   * Used to offset sub-menus to compensate for the padding.\n   */\n  _parentInnerPadding;\n  // Tracking input type is necessary so it's possible to only auto-focus\n  // the first item of the list when the menu is opened via the keyboard\n  _openedBy = undefined;\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n  get menu() {\n    return this._menu;\n  }\n  set menu(menu) {\n    if (menu === this._menu) {\n      return;\n    }\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason);\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  _menu;\n  /** Data to be passed along to any lazily-rendered content. */\n  menuData;\n  /**\n   * Whether focus should be restored when the menu is closed.\n   * Note that disabling this option can have accessibility implications\n   * and it's up to you to manage focus, if you decide to turn it off.\n   */\n  restoreFocus = true;\n  /** Event emitted when the associated menu is opened. */\n  menuOpened = new EventEmitter();\n  /**\n   * Event emitted when the associated menu is opened.\n   * @deprecated Switch to `menuOpened` instead\n   * @breaking-change 8.0.0\n   */\n  // tslint:disable-next-line:no-output-on-prefix\n  onMenuOpen = this.menuOpened;\n  /** Event emitted when the associated menu is closed. */\n  menuClosed = new EventEmitter();\n  /**\n   * Event emitted when the associated menu is closed.\n   * @deprecated Switch to `menuClosed` instead\n   * @breaking-change 8.0.0\n   */\n  // tslint:disable-next-line:no-output-on-prefix\n  onMenuClose = this.menuClosed;\n  constructor() {\n    const parentMenu = inject(MAT_MENU_PANEL, {\n      optional: true\n    });\n    const renderer = inject(Renderer2);\n    this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n    this._cleanupTouchstart = renderer.listen(this._element.nativeElement, 'touchstart', event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    }, {\n      passive: true\n    });\n  }\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n  ngOnDestroy() {\n    if (this.menu && this._ownsMenu(this.menu)) {\n      PANELS_TO_TRIGGERS.delete(this.menu);\n    }\n    this._cleanupTouchstart();\n    this._pendingRemoval?.unsubscribe();\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n  /** Whether the menu is open. */\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n  openMenu() {\n    const menu = this.menu;\n    if (this._menuOpen || !menu) {\n      return;\n    }\n    this._pendingRemoval?.unsubscribe();\n    const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n    PANELS_TO_TRIGGERS.set(menu, this);\n    // If the same menu is currently attached to another trigger,\n    // we need to close it so it doesn't end up in a broken state.\n    if (previousTrigger && previousTrigger !== this) {\n      previousTrigger.closeMenu();\n    }\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n    // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n    if (!overlayRef.hasAttached()) {\n      overlayRef.attach(this._getPortal(menu));\n      menu.lazyContent?.attach(this.menuData);\n    }\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n    if (menu instanceof MatMenu) {\n      menu._setIsOpen(true);\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n  closeMenu() {\n    this.menu?.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition() {\n    this._overlayRef?.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n  _destroyMenu(reason) {\n    const overlayRef = this._overlayRef;\n    const menu = this._menu;\n    if (!overlayRef || !this.menuOpen) {\n      return;\n    }\n    this._closingActionsSubscription.unsubscribe();\n    this._pendingRemoval?.unsubscribe();\n    // Note that we don't wait for the animation to finish if another trigger took\n    // over the menu, because the panel will end up empty which looks glitchy.\n    if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n      this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n        overlayRef.detach();\n        menu.lazyContent?.detach();\n      });\n      menu._setIsOpen(false);\n    } else {\n      overlayRef.detach();\n      menu?.lazyContent?.detach();\n    }\n    if (menu && this._ownsMenu(menu)) {\n      PANELS_TO_TRIGGERS.delete(menu);\n    }\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n    this._openedBy = undefined;\n    this._setIsMenuOpen(false);\n  }\n  // set state rather than toggle to support triggers sharing a menu\n  _setIsMenuOpen(isOpen) {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(menu, config.positionStrategy);\n      this._overlayRef = createOverlayRef(this._injector, config);\n      this._overlayRef.keydownEvents().subscribe(event => {\n        if (this.menu instanceof MatMenu) {\n          this.menu._handleKeydown(event);\n        }\n      });\n    }\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: createFlexibleConnectedPositionStrategy(this._injector, this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir || 'ltr',\n      disableAnimations: this._animationsDisabled\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        this._ngZone.run(() => {\n          const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n          const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n          menu.setPositionClasses(posX, posY);\n        });\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n    const detachments = this._overlayRef.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => this._menuOpen && active !== this._menuItemInstance)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (this.triggersSubmenu() && this._parentMaterialMenu) {\n      this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n        if (active === this._menuItemInstance && !active.disabled) {\n          this._openedBy = 'mouse';\n          this.openMenu();\n        }\n      });\n    }\n  }\n  /** Gets the portal that should be attached to the overlay. */\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n    return this._portal;\n  }\n  /**\n   * Determines whether the trigger owns a specific menu panel, at the current point in time.\n   * This allows us to distinguish the case where the same panel is passed into multiple triggers\n   * and multiple are open at a time.\n   */\n  _ownsMenu(menu) {\n    return PANELS_TO_TRIGGERS.get(menu) === this;\n  }\n  static ɵfac = function MatMenuTrigger_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMenuTrigger)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatMenuTrigger,\n    selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n    hostVars: 3,\n    hostBindings: function MatMenuTrigger_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatMenuTrigger_click_HostBindingHandler($event) {\n          return ctx._handleClick($event);\n        })(\"mousedown\", function MatMenuTrigger_mousedown_HostBindingHandler($event) {\n          return ctx._handleMousedown($event);\n        })(\"keydown\", function MatMenuTrigger_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu == null ? null : ctx.menu.panelId : null);\n      }\n    },\n    inputs: {\n      _deprecatedMatMenuTriggerFor: [0, \"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n      menu: [0, \"matMenuTriggerFor\", \"menu\"],\n      menuData: [0, \"matMenuTriggerData\", \"menuData\"],\n      restoreFocus: [0, \"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n    },\n    outputs: {\n      menuOpened: \"menuOpened\",\n      onMenuOpen: \"onMenuOpen\",\n      menuClosed: \"menuClosed\",\n      onMenuClose: \"onMenuClose\"\n    },\n    exportAs: [\"matMenuTrigger\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-mdc-menu-trigger',\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen',\n        '[attr.aria-controls]': 'menuOpen ? menu?.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      exportAs: 'matMenuTrigger'\n    }]\n  }], () => [], {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\nclass MatMenuModule {\n  static ɵfac = function MatMenuModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatMenuModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n    imports: [MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, OverlayModule, MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n  // Represents:\n  // trigger('transformMenu', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => enter',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n  // ])\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: {\n    type: 7,\n    name: 'transformMenu',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => enter',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 1,\n            transform: 'scale(1)'\n          },\n          offset: null\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms 25ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('fadeInItems', [\n  //   // TODO(crisbeto): this is inside the `transformMenu`\n  //   // now. Remove next time we do breaking changes.\n  //   state('showing', style({opacity: 1})),\n  //   transition('void => *', [\n  //     style({opacity: 0}),\n  //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: {\n    type: 7,\n    name: 'fadeInItems',\n    definitions: [{\n      type: 0,\n      name: 'showing',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => *',\n      animation: [{\n        type: 6,\n        styles: {\n          opacity: 0\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "DOCUMENT", "ChangeDetectorRef", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "TemplateRef", "ApplicationRef", "Injector", "ViewContainerRef", "Directive", "QueryList", "EventEmitter", "afterNextRender", "ContentChildren", "ViewChild", "ContentChild", "Output", "NgZone", "Renderer2", "NgModule", "FocusMonitor", "_IdGenerator", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "Subject", "merge", "Subscription", "of", "startWith", "switchMap", "takeUntil", "take", "filter", "_CdkPrivateStyleLoader", "_", "_StructuralStylesLoader", "M", "<PERSON><PERSON><PERSON><PERSON>", "TemplatePortal", "DomPortalOutlet", "_animationsDisabled", "Directionality", "createRepositionScrollStrategy", "createOverlayRef", "OverlayConfig", "createFlexibleConnectedPositionStrategy", "OverlayModule", "CdkScrollableModule", "MatRippleModule", "MatCommonModule", "_c0", "_c1", "_c2", "MatMenuItem_Conditional_4_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "_c3", "MatMenu_ng_template_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "MatMenu_ng_template_0_Template_div_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "closed", "emit", "MatMenu_ng_template_0_Template_div_animationstart_0_listener", "$event", "_onAnimationStart", "animationName", "MatMenu_ng_template_0_Template_div_animationend_0_listener", "_onAnimationDone", "MatMenu_ng_template_0_Template_div_animationcancel_0_listener", "ɵɵprojection", "ɵɵclassMap", "_classList", "ɵɵclassProp", "_panelAnimationState", "_isAnimating", "ɵɵproperty", "panelId", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "MAT_MENU_PANEL", "MatMenuItem", "_elementRef", "_document", "_focusMonitor", "_parentMenu", "optional", "_changeDetectorRef", "role", "disabled", "disable<PERSON><PERSON><PERSON>", "_hovered", "_focused", "_highlighted", "_triggersSubmenu", "constructor", "load", "addItem", "focus", "origin", "options", "focusVia", "_getHostElement", "next", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "removeItem", "complete", "_getTabIndex", "nativeElement", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_setTriggersSubmenu", "triggersSubmenu", "_hasFocus", "activeElement", "ɵfac", "MatMenuItem_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatMenuItem_HostBindings", "MatMenuItem_click_HostBindingHandler", "MatMenuItem_mouseenter_HostBindingHandler", "inputs", "exportAs", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatMenuItem_Template", "ɵɵprojectionDef", "ɵɵconditionalCreate", "ɵɵadvance", "ɵɵconditional", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "imports", "transform", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_CONTENT", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_template", "_appRef", "_injector", "_viewContainerRef", "_portal", "_outlet", "_attached", "attach", "context", "detach", "createElement", "element", "elementRef", "parentNode", "insertBefore", "outletElement", "isAttached", "dispose", "MatMenuContent_Factory", "ɵdir", "ɵɵdefineDirective", "features", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatMenu", "_keyManager", "_xPosition", "_yPosition", "_firstItemFocusRef", "_exitFallbackTimeout", "_allItems", "_directDescendantItems", "_animationDone", "parentMenu", "direction", "overlayPanelClass", "value", "setPositionClasses", "templateRef", "items", "lazyContent", "hasBackdrop", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "newClassList", "split", "for<PERSON>ach", "className", "classList", "close", "getId", "defaultOptions", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "changes", "pipe", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "destroy", "clearTimeout", "itemChanges", "_item", "_handleKeydown", "keyCode", "setFocusOrigin", "onKeydown", "focusFirstItem", "menuPanel", "_resolvePanel", "contains", "document", "setFirstItemActive", "injector", "resetActiveItem", "setElevation", "_depth", "posX", "posY", "state", "isExit", "undefined", "_setIsOpen", "isOpen", "scrollTop", "setTimeout", "reset", "notifyOn<PERSON><PERSON>es", "first", "closest", "MatMenu_Factory", "contentQueries", "MatMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "MatMenu_Query", "ɵɵviewQuery", "MatMenu_HostBindings", "outputs", "MatMenu_Template", "ɵɵtemplate", "styles", "descendants", "MAT_MENU_SCROLL_STRATEGY", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "_overlay", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "MENU_PANEL_TOP_PADDING", "PANELS_TO_TRIGGERS", "WeakMap", "MatMenuTrigger", "_element", "_menuItemInstance", "self", "_dir", "_ngZone", "_scrollStrategy", "_cleanupTouchstart", "_overlayRef", "_menuOpen", "_closingActionsSubscription", "EMPTY", "_hoverSubscription", "_menuCloseSubscription", "_pending<PERSON><PERSON><PERSON><PERSON>", "_parentMaterialMenu", "_parentInnerPadding", "_openedBy", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "unsubscribe", "reason", "_destroyMenu", "menuData", "restoreFocus", "menuOpened", "onMenuOpen", "menuClosed", "onMenuClose", "renderer", "listen", "passive", "_handleHover", "_ownsMenu", "delete", "menuOpen", "dir", "toggleMenu", "closeMenu", "openMenu", "previousTrigger", "get", "set", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "has<PERSON>tta<PERSON>", "_getPortal", "_menuClosingActions", "_setIsMenuOpen", "withLockedPosition", "reapplyLastPosition", "updatePosition", "config", "_getOverlayConfig", "_subscribeToPositions", "keydownEvents", "withGrowAfterOpen", "withTransformOriginOn", "scrollStrategy", "disableAnimations", "position", "position<PERSON><PERSON>es", "change", "run", "connectionPair", "overlayX", "overlayY", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "MatMenuTrigger_Factory", "MatMenuTrigger_HostBindings", "MatMenuTrigger_click_HostBindingHandler", "MatMenuTrigger_mousedown_HostBindingHandler", "MatMenuTrigger_keydown_HostBindingHandler", "MatMenuModule", "MatMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "matMenuAnimations", "transformMenu", "name", "definitions", "opacity", "offset", "expr", "animation", "timings", "fadeInItems"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/material/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, DOCUMENT, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    _focusMonitor = inject(FocusMonitor);\n    _parentMenu = inject(MAT_MENU_PANEL, { optional: true });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** ARIA role for the menu item. */\n    role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    _hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    _focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    _highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    _triggersSubmenu = false;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        this._parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element, so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons, so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n        this._triggersSubmenu = triggersSubmenu;\n        this._changeDetectorRef.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuItem, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatMenuItem, isStandalone: true, selector: \"[mat-menu-item]\", inputs: { role: \"role\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-mdc-menu-item-highlighted\": \"_highlighted\", \"class.mat-mdc-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-mdc-menu-item mat-focus-indicator\" }, exportAs: [\"matMenuItem\"], ngImport: i0, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\", dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', host: {\n                        '[attr.role]': 'role',\n                        'class': 'mat-mdc-menu-item mat-focus-indicator',\n                        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [MatRipple], template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { role: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n    _template = inject(TemplateRef);\n    _appRef = inject(ApplicationRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _document = inject(DOCUMENT);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _portal;\n    _outlet;\n    /** Emits when the menu content has been attached. */\n    _attached = new Subject();\n    constructor() { }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        this._changeDetectorRef.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal?.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._outlet?.dispose();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatMenuContent, isStandalone: true, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                }]\n        }], ctorParameters: () => [] });\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nclass MatMenu {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _keyManager;\n    _xPosition;\n    _yPosition;\n    _firstItemFocusRef;\n    _exitFallbackTimeout;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled = _animationsDisabled();\n    /** All items inside the menu. Includes items nested inside another menu. */\n    _allItems;\n    /** Only the direct descendant menu items. */\n    _directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    _classList = {};\n    /** Current state of the panel animation. */\n    _panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    _animationDone = new Subject();\n    /** Whether the menu is animating. */\n    _isAnimating = false;\n    /** Parent menu of the current menu panel. */\n    parentMenu;\n    /** Layout direction of the menu. */\n    direction;\n    /** Class or list of classes to be added to the overlay panel. */\n    overlayPanelClass;\n    /** Class to be added to the backdrop element. */\n    backdropClass;\n    /** aria-label for the menu panel. */\n    ariaLabel;\n    /** aria-labelledby for the menu panel. */\n    ariaLabelledby;\n    /** aria-describedby for the menu panel. */\n    ariaDescribedby;\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /** @docs-private */\n    templateRef;\n    /**\n     * List of the items inside of a menu.\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    items;\n    /**\n     * Menu content that will be rendered lazily.\n     * @docs-private\n     */\n    lazyContent;\n    /** Whether the menu should overlap its trigger. */\n    overlapTrigger;\n    /** Whether the menu has a backdrop. */\n    hasBackdrop;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        const newClassList = { ...this._classList };\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                newClassList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                newClassList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n        this._classList = newClassList;\n    }\n    _previousPanelClass;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    /** Event emitted when the menu is closed. */\n    closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    close = this.closed;\n    panelId = inject(_IdGenerator).getId('mat-menu-panel-');\n    constructor() {\n        const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n        this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n        this._xPosition = defaultOptions.xPosition;\n        this._yPosition = defaultOptions.yPosition;\n        this.backdropClass = defaultOptions.backdropClass;\n        this.overlapTrigger = defaultOptions.overlapTrigger;\n        this.hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._directDescendantItems.destroy();\n        this.closed.complete();\n        this._firstItemFocusRef?.destroy();\n        clearTimeout(this._exitFallbackTimeout);\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._firstItemFocusRef?.destroy();\n        this._firstItemFocusRef = afterNextRender(() => {\n            const menuPanel = this._resolvePanel();\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        }, { injector: this._injector });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * @deprecated No longer used and will be removed.\n     * @breaking-change 21.0.0\n     */\n    setElevation(_depth) { }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        this._classList = {\n            ...this._classList,\n            ['mat-menu-before']: posX === 'before',\n            ['mat-menu-after']: posX === 'after',\n            ['mat-menu-above']: posY === 'above',\n            ['mat-menu-below']: posY === 'below',\n        };\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(state) {\n        const isExit = state === EXIT_ANIMATION;\n        if (isExit || state === ENTER_ANIMATION) {\n            if (isExit) {\n                clearTimeout(this._exitFallbackTimeout);\n                this._exitFallbackTimeout = undefined;\n            }\n            this._animationDone.next(isExit ? 'void' : 'enter');\n            this._isAnimating = false;\n        }\n    }\n    _onAnimationStart(state) {\n        if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n            this._isAnimating = true;\n        }\n    }\n    _setIsOpen(isOpen) {\n        this._panelAnimationState = isOpen ? 'enter' : 'void';\n        if (isOpen) {\n            if (this._keyManager.activeItemIndex === 0) {\n                // Scroll the content element to the top as soon as the animation starts. This is necessary,\n                // because we move focus to the first item while it's still being animated, which can throw\n                // the browser off when it determines the scroll position. Alternatively we can move focus\n                // when the animation is done, however moving focus asynchronously will interrupt screen\n                // readers which are in the process of reading out the menu already. We take the `element`\n                // from the `event` since we can't use a `ViewChild` to access the pane.\n                const menuPanel = this._resolvePanel();\n                if (menuPanel) {\n                    menuPanel.scrollTop = 0;\n                }\n            }\n        }\n        else if (!this._animationsDisabled) {\n            // Some apps do `* { animation: none !important; }` in tests which will prevent the\n            // `animationend` event from firing. Since the exit animation is loading-bearing for\n            // removing the content from the DOM, add a fallback timer.\n            this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n        }\n        // Animation events won't fire when animations are disabled so we simulate them.\n        if (this._animationsDisabled) {\n            setTimeout(() => {\n                this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n            });\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n    /** Gets the menu panel DOM node. */\n    _resolvePanel() {\n        let menuPanel = null;\n        if (this._directDescendantItems.length) {\n            // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n            // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n            // because the panel is inside an `ng-template`. We work around it by starting from one of\n            // the items and walking up the DOM.\n            menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n        }\n        return menuPanel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenu, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatMenu, isStandalone: true, selector: \"mat-menu\", inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: [\"overlapTrigger\", \"overlapTrigger\", booleanAttribute], hasBackdrop: [\"hasBackdrop\", \"hasBackdrop\", (value) => (value == null ? null : booleanAttribute(value))], panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, host: { properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], exportAs: [\"matMenu\"], ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                    }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? null : booleanAttribute(value)) }]\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createRepositionScrollStrategy(injector);\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(_overlay) {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n    _element = inject(ElementRef);\n    _viewContainerRef = inject(ViewContainerRef);\n    _menuItemInstance = inject(MatMenuItem, { optional: true, self: true });\n    _dir = inject(Directionality, { optional: true });\n    _focusMonitor = inject(FocusMonitor);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _animationsDisabled = _animationsDisabled();\n    _cleanupTouchstart;\n    _portal;\n    _overlayRef = null;\n    _menuOpen = false;\n    _closingActionsSubscription = Subscription.EMPTY;\n    _hoverSubscription = Subscription.EMPTY;\n    _menuCloseSubscription = Subscription.EMPTY;\n    _pendingRemoval;\n    /**\n     * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n     * interface lacks some functionality around nested menus and animations.\n     */\n    _parentMaterialMenu;\n    /**\n     * Cached value of the padding of the parent menu panel.\n     * Used to offset sub-menus to compensate for the padding.\n     */\n    _parentInnerPadding;\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    _openedBy = undefined;\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n        this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    _menu;\n    /** Data to be passed along to any lazily-rendered content. */\n    menuData;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuClose = this.menuClosed;\n    constructor() {\n        const parentMenu = inject(MAT_MENU_PANEL, { optional: true });\n        const renderer = inject(Renderer2);\n        this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n        this._cleanupTouchstart = renderer.listen(this._element.nativeElement, 'touchstart', (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        }, { passive: true });\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this.menu && this._ownsMenu(this.menu)) {\n            PANELS_TO_TRIGGERS.delete(this.menu);\n        }\n        this._cleanupTouchstart();\n        this._pendingRemoval?.unsubscribe();\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        this._pendingRemoval?.unsubscribe();\n        const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n        PANELS_TO_TRIGGERS.set(menu, this);\n        // If the same menu is currently attached to another trigger,\n        // we need to close it so it doesn't end up in a broken state.\n        if (previousTrigger && previousTrigger !== this) {\n            previousTrigger.closeMenu();\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n        // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n        if (!overlayRef.hasAttached()) {\n            overlayRef.attach(this._getPortal(menu));\n            menu.lazyContent?.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n        if (menu instanceof MatMenu) {\n            menu._setIsOpen(true);\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        const overlayRef = this._overlayRef;\n        const menu = this._menu;\n        if (!overlayRef || !this.menuOpen) {\n            return;\n        }\n        this._closingActionsSubscription.unsubscribe();\n        this._pendingRemoval?.unsubscribe();\n        // Note that we don't wait for the animation to finish if another trigger took\n        // over the menu, because the panel will end up empty which looks glitchy.\n        if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n            this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n                overlayRef.detach();\n                menu.lazyContent?.detach();\n            });\n            menu._setIsOpen(false);\n        }\n        else {\n            overlayRef.detach();\n            menu?.lazyContent?.detach();\n        }\n        if (menu && this._ownsMenu(menu)) {\n            PANELS_TO_TRIGGERS.delete(menu);\n        }\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        this._setIsMenuOpen(false);\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        if (isOpen !== this._menuOpen) {\n            this._menuOpen = isOpen;\n            this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n            if (this.triggersSubmenu()) {\n                this._menuItemInstance._setHighlighted(isOpen);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = createOverlayRef(this._injector, config);\n            this._overlayRef.keydownEvents().subscribe(event => {\n                if (this.menu instanceof MatMenu) {\n                    this.menu._handleKeydown(event);\n                }\n            });\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: createFlexibleConnectedPositionStrategy(this._injector, this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir || 'ltr',\n            disableAnimations: this._animationsDisabled,\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                this._ngZone.run(() => {\n                    const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                    const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                    menu.setPositionClasses(posX, posY);\n                });\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu\n                ._hovered()\n                .pipe(filter(active => this._menuOpen && active !== this._menuItemInstance))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (this.triggersSubmenu() && this._parentMaterialMenu) {\n            this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n                if (active === this._menuItemInstance && !active.disabled) {\n                    this._openedBy = 'mouse';\n                    this.openMenu();\n                }\n            });\n        }\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n    /**\n     * Determines whether the trigger owns a specific menu panel, at the current point in time.\n     * This allows us to distinguish the case where the same panel is passed into multiple triggers\n     * and multiple are open at a time.\n     */\n    _ownsMenu(menu) {\n        return PANELS_TO_TRIGGERS.get(menu) === this;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatMenuTrigger, isStandalone: true, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen\", \"attr.aria-controls\": \"menuOpen ? menu?.panelId : null\" }, classAttribute: \"mat-mdc-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-mdc-menu-trigger',\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen',\n                        '[attr.aria-controls]': 'menuOpen ? menu?.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                    exportAs: 'matMenuTrigger',\n                }]\n        }], ctorParameters: () => [], propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n\nclass MatMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, imports: [MatRippleModule,\n            MatCommonModule,\n            OverlayModule,\n            MatMenu,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger], exports: [CdkScrollableModule,\n            MatMenu,\n            MatCommonModule,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [MatRippleModule,\n            MatCommonModule,\n            OverlayModule, CdkScrollableModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatRippleModule,\n                        MatCommonModule,\n                        OverlayModule,\n                        MatMenu,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatMenu,\n                        MatCommonModule,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n    // Represents:\n    // trigger('transformMenu', [\n    //   state(\n    //     'void',\n    //     style({\n    //       opacity: 0,\n    //       transform: 'scale(0.8)',\n    //     }),\n    //   ),\n    //   transition(\n    //     'void => enter',\n    //     animate(\n    //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n    //       style({\n    //         opacity: 1,\n    //         transform: 'scale(1)',\n    //       }),\n    //     ),\n    //   ),\n    //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n    // ])\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: {\n        type: 7,\n        name: 'transformMenu',\n        definitions: [\n            {\n                type: 0,\n                name: 'void',\n                styles: { type: 6, styles: { opacity: 0, transform: 'scale(0.8)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'void => enter',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 1, transform: 'scale(1)' }, offset: null },\n                    timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '100ms 25ms linear',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('fadeInItems', [\n    //   // TODO(crisbeto): this is inside the `transformMenu`\n    //   // now. Remove next time we do breaking changes.\n    //   state('showing', style({opacity: 1})),\n    //   transition('void => *', [\n    //     style({opacity: 0}),\n    //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    //   ]),\n    // ])\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: {\n        type: 7,\n        name: 'fadeInItems',\n        definitions: [\n            {\n                type: 0,\n                name: 'showing',\n                styles: { type: 6, styles: { opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'void => *',\n                animation: [\n                    { type: 6, styles: { opacity: 0 }, offset: null },\n                    { type: 4, styles: null, timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)' },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC5W,SAASC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AAClJ,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC3H,SAASC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvD,SAASC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC9E,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AACrE,SAASL,CAAC,IAAIM,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,8BAA8B,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,uCAAuC,EAAEC,aAAa,QAAQ,sBAAsB;AAC9J,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASX,CAAC,IAAIY,eAAe,QAAQ,sBAAsB;AAC3D,SAASZ,CAAC,IAAIa,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;;AAE5B;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwG6FrE,EAAE,CAAAuE,cAAA;IAAFvE,EAAE,CAAAwE,cAAA,YACknC,CAAC;IADrnCxE,EAAE,CAAAyE,SAAA,gBACopC,CAAC;IADvpCzE,EAAE,CAAA0E,YAAA,CAC0pC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAQ,GAAA,GAD7pC7E,EAAE,CAAA8E,gBAAA;IAAF9E,EAAE,CAAAwE,cAAA,YA4e21D,CAAC;IA5e91DxE,EAAE,CAAA+E,UAAA,mBAAAC,oDAAA;MAAFhF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4e67CF,MAAA,CAAAG,MAAA,CAAAC,IAAA,CAAY,OAAO,CAAC;IAAA,CAAC,CAAC,4BAAAC,6DAAAC,MAAA;MA5er9CxF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4eohDF,MAAA,CAAAO,iBAAA,CAAAD,MAAA,CAAAE,aAAsC,CAAC;IAAA,CAAC,CAAC,0BAAAC,2DAAAH,MAAA;MA5e/jDxF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4eolDF,MAAA,CAAAU,gBAAA,CAAAJ,MAAA,CAAAE,aAAqC,CAAC;IAAA,CAAC,CAAC,6BAAAG,8DAAAL,MAAA;MA5e9nDxF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4espDF,MAAA,CAAAU,gBAAA,CAAAJ,MAAA,CAAAE,aAAqC,CAAC;IAAA,CAAC,CAAC;IA5ehsD1F,EAAE,CAAAwE,cAAA,YA4eq4D,CAAC;IA5ex4DxE,EAAE,CAAA8F,YAAA,EA4es6D,CAAC;IA5ez6D9F,EAAE,CAAA0E,YAAA,CA4ek7D,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAa,MAAA,GA5e/7DlF,EAAE,CAAAmF,aAAA;IAAFnF,EAAE,CAAA+F,UAAA,CAAAb,MAAA,CAAAc,UA4e8tC,CAAC;IA5ejuChG,EAAE,CAAAiG,WAAA,uCAAAf,MAAA,CAAA3B,mBA4esyC,CAAC,kCAAA2B,MAAA,CAAAgB,oBAAA,WAA8E,CAAC,6BAAAhB,MAAA,CAAAiB,YAAsD,CAAC;IA5e/6CnG,EAAE,CAAAoG,UAAA,OAAAlB,MAAA,CAAAmB,OA4eksC,CAAC;IA5ersCrG,EAAE,CAAAsG,WAAA,eAAApB,MAAA,CAAAqB,SAAA,6BAAArB,MAAA,CAAAsB,cAAA,8BAAAtB,MAAA,CAAAuB,eAAA;EAAA;AAAA;AApG/F,MAAMC,cAAc,GAAG,IAAIzG,cAAc,CAAC,gBAAgB,CAAC;;AAE3D;AACA;AACA;AACA,MAAM0G,WAAW,CAAC;EACdC,WAAW,GAAG1G,MAAM,CAACC,UAAU,CAAC;EAChC0G,SAAS,GAAG3G,MAAM,CAACE,QAAQ,CAAC;EAC5B0G,aAAa,GAAG5G,MAAM,CAACwB,YAAY,CAAC;EACpCqF,WAAW,GAAG7G,MAAM,CAACwG,cAAc,EAAE;IAAEM,QAAQ,EAAE;EAAK,CAAC,CAAC;EACxDC,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;EAC9C;EACA6G,IAAI,GAAG,UAAU;EACjB;EACAC,QAAQ,GAAG,KAAK;EAChB;EACAC,aAAa,GAAG,KAAK;EACrB;EACAC,QAAQ,GAAG,IAAI9E,OAAO,CAAC,CAAC;EACxB;EACA+E,QAAQ,GAAG,IAAI/E,OAAO,CAAC,CAAC;EACxB;EACAgF,YAAY,GAAG,KAAK;EACpB;EACAC,gBAAgB,GAAG,KAAK;EACxBC,WAAWA,CAAA,EAAG;IACVvH,MAAM,CAAC8C,sBAAsB,CAAC,CAAC0E,IAAI,CAACxE,uBAAuB,CAAC;IAC5D,IAAI,CAAC6D,WAAW,EAAEY,OAAO,GAAG,IAAI,CAAC;EACrC;EACA;EACAC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAAChB,aAAa,IAAIe,MAAM,EAAE;MAC9B,IAAI,CAACf,aAAa,CAACiB,QAAQ,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAEH,MAAM,EAAEC,OAAO,CAAC;IACxE,CAAC,MACI;MACD,IAAI,CAACE,eAAe,CAAC,CAAC,CAACJ,KAAK,CAACE,OAAO,CAAC;IACzC;IACA,IAAI,CAACR,QAAQ,CAACW,IAAI,CAAC,IAAI,CAAC;EAC5B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACpB,aAAa,EAAE;MACpB;MACA;MACA;MACA,IAAI,CAACA,aAAa,CAACqB,OAAO,CAAC,IAAI,CAACvB,WAAW,EAAE,KAAK,CAAC;IACvD;EACJ;EACAwB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACtB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACuB,cAAc,CAAC,IAAI,CAACzB,WAAW,CAAC;IACvD;IACA,IAAI,IAAI,CAACG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,UAAU,EAAE;MACjD,IAAI,CAACvB,WAAW,CAACuB,UAAU,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACjB,QAAQ,CAACiB,QAAQ,CAAC,CAAC;EAC5B;EACA;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrB,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAa,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpB,WAAW,CAAC6B,aAAa;EACzC;EACA;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,IAAI,CAACxB,QAAQ,EAAE;MACfwB,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACzB,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAc,QAAQA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG,IAAI,CAACpC,WAAW,CAAC6B,aAAa,CAACQ,SAAS,CAAC,IAAI,CAAC;IAC5D,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAgB,CAAC,2BAA2B,CAAC;IACjE;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCF,KAAK,CAACE,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;IACrB;IACA,OAAON,KAAK,CAACO,WAAW,EAAEC,IAAI,CAAC,CAAC,IAAI,EAAE;EAC1C;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3B;IACA;IACA;IACA,IAAI,CAACnC,YAAY,GAAGmC,aAAa;IACjC,IAAI,CAACzC,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;EAC1C;EACAC,mBAAmBA,CAACC,eAAe,EAAE;IACjC,IAAI,CAACrC,gBAAgB,GAAGqC,eAAe;IACvC,IAAI,CAAC5C,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;EAC1C;EACAG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjD,SAAS,IAAI,IAAI,CAACA,SAAS,CAACkD,aAAa,KAAK,IAAI,CAAC/B,eAAe,CAAC,CAAC;EACpF;EACA,OAAOgC,IAAI,YAAAC,oBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFvD,WAAW;EAAA;EAC9G,OAAOwD,IAAI,kBAD8EnK,EAAE,CAAAoK,iBAAA;IAAAC,IAAA,EACJ1D,WAAW;IAAA2D,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAArG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADTrE,EAAE,CAAA+E,UAAA,mBAAA4F,qCAAAnF,MAAA;UAAA,OACJlB,GAAA,CAAAoE,cAAA,CAAAlD,MAAqB,CAAC;QAAA,CAAZ,CAAC,wBAAAoF,0CAAA;UAAA,OAAXtG,GAAA,CAAAwE,iBAAA,CAAkB,CAAC;QAAA,CAAT,CAAC;MAAA;MAAA,IAAAzE,EAAA;QADTrE,EAAE,CAAAsG,WAAA,SAAAhC,GAAA,CAAA4C,IAAA,cACJ5C,GAAA,CAAAkE,YAAA,CAAa,CAAC,mBAAAlE,GAAA,CAAA6C,QAAA,cAAA7C,GAAA,CAAA6C,QAAA,IAAF,IAAI;QADdnH,EAAE,CAAAiG,WAAA,kCAAA3B,GAAA,CAAAiD,YACM,CAAC,sCAAAjD,GAAA,CAAAkD,gBAAD,CAAC;MAAA;IAAA;IAAAqD,MAAA;MAAA3D,IAAA;MAAAC,QAAA,8BAA8G7G,gBAAgB;MAAA8G,aAAA,wCAAqD9G,gBAAgB;IAAA;IAAAwK,QAAA;IAAAC,KAAA,EAAA9G,GAAA;IAAA+G,kBAAA,EAAA7G,GAAA;IAAA8G,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qBAAAhH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD5MrE,EAAE,CAAAsL,eAAA,CAAApH,GAAA;QAAFlE,EAAE,CAAA8F,YAAA,EACiuB,CAAC;QADpuB9F,EAAE,CAAAwE,cAAA,aAC0wB,CAAC;QAD7wBxE,EAAE,CAAA8F,YAAA,KACmyB,CAAC;QADtyB9F,EAAE,CAAA0E,YAAA,CAC0yB,CAAC;QAD7yB1E,EAAE,CAAAyE,SAAA,YACw8B,CAAC;QAD38BzE,EAAE,CAAAuL,mBAAA,IAAAnH,kCAAA,qBACo+B,CAAC;MAAA;MAAA,IAAAC,EAAA;QADv+BrE,EAAE,CAAAwL,SAAA,EACg5B,CAAC;QADn5BxL,EAAE,CAAAoG,UAAA,sBAAA9B,GAAA,CAAA8C,aAAA,IAAA9C,GAAA,CAAA6C,QACg5B,CAAC,qBAAA7C,GAAA,CAAA0D,eAAA,EAA8C,CAAC;QADl8BhI,EAAE,CAAAwL,SAAA,CAC6pC,CAAC;QADhqCxL,EAAE,CAAAyL,aAAA,CAAAnH,GAAA,CAAAkD,gBAAA,SAC6pC,CAAC;MAAA;IAAA;IAAAkE,YAAA,GAA+CtI,SAAS;IAAAuI,aAAA;IAAAC,eAAA;EAAA;AACrzC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F7L,EAAE,CAAA8L,iBAAA,CAGJnF,WAAW,EAAc,CAAC;IACzG0D,IAAI,EAAE9J,SAAS;IACfwL,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAElB,QAAQ,EAAE,aAAa;MAAEmB,IAAI,EAAE;QACzD,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,uCAAuC;QAChD,uCAAuC,EAAE,cAAc;QACvD,2CAA2C,EAAE,kBAAkB;QAC/D,iBAAiB,EAAE,gBAAgB;QACnC,sBAAsB,EAAE,UAAU;QAClC,iBAAiB,EAAE,kBAAkB;QACrC,SAAS,EAAE,wBAAwB;QACnC,cAAc,EAAE;MACpB,CAAC;MAAEL,eAAe,EAAEpL,uBAAuB,CAAC0L,MAAM;MAAEP,aAAa,EAAElL,iBAAiB,CAAC0L,IAAI;MAAEC,OAAO,EAAE,CAAChJ,SAAS,CAAC;MAAEgI,QAAQ,EAAE;IAAigB,CAAC;EACzoB,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElE,IAAI,EAAE,CAAC;MAC/CmD,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAEyG,QAAQ,EAAE,CAAC;MACXkD,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE/L;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8G,aAAa,EAAE,CAAC;MAChBiD,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE/L;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAASgM,4BAA4BA,CAAA,EAAG;EACpC,MAAMC,KAAK,CAAC;AAChB,wEAAwE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,MAAMD,KAAK,CAAC;AAChB,uEAAuE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,MAAMF,KAAK,CAAC,gFAAgF,GACxF,sEAAsE,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,IAAIzM,cAAc,CAAC,gBAAgB,CAAC;AAC7D;AACA,MAAM0M,cAAc,CAAC;EACjBC,SAAS,GAAG1M,MAAM,CAACS,WAAW,CAAC;EAC/BkM,OAAO,GAAG3M,MAAM,CAACU,cAAc,CAAC;EAChCkM,SAAS,GAAG5M,MAAM,CAACW,QAAQ,CAAC;EAC5BkM,iBAAiB,GAAG7M,MAAM,CAACY,gBAAgB,CAAC;EAC5C+F,SAAS,GAAG3G,MAAM,CAACE,QAAQ,CAAC;EAC5B6G,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;EAC9C2M,OAAO;EACPC,OAAO;EACP;EACAC,SAAS,GAAG,IAAI3K,OAAO,CAAC,CAAC;EACzBkF,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;AACA;EACI0F,MAAMA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI3J,cAAc,CAAC,IAAI,CAACuJ,SAAS,EAAE,IAAI,CAACG,iBAAiB,CAAC;IAC7E;IACA,IAAI,CAACM,MAAM,CAAC,CAAC;IACb,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI3J,eAAe,CAAC,IAAI,CAACuD,SAAS,CAACyG,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAACT,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IACzG;IACA,MAAMS,OAAO,GAAG,IAAI,CAACX,SAAS,CAACY,UAAU,CAAC/E,aAAa;IACvD;IACA;IACA;IACA8E,OAAO,CAACE,UAAU,CAACC,YAAY,CAAC,IAAI,CAACT,OAAO,CAACU,aAAa,EAAEJ,OAAO,CAAC;IACpE;IACA;IACA;IACA;IACA;IACA,IAAI,CAACtG,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;IACtC,IAAI,CAACqD,OAAO,CAACG,MAAM,CAAC,IAAI,CAACF,OAAO,EAAEG,OAAO,CAAC;IAC1C,IAAI,CAACF,SAAS,CAACjF,IAAI,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACIoF,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACL,OAAO,EAAEY,UAAU,EAAE;MAC1B,IAAI,CAACZ,OAAO,CAACK,MAAM,CAAC,CAAC;IACzB;EACJ;EACAjF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiF,MAAM,CAAC,CAAC;IACb,IAAI,CAACJ,OAAO,EAAEY,OAAO,CAAC,CAAC;EAC3B;EACA,OAAO7D,IAAI,YAAA8D,uBAAA5D,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyC,cAAc;EAAA;EACjH,OAAOoB,IAAI,kBAjH8E/N,EAAE,CAAAgO,iBAAA;IAAA3D,IAAA,EAiHJsC,cAAc;IAAArC,SAAA;IAAA2D,QAAA,GAjHZjO,EAAE,CAAAkO,kBAAA,CAiHoF,CAAC;MAAEC,OAAO,EAAEzB,gBAAgB;MAAE0B,WAAW,EAAEzB;IAAe,CAAC,CAAC;EAAA;AAC/O;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAnH6F7L,EAAE,CAAA8L,iBAAA,CAmHJa,cAAc,EAAc,CAAC;IAC5GtC,IAAI,EAAEtJ,SAAS;IACfgL,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvCqC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEzB,gBAAgB;QAAE0B,WAAW,EAAEzB;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAM2B,wBAAwB,GAAG,IAAIrO,cAAc,CAAC,0BAA0B,EAAE;EAC5EsO,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,gCAAgCA,CAAA,EAAG;EACxC,OAAO;IACHC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE;EACnB,CAAC;AACL;AACA;AACA,MAAMC,eAAe,GAAG,iBAAiB;AACzC;AACA,MAAMC,cAAc,GAAG,gBAAgB;AACvC,MAAMC,OAAO,CAAC;EACVpI,WAAW,GAAG1G,MAAM,CAACC,UAAU,CAAC;EAChC8G,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;EAC9CyM,SAAS,GAAG5M,MAAM,CAACW,QAAQ,CAAC;EAC5BoO,WAAW;EACXC,UAAU;EACVC,UAAU;EACVC,kBAAkB;EAClBC,oBAAoB;EACpB;EACA9L,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3C;EACA+L,SAAS;EACT;EACAC,sBAAsB,GAAG,IAAIvO,SAAS,CAAC,CAAC;EACxC;EACAgF,UAAU,GAAG,CAAC,CAAC;EACf;EACAE,oBAAoB,GAAG,MAAM;EAC7B;EACAsJ,cAAc,GAAG,IAAIjN,OAAO,CAAC,CAAC;EAC9B;EACA4D,YAAY,GAAG,KAAK;EACpB;EACAsJ,UAAU;EACV;EACAC,SAAS;EACT;EACAC,iBAAiB;EACjB;EACAd,aAAa;EACb;EACAtI,SAAS;EACT;EACAC,cAAc;EACd;EACAC,eAAe;EACf;EACA,IAAIkI,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACO,UAAU;EAC1B;EACA,IAAIP,SAASA,CAACiB,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,OAAO,KAChB,OAAO/D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjDS,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAAC4C,UAAU,GAAGU,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA,IAAIjB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACO,UAAU;EAC1B;EACA,IAAIP,SAASA,CAACgB,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,KAAK,OAAO/D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3FW,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAAC2C,UAAU,GAAGS,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACAC,WAAW;EACX;AACJ;AACA;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,WAAW;EACX;EACAtB,cAAc;EACd;EACAuB,WAAW;EACX;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,UAAUA,CAACC,OAAO,EAAE;IACpB,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IACnD,MAAMC,YAAY,GAAG;MAAE,GAAG,IAAI,CAACtK;IAAW,CAAC;IAC3C,IAAIoK,kBAAkB,IAAIA,kBAAkB,CAAC/G,MAAM,EAAE;MACjD+G,kBAAkB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACjDH,YAAY,CAACG,SAAS,CAAC,GAAG,KAAK;MACnC,CAAC,CAAC;IACN;IACA,IAAI,CAACJ,mBAAmB,GAAGF,OAAO;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAAC9G,MAAM,EAAE;MAC3B8G,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACtCH,YAAY,CAACG,SAAS,CAAC,GAAG,IAAI;MAClC,CAAC,CAAC;MACF,IAAI,CAAC7J,WAAW,CAAC6B,aAAa,CAACgI,SAAS,GAAG,EAAE;IACjD;IACA,IAAI,CAACzK,UAAU,GAAGsK,YAAY;EAClC;EACAD,mBAAmB;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIK,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACR,UAAU;EAC1B;EACA,IAAIQ,SAASA,CAACP,OAAO,EAAE;IACnB,IAAI,CAACD,UAAU,GAAGC,OAAO;EAC7B;EACA;EACA9K,MAAM,GAAG,IAAIpE,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACI0P,KAAK,GAAG,IAAI,CAACtL,MAAM;EACnBgB,OAAO,GAAGnG,MAAM,CAACyB,YAAY,CAAC,CAACiP,KAAK,CAAC,iBAAiB,CAAC;EACvDnJ,WAAWA,CAAA,EAAG;IACV,MAAMoJ,cAAc,GAAG3Q,MAAM,CAACoO,wBAAwB,CAAC;IACvD,IAAI,CAACqB,iBAAiB,GAAGkB,cAAc,CAAClB,iBAAiB,IAAI,EAAE;IAC/D,IAAI,CAACT,UAAU,GAAG2B,cAAc,CAAClC,SAAS;IAC1C,IAAI,CAACQ,UAAU,GAAG0B,cAAc,CAACjC,SAAS;IAC1C,IAAI,CAACC,aAAa,GAAGgC,cAAc,CAAChC,aAAa;IACjD,IAAI,CAACH,cAAc,GAAGmC,cAAc,CAACnC,cAAc;IACnD,IAAI,CAACuB,WAAW,GAAGY,cAAc,CAACZ,WAAW;EACjD;EACAa,QAAQA,CAAA,EAAG;IACP,IAAI,CAACjB,kBAAkB,CAAC,CAAC;EAC7B;EACAkB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAC/B,WAAW,GAAG,IAAIrN,eAAe,CAAC,IAAI,CAAC2N,sBAAsB,CAAC,CAC9D0B,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,CAAC,CACfC,cAAc,CAAC,CAAC;IACrB,IAAI,CAAClC,WAAW,CAACmC,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAAChM,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE;IACA;IACA;IACA,IAAI,CAACiK,sBAAsB,CAAC+B,OAAO,CAC9BC,IAAI,CAAC5O,SAAS,CAAC,IAAI,CAAC4M,sBAAsB,CAAC,EAAE3M,SAAS,CAACmN,KAAK,IAAIvN,KAAK,CAAC,GAAGuN,KAAK,CAACyB,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACnK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9G+J,SAAS,CAACK,WAAW,IAAI,IAAI,CAACzC,WAAW,CAAC0C,gBAAgB,CAACD,WAAW,CAAC,CAAC;IAC7E,IAAI,CAACnC,sBAAsB,CAAC+B,OAAO,CAACD,SAAS,CAAEO,SAAS,IAAK;MACzD;MACA;MACA;MACA,MAAMC,OAAO,GAAG,IAAI,CAAC5C,WAAW;MAChC,IAAI,IAAI,CAAC/I,oBAAoB,KAAK,OAAO,IAAI2L,OAAO,CAACC,UAAU,EAAEhI,SAAS,CAAC,CAAC,EAAE;QAC1E,MAAMiG,KAAK,GAAG6B,SAAS,CAACG,OAAO,CAAC,CAAC;QACjC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACpC,KAAK,CAAC1G,MAAM,GAAG,CAAC,EAAEwI,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC,CAAC;QACnF,IAAIrC,KAAK,CAACiC,KAAK,CAAC,IAAI,CAACjC,KAAK,CAACiC,KAAK,CAAC,CAAC7K,QAAQ,EAAE;UACxC0K,OAAO,CAACQ,aAAa,CAACL,KAAK,CAAC;QAChC,CAAC,MACI;UACDH,OAAO,CAACS,iBAAiB,CAAC,CAAC;QAC/B;MACJ;IACJ,CAAC,CAAC;EACN;EACAlK,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6G,WAAW,EAAEsD,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAChD,sBAAsB,CAACgD,OAAO,CAAC,CAAC;IACrC,IAAI,CAAClN,MAAM,CAACkD,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC6G,kBAAkB,EAAEmD,OAAO,CAAC,CAAC;IAClCC,YAAY,CAAC,IAAI,CAACnD,oBAAoB,CAAC;EAC3C;EACA;EACAhI,QAAQA,CAAA,EAAG;IACP;IACA,MAAMoL,WAAW,GAAG,IAAI,CAAClD,sBAAsB,CAAC+B,OAAO;IACvD,OAAOmB,WAAW,CAAClB,IAAI,CAAC5O,SAAS,CAAC,IAAI,CAAC4M,sBAAsB,CAAC,EAAE3M,SAAS,CAACmN,KAAK,IAAIvN,KAAK,CAAC,GAAGuN,KAAK,CAACyB,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACpK,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrI;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,OAAOA,CAAC+K,KAAK,EAAE,CAAE;EACjB;AACJ;AACA;AACA;AACA;AACA;EACIpK,UAAUA,CAACoK,KAAK,EAAE,CAAE;EACpB;EACAC,cAAcA,CAAChK,KAAK,EAAE;IAClB,MAAMiK,OAAO,GAAGjK,KAAK,CAACiK,OAAO;IAC7B,MAAMf,OAAO,GAAG,IAAI,CAAC5C,WAAW;IAChC,QAAQ2D,OAAO;MACX,KAAKzQ,MAAM;QACP,IAAI,CAACC,cAAc,CAACuG,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAI,CAACvD,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAKpD,UAAU;QACX,IAAI,IAAI,CAACuN,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAACrK,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAKrD,WAAW;QACZ,IAAI,IAAI,CAACwN,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAACrK,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ;QACI,IAAIsN,OAAO,KAAK7Q,QAAQ,IAAI6Q,OAAO,KAAK5Q,UAAU,EAAE;UAChD6P,OAAO,CAACgB,cAAc,CAAC,UAAU,CAAC;QACtC;QACAhB,OAAO,CAACiB,SAAS,CAACnK,KAAK,CAAC;QACxB;IACR;EACJ;EACA;AACJ;AACA;AACA;EACIoK,cAAcA,CAAClL,MAAM,GAAG,SAAS,EAAE;IAC/B;IACA,IAAI,CAACuH,kBAAkB,EAAEmD,OAAO,CAAC,CAAC;IAClC,IAAI,CAACnD,kBAAkB,GAAGlO,eAAe,CAAC,MAAM;MAC5C,MAAM8R,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MACtC;MACA,IAAI,CAACD,SAAS,IAAI,CAACA,SAAS,CAACE,QAAQ,CAACC,QAAQ,CAACpJ,aAAa,CAAC,EAAE;QAC3D,MAAM8H,OAAO,GAAG,IAAI,CAAC5C,WAAW;QAChC4C,OAAO,CAACgB,cAAc,CAAChL,MAAM,CAAC,CAACuL,kBAAkB,CAAC,CAAC;QACnD;QACA;QACA;QACA,IAAI,CAACvB,OAAO,CAACC,UAAU,IAAIkB,SAAS,EAAE;UAClCA,SAAS,CAACpL,KAAK,CAAC,CAAC;QACrB;MACJ;IACJ,CAAC,EAAE;MAAEyL,QAAQ,EAAE,IAAI,CAACvG;IAAU,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACIwG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACrE,WAAW,CAACoD,aAAa,CAAC,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIkB,YAAYA,CAACC,MAAM,EAAE,CAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3D,kBAAkBA,CAAC4D,IAAI,GAAG,IAAI,CAAC9E,SAAS,EAAE+E,IAAI,GAAG,IAAI,CAAC9E,SAAS,EAAE;IAC7D,IAAI,CAAC5I,UAAU,GAAG;MACd,GAAG,IAAI,CAACA,UAAU;MAClB,CAAC,iBAAiB,GAAGyN,IAAI,KAAK,QAAQ;MACtC,CAAC,gBAAgB,GAAGA,IAAI,KAAK,OAAO;MACpC,CAAC,gBAAgB,GAAGC,IAAI,KAAK,OAAO;MACpC,CAAC,gBAAgB,GAAGA,IAAI,KAAK;IACjC,CAAC;IACD,IAAI,CAACzM,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;EAC1C;EACA;EACA/D,gBAAgBA,CAAC+N,KAAK,EAAE;IACpB,MAAMC,MAAM,GAAGD,KAAK,KAAK5E,cAAc;IACvC,IAAI6E,MAAM,IAAID,KAAK,KAAK7E,eAAe,EAAE;MACrC,IAAI8E,MAAM,EAAE;QACRpB,YAAY,CAAC,IAAI,CAACnD,oBAAoB,CAAC;QACvC,IAAI,CAACA,oBAAoB,GAAGwE,SAAS;MACzC;MACA,IAAI,CAACrE,cAAc,CAACvH,IAAI,CAAC2L,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;MACnD,IAAI,CAACzN,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAV,iBAAiBA,CAACkO,KAAK,EAAE;IACrB,IAAIA,KAAK,KAAK7E,eAAe,IAAI6E,KAAK,KAAK5E,cAAc,EAAE;MACvD,IAAI,CAAC5I,YAAY,GAAG,IAAI;IAC5B;EACJ;EACA2N,UAAUA,CAACC,MAAM,EAAE;IACf,IAAI,CAAC7N,oBAAoB,GAAG6N,MAAM,GAAG,OAAO,GAAG,MAAM;IACrD,IAAIA,MAAM,EAAE;MACR,IAAI,IAAI,CAAC9E,WAAW,CAACmD,eAAe,KAAK,CAAC,EAAE;QACxC;QACA;QACA;QACA;QACA;QACA;QACA,MAAMY,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;QACtC,IAAID,SAAS,EAAE;UACXA,SAAS,CAACgB,SAAS,GAAG,CAAC;QAC3B;MACJ;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACzQ,mBAAmB,EAAE;MAChC;MACA;MACA;MACA,IAAI,CAAC8L,oBAAoB,GAAG4E,UAAU,CAAC,MAAM,IAAI,CAACrO,gBAAgB,CAACmJ,cAAc,CAAC,EAAE,GAAG,CAAC;IAC5F;IACA;IACA,IAAI,IAAI,CAACxL,mBAAmB,EAAE;MAC1B0Q,UAAU,CAAC,MAAM;QACb,IAAI,CAACrO,gBAAgB,CAACmO,MAAM,GAAGjF,eAAe,GAAGC,cAAc,CAAC;MACpE,CAAC,CAAC;IACN;IACA,IAAI,CAAC9H,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqH,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC1B,SAAS,CAACgC,OAAO,CACjBC,IAAI,CAAC5O,SAAS,CAAC,IAAI,CAAC2M,SAAS,CAAC,CAAC,CAC/B+B,SAAS,CAAEtB,KAAK,IAAK;MACtB,IAAI,CAACR,sBAAsB,CAAC2E,KAAK,CAACnE,KAAK,CAAChN,MAAM,CAAC0O,IAAI,IAAIA,IAAI,CAAC1K,WAAW,KAAK,IAAI,CAAC,CAAC;MAClF,IAAI,CAACwI,sBAAsB,CAAC4E,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EACA;EACAlB,aAAaA,CAAA,EAAG;IACZ,IAAID,SAAS,GAAG,IAAI;IACpB,IAAI,IAAI,CAACzD,sBAAsB,CAAClG,MAAM,EAAE;MACpC;MACA;MACA;MACA;MACA2J,SAAS,GAAG,IAAI,CAACzD,sBAAsB,CAAC6E,KAAK,CAACpM,eAAe,CAAC,CAAC,CAACqM,OAAO,CAAC,eAAe,CAAC;IAC5F;IACA,OAAOrB,SAAS;EACpB;EACA,OAAOhJ,IAAI,YAAAsK,gBAAApK,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8E,OAAO;EAAA;EAC1G,OAAO7E,IAAI,kBA5e8EnK,EAAE,CAAAoK,iBAAA;IAAAC,IAAA,EA4eJ2E,OAAO;IAAA1E,SAAA;IAAAiK,cAAA,WAAAC,uBAAAnQ,EAAA,EAAAC,GAAA,EAAAmQ,QAAA;MAAA,IAAApQ,EAAA;QA5eLrE,EAAE,CAAA0U,cAAA,CAAAD,QAAA,EA4eyzB/H,gBAAgB;QA5e30B1M,EAAE,CAAA0U,cAAA,CAAAD,QAAA,EA4ew4B9N,WAAW;QA5er5B3G,EAAE,CAAA0U,cAAA,CAAAD,QAAA,EA4e88B9N,WAAW;MAAA;MAAA,IAAAtC,EAAA;QAAA,IAAAsQ,EAAA;QA5e39B3U,EAAE,CAAA4U,cAAA,CAAAD,EAAA,GAAF3U,EAAE,CAAA6U,WAAA,QAAAvQ,GAAA,CAAA0L,WAAA,GAAA2E,EAAA,CAAAP,KAAA;QAAFpU,EAAE,CAAA4U,cAAA,CAAAD,EAAA,GAAF3U,EAAE,CAAA6U,WAAA,QAAAvQ,GAAA,CAAAgL,SAAA,GAAAqF,EAAA;QAAF3U,EAAE,CAAA4U,cAAA,CAAAD,EAAA,GAAF3U,EAAE,CAAA6U,WAAA,QAAAvQ,GAAA,CAAAyL,KAAA,GAAA4E,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,cAAA1Q,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrE,EAAE,CAAAgV,WAAA,CA4emiCrU,WAAW;MAAA;MAAA,IAAA0D,EAAA;QAAA,IAAAsQ,EAAA;QA5ehjC3U,EAAE,CAAA4U,cAAA,CAAAD,EAAA,GAAF3U,EAAE,CAAA6U,WAAA,QAAAvQ,GAAA,CAAAwL,WAAA,GAAA6E,EAAA,CAAAP,KAAA;MAAA;IAAA;IAAA5J,QAAA;IAAAC,YAAA,WAAAwK,qBAAA5Q,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrE,EAAE,CAAAsG,WAAA,eA4eJ,IAAI,qBAAJ,IAAI,sBAAJ,IAAI;MAAA;IAAA;IAAAuE,MAAA;MAAAgE,aAAA;MAAAtI,SAAA;MAAAC,cAAA;MAAAC,eAAA;MAAAkI,SAAA;MAAAC,SAAA;MAAAF,cAAA,0CAAuVpO,gBAAgB;MAAA2P,WAAA,oCAAgDL,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGtP,gBAAgB,CAACsP,KAAK,CAAE;MAAAM,UAAA;MAAAQ,SAAA;IAAA;IAAAwE,OAAA;MAAA7P,MAAA;MAAAsL,KAAA;IAAA;IAAA7F,QAAA;IAAAmD,QAAA,GA5endjO,EAAE,CAAAkO,kBAAA,CA4emsB,CAAC;MAAEC,OAAO,EAAEzH,cAAc;MAAE0H,WAAW,EAAEY;IAAQ,CAAC,CAAC;IAAAhE,kBAAA,EAAArG,GAAA;IAAAsG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAA+J,iBAAA9Q,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA5exvBrE,EAAE,CAAAsL,eAAA;QAAFtL,EAAE,CAAAoV,UAAA,IAAAxQ,8BAAA,sBA4ekoC,CAAC;MAAA;IAAA;IAAAyQ,MAAA;IAAA1J,aAAA;IAAAC,eAAA;EAAA;AACluC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9e6F7L,EAAE,CAAA8L,iBAAA,CA8eJkD,OAAO,EAAc,CAAC;IACrG3E,IAAI,EAAE9J,SAAS;IACfwL,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEJ,eAAe,EAAEpL,uBAAuB,CAAC0L,MAAM;MAAEP,aAAa,EAAElL,iBAAiB,CAAC0L,IAAI;MAAErB,QAAQ,EAAE,SAAS;MAAEmB,IAAI,EAAE;QACtI,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE;MAC/B,CAAC;MAAEoC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEzH,cAAc;QAAE0H,WAAW,EAAEY;MAAQ,CAAC,CAAC;MAAE5D,QAAQ,EAAE,21BAA21B;MAAEiK,MAAM,EAAE,CAAC,08JAA08J;IAAE,CAAC;EAC54L,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE/F,SAAS,EAAE,CAAC;MACpDjF,IAAI,EAAElJ,eAAe;MACrB4K,IAAI,EAAE,CAACpF,WAAW,EAAE;QAAE2O,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAEzG,aAAa,EAAE,CAAC;MAChBxE,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAE6F,SAAS,EAAE,CAAC;MACZ8D,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEvF,cAAc,EAAE,CAAC;MACjB6D,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEtF,eAAe,EAAE,CAAC;MAClB4D,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE4C,SAAS,EAAE,CAAC;MACZtE,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAEkO,SAAS,EAAE,CAAC;MACZvE,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAEoP,WAAW,EAAE,CAAC;MACdzF,IAAI,EAAEjJ,SAAS;MACf2K,IAAI,EAAE,CAACpL,WAAW;IACtB,CAAC,CAAC;IAAEoP,KAAK,EAAE,CAAC;MACR1F,IAAI,EAAElJ,eAAe;MACrB4K,IAAI,EAAE,CAACpF,WAAW,EAAE;QAAE2O,WAAW,EAAE;MAAM,CAAC;IAC9C,CAAC,CAAC;IAAEtF,WAAW,EAAE,CAAC;MACd3F,IAAI,EAAEhJ,YAAY;MAClB0K,IAAI,EAAE,CAACW,gBAAgB;IAC3B,CAAC,CAAC;IAAEgC,cAAc,EAAE,CAAC;MACjBrE,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE/L;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2P,WAAW,EAAE,CAAC;MACd5F,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAGuD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGtP,gBAAgB,CAACsP,KAAK;MAAG,CAAC;IACrF,CAAC,CAAC;IAAEM,UAAU,EAAE,CAAC;MACb7F,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE2E,SAAS,EAAE,CAAC;MACZrG,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAE2E,MAAM,EAAE,CAAC;MACTgF,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEqP,KAAK,EAAE,CAAC;MACRtG,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMiU,wBAAwB,GAAG,IAAItV,cAAc,CAAC,0BAA0B,EAAE;EAC5EsO,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM6E,QAAQ,GAAGnT,MAAM,CAACW,QAAQ,CAAC;IACjC,OAAO,MAAM4C,8BAA8B,CAAC4P,QAAQ,CAAC;EACzD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASmC,gCAAgCA,CAACC,QAAQ,EAAE;EAChD,MAAMpC,QAAQ,GAAGnT,MAAM,CAACW,QAAQ,CAAC;EACjC,OAAO,MAAM4C,8BAA8B,CAAC4P,QAAQ,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqC,yCAAyC,GAAG;EAC9CvH,OAAO,EAAEoH,wBAAwB;EACjCI,IAAI,EAAE,EAAE;EACRC,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMK,sBAAsB,GAAG,CAAC;AAChC;AACA,MAAMC,kBAAkB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACxC;AACA,MAAMC,cAAc,CAAC;EACjBC,QAAQ,GAAG/V,MAAM,CAACC,UAAU,CAAC;EAC7B4M,iBAAiB,GAAG7M,MAAM,CAACY,gBAAgB,CAAC;EAC5CoV,iBAAiB,GAAGhW,MAAM,CAACyG,WAAW,EAAE;IAAEK,QAAQ,EAAE,IAAI;IAAEmP,IAAI,EAAE;EAAK,CAAC,CAAC;EACvEC,IAAI,GAAGlW,MAAM,CAACsD,cAAc,EAAE;IAAEwD,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjDF,aAAa,GAAG5G,MAAM,CAACwB,YAAY,CAAC;EACpC2U,OAAO,GAAGnW,MAAM,CAACqB,MAAM,CAAC;EACxBuL,SAAS,GAAG5M,MAAM,CAACW,QAAQ,CAAC;EAC5ByV,eAAe,GAAGpW,MAAM,CAACqV,wBAAwB,CAAC;EAClDtO,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;EAC9CkD,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3CgT,kBAAkB;EAClBvJ,OAAO;EACPwJ,WAAW,GAAG,IAAI;EAClBC,SAAS,GAAG,KAAK;EACjBC,2BAA2B,GAAGjU,YAAY,CAACkU,KAAK;EAChDC,kBAAkB,GAAGnU,YAAY,CAACkU,KAAK;EACvCE,sBAAsB,GAAGpU,YAAY,CAACkU,KAAK;EAC3CG,eAAe;EACf;AACJ;AACA;AACA;EACIC,mBAAmB;EACnB;AACJ;AACA;AACA;EACIC,mBAAmB;EACnB;EACA;EACAC,SAAS,GAAGpD,SAAS;EACrB;AACJ;AACA;AACA;EACI,IAAIqD,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,4BAA4BA,CAACE,CAAC,EAAE;IAChC,IAAI,CAACD,IAAI,GAAGC,CAAC;EACjB;EACA;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IACX,IAAIA,IAAI,KAAK,IAAI,CAACE,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACN,sBAAsB,CAACS,WAAW,CAAC,CAAC;IACzC,IAAIH,IAAI,EAAE;MACN,IAAIA,IAAI,KAAK,IAAI,CAACJ,mBAAmB,KAAK,OAAOlL,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACtFY,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAI,CAACoK,sBAAsB,GAAGM,IAAI,CAACxG,KAAK,CAACU,SAAS,CAAEkG,MAAM,IAAK;QAC3D,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;QACzB;QACA,IAAI,CAACA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,KAAK,KAAK,IAAI,CAACR,mBAAmB,EAAE;UACtE,IAAI,CAACA,mBAAmB,CAAC1R,MAAM,CAACC,IAAI,CAACiS,MAAM,CAAC;QAChD;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACrB,iBAAiB,EAAEtM,mBAAmB,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;EACvE;EACAwN,KAAK;EACL;EACAI,QAAQ;EACR;AACJ;AACA;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;EACAC,UAAU,GAAG,IAAI1W,YAAY,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;AACA;EACI;EACA2W,UAAU,GAAG,IAAI,CAACD,UAAU;EAC5B;EACAE,UAAU,GAAG,IAAI5W,YAAY,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;AACA;EACI;EACA6W,WAAW,GAAG,IAAI,CAACD,UAAU;EAC7BpQ,WAAWA,CAAA,EAAG;IACV,MAAMgI,UAAU,GAAGvP,MAAM,CAACwG,cAAc,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7D,MAAM+Q,QAAQ,GAAG7X,MAAM,CAACsB,SAAS,CAAC;IAClC,IAAI,CAACuV,mBAAmB,GAAGtH,UAAU,YAAYT,OAAO,GAAGS,UAAU,GAAGoE,SAAS;IACjF,IAAI,CAAC0C,kBAAkB,GAAGwB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC/B,QAAQ,CAACxN,aAAa,EAAE,YAAY,EAAGE,KAAK,IAAK;MAC5F,IAAI,CAAC9G,gCAAgC,CAAC8G,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACsO,SAAS,GAAG,OAAO;MAC5B;IACJ,CAAC,EAAE;MAAEgB,OAAO,EAAE;IAAK,CAAC,CAAC;EACzB;EACAlH,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACmH,YAAY,CAAC,CAAC;EACvB;EACA9P,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC+O,IAAI,IAAI,IAAI,CAACgB,SAAS,CAAC,IAAI,CAAChB,IAAI,CAAC,EAAE;MACxCrB,kBAAkB,CAACsC,MAAM,CAAC,IAAI,CAACjB,IAAI,CAAC;IACxC;IACA,IAAI,CAACZ,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACO,eAAe,EAAEQ,WAAW,CAAC,CAAC;IACnC,IAAI,CAACT,sBAAsB,CAACS,WAAW,CAAC,CAAC;IACzC,IAAI,CAACZ,2BAA2B,CAACY,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACV,kBAAkB,CAACU,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAACd,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC3I,OAAO,CAAC,CAAC;MAC1B,IAAI,CAAC2I,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA,IAAI6B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5B,SAAS;EACzB;EACA;EACA,IAAI6B,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACxG,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA/F,eAAeA,CAAA,EAAG;IACd,OAAO,CAAC,EAAE,IAAI,CAACqM,iBAAiB,IAAI,IAAI,CAACa,mBAAmB,IAAI,IAAI,CAACI,IAAI,CAAC;EAC9E;EACA;EACAoB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC9B,SAAS,GAAG,IAAI,CAAC+B,SAAS,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC9D;EACA;EACAA,QAAQA,CAAA,EAAG;IACP,MAAMtB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,IAAI,CAACV,SAAS,IAAI,CAACU,IAAI,EAAE;MACzB;IACJ;IACA,IAAI,CAACL,eAAe,EAAEQ,WAAW,CAAC,CAAC;IACnC,MAAMoB,eAAe,GAAG5C,kBAAkB,CAAC6C,GAAG,CAACxB,IAAI,CAAC;IACpDrB,kBAAkB,CAAC8C,GAAG,CAACzB,IAAI,EAAE,IAAI,CAAC;IAClC;IACA;IACA,IAAIuB,eAAe,IAAIA,eAAe,KAAK,IAAI,EAAE;MAC7CA,eAAe,CAACF,SAAS,CAAC,CAAC;IAC/B;IACA,MAAMK,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC3B,IAAI,CAAC;IAC5C,MAAM4B,aAAa,GAAGF,UAAU,CAACG,SAAS,CAAC,CAAC;IAC5C,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAgB;IACvD,IAAI,CAACC,YAAY,CAAC/B,IAAI,EAAE8B,gBAAgB,CAAC;IACzCF,aAAa,CAAC9I,WAAW,GACrBkH,IAAI,CAAClH,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAACpG,eAAe,CAAC,CAAC,GAAGsN,IAAI,CAAClH,WAAW;IACzE;IACA;IACA,IAAI,CAAC4I,UAAU,CAACM,WAAW,CAAC,CAAC,EAAE;MAC3BN,UAAU,CAAC1L,MAAM,CAAC,IAAI,CAACiM,UAAU,CAACjC,IAAI,CAAC,CAAC;MACxCA,IAAI,CAACnH,WAAW,EAAE7C,MAAM,CAAC,IAAI,CAACsK,QAAQ,CAAC;IAC3C;IACA,IAAI,CAACf,2BAA2B,GAAG,IAAI,CAAC2C,mBAAmB,CAAC,CAAC,CAAChI,SAAS,CAAC,MAAM,IAAI,CAACmH,SAAS,CAAC,CAAC,CAAC;IAC/FrB,IAAI,CAAC1H,UAAU,GAAG,IAAI,CAAC5F,eAAe,CAAC,CAAC,GAAG,IAAI,CAACkN,mBAAmB,GAAGlD,SAAS;IAC/EsD,IAAI,CAACzH,SAAS,GAAG,IAAI,CAAC4I,GAAG;IACzBnB,IAAI,CAACpE,cAAc,CAAC,IAAI,CAACkE,SAAS,IAAI,SAAS,CAAC;IAChD,IAAI,CAACqC,cAAc,CAAC,IAAI,CAAC;IACzB,IAAInC,IAAI,YAAYnI,OAAO,EAAE;MACzBmI,IAAI,CAACrD,UAAU,CAAC,IAAI,CAAC;MACrBqD,IAAI,CAAC5H,sBAAsB,CAAC+B,OAAO,CAACC,IAAI,CAAC1O,SAAS,CAACsU,IAAI,CAACxG,KAAK,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;QAC5E;QACA;QACA4H,gBAAgB,CAACM,kBAAkB,CAAC,KAAK,CAAC,CAACC,mBAAmB,CAAC,CAAC;QAChEP,gBAAgB,CAACM,kBAAkB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN;EACJ;EACA;EACAf,SAASA,CAAA,EAAG;IACR,IAAI,CAACrB,IAAI,EAAExG,KAAK,CAACrL,IAAI,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACIsC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAAChB,aAAa,IAAIe,MAAM,EAAE;MAC9B,IAAI,CAACf,aAAa,CAACiB,QAAQ,CAAC,IAAI,CAACkO,QAAQ,EAAEpO,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACmO,QAAQ,CAACxN,aAAa,CAACb,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;EACI2R,cAAcA,CAAA,EAAG;IACb,IAAI,CAACjD,WAAW,EAAEiD,cAAc,CAAC,CAAC;EACtC;EACA;EACAjC,YAAYA,CAACD,MAAM,EAAE;IACjB,MAAMsB,UAAU,GAAG,IAAI,CAACrC,WAAW;IACnC,MAAMW,IAAI,GAAG,IAAI,CAACE,KAAK;IACvB,IAAI,CAACwB,UAAU,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAC/B;IACJ;IACA,IAAI,CAAC3B,2BAA2B,CAACY,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACR,eAAe,EAAEQ,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAIH,IAAI,YAAYnI,OAAO,IAAI,IAAI,CAACmJ,SAAS,CAAChB,IAAI,CAAC,EAAE;MACjD,IAAI,CAACL,eAAe,GAAGK,IAAI,CAAC3H,cAAc,CAAC+B,IAAI,CAACzO,IAAI,CAAC,CAAC,CAAC,CAAC,CAACuO,SAAS,CAAC,MAAM;QACrEwH,UAAU,CAACxL,MAAM,CAAC,CAAC;QACnB8J,IAAI,CAACnH,WAAW,EAAE3C,MAAM,CAAC,CAAC;MAC9B,CAAC,CAAC;MACF8J,IAAI,CAACrD,UAAU,CAAC,KAAK,CAAC;IAC1B,CAAC,MACI;MACD+E,UAAU,CAACxL,MAAM,CAAC,CAAC;MACnB8J,IAAI,EAAEnH,WAAW,EAAE3C,MAAM,CAAC,CAAC;IAC/B;IACA,IAAI8J,IAAI,IAAI,IAAI,CAACgB,SAAS,CAAChB,IAAI,CAAC,EAAE;MAC9BrB,kBAAkB,CAACsC,MAAM,CAACjB,IAAI,CAAC;IACnC;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACO,YAAY,KAAKH,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAACN,SAAS,IAAI,CAAC,IAAI,CAACpN,eAAe,CAAC,CAAC,CAAC,EAAE;MAC3F,IAAI,CAACjC,KAAK,CAAC,IAAI,CAACqP,SAAS,CAAC;IAC9B;IACA,IAAI,CAACA,SAAS,GAAGpD,SAAS;IAC1B,IAAI,CAACyF,cAAc,CAAC,KAAK,CAAC;EAC9B;EACA;EACAA,cAAcA,CAACvF,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAK,IAAI,CAAC0C,SAAS,EAAE;MAC3B,IAAI,CAACA,SAAS,GAAG1C,MAAM;MACvB,IAAI,CAAC0C,SAAS,GAAG,IAAI,CAACkB,UAAU,CAACrS,IAAI,CAAC,CAAC,GAAG,IAAI,CAACuS,UAAU,CAACvS,IAAI,CAAC,CAAC;MAChE,IAAI,IAAI,CAACuE,eAAe,CAAC,CAAC,EAAE;QACxB,IAAI,CAACqM,iBAAiB,CAACzM,eAAe,CAACsK,MAAM,CAAC;MAClD;MACA,IAAI,CAAC9M,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACImP,cAAcA,CAAC3B,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACnB,MAAMkD,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACxC,IAAI,CAAC;MAC3C,IAAI,CAACyC,qBAAqB,CAACzC,IAAI,EAAEuC,MAAM,CAACT,gBAAgB,CAAC;MACzD,IAAI,CAACzC,WAAW,GAAG9S,gBAAgB,CAAC,IAAI,CAACoJ,SAAS,EAAE4M,MAAM,CAAC;MAC3D,IAAI,CAAClD,WAAW,CAACqD,aAAa,CAAC,CAAC,CAACxI,SAAS,CAAC1I,KAAK,IAAI;QAChD,IAAI,IAAI,CAACwO,IAAI,YAAYnI,OAAO,EAAE;UAC9B,IAAI,CAACmI,IAAI,CAACxE,cAAc,CAAChK,KAAK,CAAC;QACnC;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAC6N,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACImD,iBAAiBA,CAACxC,IAAI,EAAE;IACpB,OAAO,IAAIxT,aAAa,CAAC;MACrBsV,gBAAgB,EAAErV,uCAAuC,CAAC,IAAI,CAACkJ,SAAS,EAAE,IAAI,CAACmJ,QAAQ,CAAC,CACnFsD,kBAAkB,CAAC,CAAC,CACpBO,iBAAiB,CAAC,CAAC,CACnBC,qBAAqB,CAAC,sCAAsC,CAAC;MAClElL,aAAa,EAAEsI,IAAI,CAACtI,aAAa,IAAI,kCAAkC;MACvEqB,UAAU,EAAEiH,IAAI,CAACxH,iBAAiB;MAClCqK,cAAc,EAAE,IAAI,CAAC1D,eAAe,CAAC,CAAC;MACtC5G,SAAS,EAAE,IAAI,CAAC0G,IAAI,IAAI,KAAK;MAC7B6D,iBAAiB,EAAE,IAAI,CAAC1W;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIqW,qBAAqBA,CAACzC,IAAI,EAAE+C,QAAQ,EAAE;IAClC,IAAI/C,IAAI,CAACtH,kBAAkB,EAAE;MACzBqK,QAAQ,CAACC,eAAe,CAAC9I,SAAS,CAAC+I,MAAM,IAAI;QACzC,IAAI,CAAC/D,OAAO,CAACgE,GAAG,CAAC,MAAM;UACnB,MAAM5G,IAAI,GAAG2G,MAAM,CAACE,cAAc,CAACC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;UAC5E,MAAM7G,IAAI,GAAG0G,MAAM,CAACE,cAAc,CAACE,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;UACzErD,IAAI,CAACtH,kBAAkB,CAAC4D,IAAI,EAAEC,IAAI,CAAC;QACvC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIwF,YAAYA,CAAC/B,IAAI,EAAE8B,gBAAgB,EAAE;IACjC,IAAI,CAACwB,OAAO,EAAEC,eAAe,CAAC,GAAGvD,IAAI,CAACxI,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;IAClG,IAAI,CAAC6L,QAAQ,EAAEG,gBAAgB,CAAC,GAAGxD,IAAI,CAACvI,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;IACrG,IAAI,CAACgM,OAAO,EAAEC,eAAe,CAAC,GAAG,CAACL,QAAQ,EAAEG,gBAAgB,CAAC;IAC7D,IAAI,CAACJ,QAAQ,EAAEO,gBAAgB,CAAC,GAAG,CAACL,OAAO,EAAEC,eAAe,CAAC;IAC7D,IAAIK,OAAO,GAAG,CAAC;IACf,IAAI,IAAI,CAAClR,eAAe,CAAC,CAAC,EAAE;MACxB;MACA;MACAiR,gBAAgB,GAAGL,OAAO,GAAGtD,IAAI,CAACxI,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;MAC1E+L,eAAe,GAAGH,QAAQ,GAAGE,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;MAChE,IAAI,IAAI,CAAC1D,mBAAmB,EAAE;QAC1B,IAAI,IAAI,CAACC,mBAAmB,IAAI,IAAI,EAAE;UAClC,MAAMgE,SAAS,GAAG,IAAI,CAACjE,mBAAmB,CAAChH,KAAK,CAACqE,KAAK;UACtD,IAAI,CAAC4C,mBAAmB,GAAGgE,SAAS,GAAGA,SAAS,CAAChT,eAAe,CAAC,CAAC,CAACiT,SAAS,GAAG,CAAC;QACpF;QACAF,OAAO,GAAGP,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACxD,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MAC1F;IACJ,CAAC,MACI,IAAI,CAACG,IAAI,CAACzI,cAAc,EAAE;MAC3BkM,OAAO,GAAGJ,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MAC/CK,eAAe,GAAGF,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;IACnE;IACA1B,gBAAgB,CAACiC,aAAa,CAAC,CAC3B;MAAET,OAAO;MAAEG,OAAO;MAAEL,QAAQ;MAAEC,QAAQ;MAAEO;IAAQ,CAAC,EACjD;MAAEN,OAAO,EAAEC,eAAe;MAAEE,OAAO;MAAEL,QAAQ,EAAEO,gBAAgB;MAAEN,QAAQ;MAAEO;IAAQ,CAAC,EACpF;MACIN,OAAO;MACPG,OAAO,EAAEC,eAAe;MACxBN,QAAQ;MACRC,QAAQ,EAAEG,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,EACD;MACIN,OAAO,EAAEC,eAAe;MACxBE,OAAO,EAAEC,eAAe;MACxBN,QAAQ,EAAEO,gBAAgB;MAC1BN,QAAQ,EAAEG,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,CACJ,CAAC;EACN;EACA;EACA1B,mBAAmBA,CAAA,EAAG;IAClB,MAAM8B,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,aAAa,CAAC,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAAC7E,WAAW,CAAC6E,WAAW,CAAC,CAAC;IAClD,MAAMC,WAAW,GAAG,IAAI,CAACvE,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAC1R,MAAM,GAAG3C,EAAE,CAAC,CAAC;IACrF,MAAM6Y,KAAK,GAAG,IAAI,CAACxE,mBAAmB,GAChC,IAAI,CAACA,mBAAmB,CACrB1P,QAAQ,CAAC,CAAC,CACVkK,IAAI,CAACxO,MAAM,CAACyY,MAAM,IAAI,IAAI,CAAC/E,SAAS,IAAI+E,MAAM,KAAK,IAAI,CAACtF,iBAAiB,CAAC,CAAC,GAC9ExT,EAAE,CAAC,CAAC;IACV,OAAOF,KAAK,CAAC2Y,QAAQ,EAAEG,WAAW,EAAEC,KAAK,EAAEF,WAAW,CAAC;EAC3D;EACA;EACAI,gBAAgBA,CAAC9S,KAAK,EAAE;IACpB,IAAI,CAAC7G,+BAA+B,CAAC6G,KAAK,CAAC,EAAE;MACzC;MACA;MACA,IAAI,CAACsO,SAAS,GAAGtO,KAAK,CAAC+S,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG7H,SAAS;MACzD;MACA;MACA;MACA,IAAI,IAAI,CAAChK,eAAe,CAAC,CAAC,EAAE;QACxBlB,KAAK,CAACC,cAAc,CAAC,CAAC;MAC1B;IACJ;EACJ;EACA;EACA+J,cAAcA,CAAChK,KAAK,EAAE;IAClB,MAAMiK,OAAO,GAAGjK,KAAK,CAACiK,OAAO;IAC7B;IACA,IAAIA,OAAO,KAAKvQ,KAAK,IAAIuQ,OAAO,KAAKtQ,KAAK,EAAE;MACxC,IAAI,CAAC2U,SAAS,GAAG,UAAU;IAC/B;IACA,IAAI,IAAI,CAACpN,eAAe,CAAC,CAAC,KACpB+I,OAAO,KAAK3Q,WAAW,IAAI,IAAI,CAACqW,GAAG,KAAK,KAAK,IAC1C1F,OAAO,KAAK1Q,UAAU,IAAI,IAAI,CAACoW,GAAG,KAAK,KAAM,CAAC,EAAE;MACrD,IAAI,CAACrB,SAAS,GAAG,UAAU;MAC3B,IAAI,CAACwB,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA;EACAkD,YAAYA,CAAChT,KAAK,EAAE;IAChB,IAAI,IAAI,CAACkB,eAAe,CAAC,CAAC,EAAE;MACxB;MACAlB,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB,IAAI,CAAC4P,QAAQ,CAAC,CAAC;IACnB,CAAC,MACI;MACD,IAAI,CAACF,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;EACAL,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,IAAI,CAACrO,eAAe,CAAC,CAAC,IAAI,IAAI,CAACkN,mBAAmB,EAAE;MACpD,IAAI,CAACH,kBAAkB,GAAG,IAAI,CAACG,mBAAmB,CAAC1P,QAAQ,CAAC,CAAC,CAACgK,SAAS,CAACmK,MAAM,IAAI;QAC9E,IAAIA,MAAM,KAAK,IAAI,CAACtF,iBAAiB,IAAI,CAACsF,MAAM,CAACrU,QAAQ,EAAE;UACvD,IAAI,CAAC8P,SAAS,GAAG,OAAO;UACxB,IAAI,CAACwB,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAW,UAAUA,CAACjC,IAAI,EAAE;IACb;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACnK,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC8C,WAAW,KAAKqH,IAAI,CAACrH,WAAW,EAAE;MAChE,IAAI,CAAC9C,OAAO,GAAG,IAAI3J,cAAc,CAAC8T,IAAI,CAACrH,WAAW,EAAE,IAAI,CAAC/C,iBAAiB,CAAC;IAC/E;IACA,OAAO,IAAI,CAACC,OAAO;EACvB;EACA;AACJ;AACA;AACA;AACA;EACImL,SAASA,CAAChB,IAAI,EAAE;IACZ,OAAOrB,kBAAkB,CAAC6C,GAAG,CAACxB,IAAI,CAAC,KAAK,IAAI;EAChD;EACA,OAAOnN,IAAI,YAAA4R,uBAAA1R,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8L,cAAc;EAAA;EACjH,OAAOjI,IAAI,kBA7+B8E/N,EAAE,CAAAgO,iBAAA;IAAA3D,IAAA,EA6+BJ2L,cAAc;IAAA1L,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAoR,4BAAAxX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7+BZrE,EAAE,CAAA+E,UAAA,mBAAA+W,wCAAAtW,MAAA;UAAA,OA6+BJlB,GAAA,CAAAqX,YAAA,CAAAnW,MAAmB,CAAC;QAAA,CAAP,CAAC,uBAAAuW,4CAAAvW,MAAA;UAAA,OAAdlB,GAAA,CAAAmX,gBAAA,CAAAjW,MAAuB,CAAC;QAAA,CAAX,CAAC,qBAAAwW,0CAAAxW,MAAA;UAAA,OAAdlB,GAAA,CAAAqO,cAAA,CAAAnN,MAAqB,CAAC;QAAA,CAAT,CAAC;MAAA;MAAA,IAAAnB,EAAA;QA7+BZrE,EAAE,CAAAsG,WAAA,kBAAAhC,GAAA,CAAA6S,IAAA,GA6+BG,MAAM,GAAG,IAAI,mBAAA7S,GAAA,CAAA+T,QAAA,mBAAA/T,GAAA,CAAA+T,QAAA,GAAA/T,GAAA,CAAA6S,IAAA,kBAAA7S,GAAA,CAAA6S,IAAA,CAAA9Q,OAAA,GAAO,IAAI;MAAA;IAAA;IAAAwE,MAAA;MAAAqM,4BAAA;MAAAC,IAAA;MAAAM,QAAA;MAAAC,YAAA;IAAA;IAAAxC,OAAA;MAAAyC,UAAA;MAAAC,UAAA;MAAAC,UAAA;MAAAC,WAAA;IAAA;IAAAhN,QAAA;EAAA;AAC1H;AACA;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KA/+B6F7L,EAAE,CAAA8L,iBAAA,CA++BJkK,cAAc,EAAc,CAAC;IAC5G3L,IAAI,EAAEtJ,SAAS;IACfgL,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6CAA6C;MACvDC,IAAI,EAAE;QACF,OAAO,EAAE,sBAAsB;QAC/B,sBAAsB,EAAE,sBAAsB;QAC9C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,iCAAiC;QACzD,SAAS,EAAE,sBAAsB;QACjC,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE;MACjB,CAAC;MACDnB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEoM,4BAA4B,EAAE,CAAC;MACvE7M,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEoL,IAAI,EAAE,CAAC;MACP9M,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE0L,QAAQ,EAAE,CAAC;MACXpN,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE2L,YAAY,EAAE,CAAC;MACfrN,IAAI,EAAE3J,KAAK;MACXqL,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE4L,UAAU,EAAE,CAAC;MACbtN,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEsW,UAAU,EAAE,CAAC;MACbvN,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEuW,UAAU,EAAE,CAAC;MACbxN,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEwW,WAAW,EAAE,CAAC;MACdzN,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2a,aAAa,CAAC;EAChB,OAAOjS,IAAI,YAAAkS,sBAAAhS,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+R,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAthC8Enc,EAAE,CAAAoc,gBAAA;IAAA/R,IAAA,EAshCS4R;EAAa;EAYjH,OAAOI,IAAI,kBAliC8Erc,EAAE,CAAAsc,gBAAA;IAAAjO,SAAA,EAkiCmC,CAACqH,yCAAyC,CAAC;IAAAtJ,OAAA,GAAYrI,eAAe,EAC5LC,eAAe,EACfH,aAAa,EAAEC,mBAAmB,EAClCE,eAAe;EAAA;AAC3B;AACA;EAAA,QAAA6H,SAAA,oBAAAA,SAAA,KAviC6F7L,EAAE,CAAA8L,iBAAA,CAuiCJmQ,aAAa,EAAc,CAAC;IAC3G5R,IAAI,EAAE5I,QAAQ;IACdsK,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CACLrI,eAAe,EACfC,eAAe,EACfH,aAAa,EACbmL,OAAO,EACPrI,WAAW,EACXgG,cAAc,EACdqJ,cAAc,CACjB;MACDuG,OAAO,EAAE,CACLzY,mBAAmB,EACnBkL,OAAO,EACPhL,eAAe,EACf2C,WAAW,EACXgG,cAAc,EACdqJ,cAAc,CACjB;MACD3H,SAAS,EAAE,CAACqH,yCAAyC;IACzD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8G,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAE;IACXpS,IAAI,EAAE,CAAC;IACPqS,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,CACT;MACItS,IAAI,EAAE,CAAC;MACPqS,IAAI,EAAE,MAAM;MACZrH,MAAM,EAAE;QAAEhL,IAAI,EAAE,CAAC;QAAEgL,MAAM,EAAE;UAAEuH,OAAO,EAAE,CAAC;UAAEvQ,SAAS,EAAE;QAAa,CAAC;QAAEwQ,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACIxS,IAAI,EAAE,CAAC;MACPyS,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAE;QACP1S,IAAI,EAAE,CAAC;QACPgL,MAAM,EAAE;UAAEhL,IAAI,EAAE,CAAC;UAAEgL,MAAM,EAAE;YAAEuH,OAAO,EAAE,CAAC;YAAEvQ,SAAS,EAAE;UAAW,CAAC;UAAEwQ,MAAM,EAAE;QAAK,CAAC;QAChFG,OAAO,EAAE;MACb,CAAC;MACDlV,OAAO,EAAE;IACb,CAAC,EACD;MACIuC,IAAI,EAAE,CAAC;MACPyS,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE;QACP1S,IAAI,EAAE,CAAC;QACPgL,MAAM,EAAE;UAAEhL,IAAI,EAAE,CAAC;UAAEgL,MAAM,EAAE;YAAEuH,OAAO,EAAE;UAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACDlV,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACImV,WAAW,EAAE;IACT5S,IAAI,EAAE,CAAC;IACPqS,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,CACT;MACItS,IAAI,EAAE,CAAC;MACPqS,IAAI,EAAE,SAAS;MACfrH,MAAM,EAAE;QAAEhL,IAAI,EAAE,CAAC;QAAEgL,MAAM,EAAE;UAAEuH,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC5D,CAAC,EACD;MACIxS,IAAI,EAAE,CAAC;MACPyS,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,CACP;QAAE1S,IAAI,EAAE,CAAC;QAAEgL,MAAM,EAAE;UAAEuH,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAC,EACjD;QAAExS,IAAI,EAAE,CAAC;QAAEgL,MAAM,EAAE,IAAI;QAAE2H,OAAO,EAAE;MAA+C,CAAC,CACrF;MACDlV,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMmV,WAAW,GAAGT,iBAAiB,CAACS,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMR,aAAa,GAAGD,iBAAiB,CAACC,aAAa;AAErD,SAAS/P,gBAAgB,EAAE4B,wBAAwB,EAAE5H,cAAc,EAAE6O,wBAAwB,EAAEG,yCAAyC,EAAEG,sBAAsB,EAAE7G,OAAO,EAAErC,cAAc,EAAEhG,WAAW,EAAEsV,aAAa,EAAEjG,cAAc,EAAEiH,WAAW,EAAET,iBAAiB,EAAEC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}