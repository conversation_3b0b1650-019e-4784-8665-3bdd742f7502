{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent = Infinity) {\n  return mergeMap(identity, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "identity", "mergeAll", "concurrent", "Infinity"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/rxjs/dist/esm/internal/operators/mergeAll.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent = Infinity) {\n    return mergeMap(identity, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,QAAQA,CAACC,UAAU,GAAGC,QAAQ,EAAE;EAC5C,OAAOJ,QAAQ,CAACC,QAAQ,EAAEE,UAAU,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}