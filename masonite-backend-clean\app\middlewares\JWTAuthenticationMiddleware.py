from masonite.middleware import Middleware
from masonite.authentication import Auth
from app.models.User import User
from app.controllers.CorsController import CorsController
import jwt
from masonite.environment import env


class JWTAuthenticationMiddleware(Middleware):
    """JWT Authentication Middleware for API routes."""
    
    def before(self, request, response):
        """Authenticate API requests using JWT tokens."""
        # Get token from Authorization header or token parameter
        auth_header = request.header('Authorization') or ''
        token = None
        
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header[7:]  # Remove 'Bearer ' prefix
        elif request.input('token'):
            token = request.input('token')
        
        if not token:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'No authentication token provided'
                }
            }, 401)

        # Validate JWT token
        try:
            # Get JWT secret from environment
            jwt_secret = env('JWT_SECRET') or env('APP_KEY') or 'ZAq1N9Mw2BRTOyCQRew0F9pyUafCRNx4IbvDKAUc7LH7JkGYRD/pPOyYuNO1Q5po'

            # Decode JWT token without audience/issuer validation for now
            payload = jwt.decode(
                token,
                jwt_secret,
                algorithms=[env('JWT_ALGORITHM', 'HS256')],
                options={"verify_aud": False, "verify_iss": False}
            )

            # Get user ID from token payload
            user_id = payload.get('id') or payload.get('securityId')
            if not user_id:
                raise jwt.InvalidTokenError('User ID not found in token')

            # Find user by ID
            user = User.find(user_id)
            if not user:
                raise jwt.InvalidTokenError('User not found')

        except jwt.ExpiredSignatureError:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Token has expired'
                }
            }, 401)
        except jwt.InvalidTokenError:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Invalid authentication token'
                }
            }, 401)
        except Exception as e:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Token validation failed'
                }
            }, 401)
        
        # Set the authenticated user
        request.set_user(user)
        return request

    def after(self, request, response):
        return request
