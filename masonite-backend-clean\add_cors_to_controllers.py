#!/usr/bin/env python3
"""
Script to automatically add CORS headers to all controller methods
"""

import os
import re

def add_cors_to_controller(controller_path):
    """Add CORS headers to a controller file"""
    
    print(f"Processing {controller_path}...")
    
    with open(controller_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Skip if already has CorsController import
    if 'from app.controllers.CorsController import CorsController' in content:
        print(f"  ✅ {controller_path} already has CorsController import")
        return
    
    # Add CorsController import after other imports
    import_pattern = r'(from app\.models\.User import User.*?\n)'
    if re.search(import_pattern, content):
        content = re.sub(
            import_pattern,
            r'\1from app.controllers.CorsController import CorsController\n',
            content
        )
    else:
        # Add after last import
        last_import_pattern = r'(from [^\n]+\n)(?!from)'
        content = re.sub(
            last_import_pattern,
            r'\1from app.controllers.CorsController import CorsController\n',
            content,
            count=1
        )
    
    # Add CORS headers before return response.json() statements
    # Pattern to match return response.json() calls
    return_pattern = r'(\s+)(return response\.json\()'
    
    def add_cors_before_return(match):
        indent = match.group(1)
        return_stmt = match.group(2)
        cors_line = f"{indent}CorsController.add_cors_headers(response, request.header('Origin'))\n"
        return cors_line + indent + return_stmt
    
    # Apply the replacement
    content = re.sub(return_pattern, add_cors_before_return, content)
    
    # Write back to file
    with open(controller_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✅ Added CORS headers to {controller_path}")

def main():
    """Main function to process all controllers"""
    
    controllers_dir = "app/controllers"
    
    # List of controller files to process
    controller_files = [
        "TwoFactorController.py",
        "OAuthController.py", 
        "PaymentController.py",
        "OTPController.py",
        "SecurityController.py",
        "AccountController.py",
        "NotificationController.py",
        "QueueController.py"
    ]
    
    print("🔧 Adding CORS headers to all controllers...")
    print("=" * 50)
    
    for controller_file in controller_files:
        controller_path = os.path.join(controllers_dir, controller_file)
        if os.path.exists(controller_path):
            add_cors_to_controller(controller_path)
        else:
            print(f"  ⚠️  {controller_path} not found")
    
    print("=" * 50)
    print("✅ CORS headers added to all controllers!")

if __name__ == "__main__":
    main()
