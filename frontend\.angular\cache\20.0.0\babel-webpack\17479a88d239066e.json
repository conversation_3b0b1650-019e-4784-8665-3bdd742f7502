{"ast": null, "code": "import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n  if (!isObservable(data)) {\n    return of(data);\n  }\n  return data;\n}\nexport { coerceObservable };", "map": {"version": 3, "names": ["isObservable", "of", "coerceObservable", "data"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/coercion/private.mjs"], "sourcesContent": ["import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n    if (!isObservable(data)) {\n        return of(data);\n    }\n    return data;\n}\n\nexport { coerceObservable };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,EAAE,QAAQ,MAAM;;AAEvC;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAI,CAACH,YAAY,CAACG,IAAI,CAAC,EAAE;IACrB,OAAOF,EAAE,CAACE,IAAI,CAAC;EACnB;EACA,OAAOA,IAAI;AACf;AAEA,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}