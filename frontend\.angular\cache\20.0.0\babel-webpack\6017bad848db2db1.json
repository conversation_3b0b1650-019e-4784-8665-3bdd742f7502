{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { inject, RuntimeError, formatRuntimeError, ErrorHandler, DestroyRef, assertNotInReactiveContext, assertInInjectionContext, Injector, ViewContext, ChangeDetectionScheduler, EffectScheduler, setInjectorProfilerContext, emitEffectCreatedEvent, EFFECTS, noop, FLAGS, markAncestorsForTraversal, setIsRefreshingViews, NodeInjectorDestroyRef, signalAsReadonlyFn, PendingTasks, signal } from './root_effect_scheduler-0BxwqIgm.mjs';\nimport { setActiveConsumer, createComputed, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal-ePSl6jXn.mjs';\nimport { untracked as untracked$1, createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn } from './untracked-2ouAFbCz.mjs';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nclass OutputEmitterRef {\n  destroyed = false;\n  listeners = null;\n  errorHandler = inject(ErrorHandler, {\n    optional: true\n  });\n  /** @internal */\n  destroyRef = inject(DestroyRef);\n  constructor() {\n    // Clean-up all listeners and mark as destroyed upon destroy.\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n      this.listeners = null;\n    });\n  }\n  subscribe(callback) {\n    if (this.destroyed) {\n      throw new RuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    (this.listeners ??= []).push(callback);\n    return {\n      unsubscribe: () => {\n        const idx = this.listeners?.indexOf(callback);\n        if (idx !== undefined && idx !== -1) {\n          this.listeners?.splice(idx, 1);\n        }\n      }\n    };\n  }\n  /** Emits a new value to the output. */\n  emit(value) {\n    if (this.destroyed) {\n      console.warn(formatRuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected emit for destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.'));\n      return;\n    }\n    if (this.listeners === null) {\n      return;\n    }\n    const previousConsumer = setActiveConsumer(null);\n    try {\n      for (const listenerFn of this.listeners) {\n        try {\n          listenerFn(value);\n        } catch (err) {\n          this.errorHandler?.handleError(err);\n        }\n      }\n    } finally {\n      setActiveConsumer(previousConsumer);\n    }\n  }\n}\n/** Gets the owning `DestroyRef` for the given output. */\nfunction getOutputDestroyRef(ref) {\n  return ref.destroyRef;\n}\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n  return untracked$1(nonReactiveReadsFn);\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nfunction computed(computation, options) {\n  const getter = createComputed(computation, options?.equal);\n  if (ngDevMode) {\n    getter.toString = () => `[Computed: ${getter()}]`;\n    getter[SIGNAL].debugName = options?.debugName;\n  }\n  return getter;\n}\nclass EffectRefImpl {\n  [SIGNAL];\n  constructor(node) {\n    this[SIGNAL] = node;\n  }\n  destroy() {\n    this[SIGNAL].destroy();\n  }\n}\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n *\n * @publicApi 20.0\n */\nfunction effect(effectFn, options) {\n  ngDevMode && assertNotInReactiveContext(effect, 'Call `effect` outside of a reactive context. For example, schedule the ' + 'effect inside the component constructor.');\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(effect);\n  }\n  if (ngDevMode && options?.allowSignalWrites !== undefined) {\n    console.warn(`The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`);\n  }\n  const injector = options?.injector ?? inject(Injector);\n  let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n  let node;\n  const viewContext = injector.get(ViewContext, null, {\n    optional: true\n  });\n  const notifier = injector.get(ChangeDetectionScheduler);\n  if (viewContext !== null) {\n    // This effect was created in the context of a view, and will be associated with the view.\n    node = createViewEffect(viewContext.view, notifier, effectFn);\n    if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n      // The effect is being created in the same view as the `DestroyRef` references, so it will be\n      // automatically destroyed without the need for an explicit `DestroyRef` registration.\n      destroyRef = null;\n    }\n  } else {\n    // This effect was created outside the context of a view, and will be scheduled independently.\n    node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n  }\n  node.injector = injector;\n  if (destroyRef !== null) {\n    // If we need to register for cleanup, do that here.\n    node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n  }\n  const effectRef = new EffectRefImpl(node);\n  if (ngDevMode) {\n    node.debugName = options?.debugName ?? '';\n    const prevInjectorProfilerContext = setInjectorProfilerContext({\n      injector,\n      token: null\n    });\n    try {\n      emitEffectCreatedEvent(effectRef);\n    } finally {\n      setInjectorProfilerContext(prevInjectorProfilerContext);\n    }\n  }\n  return effectRef;\n}\nconst BASE_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...REACTIVE_NODE,\n  consumerIsAlwaysLive: true,\n  consumerAllowSignalWrites: true,\n  dirty: true,\n  hasRun: false,\n  cleanupFns: undefined,\n  zone: null,\n  kind: 'effect',\n  onDestroyFn: noop,\n  run() {\n    this.dirty = false;\n    if (ngDevMode && isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n    if (this.hasRun && !consumerPollProducersForChange(this)) {\n      return;\n    }\n    this.hasRun = true;\n    const registerCleanupFn = cleanupFn => (this.cleanupFns ??= []).push(cleanupFn);\n    const prevNode = consumerBeforeComputation(this);\n    // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n    // cause CD to reach the component in question.\n    const prevRefreshingViews = setIsRefreshingViews(false);\n    try {\n      this.maybeCleanup();\n      this.fn(registerCleanupFn);\n    } finally {\n      setIsRefreshingViews(prevRefreshingViews);\n      consumerAfterComputation(this, prevNode);\n    }\n  },\n  maybeCleanup() {\n    if (!this.cleanupFns?.length) {\n      return;\n    }\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n      // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n      // from the cleanup function will still crash the current run of the effect.\n      while (this.cleanupFns.length) {\n        this.cleanupFns.pop()();\n      }\n    } finally {\n      this.cleanupFns = [];\n      setActiveConsumer(prevConsumer);\n    }\n  }\n}))();\nconst ROOT_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...BASE_EFFECT_NODE,\n  consumerMarkedDirty() {\n    this.scheduler.schedule(this);\n    this.notifier.notify(12 /* NotificationSource.RootEffect */);\n  },\n  destroy() {\n    consumerDestroy(this);\n    this.onDestroyFn();\n    this.maybeCleanup();\n    this.scheduler.remove(this);\n  }\n}))();\nconst VIEW_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...BASE_EFFECT_NODE,\n  consumerMarkedDirty() {\n    this.view[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n    markAncestorsForTraversal(this.view);\n    this.notifier.notify(13 /* NotificationSource.ViewEffect */);\n  },\n  destroy() {\n    consumerDestroy(this);\n    this.onDestroyFn();\n    this.maybeCleanup();\n    this.view[EFFECTS]?.delete(this);\n  }\n}))();\nfunction createViewEffect(view, notifier, fn) {\n  const node = Object.create(VIEW_EFFECT_NODE);\n  node.view = view;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.notifier = notifier;\n  node.fn = fn;\n  view[EFFECTS] ??= new Set();\n  view[EFFECTS].add(node);\n  node.consumerMarkedDirty(node);\n  return node;\n}\nfunction createRootEffect(fn, scheduler, notifier) {\n  const node = Object.create(ROOT_EFFECT_NODE);\n  node.fn = fn;\n  node.scheduler = scheduler;\n  node.notifier = notifier;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.scheduler.add(node);\n  node.notifier.notify(12 /* NotificationSource.RootEffect */);\n  return node;\n}\nconst identityFn = v => v;\nfunction linkedSignal(optionsOrComputation, options) {\n  if (typeof optionsOrComputation === 'function') {\n    const getter = createLinkedSignal(optionsOrComputation, identityFn, options?.equal);\n    return upgradeLinkedSignalGetter(getter);\n  } else {\n    const getter = createLinkedSignal(optionsOrComputation.source, optionsOrComputation.computation, optionsOrComputation.equal);\n    return upgradeLinkedSignalGetter(getter);\n  }\n}\nfunction upgradeLinkedSignalGetter(getter) {\n  if (ngDevMode) {\n    getter.toString = () => `[LinkedSignal: ${getter()}]`;\n  }\n  const node = getter[SIGNAL];\n  const upgradedGetter = getter;\n  upgradedGetter.set = newValue => linkedSignalSetFn(node, newValue);\n  upgradedGetter.update = updateFn => linkedSignalUpdateFn(node, updateFn);\n  upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter);\n  return upgradedGetter;\n}\n\n/**\n * Whether a `Resource.value()` should throw an error when the resource is in the error state.\n *\n * This internal flag is being used to gradually roll out this behavior.\n */\nconst RESOURCE_VALUE_THROWS_ERRORS_DEFAULT = true;\nfunction resource(options) {\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(resource);\n  }\n  const oldNameForParams = options.request;\n  const params = options.params ?? oldNameForParams ?? (() => null);\n  return new ResourceImpl(params, getLoader(options), options.defaultValue, options.equal ? wrapEqualityFn(options.equal) : undefined, options.injector ?? inject(Injector), RESOURCE_VALUE_THROWS_ERRORS_DEFAULT);\n}\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nclass BaseWritableResource {\n  value;\n  constructor(value) {\n    this.value = value;\n    this.value.set = this.set.bind(this);\n    this.value.update = this.update.bind(this);\n    this.value.asReadonly = signalAsReadonlyFn;\n  }\n  isError = computed(() => this.status() === 'error');\n  update(updateFn) {\n    this.set(updateFn(untracked(this.value)));\n  }\n  isLoading = computed(() => this.status() === 'loading' || this.status() === 'reloading');\n  hasValue() {\n    // Note: we specifically read `isError()` instead of `status()` here to avoid triggering\n    // reactive consumers which read `hasValue()`. This way, if `hasValue()` is used inside of an\n    // effect, it doesn't cause the effect to rerun on every status change.\n    if (this.isError()) {\n      return false;\n    }\n    return this.value() !== undefined;\n  }\n  asReadonly() {\n    return this;\n  }\n}\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nclass ResourceImpl extends BaseWritableResource {\n  loaderFn;\n  equal;\n  pendingTasks;\n  /**\n   * The current state of the resource. Status, value, and error are derived from this.\n   */\n  state;\n  /**\n   * Combines the current request with a reload counter which allows the resource to be reloaded on\n   * imperative command.\n   */\n  extRequest;\n  effectRef;\n  pendingController;\n  resolvePendingTask = undefined;\n  destroyed = false;\n  constructor(request, loaderFn, defaultValue, equal, injector, throwErrorsFromValue = RESOURCE_VALUE_THROWS_ERRORS_DEFAULT) {\n    super(\n    // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n    // `WritableSignal` that delegates to `ResourceImpl.set`.\n    computed(() => {\n      const streamValue = this.state().stream?.();\n      if (!streamValue) {\n        return defaultValue;\n      }\n      // Prevents `hasValue()` from throwing an error when a reload happened in the error state\n      if (this.state().status === 'loading' && this.error()) {\n        return defaultValue;\n      }\n      if (!isResolved(streamValue)) {\n        if (throwErrorsFromValue) {\n          throw new ResourceValueError(this.error());\n        } else {\n          return defaultValue;\n        }\n      }\n      return streamValue.value;\n    }, {\n      equal\n    }));\n    this.loaderFn = loaderFn;\n    this.equal = equal;\n    // Extend `request()` to include a writable reload signal.\n    this.extRequest = linkedSignal({\n      source: request,\n      computation: request => ({\n        request,\n        reload: 0\n      })\n    });\n    // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n    // state instantaneously when the request signal changes.\n    this.state = linkedSignal({\n      // Whenever the request changes,\n      source: this.extRequest,\n      // Compute the state of the resource given a change in status.\n      computation: (extRequest, previous) => {\n        const status = extRequest.request === undefined ? 'idle' : 'loading';\n        if (!previous) {\n          return {\n            extRequest,\n            status,\n            previousStatus: 'idle',\n            stream: undefined\n          };\n        } else {\n          return {\n            extRequest,\n            status,\n            previousStatus: projectStatusOfState(previous.value),\n            // If the request hasn't changed, keep the previous stream.\n            stream: previous.value.extRequest.request === extRequest.request ? previous.value.stream : undefined\n          };\n        }\n      }\n    });\n    this.effectRef = effect(this.loadEffect.bind(this), {\n      injector,\n      manualCleanup: true\n    });\n    this.pendingTasks = injector.get(PendingTasks);\n    // Cancel any pending request when the resource itself is destroyed.\n    injector.get(DestroyRef).onDestroy(() => this.destroy());\n  }\n  status = computed(() => projectStatusOfState(this.state()));\n  error = computed(() => {\n    const stream = this.state().stream?.();\n    return stream && !isResolved(stream) ? stream.error : undefined;\n  });\n  /**\n   * Called either directly via `WritableResource.set` or via `.value.set()`.\n   */\n  set(value) {\n    if (this.destroyed) {\n      return;\n    }\n    const current = untracked(this.value);\n    const state = untracked(this.state);\n    if (state.status === 'local' && (this.equal ? this.equal(current, value) : current === value)) {\n      return;\n    }\n    // Enter Local state with the user-defined value.\n    this.state.set({\n      extRequest: state.extRequest,\n      status: 'local',\n      previousStatus: 'local',\n      stream: signal({\n        value\n      })\n    });\n    // We're departing from whatever state the resource was in previously, so cancel any in-progress\n    // loading operations.\n    this.abortInProgressLoad();\n  }\n  reload() {\n    // We don't want to restart in-progress loads.\n    const {\n      status\n    } = untracked(this.state);\n    if (status === 'idle' || status === 'loading') {\n      return false;\n    }\n    // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n    this.extRequest.update(({\n      request,\n      reload\n    }) => ({\n      request,\n      reload: reload + 1\n    }));\n    return true;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.effectRef.destroy();\n    this.abortInProgressLoad();\n    // Destroyed resources enter Idle state.\n    this.state.set({\n      extRequest: {\n        request: undefined,\n        reload: 0\n      },\n      status: 'idle',\n      previousStatus: 'idle',\n      stream: undefined\n    });\n  }\n  loadEffect() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const extRequest = _this.extRequest();\n      // Capture the previous status before any state transitions. Note that this is `untracked` since\n      // we do not want the effect to depend on the state of the resource, only on the request.\n      const {\n        status: currentStatus,\n        previousStatus\n      } = untracked(_this.state);\n      if (extRequest.request === undefined) {\n        // Nothing to load (and we should already be in a non-loading state).\n        return;\n      } else if (currentStatus !== 'loading') {\n        // We're not in a loading or reloading state, so this loading request is stale.\n        return;\n      }\n      // Cancel any previous loading attempts.\n      _this.abortInProgressLoad();\n      // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n      // resolve it twice:\n      //\n      //  1. when the loading function promise resolves/rejects\n      //  2. when cancelling the loading operation\n      //\n      // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n      // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n      // response to (1) below, we need to cancel the locally saved task.\n      let resolvePendingTask = _this.resolvePendingTask = _this.pendingTasks.add();\n      const {\n        signal: abortSignal\n      } = _this.pendingController = new AbortController();\n      try {\n        // The actual loading is run through `untracked` - only the request side of `resource` is\n        // reactive. This avoids any confusion with signals tracking or not tracking depending on\n        // which side of the `await` they are.\n        const stream = yield untracked(() => {\n          return _this.loaderFn({\n            params: extRequest.request,\n            // TODO(alxhub): cleanup after g3 removal of `request` alias.\n            request: extRequest.request,\n            abortSignal,\n            previous: {\n              status: previousStatus\n            }\n          });\n        });\n        // If this request has been aborted, or the current request no longer\n        // matches this load, then we should ignore this resolution.\n        if (abortSignal.aborted || untracked(_this.extRequest) !== extRequest) {\n          return;\n        }\n        _this.state.set({\n          extRequest,\n          status: 'resolved',\n          previousStatus: 'resolved',\n          stream\n        });\n      } catch (err) {\n        if (abortSignal.aborted || untracked(_this.extRequest) !== extRequest) {\n          return;\n        }\n        _this.state.set({\n          extRequest,\n          status: 'resolved',\n          previousStatus: 'error',\n          stream: signal({\n            error: encapsulateResourceError(err)\n          })\n        });\n      } finally {\n        // Resolve the pending task now that the resource has a value.\n        resolvePendingTask?.();\n        resolvePendingTask = undefined;\n      }\n    })();\n  }\n  abortInProgressLoad() {\n    untracked(() => this.pendingController?.abort());\n    this.pendingController = undefined;\n    // Once the load is aborted, we no longer want to block stability on its resolution.\n    this.resolvePendingTask?.();\n    this.resolvePendingTask = undefined;\n  }\n}\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn(equal) {\n  return (a, b) => a === undefined || b === undefined ? a === b : equal(a, b);\n}\nfunction getLoader(options) {\n  if (isStreamingResourceOptions(options)) {\n    return options.stream;\n  }\n  return /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (params) {\n      try {\n        return signal({\n          value: yield options.loader(params)\n        });\n      } catch (err) {\n        return signal({\n          error: encapsulateResourceError(err)\n        });\n      }\n    });\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n}\nfunction isStreamingResourceOptions(options) {\n  return !!options.stream;\n}\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state) {\n  switch (state.status) {\n    case 'loading':\n      return state.extRequest.reload === 0 ? 'loading' : 'reloading';\n    case 'resolved':\n      return isResolved(state.stream()) ? 'resolved' : 'error';\n    default:\n      return state.status;\n  }\n}\nfunction isResolved(state) {\n  return state.error === undefined;\n}\nfunction encapsulateResourceError(error) {\n  if (error instanceof Error) {\n    return error;\n  }\n  return new ResourceWrappedError(error);\n}\nclass ResourceValueError extends Error {\n  constructor(error) {\n    super(ngDevMode ? `Resource is currently in an error state (see Error.cause for details): ${error.message}` : error.message, {\n      cause: error\n    });\n  }\n}\nclass ResourceWrappedError extends Error {\n  constructor(error) {\n    super(ngDevMode ? `Resource returned an error that's not an Error instance: ${String(error)}. Check this error's .cause for the actual error.` : String(error), {\n      cause: error\n    });\n  }\n}\nexport { OutputEmitterRef, ResourceImpl, computed, effect, encapsulateResourceError, getOutputDestroyRef, linkedSignal, resource, untracked };", "map": {"version": 3, "names": ["inject", "RuntimeError", "formatRuntimeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DestroyRef", "assertNotInReactiveContext", "assertInInjectionContext", "Injector", "ViewContext", "ChangeDetectionScheduler", "EffectScheduler", "setInjectorProfilerContext", "emitEffectCreatedEvent", "EFFECTS", "noop", "FLAGS", "markAncestorsForTraversal", "setIsRefreshingViews", "NodeInjectorDestroyRef", "signalAsReadonlyFn", "PendingTasks", "signal", "setActiveConsumer", "createComputed", "SIGNAL", "REACTIVE_NODE", "consumerDestroy", "isInNotificationPhase", "consumerPollProducersForChange", "consumerBeforeComputation", "consumerAfterComputation", "untracked", "untracked$1", "createLinkedSignal", "linkedSignalSetFn", "linkedSignalUpdateFn", "OutputEmitterRef", "destroyed", "listeners", "<PERSON><PERSON><PERSON><PERSON>", "optional", "destroyRef", "constructor", "onDestroy", "subscribe", "callback", "ngDevMode", "push", "unsubscribe", "idx", "indexOf", "undefined", "splice", "emit", "value", "console", "warn", "previousConsumer", "listenerFn", "err", "handleError", "getOutputDestroyRef", "ref", "nonReactiveReadsFn", "computed", "computation", "options", "getter", "equal", "toString", "debugName", "EffectRefImpl", "node", "destroy", "effect", "effectFn", "injector", "allowSignalWrites", "manualCleanup", "get", "viewContext", "notifier", "createViewEffect", "view", "_l<PERSON>iew", "createRootEffect", "onDestroyFn", "effectRef", "prevInjectorProfilerContext", "token", "BASE_EFFECT_NODE", "consumerIsAlwaysLive", "consumerAllowSignalWrites", "dirty", "<PERSON><PERSON>un", "cleanupFns", "zone", "kind", "run", "Error", "registerCleanupFn", "cleanupFn", "prevNode", "prevRefreshingViews", "maybeCleanup", "fn", "length", "prevConsumer", "pop", "ROOT_EFFECT_NODE", "consumerMarkedDirty", "scheduler", "schedule", "notify", "remove", "VIEW_EFFECT_NODE", "delete", "Object", "create", "Zone", "current", "Set", "add", "identityFn", "v", "linkedSignal", "optionsOrComputation", "upgradeLinkedSignalGetter", "source", "upgradedGetter", "set", "newValue", "update", "updateFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "RESOURCE_VALUE_THROWS_ERRORS_DEFAULT", "resource", "oldNameForParams", "request", "params", "ResourceImpl", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "wrapEqualityFn", "BaseWritableResource", "isError", "status", "isLoading", "hasValue", "loaderFn", "pendingTasks", "state", "extRequest", "pendingController", "resolvePendingTask", "throwErrors<PERSON>rom<PERSON><PERSON>ue", "streamValue", "stream", "error", "isResolved", "ResourceValueError", "reload", "previous", "previousStatus", "projectStatusOfState", "loadEffect", "abortInProgressLoad", "_this", "_asyncToGenerator", "currentStatus", "abortSignal", "AbortController", "aborted", "encapsulateResourceError", "abort", "a", "b", "isStreamingResourceOptions", "_ref", "loader", "_x", "apply", "arguments", "ResourceWrappedError", "message", "cause", "String"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/core/fesm2022/resource-BPCh38bN.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { inject, RuntimeError, formatRuntimeError, ErrorHandler, DestroyRef, assertNotInReactiveContext, assertInInjectionContext, Injector, ViewContext, ChangeDetectionScheduler, EffectScheduler, setInjectorProfilerContext, emitEffectCreatedEvent, EFFECTS, noop, FLAGS, markAncestorsForTraversal, setIsRefreshingViews, NodeInjectorDestroyRef, signalAsReadonlyFn, PendingTasks, signal } from './root_effect_scheduler-0BxwqIgm.mjs';\nimport { setActiveConsumer, createComputed, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal-ePSl6jXn.mjs';\nimport { untracked as untracked$1, createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn } from './untracked-2ouAFbCz.mjs';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nclass OutputEmitterRef {\n    destroyed = false;\n    listeners = null;\n    errorHandler = inject(ErrorHandler, { optional: true });\n    /** @internal */\n    destroyRef = inject(DestroyRef);\n    constructor() {\n        // Clean-up all listeners and mark as destroyed upon destroy.\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n            this.listeners = null;\n        });\n    }\n    subscribe(callback) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        (this.listeners ??= []).push(callback);\n        return {\n            unsubscribe: () => {\n                const idx = this.listeners?.indexOf(callback);\n                if (idx !== undefined && idx !== -1) {\n                    this.listeners?.splice(idx, 1);\n                }\n            },\n        };\n    }\n    /** Emits a new value to the output. */\n    emit(value) {\n        if (this.destroyed) {\n            console.warn(formatRuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected emit for destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.'));\n            return;\n        }\n        if (this.listeners === null) {\n            return;\n        }\n        const previousConsumer = setActiveConsumer(null);\n        try {\n            for (const listenerFn of this.listeners) {\n                try {\n                    listenerFn(value);\n                }\n                catch (err) {\n                    this.errorHandler?.handleError(err);\n                }\n            }\n        }\n        finally {\n            setActiveConsumer(previousConsumer);\n        }\n    }\n}\n/** Gets the owning `DestroyRef` for the given output. */\nfunction getOutputDestroyRef(ref) {\n    return ref.destroyRef;\n}\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n    return untracked$1(nonReactiveReadsFn);\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nfunction computed(computation, options) {\n    const getter = createComputed(computation, options?.equal);\n    if (ngDevMode) {\n        getter.toString = () => `[Computed: ${getter()}]`;\n        getter[SIGNAL].debugName = options?.debugName;\n    }\n    return getter;\n}\n\nclass EffectRefImpl {\n    [SIGNAL];\n    constructor(node) {\n        this[SIGNAL] = node;\n    }\n    destroy() {\n        this[SIGNAL].destroy();\n    }\n}\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n *\n * @publicApi 20.0\n */\nfunction effect(effectFn, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(effect, 'Call `effect` outside of a reactive context. For example, schedule the ' +\n            'effect inside the component constructor.');\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(effect);\n    }\n    if (ngDevMode && options?.allowSignalWrites !== undefined) {\n        console.warn(`The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n    let node;\n    const viewContext = injector.get(ViewContext, null, { optional: true });\n    const notifier = injector.get(ChangeDetectionScheduler);\n    if (viewContext !== null) {\n        // This effect was created in the context of a view, and will be associated with the view.\n        node = createViewEffect(viewContext.view, notifier, effectFn);\n        if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n            // The effect is being created in the same view as the `DestroyRef` references, so it will be\n            // automatically destroyed without the need for an explicit `DestroyRef` registration.\n            destroyRef = null;\n        }\n    }\n    else {\n        // This effect was created outside the context of a view, and will be scheduled independently.\n        node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n    }\n    node.injector = injector;\n    if (destroyRef !== null) {\n        // If we need to register for cleanup, do that here.\n        node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n    }\n    const effectRef = new EffectRefImpl(node);\n    if (ngDevMode) {\n        node.debugName = options?.debugName ?? '';\n        const prevInjectorProfilerContext = setInjectorProfilerContext({ injector, token: null });\n        try {\n            emitEffectCreatedEvent(effectRef);\n        }\n        finally {\n            setInjectorProfilerContext(prevInjectorProfilerContext);\n        }\n    }\n    return effectRef;\n}\nconst BASE_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: true,\n    dirty: true,\n    hasRun: false,\n    cleanupFns: undefined,\n    zone: null,\n    kind: 'effect',\n    onDestroyFn: noop,\n    run() {\n        this.dirty = false;\n        if (ngDevMode && isInNotificationPhase()) {\n            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n        }\n        if (this.hasRun && !consumerPollProducersForChange(this)) {\n            return;\n        }\n        this.hasRun = true;\n        const registerCleanupFn = (cleanupFn) => (this.cleanupFns ??= []).push(cleanupFn);\n        const prevNode = consumerBeforeComputation(this);\n        // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n        // cause CD to reach the component in question.\n        const prevRefreshingViews = setIsRefreshingViews(false);\n        try {\n            this.maybeCleanup();\n            this.fn(registerCleanupFn);\n        }\n        finally {\n            setIsRefreshingViews(prevRefreshingViews);\n            consumerAfterComputation(this, prevNode);\n        }\n    },\n    maybeCleanup() {\n        if (!this.cleanupFns?.length) {\n            return;\n        }\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n            // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n            // from the cleanup function will still crash the current run of the effect.\n            while (this.cleanupFns.length) {\n                this.cleanupFns.pop()();\n            }\n        }\n        finally {\n            this.cleanupFns = [];\n            setActiveConsumer(prevConsumer);\n        }\n    },\n}))();\nconst ROOT_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty() {\n        this.scheduler.schedule(this);\n        this.notifier.notify(12 /* NotificationSource.RootEffect */);\n    },\n    destroy() {\n        consumerDestroy(this);\n        this.onDestroyFn();\n        this.maybeCleanup();\n        this.scheduler.remove(this);\n    },\n}))();\nconst VIEW_EFFECT_NODE = \n/* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty() {\n        this.view[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n        markAncestorsForTraversal(this.view);\n        this.notifier.notify(13 /* NotificationSource.ViewEffect */);\n    },\n    destroy() {\n        consumerDestroy(this);\n        this.onDestroyFn();\n        this.maybeCleanup();\n        this.view[EFFECTS]?.delete(this);\n    },\n}))();\nfunction createViewEffect(view, notifier, fn) {\n    const node = Object.create(VIEW_EFFECT_NODE);\n    node.view = view;\n    node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n    node.notifier = notifier;\n    node.fn = fn;\n    view[EFFECTS] ??= new Set();\n    view[EFFECTS].add(node);\n    node.consumerMarkedDirty(node);\n    return node;\n}\nfunction createRootEffect(fn, scheduler, notifier) {\n    const node = Object.create(ROOT_EFFECT_NODE);\n    node.fn = fn;\n    node.scheduler = scheduler;\n    node.notifier = notifier;\n    node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n    node.scheduler.add(node);\n    node.notifier.notify(12 /* NotificationSource.RootEffect */);\n    return node;\n}\n\nconst identityFn = (v) => v;\nfunction linkedSignal(optionsOrComputation, options) {\n    if (typeof optionsOrComputation === 'function') {\n        const getter = createLinkedSignal(optionsOrComputation, (identityFn), options?.equal);\n        return upgradeLinkedSignalGetter(getter);\n    }\n    else {\n        const getter = createLinkedSignal(optionsOrComputation.source, optionsOrComputation.computation, optionsOrComputation.equal);\n        return upgradeLinkedSignalGetter(getter);\n    }\n}\nfunction upgradeLinkedSignalGetter(getter) {\n    if (ngDevMode) {\n        getter.toString = () => `[LinkedSignal: ${getter()}]`;\n    }\n    const node = getter[SIGNAL];\n    const upgradedGetter = getter;\n    upgradedGetter.set = (newValue) => linkedSignalSetFn(node, newValue);\n    upgradedGetter.update = (updateFn) => linkedSignalUpdateFn(node, updateFn);\n    upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter);\n    return upgradedGetter;\n}\n\n/**\n * Whether a `Resource.value()` should throw an error when the resource is in the error state.\n *\n * This internal flag is being used to gradually roll out this behavior.\n */\nconst RESOURCE_VALUE_THROWS_ERRORS_DEFAULT = true;\nfunction resource(options) {\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(resource);\n    }\n    const oldNameForParams = options.request;\n    const params = (options.params ?? oldNameForParams ?? (() => null));\n    return new ResourceImpl(params, getLoader(options), options.defaultValue, options.equal ? wrapEqualityFn(options.equal) : undefined, options.injector ?? inject(Injector), RESOURCE_VALUE_THROWS_ERRORS_DEFAULT);\n}\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nclass BaseWritableResource {\n    value;\n    constructor(value) {\n        this.value = value;\n        this.value.set = this.set.bind(this);\n        this.value.update = this.update.bind(this);\n        this.value.asReadonly = signalAsReadonlyFn;\n    }\n    isError = computed(() => this.status() === 'error');\n    update(updateFn) {\n        this.set(updateFn(untracked(this.value)));\n    }\n    isLoading = computed(() => this.status() === 'loading' || this.status() === 'reloading');\n    hasValue() {\n        // Note: we specifically read `isError()` instead of `status()` here to avoid triggering\n        // reactive consumers which read `hasValue()`. This way, if `hasValue()` is used inside of an\n        // effect, it doesn't cause the effect to rerun on every status change.\n        if (this.isError()) {\n            return false;\n        }\n        return this.value() !== undefined;\n    }\n    asReadonly() {\n        return this;\n    }\n}\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nclass ResourceImpl extends BaseWritableResource {\n    loaderFn;\n    equal;\n    pendingTasks;\n    /**\n     * The current state of the resource. Status, value, and error are derived from this.\n     */\n    state;\n    /**\n     * Combines the current request with a reload counter which allows the resource to be reloaded on\n     * imperative command.\n     */\n    extRequest;\n    effectRef;\n    pendingController;\n    resolvePendingTask = undefined;\n    destroyed = false;\n    constructor(request, loaderFn, defaultValue, equal, injector, throwErrorsFromValue = RESOURCE_VALUE_THROWS_ERRORS_DEFAULT) {\n        super(\n        // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n        // `WritableSignal` that delegates to `ResourceImpl.set`.\n        computed(() => {\n            const streamValue = this.state().stream?.();\n            if (!streamValue) {\n                return defaultValue;\n            }\n            // Prevents `hasValue()` from throwing an error when a reload happened in the error state\n            if (this.state().status === 'loading' && this.error()) {\n                return defaultValue;\n            }\n            if (!isResolved(streamValue)) {\n                if (throwErrorsFromValue) {\n                    throw new ResourceValueError(this.error());\n                }\n                else {\n                    return defaultValue;\n                }\n            }\n            return streamValue.value;\n        }, { equal }));\n        this.loaderFn = loaderFn;\n        this.equal = equal;\n        // Extend `request()` to include a writable reload signal.\n        this.extRequest = linkedSignal({\n            source: request,\n            computation: (request) => ({ request, reload: 0 }),\n        });\n        // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n        // state instantaneously when the request signal changes.\n        this.state = linkedSignal({\n            // Whenever the request changes,\n            source: this.extRequest,\n            // Compute the state of the resource given a change in status.\n            computation: (extRequest, previous) => {\n                const status = extRequest.request === undefined ? 'idle' : 'loading';\n                if (!previous) {\n                    return {\n                        extRequest,\n                        status,\n                        previousStatus: 'idle',\n                        stream: undefined,\n                    };\n                }\n                else {\n                    return {\n                        extRequest,\n                        status,\n                        previousStatus: projectStatusOfState(previous.value),\n                        // If the request hasn't changed, keep the previous stream.\n                        stream: previous.value.extRequest.request === extRequest.request\n                            ? previous.value.stream\n                            : undefined,\n                    };\n                }\n            },\n        });\n        this.effectRef = effect(this.loadEffect.bind(this), {\n            injector,\n            manualCleanup: true,\n        });\n        this.pendingTasks = injector.get(PendingTasks);\n        // Cancel any pending request when the resource itself is destroyed.\n        injector.get(DestroyRef).onDestroy(() => this.destroy());\n    }\n    status = computed(() => projectStatusOfState(this.state()));\n    error = computed(() => {\n        const stream = this.state().stream?.();\n        return stream && !isResolved(stream) ? stream.error : undefined;\n    });\n    /**\n     * Called either directly via `WritableResource.set` or via `.value.set()`.\n     */\n    set(value) {\n        if (this.destroyed) {\n            return;\n        }\n        const current = untracked(this.value);\n        const state = untracked(this.state);\n        if (state.status === 'local' && (this.equal ? this.equal(current, value) : current === value)) {\n            return;\n        }\n        // Enter Local state with the user-defined value.\n        this.state.set({\n            extRequest: state.extRequest,\n            status: 'local',\n            previousStatus: 'local',\n            stream: signal({ value }),\n        });\n        // We're departing from whatever state the resource was in previously, so cancel any in-progress\n        // loading operations.\n        this.abortInProgressLoad();\n    }\n    reload() {\n        // We don't want to restart in-progress loads.\n        const { status } = untracked(this.state);\n        if (status === 'idle' || status === 'loading') {\n            return false;\n        }\n        // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n        this.extRequest.update(({ request, reload }) => ({ request, reload: reload + 1 }));\n        return true;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.effectRef.destroy();\n        this.abortInProgressLoad();\n        // Destroyed resources enter Idle state.\n        this.state.set({\n            extRequest: { request: undefined, reload: 0 },\n            status: 'idle',\n            previousStatus: 'idle',\n            stream: undefined,\n        });\n    }\n    async loadEffect() {\n        const extRequest = this.extRequest();\n        // Capture the previous status before any state transitions. Note that this is `untracked` since\n        // we do not want the effect to depend on the state of the resource, only on the request.\n        const { status: currentStatus, previousStatus } = untracked(this.state);\n        if (extRequest.request === undefined) {\n            // Nothing to load (and we should already be in a non-loading state).\n            return;\n        }\n        else if (currentStatus !== 'loading') {\n            // We're not in a loading or reloading state, so this loading request is stale.\n            return;\n        }\n        // Cancel any previous loading attempts.\n        this.abortInProgressLoad();\n        // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n        // resolve it twice:\n        //\n        //  1. when the loading function promise resolves/rejects\n        //  2. when cancelling the loading operation\n        //\n        // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n        // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n        // response to (1) below, we need to cancel the locally saved task.\n        let resolvePendingTask = (this.resolvePendingTask =\n            this.pendingTasks.add());\n        const { signal: abortSignal } = (this.pendingController = new AbortController());\n        try {\n            // The actual loading is run through `untracked` - only the request side of `resource` is\n            // reactive. This avoids any confusion with signals tracking or not tracking depending on\n            // which side of the `await` they are.\n            const stream = await untracked(() => {\n                return this.loaderFn({\n                    params: extRequest.request,\n                    // TODO(alxhub): cleanup after g3 removal of `request` alias.\n                    request: extRequest.request,\n                    abortSignal,\n                    previous: {\n                        status: previousStatus,\n                    },\n                });\n            });\n            // If this request has been aborted, or the current request no longer\n            // matches this load, then we should ignore this resolution.\n            if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n                return;\n            }\n            this.state.set({\n                extRequest,\n                status: 'resolved',\n                previousStatus: 'resolved',\n                stream,\n            });\n        }\n        catch (err) {\n            if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n                return;\n            }\n            this.state.set({\n                extRequest,\n                status: 'resolved',\n                previousStatus: 'error',\n                stream: signal({ error: encapsulateResourceError(err) }),\n            });\n        }\n        finally {\n            // Resolve the pending task now that the resource has a value.\n            resolvePendingTask?.();\n            resolvePendingTask = undefined;\n        }\n    }\n    abortInProgressLoad() {\n        untracked(() => this.pendingController?.abort());\n        this.pendingController = undefined;\n        // Once the load is aborted, we no longer want to block stability on its resolution.\n        this.resolvePendingTask?.();\n        this.resolvePendingTask = undefined;\n    }\n}\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn(equal) {\n    return (a, b) => (a === undefined || b === undefined ? a === b : equal(a, b));\n}\nfunction getLoader(options) {\n    if (isStreamingResourceOptions(options)) {\n        return options.stream;\n    }\n    return async (params) => {\n        try {\n            return signal({ value: await options.loader(params) });\n        }\n        catch (err) {\n            return signal({ error: encapsulateResourceError(err) });\n        }\n    };\n}\nfunction isStreamingResourceOptions(options) {\n    return !!options.stream;\n}\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state) {\n    switch (state.status) {\n        case 'loading':\n            return state.extRequest.reload === 0 ? 'loading' : 'reloading';\n        case 'resolved':\n            return isResolved(state.stream()) ? 'resolved' : 'error';\n        default:\n            return state.status;\n    }\n}\nfunction isResolved(state) {\n    return state.error === undefined;\n}\nfunction encapsulateResourceError(error) {\n    if (error instanceof Error) {\n        return error;\n    }\n    return new ResourceWrappedError(error);\n}\nclass ResourceValueError extends Error {\n    constructor(error) {\n        super(ngDevMode\n            ? `Resource is currently in an error state (see Error.cause for details): ${error.message}`\n            : error.message, { cause: error });\n    }\n}\nclass ResourceWrappedError extends Error {\n    constructor(error) {\n        super(ngDevMode\n            ? `Resource returned an error that's not an Error instance: ${String(error)}. Check this error's .cause for the actual error.`\n            : String(error), { cause: error });\n    }\n}\n\nexport { OutputEmitterRef, ResourceImpl, computed, effect, encapsulateResourceError, getOutputDestroyRef, linkedSignal, resource, untracked };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAM,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,MAAM,QAAQ,sCAAsC;AAC9a,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,8BAA8B,EAAEC,yBAAyB,EAAEC,wBAAwB,QAAQ,uBAAuB;AAC7N,SAASC,SAAS,IAAIC,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,0BAA0B;;AAEhI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,IAAI;EAChBC,YAAY,GAAGvC,MAAM,CAACG,YAAY,EAAE;IAAEqC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvD;EACAC,UAAU,GAAGzC,MAAM,CAACI,UAAU,CAAC;EAC/BsC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACD,UAAU,CAACE,SAAS,CAAC,MAAM;MAC5B,IAAI,CAACN,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACzB,CAAC,CAAC;EACN;EACAM,SAASA,CAACC,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,MAAM,IAAIpC,YAAY,CAAC,GAAG,CAAC,6CAA6C6C,SAAS,IAC7E,oDAAoD,GAChD,8CAA8C,CAAC;IAC3D;IACA,CAAC,IAAI,CAACR,SAAS,KAAK,EAAE,EAAES,IAAI,CAACF,QAAQ,CAAC;IACtC,OAAO;MACHG,WAAW,EAAEA,CAAA,KAAM;QACf,MAAMC,GAAG,GAAG,IAAI,CAACX,SAAS,EAAEY,OAAO,CAACL,QAAQ,CAAC;QAC7C,IAAII,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,CAAC,CAAC,EAAE;UACjC,IAAI,CAACX,SAAS,EAAEc,MAAM,CAACH,GAAG,EAAE,CAAC,CAAC;QAClC;MACJ;IACJ,CAAC;EACL;EACA;EACAI,IAAIA,CAACC,KAAK,EAAE;IACR,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChBkB,OAAO,CAACC,IAAI,CAACtD,kBAAkB,CAAC,GAAG,CAAC,6CAA6C4C,SAAS,IACtF,6CAA6C,GACzC,8CAA8C,CAAC,CAAC;MACxD;IACJ;IACA,IAAI,IAAI,CAACR,SAAS,KAAK,IAAI,EAAE;MACzB;IACJ;IACA,MAAMmB,gBAAgB,GAAGnC,iBAAiB,CAAC,IAAI,CAAC;IAChD,IAAI;MACA,KAAK,MAAMoC,UAAU,IAAI,IAAI,CAACpB,SAAS,EAAE;QACrC,IAAI;UACAoB,UAAU,CAACJ,KAAK,CAAC;QACrB,CAAC,CACD,OAAOK,GAAG,EAAE;UACR,IAAI,CAACpB,YAAY,EAAEqB,WAAW,CAACD,GAAG,CAAC;QACvC;MACJ;IACJ,CAAC,SACO;MACJrC,iBAAiB,CAACmC,gBAAgB,CAAC;IACvC;EACJ;AACJ;AACA;AACA,SAASI,mBAAmBA,CAACC,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAACrB,UAAU;AACzB;;AAEA;AACA;AACA;AACA;AACA,SAASV,SAASA,CAACgC,kBAAkB,EAAE;EACnC,OAAO/B,WAAW,CAAC+B,kBAAkB,CAAC;AAC1C;;AAEA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAE;EACpC,MAAMC,MAAM,GAAG5C,cAAc,CAAC0C,WAAW,EAAEC,OAAO,EAAEE,KAAK,CAAC;EAC1D,IAAItB,SAAS,EAAE;IACXqB,MAAM,CAACE,QAAQ,GAAG,MAAM,cAAcF,MAAM,CAAC,CAAC,GAAG;IACjDA,MAAM,CAAC3C,MAAM,CAAC,CAAC8C,SAAS,GAAGJ,OAAO,EAAEI,SAAS;EACjD;EACA,OAAOH,MAAM;AACjB;AAEA,MAAMI,aAAa,CAAC;EAChB,CAAC/C,MAAM;EACPkB,WAAWA,CAAC8B,IAAI,EAAE;IACd,IAAI,CAAChD,MAAM,CAAC,GAAGgD,IAAI;EACvB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjD,MAAM,CAAC,CAACiD,OAAO,CAAC,CAAC;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,QAAQ,EAAET,OAAO,EAAE;EAC/BpB,SAAS,IACLzC,0BAA0B,CAACqE,MAAM,EAAE,yEAAyE,GACxG,0CAA0C,CAAC;EACnD,IAAI5B,SAAS,IAAI,CAACoB,OAAO,EAAEU,QAAQ,EAAE;IACjCtE,wBAAwB,CAACoE,MAAM,CAAC;EACpC;EACA,IAAI5B,SAAS,IAAIoB,OAAO,EAAEW,iBAAiB,KAAK1B,SAAS,EAAE;IACvDI,OAAO,CAACC,IAAI,CAAC,uGAAuG,CAAC;EACzH;EACA,MAAMoB,QAAQ,GAAGV,OAAO,EAAEU,QAAQ,IAAI5E,MAAM,CAACO,QAAQ,CAAC;EACtD,IAAIkC,UAAU,GAAGyB,OAAO,EAAEY,aAAa,KAAK,IAAI,GAAGF,QAAQ,CAACG,GAAG,CAAC3E,UAAU,CAAC,GAAG,IAAI;EAClF,IAAIoE,IAAI;EACR,MAAMQ,WAAW,GAAGJ,QAAQ,CAACG,GAAG,CAACvE,WAAW,EAAE,IAAI,EAAE;IAAEgC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvE,MAAMyC,QAAQ,GAAGL,QAAQ,CAACG,GAAG,CAACtE,wBAAwB,CAAC;EACvD,IAAIuE,WAAW,KAAK,IAAI,EAAE;IACtB;IACAR,IAAI,GAAGU,gBAAgB,CAACF,WAAW,CAACG,IAAI,EAAEF,QAAQ,EAAEN,QAAQ,CAAC;IAC7D,IAAIlC,UAAU,YAAYvB,sBAAsB,IAAIuB,UAAU,CAAC2C,MAAM,KAAKJ,WAAW,CAACG,IAAI,EAAE;MACxF;MACA;MACA1C,UAAU,GAAG,IAAI;IACrB;EACJ,CAAC,MACI;IACD;IACA+B,IAAI,GAAGa,gBAAgB,CAACV,QAAQ,EAAEC,QAAQ,CAACG,GAAG,CAACrE,eAAe,CAAC,EAAEuE,QAAQ,CAAC;EAC9E;EACAT,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EACxB,IAAInC,UAAU,KAAK,IAAI,EAAE;IACrB;IACA+B,IAAI,CAACc,WAAW,GAAG7C,UAAU,CAACE,SAAS,CAAC,MAAM6B,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EACjE;EACA,MAAMc,SAAS,GAAG,IAAIhB,aAAa,CAACC,IAAI,CAAC;EACzC,IAAI1B,SAAS,EAAE;IACX0B,IAAI,CAACF,SAAS,GAAGJ,OAAO,EAAEI,SAAS,IAAI,EAAE;IACzC,MAAMkB,2BAA2B,GAAG7E,0BAA0B,CAAC;MAAEiE,QAAQ;MAAEa,KAAK,EAAE;IAAK,CAAC,CAAC;IACzF,IAAI;MACA7E,sBAAsB,CAAC2E,SAAS,CAAC;IACrC,CAAC,SACO;MACJ5E,0BAA0B,CAAC6E,2BAA2B,CAAC;IAC3D;EACJ;EACA,OAAOD,SAAS;AACpB;AACA,MAAMG,gBAAgB,GACtB,eAAgB,CAAC,OAAO;EACpB,GAAGjE,aAAa;EAChBkE,oBAAoB,EAAE,IAAI;EAC1BC,yBAAyB,EAAE,IAAI;EAC/BC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE5C,SAAS;EACrB6C,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,QAAQ;EACdX,WAAW,EAAExE,IAAI;EACjBoF,GAAGA,CAAA,EAAG;IACF,IAAI,CAACL,KAAK,GAAG,KAAK;IAClB,IAAI/C,SAAS,IAAInB,qBAAqB,CAAC,CAAC,EAAE;MACtC,MAAM,IAAIwE,KAAK,CAAC,mEAAmE,CAAC;IACxF;IACA,IAAI,IAAI,CAACL,MAAM,IAAI,CAAClE,8BAA8B,CAAC,IAAI,CAAC,EAAE;MACtD;IACJ;IACA,IAAI,CAACkE,MAAM,GAAG,IAAI;IAClB,MAAMM,iBAAiB,GAAIC,SAAS,IAAK,CAAC,IAAI,CAACN,UAAU,KAAK,EAAE,EAAEhD,IAAI,CAACsD,SAAS,CAAC;IACjF,MAAMC,QAAQ,GAAGzE,yBAAyB,CAAC,IAAI,CAAC;IAChD;IACA;IACA,MAAM0E,mBAAmB,GAAGtF,oBAAoB,CAAC,KAAK,CAAC;IACvD,IAAI;MACA,IAAI,CAACuF,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,EAAE,CAACL,iBAAiB,CAAC;IAC9B,CAAC,SACO;MACJnF,oBAAoB,CAACsF,mBAAmB,CAAC;MACzCzE,wBAAwB,CAAC,IAAI,EAAEwE,QAAQ,CAAC;IAC5C;EACJ,CAAC;EACDE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACT,UAAU,EAAEW,MAAM,EAAE;MAC1B;IACJ;IACA,MAAMC,YAAY,GAAGrF,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACyE,UAAU,CAACW,MAAM,EAAE;QAC3B,IAAI,CAACX,UAAU,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3B;IACJ,CAAC,SACO;MACJ,IAAI,CAACb,UAAU,GAAG,EAAE;MACpBzE,iBAAiB,CAACqF,YAAY,CAAC;IACnC;EACJ;AACJ,CAAC,CAAC,EAAE,CAAC;AACL,MAAME,gBAAgB,GACtB,eAAgB,CAAC,OAAO;EACpB,GAAGnB,gBAAgB;EACnBoB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC7B,IAAI,CAAC/B,QAAQ,CAACgC,MAAM,CAAC,EAAE,CAAC,mCAAmC,CAAC;EAChE,CAAC;EACDxC,OAAOA,CAAA,EAAG;IACN/C,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI,CAAC4D,WAAW,CAAC,CAAC;IAClB,IAAI,CAACkB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACO,SAAS,CAACG,MAAM,CAAC,IAAI,CAAC;EAC/B;AACJ,CAAC,CAAC,EAAE,CAAC;AACL,MAAMC,gBAAgB,GACtB,eAAgB,CAAC,OAAO;EACpB,GAAGzB,gBAAgB;EACnBoB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC3B,IAAI,CAACpE,KAAK,CAAC,IAAI,IAAI,CAAC;IACzBC,yBAAyB,CAAC,IAAI,CAACmE,IAAI,CAAC;IACpC,IAAI,CAACF,QAAQ,CAACgC,MAAM,CAAC,EAAE,CAAC,mCAAmC,CAAC;EAChE,CAAC;EACDxC,OAAOA,CAAA,EAAG;IACN/C,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI,CAAC4D,WAAW,CAAC,CAAC;IAClB,IAAI,CAACkB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACrB,IAAI,CAACtE,OAAO,CAAC,EAAEuG,MAAM,CAAC,IAAI,CAAC;EACpC;AACJ,CAAC,CAAC,EAAE,CAAC;AACL,SAASlC,gBAAgBA,CAACC,IAAI,EAAEF,QAAQ,EAAEwB,EAAE,EAAE;EAC1C,MAAMjC,IAAI,GAAG6C,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAAC;EAC5C3C,IAAI,CAACW,IAAI,GAAGA,IAAI;EAChBX,IAAI,CAACwB,IAAI,GAAG,OAAOuB,IAAI,KAAK,WAAW,GAAGA,IAAI,CAACC,OAAO,GAAG,IAAI;EAC7DhD,IAAI,CAACS,QAAQ,GAAGA,QAAQ;EACxBT,IAAI,CAACiC,EAAE,GAAGA,EAAE;EACZtB,IAAI,CAACtE,OAAO,CAAC,KAAK,IAAI4G,GAAG,CAAC,CAAC;EAC3BtC,IAAI,CAACtE,OAAO,CAAC,CAAC6G,GAAG,CAAClD,IAAI,CAAC;EACvBA,IAAI,CAACsC,mBAAmB,CAACtC,IAAI,CAAC;EAC9B,OAAOA,IAAI;AACf;AACA,SAASa,gBAAgBA,CAACoB,EAAE,EAAEM,SAAS,EAAE9B,QAAQ,EAAE;EAC/C,MAAMT,IAAI,GAAG6C,MAAM,CAACC,MAAM,CAACT,gBAAgB,CAAC;EAC5CrC,IAAI,CAACiC,EAAE,GAAGA,EAAE;EACZjC,IAAI,CAACuC,SAAS,GAAGA,SAAS;EAC1BvC,IAAI,CAACS,QAAQ,GAAGA,QAAQ;EACxBT,IAAI,CAACwB,IAAI,GAAG,OAAOuB,IAAI,KAAK,WAAW,GAAGA,IAAI,CAACC,OAAO,GAAG,IAAI;EAC7DhD,IAAI,CAACuC,SAAS,CAACW,GAAG,CAAClD,IAAI,CAAC;EACxBA,IAAI,CAACS,QAAQ,CAACgC,MAAM,CAAC,EAAE,CAAC,mCAAmC,CAAC;EAC5D,OAAOzC,IAAI;AACf;AAEA,MAAMmD,UAAU,GAAIC,CAAC,IAAKA,CAAC;AAC3B,SAASC,YAAYA,CAACC,oBAAoB,EAAE5D,OAAO,EAAE;EACjD,IAAI,OAAO4D,oBAAoB,KAAK,UAAU,EAAE;IAC5C,MAAM3D,MAAM,GAAGlC,kBAAkB,CAAC6F,oBAAoB,EAAGH,UAAU,EAAGzD,OAAO,EAAEE,KAAK,CAAC;IACrF,OAAO2D,yBAAyB,CAAC5D,MAAM,CAAC;EAC5C,CAAC,MACI;IACD,MAAMA,MAAM,GAAGlC,kBAAkB,CAAC6F,oBAAoB,CAACE,MAAM,EAAEF,oBAAoB,CAAC7D,WAAW,EAAE6D,oBAAoB,CAAC1D,KAAK,CAAC;IAC5H,OAAO2D,yBAAyB,CAAC5D,MAAM,CAAC;EAC5C;AACJ;AACA,SAAS4D,yBAAyBA,CAAC5D,MAAM,EAAE;EACvC,IAAIrB,SAAS,EAAE;IACXqB,MAAM,CAACE,QAAQ,GAAG,MAAM,kBAAkBF,MAAM,CAAC,CAAC,GAAG;EACzD;EACA,MAAMK,IAAI,GAAGL,MAAM,CAAC3C,MAAM,CAAC;EAC3B,MAAMyG,cAAc,GAAG9D,MAAM;EAC7B8D,cAAc,CAACC,GAAG,GAAIC,QAAQ,IAAKjG,iBAAiB,CAACsC,IAAI,EAAE2D,QAAQ,CAAC;EACpEF,cAAc,CAACG,MAAM,GAAIC,QAAQ,IAAKlG,oBAAoB,CAACqC,IAAI,EAAE6D,QAAQ,CAAC;EAC1EJ,cAAc,CAACK,UAAU,GAAGnH,kBAAkB,CAACoH,IAAI,CAACpE,MAAM,CAAC;EAC3D,OAAO8D,cAAc;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMO,oCAAoC,GAAG,IAAI;AACjD,SAASC,QAAQA,CAACvE,OAAO,EAAE;EACvB,IAAIpB,SAAS,IAAI,CAACoB,OAAO,EAAEU,QAAQ,EAAE;IACjCtE,wBAAwB,CAACmI,QAAQ,CAAC;EACtC;EACA,MAAMC,gBAAgB,GAAGxE,OAAO,CAACyE,OAAO;EACxC,MAAMC,MAAM,GAAI1E,OAAO,CAAC0E,MAAM,IAAIF,gBAAgB,KAAK,MAAM,IAAI,CAAE;EACnE,OAAO,IAAIG,YAAY,CAACD,MAAM,EAAEE,SAAS,CAAC5E,OAAO,CAAC,EAAEA,OAAO,CAAC6E,YAAY,EAAE7E,OAAO,CAACE,KAAK,GAAG4E,cAAc,CAAC9E,OAAO,CAACE,KAAK,CAAC,GAAGjB,SAAS,EAAEe,OAAO,CAACU,QAAQ,IAAI5E,MAAM,CAACO,QAAQ,CAAC,EAAEiI,oCAAoC,CAAC;AACpN;AACA;AACA;AACA;AACA,MAAMS,oBAAoB,CAAC;EACvB3F,KAAK;EACLZ,WAAWA,CAACY,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACA,KAAK,CAAC4E,GAAG,GAAG,IAAI,CAACA,GAAG,CAACK,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACjF,KAAK,CAAC8E,MAAM,GAAG,IAAI,CAACA,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACjF,KAAK,CAACgF,UAAU,GAAGnH,kBAAkB;EAC9C;EACA+H,OAAO,GAAGlF,QAAQ,CAAC,MAAM,IAAI,CAACmF,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC;EACnDf,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACH,GAAG,CAACG,QAAQ,CAACtG,SAAS,CAAC,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC;EAC7C;EACA8F,SAAS,GAAGpF,QAAQ,CAAC,MAAM,IAAI,CAACmF,MAAM,CAAC,CAAC,KAAK,SAAS,IAAI,IAAI,CAACA,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC;EACxFE,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA,IAAI,IAAI,CAACH,OAAO,CAAC,CAAC,EAAE;MAChB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAAC5F,KAAK,CAAC,CAAC,KAAKH,SAAS;EACrC;EACAmF,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA,MAAMO,YAAY,SAASI,oBAAoB,CAAC;EAC5CK,QAAQ;EACRlF,KAAK;EACLmF,YAAY;EACZ;AACJ;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACVlE,SAAS;EACTmE,iBAAiB;EACjBC,kBAAkB,GAAGxG,SAAS;EAC9Bd,SAAS,GAAG,KAAK;EACjBK,WAAWA,CAACiG,OAAO,EAAEW,QAAQ,EAAEP,YAAY,EAAE3E,KAAK,EAAEQ,QAAQ,EAAEgF,oBAAoB,GAAGpB,oCAAoC,EAAE;IACvH,KAAK;IACL;IACA;IACAxE,QAAQ,CAAC,MAAM;MACX,MAAM6F,WAAW,GAAG,IAAI,CAACL,KAAK,CAAC,CAAC,CAACM,MAAM,GAAG,CAAC;MAC3C,IAAI,CAACD,WAAW,EAAE;QACd,OAAOd,YAAY;MACvB;MACA;MACA,IAAI,IAAI,CAACS,KAAK,CAAC,CAAC,CAACL,MAAM,KAAK,SAAS,IAAI,IAAI,CAACY,KAAK,CAAC,CAAC,EAAE;QACnD,OAAOhB,YAAY;MACvB;MACA,IAAI,CAACiB,UAAU,CAACH,WAAW,CAAC,EAAE;QAC1B,IAAID,oBAAoB,EAAE;UACtB,MAAM,IAAIK,kBAAkB,CAAC,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC;QAC9C,CAAC,MACI;UACD,OAAOhB,YAAY;QACvB;MACJ;MACA,OAAOc,WAAW,CAACvG,KAAK;IAC5B,CAAC,EAAE;MAAEc;IAAM,CAAC,CAAC,CAAC;IACd,IAAI,CAACkF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAClF,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACqF,UAAU,GAAG5B,YAAY,CAAC;MAC3BG,MAAM,EAAEW,OAAO;MACf1E,WAAW,EAAG0E,OAAO,KAAM;QAAEA,OAAO;QAAEuB,MAAM,EAAE;MAAE,CAAC;IACrD,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACV,KAAK,GAAG3B,YAAY,CAAC;MACtB;MACAG,MAAM,EAAE,IAAI,CAACyB,UAAU;MACvB;MACAxF,WAAW,EAAEA,CAACwF,UAAU,EAAEU,QAAQ,KAAK;QACnC,MAAMhB,MAAM,GAAGM,UAAU,CAACd,OAAO,KAAKxF,SAAS,GAAG,MAAM,GAAG,SAAS;QACpE,IAAI,CAACgH,QAAQ,EAAE;UACX,OAAO;YACHV,UAAU;YACVN,MAAM;YACNiB,cAAc,EAAE,MAAM;YACtBN,MAAM,EAAE3G;UACZ,CAAC;QACL,CAAC,MACI;UACD,OAAO;YACHsG,UAAU;YACVN,MAAM;YACNiB,cAAc,EAAEC,oBAAoB,CAACF,QAAQ,CAAC7G,KAAK,CAAC;YACpD;YACAwG,MAAM,EAAEK,QAAQ,CAAC7G,KAAK,CAACmG,UAAU,CAACd,OAAO,KAAKc,UAAU,CAACd,OAAO,GAC1DwB,QAAQ,CAAC7G,KAAK,CAACwG,MAAM,GACrB3G;UACV,CAAC;QACL;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACoC,SAAS,GAAGb,MAAM,CAAC,IAAI,CAAC4F,UAAU,CAAC/B,IAAI,CAAC,IAAI,CAAC,EAAE;MAChD3D,QAAQ;MACRE,aAAa,EAAE;IACnB,CAAC,CAAC;IACF,IAAI,CAACyE,YAAY,GAAG3E,QAAQ,CAACG,GAAG,CAAC3D,YAAY,CAAC;IAC9C;IACAwD,QAAQ,CAACG,GAAG,CAAC3E,UAAU,CAAC,CAACuC,SAAS,CAAC,MAAM,IAAI,CAAC8B,OAAO,CAAC,CAAC,CAAC;EAC5D;EACA0E,MAAM,GAAGnF,QAAQ,CAAC,MAAMqG,oBAAoB,CAAC,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3DO,KAAK,GAAG/F,QAAQ,CAAC,MAAM;IACnB,MAAM8F,MAAM,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC,CAACM,MAAM,GAAG,CAAC;IACtC,OAAOA,MAAM,IAAI,CAACE,UAAU,CAACF,MAAM,CAAC,GAAGA,MAAM,CAACC,KAAK,GAAG5G,SAAS;EACnE,CAAC,CAAC;EACF;AACJ;AACA;EACI+E,GAAGA,CAAC5E,KAAK,EAAE;IACP,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChB;IACJ;IACA,MAAMmF,OAAO,GAAGzF,SAAS,CAAC,IAAI,CAACuB,KAAK,CAAC;IACrC,MAAMkG,KAAK,GAAGzH,SAAS,CAAC,IAAI,CAACyH,KAAK,CAAC;IACnC,IAAIA,KAAK,CAACL,MAAM,KAAK,OAAO,KAAK,IAAI,CAAC/E,KAAK,GAAG,IAAI,CAACA,KAAK,CAACoD,OAAO,EAAElE,KAAK,CAAC,GAAGkE,OAAO,KAAKlE,KAAK,CAAC,EAAE;MAC3F;IACJ;IACA;IACA,IAAI,CAACkG,KAAK,CAACtB,GAAG,CAAC;MACXuB,UAAU,EAAED,KAAK,CAACC,UAAU;MAC5BN,MAAM,EAAE,OAAO;MACfiB,cAAc,EAAE,OAAO;MACvBN,MAAM,EAAEzI,MAAM,CAAC;QAAEiC;MAAM,CAAC;IAC5B,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACiH,mBAAmB,CAAC,CAAC;EAC9B;EACAL,MAAMA,CAAA,EAAG;IACL;IACA,MAAM;MAAEf;IAAO,CAAC,GAAGpH,SAAS,CAAC,IAAI,CAACyH,KAAK,CAAC;IACxC,IAAIL,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;MAC3C,OAAO,KAAK;IAChB;IACA;IACA,IAAI,CAACM,UAAU,CAACrB,MAAM,CAAC,CAAC;MAAEO,OAAO;MAAEuB;IAAO,CAAC,MAAM;MAAEvB,OAAO;MAAEuB,MAAM,EAAEA,MAAM,GAAG;IAAE,CAAC,CAAC,CAAC;IAClF,OAAO,IAAI;EACf;EACAzF,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACkD,SAAS,CAACd,OAAO,CAAC,CAAC;IACxB,IAAI,CAAC8F,mBAAmB,CAAC,CAAC;IAC1B;IACA,IAAI,CAACf,KAAK,CAACtB,GAAG,CAAC;MACXuB,UAAU,EAAE;QAAEd,OAAO,EAAExF,SAAS;QAAE+G,MAAM,EAAE;MAAE,CAAC;MAC7Cf,MAAM,EAAE,MAAM;MACdiB,cAAc,EAAE,MAAM;MACtBN,MAAM,EAAE3G;IACZ,CAAC,CAAC;EACN;EACMmH,UAAUA,CAAA,EAAG;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MACf,MAAMhB,UAAU,GAAGe,KAAI,CAACf,UAAU,CAAC,CAAC;MACpC;MACA;MACA,MAAM;QAAEN,MAAM,EAAEuB,aAAa;QAAEN;MAAe,CAAC,GAAGrI,SAAS,CAACyI,KAAI,CAAChB,KAAK,CAAC;MACvE,IAAIC,UAAU,CAACd,OAAO,KAAKxF,SAAS,EAAE;QAClC;QACA;MACJ,CAAC,MACI,IAAIuH,aAAa,KAAK,SAAS,EAAE;QAClC;QACA;MACJ;MACA;MACAF,KAAI,CAACD,mBAAmB,CAAC,CAAC;MAC1B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIZ,kBAAkB,GAAIa,KAAI,CAACb,kBAAkB,GAC7Ca,KAAI,CAACjB,YAAY,CAAC7B,GAAG,CAAC,CAAE;MAC5B,MAAM;QAAErG,MAAM,EAAEsJ;MAAY,CAAC,GAAIH,KAAI,CAACd,iBAAiB,GAAG,IAAIkB,eAAe,CAAC,CAAE;MAChF,IAAI;QACA;QACA;QACA;QACA,MAAMd,MAAM,SAAS/H,SAAS,CAAC,MAAM;UACjC,OAAOyI,KAAI,CAAClB,QAAQ,CAAC;YACjBV,MAAM,EAAEa,UAAU,CAACd,OAAO;YAC1B;YACAA,OAAO,EAAEc,UAAU,CAACd,OAAO;YAC3BgC,WAAW;YACXR,QAAQ,EAAE;cACNhB,MAAM,EAAEiB;YACZ;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACF;QACA;QACA,IAAIO,WAAW,CAACE,OAAO,IAAI9I,SAAS,CAACyI,KAAI,CAACf,UAAU,CAAC,KAAKA,UAAU,EAAE;UAClE;QACJ;QACAe,KAAI,CAAChB,KAAK,CAACtB,GAAG,CAAC;UACXuB,UAAU;UACVN,MAAM,EAAE,UAAU;UAClBiB,cAAc,EAAE,UAAU;UAC1BN;QACJ,CAAC,CAAC;MACN,CAAC,CACD,OAAOnG,GAAG,EAAE;QACR,IAAIgH,WAAW,CAACE,OAAO,IAAI9I,SAAS,CAACyI,KAAI,CAACf,UAAU,CAAC,KAAKA,UAAU,EAAE;UAClE;QACJ;QACAe,KAAI,CAAChB,KAAK,CAACtB,GAAG,CAAC;UACXuB,UAAU;UACVN,MAAM,EAAE,UAAU;UAClBiB,cAAc,EAAE,OAAO;UACvBN,MAAM,EAAEzI,MAAM,CAAC;YAAE0I,KAAK,EAAEe,wBAAwB,CAACnH,GAAG;UAAE,CAAC;QAC3D,CAAC,CAAC;MACN,CAAC,SACO;QACJ;QACAgG,kBAAkB,GAAG,CAAC;QACtBA,kBAAkB,GAAGxG,SAAS;MAClC;IAAC;EACL;EACAoH,mBAAmBA,CAAA,EAAG;IAClBxI,SAAS,CAAC,MAAM,IAAI,CAAC2H,iBAAiB,EAAEqB,KAAK,CAAC,CAAC,CAAC;IAChD,IAAI,CAACrB,iBAAiB,GAAGvG,SAAS;IAClC;IACA,IAAI,CAACwG,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACA,kBAAkB,GAAGxG,SAAS;EACvC;AACJ;AACA;AACA;AACA;AACA,SAAS6F,cAAcA,CAAC5E,KAAK,EAAE;EAC3B,OAAO,CAAC4G,CAAC,EAAEC,CAAC,KAAMD,CAAC,KAAK7H,SAAS,IAAI8H,CAAC,KAAK9H,SAAS,GAAG6H,CAAC,KAAKC,CAAC,GAAG7G,KAAK,CAAC4G,CAAC,EAAEC,CAAC,CAAE;AACjF;AACA,SAASnC,SAASA,CAAC5E,OAAO,EAAE;EACxB,IAAIgH,0BAA0B,CAAChH,OAAO,CAAC,EAAE;IACrC,OAAOA,OAAO,CAAC4F,MAAM;EACzB;EACA;IAAA,IAAAqB,IAAA,GAAAV,iBAAA,CAAO,WAAO7B,MAAM,EAAK;MACrB,IAAI;QACA,OAAOvH,MAAM,CAAC;UAAEiC,KAAK,QAAQY,OAAO,CAACkH,MAAM,CAACxC,MAAM;QAAE,CAAC,CAAC;MAC1D,CAAC,CACD,OAAOjF,GAAG,EAAE;QACR,OAAOtC,MAAM,CAAC;UAAE0I,KAAK,EAAEe,wBAAwB,CAACnH,GAAG;QAAE,CAAC,CAAC;MAC3D;IACJ,CAAC;IAAA,iBAAA0H,EAAA;MAAA,OAAAF,IAAA,CAAAG,KAAA,OAAAC,SAAA;IAAA;EAAA;AACL;AACA,SAASL,0BAA0BA,CAAChH,OAAO,EAAE;EACzC,OAAO,CAAC,CAACA,OAAO,CAAC4F,MAAM;AAC3B;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAACb,KAAK,EAAE;EACjC,QAAQA,KAAK,CAACL,MAAM;IAChB,KAAK,SAAS;MACV,OAAOK,KAAK,CAACC,UAAU,CAACS,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,WAAW;IAClE,KAAK,UAAU;MACX,OAAOF,UAAU,CAACR,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO;IAC5D;MACI,OAAON,KAAK,CAACL,MAAM;EAC3B;AACJ;AACA,SAASa,UAAUA,CAACR,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACO,KAAK,KAAK5G,SAAS;AACpC;AACA,SAAS2H,wBAAwBA,CAACf,KAAK,EAAE;EACrC,IAAIA,KAAK,YAAY5D,KAAK,EAAE;IACxB,OAAO4D,KAAK;EAChB;EACA,OAAO,IAAIyB,oBAAoB,CAACzB,KAAK,CAAC;AAC1C;AACA,MAAME,kBAAkB,SAAS9D,KAAK,CAAC;EACnCzD,WAAWA,CAACqH,KAAK,EAAE;IACf,KAAK,CAACjH,SAAS,GACT,0EAA0EiH,KAAK,CAAC0B,OAAO,EAAE,GACzF1B,KAAK,CAAC0B,OAAO,EAAE;MAAEC,KAAK,EAAE3B;IAAM,CAAC,CAAC;EAC1C;AACJ;AACA,MAAMyB,oBAAoB,SAASrF,KAAK,CAAC;EACrCzD,WAAWA,CAACqH,KAAK,EAAE;IACf,KAAK,CAACjH,SAAS,GACT,4DAA4D6I,MAAM,CAAC5B,KAAK,CAAC,mDAAmD,GAC5H4B,MAAM,CAAC5B,KAAK,CAAC,EAAE;MAAE2B,KAAK,EAAE3B;IAAM,CAAC,CAAC;EAC1C;AACJ;AAEA,SAAS3H,gBAAgB,EAAEyG,YAAY,EAAE7E,QAAQ,EAAEU,MAAM,EAAEoG,wBAAwB,EAAEjH,mBAAmB,EAAEgE,YAAY,EAAEY,QAAQ,EAAE1G,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}