{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class OAuthStateService {\n  constructor() {\n    this.callbackData = null;\n    this.isProcessing = false;\n    this.wasProcessedInSession = false;\n  }\n  setCallbackData(data) {\n    if (!this.callbackData || !this.callbackData.processed) {\n      this.callbackData = {\n        ...data\n      };\n      console.log('🔄 OAuth State Service - Callback data set:', this.callbackData);\n    }\n  }\n  getCallbackData() {\n    return this.callbackData;\n  }\n  markAsProcessed() {\n    if (this.callbackData) {\n      this.callbackData.processed = true;\n      this.wasProcessedInSession = true;\n      console.log('✅ OAuth State Service - Marked as processed');\n    }\n  }\n  clear() {\n    this.callbackData = null;\n    this.isProcessing = false;\n    // Don't clear wasProcessedInSession to remember we processed OAuth in this session\n    console.log('🧹 OAuth State Service - Cleared');\n  }\n  setProcessing(processing) {\n    this.isProcessing = processing;\n  }\n  getProcessing() {\n    return this.isProcessing;\n  }\n  isAlreadyProcessed() {\n    return this.callbackData?.processed === true || this.wasProcessedInSession;\n  }\n  resetSession() {\n    this.callbackData = null;\n    this.isProcessing = false;\n    this.wasProcessedInSession = false;\n    console.log('🔄 OAuth State Service - Session reset');\n  }\n  static #_ = this.ɵfac = function OAuthStateService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OAuthStateService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: OAuthStateService,\n    factory: OAuthStateService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["OAuthStateService", "constructor", "callbackData", "isProcessing", "wasProcessedInSession", "setCallbackData", "data", "processed", "console", "log", "getCallbackData", "markAsProcessed", "clear", "setProcessing", "processing", "getProcessing", "isAlreadyProcessed", "resetSession", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\services\\oauth-state.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\nexport interface OAuthCallbackData {\r\n  token?: string;\r\n  isNewUser?: boolean;\r\n  provider?: string;\r\n  error?: string;\r\n  processed?: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OAuthStateService {\r\n  private callbackData: OAuthCallbackData | null = null;\r\n  private isProcessing = false;\r\n  private wasProcessedInSession = false;\r\n\r\n  setCallbackData(data: OAuthCallbackData): void {\r\n    if (!this.callbackData || !this.callbackData.processed) {\r\n      this.callbackData = { ...data };\r\n      console.log('🔄 OAuth State Service - Callback data set:', this.callbackData);\r\n    }\r\n  }\r\n\r\n  getCallbackData(): OAuthCallbackData | null {\r\n    return this.callbackData;\r\n  }\r\n\r\n  markAsProcessed(): void {\r\n    if (this.callbackData) {\r\n      this.callbackData.processed = true;\r\n      this.wasProcessedInSession = true;\r\n      console.log('✅ OAuth State Service - Marked as processed');\r\n    }\r\n  }\r\n\r\n  clear(): void {\r\n    this.callbackData = null;\r\n    this.isProcessing = false;\r\n    // Don't clear wasProcessedInSession to remember we processed OAuth in this session\r\n    console.log('🧹 OAuth State Service - Cleared');\r\n  }\r\n\r\n  setProcessing(processing: boolean): void {\r\n    this.isProcessing = processing;\r\n  }\r\n\r\n  getProcessing(): boolean {\r\n    return this.isProcessing;\r\n  }\r\n\r\n  isAlreadyProcessed(): boolean {\r\n    return this.callbackData?.processed === true || this.wasProcessedInSession;\r\n  }\r\n\r\n  resetSession(): void {\r\n    this.callbackData = null;\r\n    this.isProcessing = false;\r\n    this.wasProcessedInSession = false;\r\n    console.log('🔄 OAuth State Service - Session reset');\r\n  }\r\n}\r\n"], "mappings": ";AAaA,OAAM,MAAOA,iBAAiB;EAH9BC,YAAA;IAIU,KAAAC,YAAY,GAA6B,IAAI;IAC7C,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,qBAAqB,GAAG,KAAK;;EAErCC,eAAeA,CAACC,IAAuB;IACrC,IAAI,CAAC,IAAI,CAACJ,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACK,SAAS,EAAE;MACtD,IAAI,CAACL,YAAY,GAAG;QAAE,GAAGI;MAAI,CAAE;MAC/BE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,IAAI,CAACP,YAAY,CAAC;IAC/E;EACF;EAEAQ,eAAeA,CAAA;IACb,OAAO,IAAI,CAACR,YAAY;EAC1B;EAEAS,eAAeA,CAAA;IACb,IAAI,IAAI,CAACT,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACK,SAAS,GAAG,IAAI;MAClC,IAAI,CAACH,qBAAqB,GAAG,IAAI;MACjCI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC5D;EACF;EAEAG,KAAKA,CAAA;IACH,IAAI,CAACV,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACAK,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEAI,aAAaA,CAACC,UAAmB;IAC/B,IAAI,CAACX,YAAY,GAAGW,UAAU;EAChC;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACZ,YAAY;EAC1B;EAEAa,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACd,YAAY,EAAEK,SAAS,KAAK,IAAI,IAAI,IAAI,CAACH,qBAAqB;EAC5E;EAEAa,YAAYA,CAAA;IACV,IAAI,CAACf,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClCI,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EACvD;EAAC,QAAAS,CAAA,G;qCAhDUlB,iBAAiB;EAAA;EAAA,QAAAmB,EAAA,G;WAAjBnB,iBAAiB;IAAAoB,OAAA,EAAjBpB,iBAAiB,CAAAqB,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}