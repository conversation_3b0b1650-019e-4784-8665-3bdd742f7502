"""Working CORS Middleware based on Masonite documentation"""

from masonite.middleware import Middleware
from masonite.request import Request
from masonite.response import Response


class CustomCorsMiddleware(Middleware):
    """Working CORS middleware that follows Masonite patterns"""

    def before(self, request: Request, response: Response):
        """Handle CORS headers before request processing"""

        # Get origin from request
        origin = request.header('Origin')

        # Define allowed origins
        allowed_origins = [
            'http://localhost:4200',  # Angular frontend
            'http://localhost:3000',  # Alternative frontend port
            'http://localhost:3001',  # Alternative frontend port
        ]

        # Set CORS headers for all requests
        if origin and origin in allowed_origins:
            response.header('Access-Control-Allow-Origin', origin)
        else:
            response.header('Access-Control-Allow-Origin', '*')  # Allow all for development

        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Max-Age', '86400')
        response.header('Access-Control-Expose-Headers', 'x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset')

        # Handle preflight OPTIONS requests
        if request.get_request_method().upper() == 'OPTIONS':
            # Return 200 status for preflight requests
            return response.json({'message': 'CORS preflight'}, status=200)

        return request

    def after(self, request: Request, response: Response):
        """Ensure CORS headers are set after request processing"""

        # Always ensure CORS headers are present in final response
        origin = request.header('Origin')

        if not response.get_header('Access-Control-Allow-Origin'):
            allowed_origins = [
                'http://localhost:4200',
                'http://localhost:3000',
                'http://localhost:3001',
            ]

            if origin and origin in allowed_origins:
                response.header('Access-Control-Allow-Origin', origin)
            else:
                response.header('Access-Control-Allow-Origin', '*')

            response.header('Access-Control-Allow-Credentials', 'true')
            response.header('Access-Control-Expose-Headers', 'x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset')

        return request
