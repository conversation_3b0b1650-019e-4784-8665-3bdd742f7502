{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nexport class ConfirmationDialogComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onConfirm() {\n    this.dialogRef.close(true);\n  }\n  onCancel() {\n    this.dialogRef.close(false);\n  }\n  static #_ = this.ɵfac = function ConfirmationDialogComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfirmationDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmationDialogComponent,\n    selectors: [[\"app-confirmation-dialog\"]],\n    decls: 10,\n    vars: 5,\n    consts: [[\"mat-dialog-content\", \"\"], [\"mat-dialog-title\", \"\"], [\"mat-dialog-actions\", \"\", \"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", 3, \"click\", \"color\"]],\n    template: function ConfirmationDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"p\");\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 2)(6, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_6_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_8_listener() {\n          return ctx.onConfirm();\n        });\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.data.title);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.data.message);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.data.cancelText, \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"color\", ctx.data.isDangerous ? \"warn\" : \"primary\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.data.confirmText, \" \");\n      }\n    },\n    dependencies: [CommonModule, MatDialogModule, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, MatButtonModule, i2.MatButton],\n    styles: [\".dialog-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.dialog-actions[_ngcontent-%COMP%] {\\n  gap: 8px;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9jb25maXJtYXRpb24tZGlhbG9nL2NvbmZpcm1hdGlvbi1kaWFsb2cuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi9Nb2R1bGFyJTIwYmFja2VuZCUyMHNlY3VyZSUyMHVzZXIlMjBzeXN0ZW0lMjBhbmQlMjBwYXltZW50X0NvZHkvZnJvbnRlbmQvc3JjL2FwcC9jb21wb25lbnRzL2NvbmZpcm1hdGlvbi1kaWFsb2cvY29uZmlybWF0aW9uLWRpYWxvZy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7QUNDRjs7QURFQTtFQUNFLFFBQUE7QUNDRjs7QURFQTtFQUNFLGdCQUFBO0FDQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuZGlhbG9nLWNvbnRlbnQge1xyXG4gIHBhZGRpbmc6IDIwcHg7XHJcbn1cclxuXHJcbi5kaWFsb2ctYWN0aW9ucyB7XHJcbiAgZ2FwOiA4cHg7XHJcbn1cclxuXHJcbmJ1dHRvbiB7XHJcbiAgbWFyZ2luLWxlZnQ6IDhweDtcclxufVxyXG4iLCIuZGlhbG9nLWNvbnRlbnQge1xuICBwYWRkaW5nOiAyMHB4O1xufVxuXG4uZGlhbG9nLWFjdGlvbnMge1xuICBnYXA6IDhweDtcbn1cblxuYnV0dG9uIHtcbiAgbWFyZ2luLWxlZnQ6IDhweDtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogModule", "MatButtonModule", "CommonModule", "ConfirmationDialogComponent", "constructor", "dialogRef", "data", "onConfirm", "close", "onCancel", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "_2", "selectors", "decls", "vars", "consts", "template", "ConfirmationDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ConfirmationDialogComponent_Template_button_click_6_listener", "ConfirmationDialogComponent_Template_button_click_8_listener", "ɵɵadvance", "ɵɵtextInterpolate", "title", "message", "ɵɵtextInterpolate1", "cancelText", "ɵɵproperty", "isDangerous", "confirmText", "MatDialogTitle", "MatDialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "MatButton", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\confirmation-dialog\\confirmation-dialog.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\confirmation-dialog\\confirmation-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface ConfirmationDialogData {\r\n  title: string;\r\n  message: string;\r\n  confirmText: string;\r\n  cancelText: string;\r\n  isDangerous?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-confirmation-dialog',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    MatDialogModule,\r\n    MatButtonModule\r\n  ],\r\n  templateUrl: './confirmation-dialog.component.html',\r\n  styleUrls: ['./confirmation-dialog.component.scss']\r\n})\r\nexport class ConfirmationDialogComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogData\r\n  ) {}\r\n\r\n  onConfirm(): void {\r\n    this.dialogRef.close(true);\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.dialogRef.close(false);\r\n  }\r\n}\r\n", "<div mat-dialog-content>\r\n  <h2 mat-dialog-title>{{ data.title }}</h2>\r\n  <p>{{ data.message }}</p>\r\n</div>\r\n\r\n<div mat-dialog-actions align=\"end\">\r\n  <button mat-button (click)=\"onCancel()\">\r\n    {{ data.cancelText }}\r\n  </button>\r\n  <button \r\n    mat-raised-button \r\n    [color]=\"data.isDangerous ? 'warn' : 'primary'\"\r\n    (click)=\"onConfirm()\">\r\n    {{ data.confirmText }}\r\n  </button>\r\n</div>\r\n"], "mappings": "AACA,SAAuBA,eAAe,EAAEC,eAAe,QAAQ,0BAA0B;AACzF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;;;;AAqB9C,OAAM,MAAOC,2BAA2B;EACtCC,YACSC,SAAoD,EAC3BC,IAA4B;IADrD,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;EACnC;EAEHC,SAASA,CAAA;IACP,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC,IAAI,CAAC;EAC5B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACJ,SAAS,CAACG,KAAK,CAAC,KAAK,CAAC;EAC7B;EAAC,QAAAE,CAAA,G;qCAZUP,2BAA2B,EAAAQ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAG5Bb,eAAe;EAAA;EAAA,QAAAgB,EAAA,G;UAHdZ,2BAA2B;IAAAa,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvBtCX,EADF,CAAAa,cAAA,aAAwB,YACD;QAAAb,EAAA,CAAAc,MAAA,GAAgB;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAC1Cf,EAAA,CAAAa,cAAA,QAAG;QAAAb,EAAA,CAAAc,MAAA,GAAkB;QACvBd,EADuB,CAAAe,YAAA,EAAI,EACrB;QAGJf,EADF,CAAAa,cAAA,aAAoC,gBACM;QAArBb,EAAA,CAAAgB,UAAA,mBAAAC,6DAAA;UAAA,OAASL,GAAA,CAAAd,QAAA,EAAU;QAAA,EAAC;QACrCE,EAAA,CAAAc,MAAA,GACF;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACTf,EAAA,CAAAa,cAAA,gBAGwB;QAAtBb,EAAA,CAAAgB,UAAA,mBAAAE,6DAAA;UAAA,OAASN,GAAA,CAAAhB,SAAA,EAAW;QAAA,EAAC;QACrBI,EAAA,CAAAc,MAAA,GACF;QACFd,EADE,CAAAe,YAAA,EAAS,EACL;;;QAdiBf,EAAA,CAAAmB,SAAA,GAAgB;QAAhBnB,EAAA,CAAAoB,iBAAA,CAAAR,GAAA,CAAAjB,IAAA,CAAA0B,KAAA,CAAgB;QAClCrB,EAAA,CAAAmB,SAAA,GAAkB;QAAlBnB,EAAA,CAAAoB,iBAAA,CAAAR,GAAA,CAAAjB,IAAA,CAAA2B,OAAA,CAAkB;QAKnBtB,EAAA,CAAAmB,SAAA,GACF;QADEnB,EAAA,CAAAuB,kBAAA,MAAAX,GAAA,CAAAjB,IAAA,CAAA6B,UAAA,MACF;QAGExB,EAAA,CAAAmB,SAAA,EAA+C;QAA/CnB,EAAA,CAAAyB,UAAA,UAAAb,GAAA,CAAAjB,IAAA,CAAA+B,WAAA,sBAA+C;QAE/C1B,EAAA,CAAAmB,SAAA,EACF;QADEnB,EAAA,CAAAuB,kBAAA,MAAAX,GAAA,CAAAjB,IAAA,CAAAgC,WAAA,MACF;;;mBDGEpC,YAAY,EACZF,eAAe,EAAAa,EAAA,CAAA0B,cAAA,EAAA1B,EAAA,CAAA2B,gBAAA,EAAA3B,EAAA,CAAA4B,gBAAA,EACfxC,eAAe,EAAAyC,EAAA,CAAAC,SAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}