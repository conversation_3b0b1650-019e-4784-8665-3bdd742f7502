{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/account-deletion.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nfunction AccountDeletionComponent_div_8_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid number between 1 and 365 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_8_div_47_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3);\n  }\n}\nfunction AccountDeletionComponent_div_8_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h4\");\n    i0.ɵɵtext(2, \"Data Preservation Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, AccountDeletionComponent_div_8_div_47_li_4_Template, 2, 1, \"li\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 26)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Preserved data will be kept for \");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.preservedDataSummary);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.retentionPeriodText);\n  }\n}\nfunction AccountDeletionComponent_div_8_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" You must confirm before proceeding \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_8_mat_spinner_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction AccountDeletionComponent_div_8_mat_icon_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"delete_forever\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"mat-icon\", 2);\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Warning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Account deletion is permanent and cannot be undone immediately. Please carefully review your preferences below. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"form\", 6);\n    i0.ɵɵlistener(\"ngSubmit\", function AccountDeletionComponent_div_8_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestDeletion());\n    });\n    i0.ɵɵelementStart(9, \"h3\");\n    i0.ɵɵtext(10, \"Data Preservation Preferences\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 7);\n    i0.ɵɵtext(12, \" Choose which data you want to preserve for potential restoration: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"mat-checkbox\", 9)(15, \"strong\");\n    i0.ɵɵtext(16, \"Preserve Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 10);\n    i0.ɵɵtext(18, \"Keep payment methods and billing information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-checkbox\", 11)(20, \"strong\");\n    i0.ɵɵtext(21, \"Preserve Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 10);\n    i0.ɵɵtext(23, \"Keep records of past transactions and orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"mat-checkbox\", 12)(25, \"strong\");\n    i0.ɵɵtext(26, \"Preserve Profile Backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 10);\n    i0.ɵɵtext(28, \"Keep a backup of your profile information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-checkbox\", 13)(30, \"strong\");\n    i0.ɵɵtext(31, \"Preserve Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 10);\n    i0.ɵɵtext(33, \"Keep login history and security events\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"mat-form-field\", 14)(35, \"mat-label\");\n    i0.ɵɵtext(36, \"Data Retention Period (days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 15);\n    i0.ɵɵelementStart(38, \"mat-hint\");\n    i0.ɵɵtext(39, \"How long to keep your preserved data (1-365 days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, AccountDeletionComponent_div_8_mat_error_40_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-form-field\", 14)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Reason for Deletion (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"textarea\", 17);\n    i0.ɵɵelementStart(45, \"mat-hint\");\n    i0.ɵɵtext(46, \"This helps us improve our service\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(47, AccountDeletionComponent_div_8_div_47_Template, 11, 2, \"div\", 18);\n    i0.ɵɵelementStart(48, \"mat-checkbox\", 19)(49, \"strong\");\n    i0.ɵɵtext(50, \"I understand that this action cannot be undone immediately\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(51, AccountDeletionComponent_div_8_mat_error_51_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementStart(52, \"div\", 20)(53, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_8_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToSettings());\n    });\n    i0.ɵɵelementStart(54, \"mat-icon\");\n    i0.ɵɵtext(55, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Back to Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 22);\n    i0.ɵɵtemplate(58, AccountDeletionComponent_div_8_mat_spinner_58_Template, 1, 0, \"mat-spinner\", 23)(59, AccountDeletionComponent_div_8_mat_icon_59_Template, 2, 0, \"mat-icon\", 16);\n    i0.ɵɵtext(60, \" Request Account Deletion \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.deletionForm);\n    i0.ɵɵadvance(32);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.deletionForm.get(\"customRetentionPeriod\")) == null ? null : tmp_2_0.invalid);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.preservedDataSummary.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.deletionForm.get(\"confirmDeletion\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r1.deletionForm.get(\"confirmDeletion\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.deletionForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n  }\n}\nfunction AccountDeletionComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 28)(2, \"mat-icon\", 29);\n    i0.ɵɵtext(3, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Check Your Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \" We've sent a confirmation email with a link to complete your account deletion. Please check your inbox and follow the instructions. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 30)(9, \"strong\");\n    i0.ɵɵtext(10, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" The confirmation link will expire in 24 hours. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_9_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.step = \"preferences\");\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Change Preferences \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_9_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkExistingDeletionStatus());\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Refresh Status \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountDeletionComponent_div_10_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"strong\");\n    i0.ɵɵtext(2, \"Data Expiry:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, ctx_r1.currentStatus.deletionRecord.dataExpiryDate, \"medium\"), \" \");\n  }\n}\nfunction AccountDeletionComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"h4\");\n    i0.ɵɵtext(2, \"Request Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"strong\");\n    i0.ɵɵtext(5, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"strong\");\n    i0.ɵɵtext(9, \"Requested:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 39)(13, \"strong\");\n    i0.ɵɵtext(14, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 40);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, AccountDeletionComponent_div_10_div_8_div_18_Template, 5, 4, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStatus.deletionRecord.email, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 4, ctx_r1.currentStatus.deletionRecord.deletionRequestedAt, \"medium\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 7, ctx_r1.currentStatus.deletionRecord.deletionStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStatus.deletionRecord.dataExpiryDate);\n  }\n}\nfunction AccountDeletionComponent_div_10_mat_spinner_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction AccountDeletionComponent_div_10_mat_icon_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 33)(2, \"mat-icon\", 34);\n    i0.ɵɵtext(3, \"pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Deletion Request Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Your account deletion request is currently pending confirmation.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AccountDeletionComponent_div_10_div_8_Template, 19, 9, \"div\", 35);\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_10_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkExistingDeletionStatus());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Refresh Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_10_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelDeletion());\n    });\n    i0.ɵɵtemplate(15, AccountDeletionComponent_div_10_mat_spinner_15_Template, 1, 0, \"mat-spinner\", 23)(16, AccountDeletionComponent_div_10_mat_icon_16_Template, 2, 0, \"mat-icon\", 16);\n    i0.ɵɵtext(17, \" Cancel Deletion Request \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStatus.deletionRecord);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n  }\n}\nexport class AccountDeletionComponent {\n  constructor(fb, accountDeletionService, authService, router, snackBar, dialog) {\n    this.fb = fb;\n    this.accountDeletionService = accountDeletionService;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.currentStatus = null;\n    this.isLoading = false;\n    this.step = 'preferences';\n    this.deletionForm = this.fb.group({\n      preservePaymentData: [true],\n      preserveTransactionHistory: [true],\n      preserveProfileData: [false],\n      preserveSecurityLogs: [false],\n      customRetentionPeriod: [30, [Validators.min(1), Validators.max(365)]],\n      reason: ['', Validators.maxLength(500)],\n      confirmDeletion: [false, Validators.requiredTrue]\n    });\n  }\n  ngOnInit() {\n    this.checkExistingDeletionStatus();\n  }\n  checkExistingDeletionStatus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const status = yield _this.accountDeletionService.getDeletionStatus().toPromise();\n        if (status && status.hasPendingDeletion && status.deletionRecord) {\n          _this.currentStatus = status;\n          _this.step = 'status';\n        }\n      } catch (error) {\n        console.error('Error checking deletion status:', error);\n      }\n    })();\n  }\n  requestDeletion() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.deletionForm.invalid) {\n        _this2.markFormGroupTouched();\n        return;\n      }\n      const dialogRef = _this2.dialog.open(ConfirmationDialogComponent, {\n        width: '400px',\n        data: {\n          title: 'Confirm Account Deletion',\n          message: 'Are you absolutely sure you want to delete your account? This action cannot be undone immediately.',\n          confirmText: 'Yes, Delete My Account',\n          cancelText: 'Cancel',\n          isDangerous: true\n        }\n      });\n      const confirmed = yield dialogRef.afterClosed().toPromise();\n      if (!confirmed) return;\n      _this2.isLoading = true;\n      try {\n        const preferences = {\n          preservePaymentData: _this2.deletionForm.value.preservePaymentData,\n          preserveTransactionHistory: _this2.deletionForm.value.preserveTransactionHistory,\n          preserveProfileData: _this2.deletionForm.value.preserveProfileData,\n          preserveSecurityLogs: _this2.deletionForm.value.preserveSecurityLogs,\n          customRetentionPeriod: _this2.deletionForm.value.customRetentionPeriod,\n          reason: _this2.deletionForm.value.reason?.trim() || undefined\n        };\n        const result = yield _this2.accountDeletionService.requestAccountDeletion(preferences).toPromise();\n        _this2.snackBar.open('Account deletion requested! Please check your email for confirmation instructions.', 'Close', {\n          duration: 8000,\n          panelClass: ['snack-bar-warning']\n        });\n        _this2.step = 'confirmation';\n        yield _this2.checkExistingDeletionStatus();\n      } catch (error) {\n        console.error('Error requesting deletion:', error);\n        _this2.snackBar.open(error.message || 'Failed to request account deletion. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-error']\n        });\n      } finally {\n        _this2.isLoading = false;\n      }\n    })();\n  }\n  cancelDeletion() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.currentStatus?.deletionRecord?.id) return;\n      const dialogRef = _this3.dialog.open(ConfirmationDialogComponent, {\n        width: '400px',\n        data: {\n          title: 'Cancel Account Deletion',\n          message: 'Are you sure you want to cancel the account deletion request?',\n          confirmText: 'Yes, Cancel Deletion',\n          cancelText: 'Keep Deletion Request'\n        }\n      });\n      const confirmed = yield dialogRef.afterClosed().toPromise();\n      if (!confirmed) return;\n      _this3.isLoading = true;\n      try {\n        yield _this3.accountDeletionService.cancelDeletion().toPromise();\n        _this3.snackBar.open('Account deletion request has been cancelled.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-success']\n        });\n        _this3.currentStatus = null;\n        _this3.step = 'preferences';\n        _this3.deletionForm.reset();\n      } catch (error) {\n        console.error('Error cancelling deletion:', error);\n        _this3.snackBar.open(error.message || 'Failed to cancel account deletion. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-error']\n        });\n      } finally {\n        _this3.isLoading = false;\n      }\n    })();\n  }\n  goToSettings() {\n    this.router.navigate(['/dashboard/settings']);\n  }\n  markFormGroupTouched() {\n    Object.keys(this.deletionForm.controls).forEach(key => {\n      this.deletionForm.get(key)?.markAsTouched();\n    });\n  }\n  get preservedDataSummary() {\n    const preferences = this.deletionForm.value;\n    const preserved = [];\n    if (preferences.preservePaymentData) preserved.push('Payment data');\n    if (preferences.preserveTransactionHistory) preserved.push('Transaction history');\n    if (preferences.preserveProfileData) preserved.push('Profile data');\n    if (preferences.preserveSecurityLogs) preserved.push('Security logs');\n    return preserved;\n  }\n  get retentionPeriodText() {\n    const days = this.deletionForm.value.customRetentionPeriod || 30;\n    if (days === 1) return '1 day';\n    if (days < 30) return `${days} days`;\n    if (days === 30) return '1 month';\n    if (days < 365) return `${Math.round(days / 30)} months`;\n    return '1 year';\n  }\n  static #_ = this.ɵfac = function AccountDeletionComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AccountDeletionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountDeletionService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.MatDialog));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AccountDeletionComponent,\n    selectors: [[\"app-account-deletion\"]],\n    decls: 11,\n    vars: 3,\n    consts: [[1, \"account-deletion-container\"], [1, \"deletion-card\"], [\"color\", \"warn\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"warning-message\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"section-description\"], [1, \"preferences-section\"], [\"formControlName\", \"preservePaymentData\"], [1, \"preference-description\"], [\"formControlName\", \"preserveTransactionHistory\"], [\"formControlName\", \"preserveProfileData\"], [\"formControlName\", \"preserveSecurityLogs\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"customRetentionPeriod\", \"min\", \"1\", \"max\", \"365\", \"placeholder\", \"30\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"reason\", \"rows\", \"3\", \"placeholder\", \"Tell us why you're deleting your account...\"], [\"class\", \"summary-section\", 4, \"ngIf\"], [\"formControlName\", \"confirmDeletion\", 1, \"confirmation-checkbox\"], [1, \"actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"summary-section\"], [4, \"ngFor\", \"ngForOf\"], [1, \"retention-info\"], [\"diameter\", \"20\"], [1, \"confirmation-message\"], [\"color\", \"primary\", 1, \"large-icon\"], [1, \"email-note\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"status-message\"], [\"color\", \"warn\", 1, \"large-icon\"], [\"class\", \"status-details\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [1, \"status-details\"], [1, \"detail-item\"], [1, \"status-badge\"], [\"class\", \"detail-item\", 4, \"ngIf\"]],\n    template: function AccountDeletionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n        i0.ɵɵtext(5, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Account Deletion \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"mat-card-content\");\n        i0.ɵɵtemplate(8, AccountDeletionComponent_div_8_Template, 61, 7, \"div\", 3)(9, AccountDeletionComponent_div_9_Template, 21, 0, \"div\", 3)(10, AccountDeletionComponent_div_10_Template, 18, 4, \"div\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.step === \"preferences\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.step === \"confirmation\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.step === \"status\" && ctx.currentStatus);\n      }\n    },\n    dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.TitleCasePipe, i7.DatePipe, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, MatIconModule, i9.MatIcon, MatButtonModule, i10.MatButton, MatCheckboxModule, i11.MatCheckbox, MatFormFieldModule, i12.MatFormField, i12.MatLabel, i12.MatHint, i12.MatError, MatInputModule, i13.MatInput, MatProgressSpinnerModule, i14.MatProgressSpinner],\n    styles: [\".account-deletion-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.deletion-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.mat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.warning-message[_ngcontent-%COMP%], \\n.confirmation-message[_ngcontent-%COMP%], \\n.status-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 15px;\\n  padding: 15px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n.warning-message[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  color: #856404;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%], \\n.status-message[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border: 1px solid #bbdefb;\\n  color: #1565c0;\\n  flex-direction: column;\\n  text-align: center;\\n}\\n\\n.large-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 15px;\\n}\\n\\n.preferences-section[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.preference-description[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #888;\\n  margin-top: 4px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 15px;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  margin: 20px 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #495057;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n  padding-left: 20px;\\n}\\n\\n.retention-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  margin-top: 10px;\\n  font-size: 14px;\\n  color: #6c757d;\\n}\\n\\n.confirmation-checkbox[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.status-details[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 15px;\\n  border-radius: 8px;\\n  margin: 20px 0;\\n}\\n\\n.status-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #495057;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #212529;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 20px;\\n  gap: 15px;\\n}\\n\\n.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.email-note[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c757d;\\n  margin-top: 10px;\\n}\\n\\n@media (max-width: 768px) {\\n  .account-deletion-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .warning-message[_ngcontent-%COMP%], \\n   .confirmation-message[_ngcontent-%COMP%], \\n   .status-message[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "ReactiveFormsModule", "CommonModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatCheckboxModule", "MatFormFieldModule", "MatInputModule", "MatProgressSpinnerModule", "ConfirmationDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r3", "ɵɵtemplate", "AccountDeletionComponent_div_8_div_47_li_4_Template", "ɵɵproperty", "ctx_r1", "preservedDataSummary", "retentionPeriodText", "ɵɵelement", "ɵɵlistener", "AccountDeletionComponent_div_8_Template_form_ngSubmit_8_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "requestDeletion", "AccountDeletionComponent_div_8_mat_error_40_Template", "AccountDeletionComponent_div_8_div_47_Template", "AccountDeletionComponent_div_8_mat_error_51_Template", "AccountDeletionComponent_div_8_Template_button_click_53_listener", "goToSettings", "AccountDeletionComponent_div_8_mat_spinner_58_Template", "AccountDeletionComponent_div_8_mat_icon_59_Template", "deletionForm", "tmp_2_0", "get", "invalid", "length", "tmp_4_0", "touched", "isLoading", "AccountDeletionComponent_div_9_Template_button_click_13_listener", "_r4", "step", "AccountDeletionComponent_div_9_Template_button_click_17_listener", "checkExistingDeletionStatus", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "currentStatus", "deletionRecord", "dataExpiryDate", "AccountDeletionComponent_div_10_div_8_div_18_Template", "email", "deletionRequestedAt", "ɵɵpipeBind1", "deletionStatus", "AccountDeletionComponent_div_10_div_8_Template", "AccountDeletionComponent_div_10_Template_button_click_10_listener", "_r5", "AccountDeletionComponent_div_10_Template_button_click_14_listener", "cancelDeletion", "AccountDeletionComponent_div_10_mat_spinner_15_Template", "AccountDeletionComponent_div_10_mat_icon_16_Template", "AccountDeletionComponent", "constructor", "fb", "accountDeletionService", "authService", "router", "snackBar", "dialog", "group", "preservePaymentData", "preserveTransactionHistory", "preserveProfileData", "preserveSecurityLogs", "customRetentionPeriod", "min", "max", "reason", "max<PERSON><PERSON><PERSON>", "confirmDeletion", "requiredTrue", "ngOnInit", "_this", "_asyncToGenerator", "status", "getDeletionStatus", "to<PERSON>romise", "hasPendingDeletion", "error", "console", "_this2", "markFormGroupTouched", "dialogRef", "open", "width", "data", "title", "message", "confirmText", "cancelText", "isDangerous", "confirmed", "afterClosed", "preferences", "value", "trim", "undefined", "result", "requestAccountDeletion", "duration", "panelClass", "_this3", "id", "reset", "navigate", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "preserved", "push", "days", "Math", "round", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AccountDeletionService", "i3", "AuthService", "i4", "Router", "i5", "MatSnackBar", "i6", "MatDialog", "_2", "selectors", "decls", "vars", "consts", "template", "AccountDeletionComponent_Template", "rf", "ctx", "AccountDeletionComponent_div_8_Template", "AccountDeletionComponent_div_9_Template", "AccountDeletionComponent_div_10_Template", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "DatePipe", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MinValidator", "MaxValidator", "FormGroupDirective", "FormControlName", "i8", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i9", "MatIcon", "i10", "MatButton", "i11", "MatCheckbox", "i12", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "i13", "MatInput", "i14", "MatProgressSpinner", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\account-deletion\\account-deletion.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\account-deletion\\account-deletion.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { AccountDeletionService } from '../../services/account-deletion.service';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { DeletionPreferences, DeletionStatus } from '../../models/account-deletion.model';\r\nimport { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';\r\n\r\n@Component({\r\n  selector: 'app-account-deletion',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatCardModule,\r\n    MatIconModule,\r\n    MatButtonModule,\r\n    MatCheckboxModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatProgressSpinnerModule,\r\n    DatePipe,\r\n    TitleCasePipe\r\n  ],\r\n  templateUrl: './account-deletion.component.html',\r\n  styleUrls: ['./account-deletion.component.scss']\r\n})\r\nexport class AccountDeletionComponent implements OnInit {\r\n  deletionForm: FormGroup;\r\n  currentStatus: DeletionStatus | null = null;\r\n  isLoading = false;\r\n  step: 'preferences' | 'confirmation' | 'status' = 'preferences';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private accountDeletionService: AccountDeletionService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar,\r\n    private dialog: MatDialog\r\n  ) {\r\n    this.deletionForm = this.fb.group({\r\n      preservePaymentData: [true],\r\n      preserveTransactionHistory: [true],\r\n      preserveProfileData: [false],\r\n      preserveSecurityLogs: [false],\r\n      customRetentionPeriod: [30, [Validators.min(1), Validators.max(365)]],\r\n      reason: ['', Validators.maxLength(500)],\r\n      confirmDeletion: [false, Validators.requiredTrue]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.checkExistingDeletionStatus();\r\n  }\r\n  async checkExistingDeletionStatus(): Promise<void> {\r\n    try {\r\n      const status = await this.accountDeletionService.getDeletionStatus().toPromise();\r\n      if (status && status.hasPendingDeletion && status.deletionRecord) {\r\n        this.currentStatus = status;\r\n        this.step = 'status';\r\n      }\r\n    } catch (error) {\r\n      console.error('Error checking deletion status:', error);\r\n    }\r\n  }\r\n\r\n  async requestDeletion(): Promise<void> {\r\n    if (this.deletionForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {\r\n      width: '400px',\r\n      data: {\r\n        title: 'Confirm Account Deletion',\r\n        message: 'Are you absolutely sure you want to delete your account? This action cannot be undone immediately.',\r\n        confirmText: 'Yes, Delete My Account',\r\n        cancelText: 'Cancel',\r\n        isDangerous: true\r\n      }\r\n    });\r\n\r\n    const confirmed = await dialogRef.afterClosed().toPromise();\r\n    if (!confirmed) return;\r\n\r\n    this.isLoading = true;\r\n\r\n    try {\r\n      const preferences: DeletionPreferences = {\r\n        preservePaymentData: this.deletionForm.value.preservePaymentData,\r\n        preserveTransactionHistory: this.deletionForm.value.preserveTransactionHistory,\r\n        preserveProfileData: this.deletionForm.value.preserveProfileData,\r\n        preserveSecurityLogs: this.deletionForm.value.preserveSecurityLogs,\r\n        customRetentionPeriod: this.deletionForm.value.customRetentionPeriod,\r\n        reason: this.deletionForm.value.reason?.trim() || undefined\r\n      };\r\n\r\n      const result = await this.accountDeletionService.requestAccountDeletion(preferences).toPromise();\r\n      \r\n      this.snackBar.open(\r\n        'Account deletion requested! Please check your email for confirmation instructions.',\r\n        'Close',\r\n        { duration: 8000, panelClass: ['snack-bar-warning'] }\r\n      );\r\n\r\n      this.step = 'confirmation';\r\n      await this.checkExistingDeletionStatus();\r\n\r\n    } catch (error: any) {\r\n      console.error('Error requesting deletion:', error);\r\n      this.snackBar.open(\r\n        error.message || 'Failed to request account deletion. Please try again.',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['snack-bar-error'] }\r\n      );\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  async cancelDeletion(): Promise<void> {\r\n    if (!this.currentStatus?.deletionRecord?.id) return;\r\n\r\n    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {\r\n      width: '400px',\r\n      data: {\r\n        title: 'Cancel Account Deletion',\r\n        message: 'Are you sure you want to cancel the account deletion request?',\r\n        confirmText: 'Yes, Cancel Deletion',\r\n        cancelText: 'Keep Deletion Request'\r\n      }\r\n    });\r\n\r\n    const confirmed = await dialogRef.afterClosed().toPromise();\r\n    if (!confirmed) return;\r\n\r\n    this.isLoading = true;\r\n\r\n    try {\r\n      await this.accountDeletionService.cancelDeletion().toPromise();\r\n      \r\n      this.snackBar.open(\r\n        'Account deletion request has been cancelled.',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['snack-bar-success'] }\r\n      );\r\n\r\n      this.currentStatus = null;\r\n      this.step = 'preferences';\r\n      this.deletionForm.reset();\r\n\r\n    } catch (error: any) {\r\n      console.error('Error cancelling deletion:', error);\r\n      this.snackBar.open(\r\n        error.message || 'Failed to cancel account deletion. Please try again.',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['snack-bar-error'] }\r\n      );\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  goToSettings(): void {\r\n    this.router.navigate(['/dashboard/settings']);\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.deletionForm.controls).forEach(key => {\r\n      this.deletionForm.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  get preservedDataSummary(): string[] {\r\n    const preferences = this.deletionForm.value;\r\n    const preserved: string[] = [];\r\n    \r\n    if (preferences.preservePaymentData) preserved.push('Payment data');\r\n    if (preferences.preserveTransactionHistory) preserved.push('Transaction history');\r\n    if (preferences.preserveProfileData) preserved.push('Profile data');\r\n    if (preferences.preserveSecurityLogs) preserved.push('Security logs');\r\n    \r\n    return preserved;\r\n  }\r\n\r\n  get retentionPeriodText(): string {\r\n    const days = this.deletionForm.value.customRetentionPeriod || 30;\r\n    if (days === 1) return '1 day';\r\n    if (days < 30) return `${days} days`;\r\n    if (days === 30) return '1 month';\r\n    if (days < 365) return `${Math.round(days / 30)} months`;\r\n    return '1 year';\r\n  }\r\n}\r\n", "<div class=\"account-deletion-container\">\r\n  <mat-card class=\"deletion-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon color=\"warn\">warning</mat-icon>\r\n        Account Deletion\r\n      </mat-card-title>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <!-- Step 1: Deletion Preferences -->\r\n      <div *ngIf=\"step === 'preferences'\" class=\"step-content\">\r\n        <div class=\"warning-message\">\r\n          <mat-icon color=\"warn\">info</mat-icon>\r\n          <p>\r\n            <strong>Warning:</strong> Account deletion is permanent and cannot be undone immediately. \r\n            Please carefully review your preferences below.\r\n          </p>\r\n        </div>\r\n\r\n        <form [formGroup]=\"deletionForm\" (ngSubmit)=\"requestDeletion()\">\r\n          <h3>Data Preservation Preferences</h3>\r\n          <p class=\"section-description\">\r\n            Choose which data you want to preserve for potential restoration:\r\n          </p>\r\n\r\n          <div class=\"preferences-section\">\r\n            <mat-checkbox formControlName=\"preservePaymentData\">\r\n              <strong>Preserve Payment Data</strong>\r\n              <div class=\"preference-description\">Keep payment methods and billing information</div>\r\n            </mat-checkbox>\r\n\r\n            <mat-checkbox formControlName=\"preserveTransactionHistory\">\r\n              <strong>Preserve Transaction History</strong>\r\n              <div class=\"preference-description\">Keep records of past transactions and orders</div>\r\n            </mat-checkbox>\r\n\r\n            <mat-checkbox formControlName=\"preserveProfileData\">\r\n              <strong>Preserve Profile Backup</strong>\r\n              <div class=\"preference-description\">Keep a backup of your profile information</div>\r\n            </mat-checkbox>\r\n\r\n            <mat-checkbox formControlName=\"preserveSecurityLogs\">\r\n              <strong>Preserve Security Logs</strong>\r\n              <div class=\"preference-description\">Keep login history and security events</div>\r\n            </mat-checkbox>\r\n          </div>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Data Retention Period (days)</mat-label>\r\n            <input matInput type=\"number\" formControlName=\"customRetentionPeriod\" \r\n                   min=\"1\" max=\"365\" placeholder=\"30\">\r\n            <mat-hint>How long to keep your preserved data (1-365 days)</mat-hint>\r\n            <mat-error *ngIf=\"deletionForm.get('customRetentionPeriod')?.invalid\">\r\n              Please enter a valid number between 1 and 365\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Reason for Deletion (Optional)</mat-label>\r\n            <textarea matInput formControlName=\"reason\" rows=\"3\" \r\n                      placeholder=\"Tell us why you're deleting your account...\"></textarea>\r\n            <mat-hint>This helps us improve our service</mat-hint>\r\n          </mat-form-field>\r\n\r\n          <div class=\"summary-section\" *ngIf=\"preservedDataSummary.length > 0\">\r\n            <h4>Data Preservation Summary</h4>\r\n            <ul>\r\n              <li *ngFor=\"let item of preservedDataSummary\">{{ item }}</li>\r\n            </ul>\r\n            <p class=\"retention-info\">\r\n              <mat-icon>schedule</mat-icon>\r\n              Preserved data will be kept for <strong>{{ retentionPeriodText }}</strong>\r\n            </p>\r\n          </div>\r\n\r\n          <mat-checkbox formControlName=\"confirmDeletion\" class=\"confirmation-checkbox\">\r\n            <strong>I understand that this action cannot be undone immediately</strong>\r\n          </mat-checkbox>\r\n          <mat-error *ngIf=\"deletionForm.get('confirmDeletion')?.invalid && deletionForm.get('confirmDeletion')?.touched\">\r\n            You must confirm before proceeding\r\n          </mat-error>\r\n\r\n          <div class=\"actions\">\r\n            <button mat-button type=\"button\" (click)=\"goToSettings()\">\r\n              <mat-icon>arrow_back</mat-icon>\r\n              Back to Settings\r\n            </button>\r\n            <button mat-raised-button color=\"warn\" type=\"submit\" \r\n                    [disabled]=\"isLoading || deletionForm.invalid\">\r\n              <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\r\n              <mat-icon *ngIf=\"!isLoading\">delete_forever</mat-icon>\r\n              Request Account Deletion\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <!-- Step 2: Email Confirmation Required -->\r\n      <div *ngIf=\"step === 'confirmation'\" class=\"step-content\">\r\n        <div class=\"confirmation-message\">\r\n          <mat-icon color=\"primary\" class=\"large-icon\">mail</mat-icon>\r\n          <h3>Check Your Email</h3>\r\n          <p>\r\n            We've sent a confirmation email with a link to complete your account deletion. \r\n            Please check your inbox and follow the instructions.\r\n          </p>\r\n          <p class=\"email-note\">\r\n            <strong>Note:</strong> The confirmation link will expire in 24 hours.\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"actions\">\r\n          <button mat-button (click)=\"step = 'preferences'\">\r\n            <mat-icon>edit</mat-icon>\r\n            Change Preferences\r\n          </button>\r\n          <button mat-raised-button color=\"primary\" (click)=\"checkExistingDeletionStatus()\">\r\n            <mat-icon>refresh</mat-icon>\r\n            Refresh Status\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Step 3: Deletion Status -->\r\n      <div *ngIf=\"step === 'status' && currentStatus\" class=\"step-content\">\r\n        <div class=\"status-message\">\r\n          <mat-icon color=\"warn\" class=\"large-icon\">pending</mat-icon>\r\n          <h3>Deletion Request Pending</h3>\r\n          <p>Your account deletion request is currently pending confirmation.</p>\r\n        </div>\r\n\r\n        <div class=\"status-details\" *ngIf=\"currentStatus.deletionRecord\">\r\n          <h4>Request Details</h4>\r\n          <div class=\"detail-item\">\r\n            <strong>Email:</strong> {{ currentStatus.deletionRecord.email }}\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <strong>Requested:</strong> {{ currentStatus.deletionRecord.deletionRequestedAt | date:'medium' }}\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <strong>Status:</strong> \r\n            <span class=\"status-badge\">{{ currentStatus.deletionRecord.deletionStatus | titlecase }}</span>\r\n          </div>\r\n          <div class=\"detail-item\" *ngIf=\"currentStatus.deletionRecord.dataExpiryDate\">\r\n            <strong>Data Expiry:</strong> {{ currentStatus.deletionRecord.dataExpiryDate | date:'medium' }}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"actions\">\r\n          <button mat-button color=\"primary\" (click)=\"checkExistingDeletionStatus()\">\r\n            <mat-icon>refresh</mat-icon>\r\n            Refresh Status\r\n          </button>\r\n          <button mat-raised-button color=\"accent\" (click)=\"cancelDeletion()\" \r\n                  [disabled]=\"isLoading\">\r\n            <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\r\n            <mat-icon *ngIf=\"!isLoading\">cancel</mat-icon>\r\n            Cancel Deletion Request\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": ";AACA,SAAiCA,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AAIxF,SAASC,YAAY,QAAiC,iBAAiB;AACvE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAI7E,SAASC,2BAA2B,QAAQ,sDAAsD;;;;;;;;;;;;;;;;;;ICqCtFC,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,MAAA,sDACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAaVH,EAAA,CAAAC,cAAA,SAA8C;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAU;;;;;IAF1DN,EADF,CAAAC,cAAA,cAAqE,SAC/D;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAC,mDAAA,iBAA8C;IAChDR,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,YAA0B,eACd;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EACxE,EACA;;;;IANmBH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAC,oBAAA,CAAuB;IAIJX,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAK,MAAA,CAAAE,mBAAA,CAAyB;;;;;IAOrEZ,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASRH,EAAA,CAAAa,SAAA,sBAA2D;;;;;IAC3Db,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IA9E1DH,EAFJ,CAAAC,cAAA,aAAyD,aAC1B,kBACJ;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EADF,CAAAC,cAAA,QAAG,aACO;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,wHAE5B;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAENH,EAAA,CAAAC,cAAA,cAAgE;IAA/BD,EAAA,CAAAc,UAAA,sBAAAC,iEAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAV,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAYT,MAAA,CAAAU,eAAA,EAAiB;IAAA,EAAC;IAC7DpB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,YAA+B;IAC7BD,EAAA,CAAAE,MAAA,2EACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIAH,EAFJ,CAAAC,cAAA,cAAiC,uBACqB,cAC1C;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,oDAA4C;IAClFF,EADkF,CAAAG,YAAA,EAAM,EACzE;IAGbH,EADF,CAAAC,cAAA,wBAA2D,cACjD;IAAAD,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,oDAA4C;IAClFF,EADkF,CAAAG,YAAA,EAAM,EACzE;IAGbH,EADF,CAAAC,cAAA,wBAAoD,cAC1C;IAAAD,EAAA,CAAAE,MAAA,+BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,iDAAyC;IAC/EF,EAD+E,CAAAG,YAAA,EAAM,EACtE;IAGbH,EADF,CAAAC,cAAA,wBAAqD,cAC3C;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAE9EF,EAF8E,CAAAG,YAAA,EAAM,EACnE,EACX;IAGJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnDH,EAAA,CAAAa,SAAA,iBAC0C;IAC1Cb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,yDAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtEH,EAAA,CAAAO,UAAA,KAAAc,oDAAA,wBAAsE;IAGxErB,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrDH,EAAA,CAAAa,SAAA,oBAC+E;IAC/Eb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,yCAAiC;IAC7CF,EAD6C,CAAAG,YAAA,EAAW,EACvC;IAEjBH,EAAA,CAAAO,UAAA,KAAAe,8CAAA,mBAAqE;IAYnEtB,EADF,CAAAC,cAAA,wBAA8E,cACpE;IAAAD,EAAA,CAAAE,MAAA,kEAA0D;IACpEF,EADoE,CAAAG,YAAA,EAAS,EAC9D;IACfH,EAAA,CAAAO,UAAA,KAAAgB,oDAAA,wBAAgH;IAK9GvB,EADF,CAAAC,cAAA,eAAqB,kBACuC;IAAzBD,EAAA,CAAAc,UAAA,mBAAAU,iEAAA;MAAAxB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAV,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAST,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IACvDzB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACuD;IAErDD,EADA,CAAAO,UAAA,KAAAmB,sDAAA,0BAA6C,KAAAC,mDAAA,uBAChB;IAC7B3B,EAAA,CAAAE,MAAA,kCACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACH;;;;;;IA5EEH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAS,UAAA,cAAAC,MAAA,CAAAkB,YAAA,CAA0B;IAiChB5B,EAAA,CAAAI,SAAA,IAAwD;IAAxDJ,EAAA,CAAAS,UAAA,UAAAoB,OAAA,GAAAnB,MAAA,CAAAkB,YAAA,CAAAE,GAAA,4CAAAD,OAAA,CAAAE,OAAA,CAAwD;IAYxC/B,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAC,oBAAA,CAAAqB,MAAA,KAAqC;IAcvDhC,EAAA,CAAAI,SAAA,GAAkG;IAAlGJ,EAAA,CAAAS,UAAA,WAAAwB,OAAA,GAAAvB,MAAA,CAAAkB,YAAA,CAAAE,GAAA,sCAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAvB,MAAA,CAAAkB,YAAA,CAAAE,GAAA,sCAAAG,OAAA,CAAAC,OAAA,EAAkG;IAUpGlC,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAS,UAAA,aAAAC,MAAA,CAAAyB,SAAA,IAAAzB,MAAA,CAAAkB,YAAA,CAAAG,OAAA,CAA8C;IACxB/B,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAyB,SAAA,CAAe;IAChCnC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAyB,SAAA,CAAgB;;;;;;IAU/BnC,EAFJ,CAAAC,cAAA,aAA0D,cACtB,mBACa;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,4IAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,YAAsB,aACZ;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,wDACzB;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAGJH,EADF,CAAAC,cAAA,eAAqB,kBAC+B;IAA/BD,EAAA,CAAAc,UAAA,mBAAAsB,iEAAA;MAAApC,EAAA,CAAAgB,aAAA,CAAAqB,GAAA;MAAA,MAAA3B,MAAA,GAAAV,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAT,MAAA,CAAA4B,IAAA,GAAgB,aAAa;IAAA,EAAC;IAC/CtC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAkF;IAAxCD,EAAA,CAAAc,UAAA,mBAAAyB,iEAAA;MAAAvC,EAAA,CAAAgB,aAAA,CAAAqB,GAAA;MAAA,MAAA3B,MAAA,GAAAV,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAST,MAAA,CAAA8B,2BAAA,EAA6B;IAAA,EAAC;IAC/ExC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,wBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAuBAH,EADF,CAAAC,cAAA,cAA6E,aACnE;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAChC;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD0BH,EAAA,CAAAI,SAAA,GAChC;IADgCJ,EAAA,CAAAyC,kBAAA,MAAAzC,EAAA,CAAA0C,WAAA,OAAAhC,MAAA,CAAAiC,aAAA,CAAAC,cAAA,CAAAC,cAAA,iBAChC;;;;;IAbA7C,EADF,CAAAC,cAAA,cAAiE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEtBH,EADF,CAAAC,cAAA,cAAyB,aACf;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,aACf;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC9B;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,cACf;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA6D;;IAC1FF,EAD0F,CAAAG,YAAA,EAAO,EAC3F;IACNH,EAAA,CAAAO,UAAA,KAAAuC,qDAAA,kBAA6E;IAG/E9C,EAAA,CAAAG,YAAA,EAAM;;;;IAZsBH,EAAA,CAAAI,SAAA,GAC1B;IAD0BJ,EAAA,CAAAyC,kBAAA,MAAA/B,MAAA,CAAAiC,aAAA,CAAAC,cAAA,CAAAG,KAAA,MAC1B;IAE8B/C,EAAA,CAAAI,SAAA,GAC9B;IAD8BJ,EAAA,CAAAyC,kBAAA,MAAAzC,EAAA,CAAA0C,WAAA,QAAAhC,MAAA,CAAAiC,aAAA,CAAAC,cAAA,CAAAI,mBAAA,iBAC9B;IAG6BhD,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAiD,WAAA,QAAAvC,MAAA,CAAAiC,aAAA,CAAAC,cAAA,CAAAM,cAAA,EAA6D;IAEhElD,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAiC,aAAA,CAAAC,cAAA,CAAAC,cAAA,CAAiD;;;;;IAYzE7C,EAAA,CAAAa,SAAA,sBAA2D;;;;;IAC3Db,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IA9BhDH,EAFJ,CAAAC,cAAA,aAAqE,cACvC,mBACgB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,uEAAgE;IACrEF,EADqE,CAAAG,YAAA,EAAI,EACnE;IAENH,EAAA,CAAAO,UAAA,IAAA4C,8CAAA,mBAAiE;IAkB/DnD,EADF,CAAAC,cAAA,cAAqB,kBACwD;IAAxCD,EAAA,CAAAc,UAAA,mBAAAsC,kEAAA;MAAApD,EAAA,CAAAgB,aAAA,CAAAqC,GAAA;MAAA,MAAA3C,MAAA,GAAAV,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAST,MAAA,CAAA8B,2BAAA,EAA6B;IAAA,EAAC;IACxExC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAC+B;IADUD,EAAA,CAAAc,UAAA,mBAAAwC,kEAAA;MAAAtD,EAAA,CAAAgB,aAAA,CAAAqC,GAAA;MAAA,MAAA3C,MAAA,GAAAV,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAST,MAAA,CAAA6C,cAAA,EAAgB;IAAA,EAAC;IAGjEvD,EADA,CAAAO,UAAA,KAAAiD,uDAAA,0BAA6C,KAAAC,oDAAA,uBAChB;IAC7BzD,EAAA,CAAAE,MAAA,iCACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IA7ByBH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAiC,aAAA,CAAAC,cAAA,CAAkC;IAuBrD5C,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAS,UAAA,aAAAC,MAAA,CAAAyB,SAAA,CAAsB;IACAnC,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAyB,SAAA,CAAe;IAChCnC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAyB,SAAA,CAAgB;;;ADxHvC,OAAM,MAAOuB,wBAAwB;EAMnCC,YACUC,EAAe,EACfC,sBAA8C,EAC9CC,WAAwB,EACxBC,MAAc,EACdC,QAAqB,EACrBC,MAAiB;IALjB,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAtB,aAAa,GAA0B,IAAI;IAC3C,KAAAR,SAAS,GAAG,KAAK;IACjB,KAAAG,IAAI,GAA8C,aAAa;IAU7D,IAAI,CAACV,YAAY,GAAG,IAAI,CAACgC,EAAE,CAACM,KAAK,CAAC;MAChCC,mBAAmB,EAAE,CAAC,IAAI,CAAC;MAC3BC,0BAA0B,EAAE,CAAC,IAAI,CAAC;MAClCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,oBAAoB,EAAE,CAAC,KAAK,CAAC;MAC7BC,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAClF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,EAAEnF,UAAU,CAACoF,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACrEC,MAAM,EAAE,CAAC,EAAE,EAAErF,UAAU,CAACsF,SAAS,CAAC,GAAG,CAAC,CAAC;MACvCC,eAAe,EAAE,CAAC,KAAK,EAAEvF,UAAU,CAACwF,YAAY;KACjD,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACtC,2BAA2B,EAAE;EACpC;EACMA,2BAA2BA,CAAA;IAAA,IAAAuC,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAI;QACF,MAAMC,MAAM,SAASF,KAAI,CAAClB,sBAAsB,CAACqB,iBAAiB,EAAE,CAACC,SAAS,EAAE;QAChF,IAAIF,MAAM,IAAIA,MAAM,CAACG,kBAAkB,IAAIH,MAAM,CAACrC,cAAc,EAAE;UAChEmC,KAAI,CAACpC,aAAa,GAAGsC,MAAM;UAC3BF,KAAI,CAACzC,IAAI,GAAG,QAAQ;QACtB;MACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IAAC;EACH;EAEMjE,eAAeA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAAP,iBAAA;MACnB,IAAIO,MAAI,CAAC3D,YAAY,CAACG,OAAO,EAAE;QAC7BwD,MAAI,CAACC,oBAAoB,EAAE;QAC3B;MACF;MAEA,MAAMC,SAAS,GAAGF,MAAI,CAACtB,MAAM,CAACyB,IAAI,CAAC3F,2BAA2B,EAAE;QAC9D4F,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UACJC,KAAK,EAAE,0BAA0B;UACjCC,OAAO,EAAE,oGAAoG;UAC7GC,WAAW,EAAE,wBAAwB;UACrCC,UAAU,EAAE,QAAQ;UACpBC,WAAW,EAAE;;OAEhB,CAAC;MAEF,MAAMC,SAAS,SAAST,SAAS,CAACU,WAAW,EAAE,CAAChB,SAAS,EAAE;MAC3D,IAAI,CAACe,SAAS,EAAE;MAEhBX,MAAI,CAACpD,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAMiE,WAAW,GAAwB;UACvCjC,mBAAmB,EAAEoB,MAAI,CAAC3D,YAAY,CAACyE,KAAK,CAAClC,mBAAmB;UAChEC,0BAA0B,EAAEmB,MAAI,CAAC3D,YAAY,CAACyE,KAAK,CAACjC,0BAA0B;UAC9EC,mBAAmB,EAAEkB,MAAI,CAAC3D,YAAY,CAACyE,KAAK,CAAChC,mBAAmB;UAChEC,oBAAoB,EAAEiB,MAAI,CAAC3D,YAAY,CAACyE,KAAK,CAAC/B,oBAAoB;UAClEC,qBAAqB,EAAEgB,MAAI,CAAC3D,YAAY,CAACyE,KAAK,CAAC9B,qBAAqB;UACpEG,MAAM,EAAEa,MAAI,CAAC3D,YAAY,CAACyE,KAAK,CAAC3B,MAAM,EAAE4B,IAAI,EAAE,IAAIC;SACnD;QAED,MAAMC,MAAM,SAASjB,MAAI,CAAC1B,sBAAsB,CAAC4C,sBAAsB,CAACL,WAAW,CAAC,CAACjB,SAAS,EAAE;QAEhGI,MAAI,CAACvB,QAAQ,CAAC0B,IAAI,CAChB,oFAAoF,EACpF,OAAO,EACP;UAAEgB,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,mBAAmB;QAAC,CAAE,CACtD;QAEDpB,MAAI,CAACjD,IAAI,GAAG,cAAc;QAC1B,MAAMiD,MAAI,CAAC/C,2BAA2B,EAAE;MAE1C,CAAC,CAAC,OAAO6C,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDE,MAAI,CAACvB,QAAQ,CAAC0B,IAAI,CAChBL,KAAK,CAACS,OAAO,IAAI,uDAAuD,EACxE,OAAO,EACP;UAAEY,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,iBAAiB;QAAC,CAAE,CACpD;MACH,CAAC,SAAS;QACRpB,MAAI,CAACpD,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEMoB,cAAcA,CAAA;IAAA,IAAAqD,MAAA;IAAA,OAAA5B,iBAAA;MAClB,IAAI,CAAC4B,MAAI,CAACjE,aAAa,EAAEC,cAAc,EAAEiE,EAAE,EAAE;MAE7C,MAAMpB,SAAS,GAAGmB,MAAI,CAAC3C,MAAM,CAACyB,IAAI,CAAC3F,2BAA2B,EAAE;QAC9D4F,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UACJC,KAAK,EAAE,yBAAyB;UAChCC,OAAO,EAAE,+DAA+D;UACxEC,WAAW,EAAE,sBAAsB;UACnCC,UAAU,EAAE;;OAEf,CAAC;MAEF,MAAME,SAAS,SAAST,SAAS,CAACU,WAAW,EAAE,CAAChB,SAAS,EAAE;MAC3D,IAAI,CAACe,SAAS,EAAE;MAEhBU,MAAI,CAACzE,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAMyE,MAAI,CAAC/C,sBAAsB,CAACN,cAAc,EAAE,CAAC4B,SAAS,EAAE;QAE9DyB,MAAI,CAAC5C,QAAQ,CAAC0B,IAAI,CAChB,8CAA8C,EAC9C,OAAO,EACP;UAAEgB,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,mBAAmB;QAAC,CAAE,CACtD;QAEDC,MAAI,CAACjE,aAAa,GAAG,IAAI;QACzBiE,MAAI,CAACtE,IAAI,GAAG,aAAa;QACzBsE,MAAI,CAAChF,YAAY,CAACkF,KAAK,EAAE;MAE3B,CAAC,CAAC,OAAOzB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDuB,MAAI,CAAC5C,QAAQ,CAAC0B,IAAI,CAChBL,KAAK,CAACS,OAAO,IAAI,sDAAsD,EACvE,OAAO,EACP;UAAEY,QAAQ,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC,iBAAiB;QAAC,CAAE,CACpD;MACH,CAAC,SAAS;QACRC,MAAI,CAACzE,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEAV,YAAYA,CAAA;IACV,IAAI,CAACsC,MAAM,CAACgD,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;EAEQvB,oBAAoBA,CAAA;IAC1BwB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrF,YAAY,CAACsF,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,IAAI,CAACxF,YAAY,CAACE,GAAG,CAACsF,GAAG,CAAC,EAAEC,aAAa,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEA,IAAI1G,oBAAoBA,CAAA;IACtB,MAAMyF,WAAW,GAAG,IAAI,CAACxE,YAAY,CAACyE,KAAK;IAC3C,MAAMiB,SAAS,GAAa,EAAE;IAE9B,IAAIlB,WAAW,CAACjC,mBAAmB,EAAEmD,SAAS,CAACC,IAAI,CAAC,cAAc,CAAC;IACnE,IAAInB,WAAW,CAAChC,0BAA0B,EAAEkD,SAAS,CAACC,IAAI,CAAC,qBAAqB,CAAC;IACjF,IAAInB,WAAW,CAAC/B,mBAAmB,EAAEiD,SAAS,CAACC,IAAI,CAAC,cAAc,CAAC;IACnE,IAAInB,WAAW,CAAC9B,oBAAoB,EAAEgD,SAAS,CAACC,IAAI,CAAC,eAAe,CAAC;IAErE,OAAOD,SAAS;EAClB;EAEA,IAAI1G,mBAAmBA,CAAA;IACrB,MAAM4G,IAAI,GAAG,IAAI,CAAC5F,YAAY,CAACyE,KAAK,CAAC9B,qBAAqB,IAAI,EAAE;IAChE,IAAIiD,IAAI,KAAK,CAAC,EAAE,OAAO,OAAO;IAC9B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,GAAGA,IAAI,OAAO;IACpC,IAAIA,IAAI,KAAK,EAAE,EAAE,OAAO,SAAS;IACjC,IAAIA,IAAI,GAAG,GAAG,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC,SAAS;IACxD,OAAO,QAAQ;EACjB;EAAC,QAAAG,CAAA,G;qCAvKUjE,wBAAwB,EAAA1D,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAAhI,EAAA,CAAA4H,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAA4H,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAApI,EAAA,CAAA4H,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAtI,EAAA,CAAA4H,iBAAA,CAAAW,EAAA,CAAAC,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxB/E,wBAAwB;IAAAgF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjC7BhJ,EAJR,CAAAC,cAAA,aAAwC,kBACN,sBACb,qBACC,kBACS;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzCH,EAAA,CAAAE,MAAA,yBACF;QACFF,EADE,CAAAG,YAAA,EAAiB,EACD;QAElBH,EAAA,CAAAC,cAAA,uBAAkB;QAoHhBD,EAlHA,CAAAO,UAAA,IAAA2I,uCAAA,kBAAyD,IAAAC,uCAAA,kBAwFC,KAAAC,wCAAA,kBA0BW;QAuC3EpJ,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;;;QAzJMH,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAS,UAAA,SAAAwI,GAAA,CAAA3G,IAAA,mBAA4B;QAwF5BtC,EAAA,CAAAI,SAAA,EAA6B;QAA7BJ,EAAA,CAAAS,UAAA,SAAAwI,GAAA,CAAA3G,IAAA,oBAA6B;QA0B7BtC,EAAA,CAAAI,SAAA,EAAwC;QAAxCJ,EAAA,CAAAS,UAAA,SAAAwI,GAAA,CAAA3G,IAAA,iBAAA2G,GAAA,CAAAtG,aAAA,CAAwC;;;mBDvGhDpD,YAAY,EAAA8J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,QAAA,EACZnK,mBAAmB,EAAAuI,EAAA,CAAA6B,aAAA,EAAA7B,EAAA,CAAA8B,oBAAA,EAAA9B,EAAA,CAAA+B,mBAAA,EAAA/B,EAAA,CAAAgC,eAAA,EAAAhC,EAAA,CAAAiC,oBAAA,EAAAjC,EAAA,CAAAkC,YAAA,EAAAlC,EAAA,CAAAmC,YAAA,EAAAnC,EAAA,CAAAoC,kBAAA,EAAApC,EAAA,CAAAqC,eAAA,EACnB1K,aAAa,EAAA2K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb9K,aAAa,EAAA+K,EAAA,CAAAC,OAAA,EACb/K,eAAe,EAAAgL,GAAA,CAAAC,SAAA,EACfhL,iBAAiB,EAAAiL,GAAA,CAAAC,WAAA,EACjBjL,kBAAkB,EAAAkL,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,OAAA,EAAAH,GAAA,CAAAI,QAAA,EAClBrL,cAAc,EAAAsL,GAAA,CAAAC,QAAA,EACdtL,wBAAwB,EAAAuL,GAAA,CAAAC,kBAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}