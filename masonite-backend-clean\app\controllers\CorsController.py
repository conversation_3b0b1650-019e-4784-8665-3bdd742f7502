"""CORS Controller for handling preflight requests and adding CORS headers"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response


class Cors<PERSON><PERSON>roller(Controller):
    """Handle CORS preflight requests and provide CORS functionality"""

    def preflight(self, request: Request, response: Response):
        """
        Handle OPTIONS preflight requests for CORS
        OPTIONS /*
        """
        # Get origin from request
        origin = request.header('Origin')

        # Define allowed origins
        allowed_origins = [
            'http://localhost:4200',  # Angular frontend
            'http://localhost:3000',  # Alternative frontend port
            'http://localhost:3001',  # Alternative frontend port
        ]

        # Set CORS headers
        if origin and origin in allowed_origins:
            response.header('Access-Control-Allow-Origin', origin)
        else:
            response.header('Access-Control-Allow-Origin', '*')  # Allow all for development

        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Max-Age', '86400')
        response.header('Access-Control-Expose-Headers', 'x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset')

        return response.json({'message': 'CORS preflight'}, 200)

    @staticmethod
    def add_cors_headers(response, origin=None):
        """Static method to add CORS headers to any response"""

        # Define allowed origins
        allowed_origins = [
            'http://localhost:4200',  # Angular frontend
            'http://localhost:3000',  # Alternative frontend port
            'http://localhost:3001',  # Alternative frontend port
        ]

        # Set CORS headers
        if origin and origin in allowed_origins:
            response.header('Access-Control-Allow-Origin', origin)
        else:
            response.header('Access-Control-Allow-Origin', '*')  # Allow all for development

        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Expose-Headers', 'x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset')

        return response
