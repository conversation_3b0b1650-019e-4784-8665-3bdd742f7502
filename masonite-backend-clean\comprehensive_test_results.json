{"summary": {"total_tests": 73, "passed_tests": 71, "failed_tests": 2, "success_rate": 97.26027397260275, "test_date": "2025-06-16T05:48:21.553271"}, "detailed_results": [{"endpoint": "/cors/preflight", "method": "OPTIONS", "status": "PASS", "expected_code": 200, "actual_code": 204, "message": "", "timestamp": "2025-06-16T05:45:33.488358"}, {"endpoint": "/auth/login", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:45:36.182056"}, {"endpoint": "/auth/signup", "method": "POST", "status": "PASS", "expected_code": 201, "actual_code": 201, "message": "", "timestamp": "2025-06-16T05:45:38.728174"}, {"endpoint": "/auth/me", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:45:40.861532"}, {"endpoint": "/auth/profile", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:45:43.092357"}, {"endpoint": "/auth/refresh", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:45:45.409457"}, {"endpoint": "/auth/verify-email", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:45:47.623834"}, {"endpoint": "/auth/forgot-password", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:45:49.803030"}, {"endpoint": "/auth/reset-password", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T05:45:51.837969"}, {"endpoint": "/auth/logout", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:45:54.015802"}, {"endpoint": "/2fa/setup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:00.841969"}, {"endpoint": "/2fa/verify", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:02.945231"}, {"endpoint": "/2fa/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:05.063190"}, {"endpoint": "/2fa/recovery-codes", "method": "GET", "status": "FAIL", "expected_code": 200, "actual_code": 400, "message": "{'error': {'statusCode': 400, 'name': 'BadRequestError', 'message': 'Two-factor authentication is not enabled'}}", "timestamp": "2025-06-16T05:46:07.151460"}, {"endpoint": "/2fa/regenerate-codes", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:09.365792"}, {"endpoint": "/2fa/disable", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:11.467724"}, {"endpoint": "/2fa/send-sms", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 400, "message": "", "timestamp": "2025-06-16T05:46:13.581918"}, {"endpoint": "/2fa/verify-sms", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:15.818786"}, {"endpoint": "/2fa/send-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:18.061662"}, {"endpoint": "/2fa/verify-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:20.288434"}, {"endpoint": "/auth/oauth/providers", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:22.325228"}, {"endpoint": "/auth/oauth/google/url", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:24.361106"}, {"endpoint": "/auth/oauth/github/url", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:26.434137"}, {"endpoint": "/auth/oauth/exchange-token", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:28.826563"}, {"endpoint": "/auth/oauth/callback", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 400, "message": "Expected response", "timestamp": "2025-06-16T05:46:30.860444"}, {"endpoint": "/payments/test", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:32.900128"}, {"endpoint": "/payments/create-order", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:35.093779"}, {"endpoint": "/payments/verify", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:37.269166"}, {"endpoint": "/payments/my-payments", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:39.852712"}, {"endpoint": "/payments/analytics", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:42.035029"}, {"endpoint": "/payments/refunds", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:44.373490"}, {"endpoint": "/payments/refund", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:46.668437"}, {"endpoint": "/payments/cancel", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:48.837914"}, {"endpoint": "/payments/status/{order_id}", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:51.231831"}, {"endpoint": "/payments/webhook", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:46:53.274645"}, {"endpoint": "/account/request-deletion", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:55.572135"}, {"endpoint": "/account/deletion-status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:46:57.844801"}, {"endpoint": "/account/cancel-deletion", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:00.081905"}, {"endpoint": "/account/export-data", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:02.440200"}, {"endpoint": "/account/request-export", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:05.022611"}, {"endpoint": "/account/cleanup-expired", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:07.121538"}, {"endpoint": "/account/confirm-deletion", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:47:09.253909"}, {"endpoint": "/account/check-preserved-data", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:11.349757"}, {"endpoint": "/account/restore-data", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:47:13.560694"}, {"endpoint": "/account/delete-preserved-data", "method": "DELETE", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:15.719610"}, {"endpoint": "/otp/send", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:18.256464"}, {"endpoint": "/otp/send-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:20.772754"}, {"endpoint": "/otp/send-sms", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'error': {'statusCode': 500, 'name': 'InternalServerError', 'message': 'Phone number not registered. Please register first or use email.'}}", "timestamp": "2025-06-16T05:47:22.876777"}, {"endpoint": "/otp/verify", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "Expected response", "timestamp": "2025-06-16T05:47:25.269378"}, {"endpoint": "/otp/login", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 401, "message": "Expected error response", "timestamp": "2025-06-16T05:47:27.549966"}, {"endpoint": "/otp/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:29.670079"}, {"endpoint": "/otp/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:32.176524"}, {"endpoint": "/security/dashboard", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:34.972074"}, {"endpoint": "/security/events", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:37.160715"}, {"endpoint": "/security/events/user", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:39.527527"}, {"endpoint": "/security/suspicious-activity", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:41.731996"}, {"endpoint": "/security/analysis", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:44.025038"}, {"endpoint": "/security/statistics", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:46.795773"}, {"endpoint": "/security/account-status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:49.093418"}, {"endpoint": "/security/unlock-account", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:47:51.295120"}, {"endpoint": "/security/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:53.577594"}, {"endpoint": "/security/events/{event_id}/resolve", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:47:55.808441"}, {"endpoint": "/notifications", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:47:58.110642"}, {"endpoint": "/notifications/test", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:00.378612"}, {"endpoint": "/notifications/{notification_id}/read", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:48:02.609477"}, {"endpoint": "/queue/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:04.906590"}, {"endpoint": "/queue/stats", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:07.096230"}, {"endpoint": "/queue/failed-jobs", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:09.389563"}, {"endpoint": "/queue/test-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:11.565018"}, {"endpoint": "/queue/test-security-processing", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:13.943257"}, {"endpoint": "/queue/test-cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:16.357990"}, {"endpoint": "/queue/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:19.239399"}, {"endpoint": "/queue/process-security-events", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:48:21.552278"}]}