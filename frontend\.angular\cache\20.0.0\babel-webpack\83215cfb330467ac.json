{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule } from '@angular/forms';\n// Angular Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n// App Components\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { RateLimitNotificationComponent } from './components/rate-limit-notification/rate-limit-notification.component';\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { PaymentService } from './services/payment.service';\nimport { TwoFactorService } from './services/two-factor.service';\nimport { LoadingService } from './services/loading.service';\nimport { RateLimitService } from './services/rate-limit.service';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static #_ = this.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }, AuthService, PaymentService, TwoFactorService, LoadingService, RateLimitService],\n    imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, AppRoutingModule, RateLimitNotificationComponent,\n    // Angular Material\n    MatToolbarModule, MatButtonModule, MatIconModule, MatMenuModule, MatSnackBarModule, MatProgressSpinnerModule, MatDividerModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, AppRoutingModule, RateLimitNotificationComponent,\n    // Angular Material\n    MatToolbarModule, MatButtonModule, MatIconModule, MatMenuModule, MatSnackBarModule, MatProgressSpinnerModule, MatDividerModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ReactiveFormsModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatMenuModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatDividerModule", "AppRoutingModule", "AppComponent", "RateLimitNotificationComponent", "AuthInterceptor", "AuthService", "PaymentService", "TwoFactorService", "LoadingService", "RateLimitService", "AppModule", "_", "_2", "bootstrap", "_3", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n\n// App Components\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { RateLimitNotificationComponent } from './components/rate-limit-notification/rate-limit-notification.component';\n\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n\n// Services\nimport { AuthService } from './services/auth.service';\nimport { PaymentService } from './services/payment.service';\nimport { TwoFactorService } from './services/two-factor.service';\nimport { LoadingService } from './services/loading.service';\nimport { RateLimitService } from './services/rate-limit.service';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    AppRoutingModule,\n    RateLimitNotificationComponent,\n    \n    // Angular Material\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatMenuModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatDividerModule\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    },\n    AuthService,\n    PaymentService,\n    TwoFactorService,\n    LoadingService,\n    RateLimitService\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,8BAA8B,QAAQ,wEAAwE;AAEvH;AACA,SAASC,eAAe,QAAQ,iCAAiC;AAEjE;AACA,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,gBAAgB,QAAQ,+BAA+B;;AAqChE,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qCAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF,SAAS;IAAAG,SAAA,GAFRX,YAAY;EAAA;EAAA,QAAAY,EAAA,G;eAZb,CACT;MACEC,OAAO,EAAEvB,iBAAiB;MAC1BwB,QAAQ,EAAEZ,eAAe;MACzBa,KAAK,EAAE;KACR,EACDZ,WAAW,EACXC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,CACjB;IAAAS,OAAA,GA3BC7B,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBQ,gBAAgB,EAChBE,8BAA8B;IAE9B;IACAT,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB;EAAA;;;2EAgBPU,SAAS;IAAAS,YAAA,GAjClBjB,YAAY;IAAAgB,OAAA,GAGZ7B,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,mBAAmB,EACnBQ,gBAAgB,EAChBE,8BAA8B;IAE9B;IACAT,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}