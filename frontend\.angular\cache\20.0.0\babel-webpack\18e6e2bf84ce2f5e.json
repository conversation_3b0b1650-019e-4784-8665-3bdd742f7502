{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport hotEmitter from \"webpack/hot/emitter.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, createOverlay } from \"./overlay.js\";\nimport { log, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport { isProgressSupported, defineProgressElement } from \"./progress.js\";\n\n/**\n * @typedef {Object} OverlayOptions\n * @property {boolean | (error: Error) => boolean} [warnings]\n * @property {boolean | (error: Error) => boolean} [errors]\n * @property {boolean | (error: Error) => boolean} [runtimeErrors]\n * @property {string} [trustedTypesPolicyName]\n */\n\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | OverlayOptions} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @param {boolean | { warnings?: boolean | string; errors?: boolean | string; runtimeErrors?: boolean | string; }} overlayOptions\n */\nvar decodeOverlayOptions = function decodeOverlayOptions(overlayOptions) {\n  if (_typeof(overlayOptions) === \"object\") {\n    [\"warnings\", \"errors\", \"runtimeErrors\"].forEach(function (property) {\n      if (typeof overlayOptions[property] === \"string\") {\n        var overlayFilterFunctionString = decodeURIComponent(overlayOptions[property]);\n\n        // eslint-disable-next-line no-new-func\n        overlayOptions[property] = new Function(\"message\", \"var callback = \".concat(overlayFilterFunctionString, \"\\n        return callback(message)\"));\n      }\n    });\n  }\n};\n\n/**\n * @type {Status}\n */\nvar status = {\n  isUnloading: false,\n  // eslint-disable-next-line camelcase\n  currentHash: __webpack_hash__\n};\n\n/**\n * @returns {string}\n */\nvar getCurrentScriptSource = function getCurrentScriptSource() {\n  // `document.currentScript` is the most accurate way to find the current script,\n  // but is not supported in all browsers.\n  if (document.currentScript) {\n    return document.currentScript.getAttribute(\"src\");\n  }\n\n  // Fallback to getting all scripts running in the document.\n  var scriptElements = document.scripts || [];\n  var scriptElementsWithSrc = Array.prototype.filter.call(scriptElements, function (element) {\n    return element.getAttribute(\"src\");\n  });\n  if (scriptElementsWithSrc.length > 0) {\n    var currentScript = scriptElementsWithSrc[scriptElementsWithSrc.length - 1];\n    return currentScript.getAttribute(\"src\");\n  }\n\n  // Fail as there was no script to use.\n  throw new Error(\"[webpack-dev-server] Failed to get current script source.\");\n};\n\n/**\n * @param {string} resourceQuery\n * @returns {{ [key: string]: string | boolean }}\n */\nvar parseURL = function parseURL(resourceQuery) {\n  /** @type {{ [key: string]: string }} */\n  var result = {};\n  if (typeof resourceQuery === \"string\" && resourceQuery !== \"\") {\n    var searchParams = resourceQuery.slice(1).split(\"&\");\n    for (var i = 0; i < searchParams.length; i++) {\n      var pair = searchParams[i].split(\"=\");\n      result[pair[0]] = decodeURIComponent(pair[1]);\n    }\n  } else {\n    // Else, get the url from the <script> this file was called with.\n    var scriptSource = getCurrentScriptSource();\n    var scriptSourceURL;\n    try {\n      // The placeholder `baseURL` with `window.location.href`,\n      // is to allow parsing of path-relative or protocol-relative URLs,\n      // and will have no effect if `scriptSource` is a fully valid URL.\n      scriptSourceURL = new URL(scriptSource, self.location.href);\n    } catch (error) {\n      // URL parsing failed, do nothing.\n      // We will still proceed to see if we can recover using `resourceQuery`\n    }\n    if (scriptSourceURL) {\n      result = scriptSourceURL;\n      result.fromCurrentScript = true;\n    }\n  }\n  return result;\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\nvar enabledFeatures = {\n  \"Hot Module Replacement\": false,\n  \"Live Reloading\": false,\n  Progress: false,\n  Overlay: false\n};\n\n/** @type {Options} */\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  enabledFeatures[\"Hot Module Replacement\"] = true;\n}\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  enabledFeatures[\"Live Reloading\"] = true;\n}\nif (parsedResourceQuery.progress === \"true\") {\n  options.progress = true;\n  enabledFeatures.Progress = true;\n}\nif (parsedResourceQuery.overlay) {\n  try {\n    options.overlay = JSON.parse(parsedResourceQuery.overlay);\n  } catch (e) {\n    log.error(\"Error parsing overlay options from resource query:\", e);\n  }\n\n  // Fill in default \"true\" params for partially-specified objects.\n  if (_typeof(options.overlay) === \"object\") {\n    options.overlay = _objectSpread({\n      errors: true,\n      warnings: true,\n      runtimeErrors: true\n    }, options.overlay);\n    decodeOverlayOptions(options.overlay);\n  }\n  enabledFeatures.Overlay = true;\n}\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n\n/**\n * @param {string} level\n */\nvar setAllLogLevel = function setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n};\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var listEnabledFeatures = Object.keys(features);\n  if (!features || listEnabledFeatures.length === 0) {\n    return;\n  }\n  var logString = \"Server started:\";\n\n  // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n  for (var i = 0; i < listEnabledFeatures.length; i++) {\n    var key = listEnabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  }\n  // replace last comma with a period\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\nlogEnabledFeatures(enabledFeatures);\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar overlay = typeof window !== \"undefined\" ? createOverlay(_typeof(options.overlay) === \"object\" ? {\n  trustedTypesPolicyName: options.overlay.trustedTypesPolicyName,\n  catchRuntimeError: options.overlay.runtimeErrors\n} : {\n  trustedTypesPolicyName: false,\n  catchRuntimeError: options.overlay\n}) : {\n  send: function send() {}\n};\n\n/**\n * @param {Options} options\n * @param {Status} currentStatus\n */\nvar reloadApp = function reloadApp(_ref, currentStatus) {\n  var hot = _ref.hot,\n    liveReload = _ref.liveReload;\n  if (currentStatus.isUnloading) {\n    return;\n  }\n  var currentHash = currentStatus.currentHash,\n    previousHash = currentStatus.previousHash;\n  var isInitial = currentHash.indexOf(/** @type {string} */previousHash) >= 0;\n  if (isInitial) {\n    return;\n  }\n\n  /**\n   * @param {Window} rootWindow\n   * @param {number} intervalId\n   */\n  function applyReload(rootWindow, intervalId) {\n    clearInterval(intervalId);\n    log.info(\"App updated. Reloading...\");\n    rootWindow.location.reload();\n  }\n  var search = self.location.search.toLowerCase();\n  var allowToHot = search.indexOf(\"webpack-dev-server-hot=false\") === -1;\n  var allowToLiveReload = search.indexOf(\"webpack-dev-server-live-reload=false\") === -1;\n  if (hot && allowToHot) {\n    log.info(\"App hot update...\");\n    hotEmitter.emit(\"webpackHotUpdate\", currentStatus.currentHash);\n    if (typeof self !== \"undefined\" && self.window) {\n      // broadcast update to window\n      self.postMessage(\"webpackHotUpdate\".concat(currentStatus.currentHash), \"*\");\n    }\n  }\n  // allow refreshing the page only if liveReload isn't disabled\n  else if (liveReload && allowToLiveReload) {\n    var rootWindow = self;\n\n    // use parent window for reload (in case we're in an iframe with no valid src)\n    var intervalId = self.setInterval(function () {\n      if (rootWindow.location.protocol !== \"about:\") {\n        // reload immediately if protocol is valid\n        applyReload(rootWindow, intervalId);\n      } else {\n        rootWindow = rootWindow.parent;\n        if (rootWindow.parent === rootWindow) {\n          // if parent equals current window we've reached the root which would continue forever, so trigger a reload anyways\n          applyReload(rootWindow, intervalId);\n        }\n      }\n    });\n  }\n};\nvar ansiRegex = new RegExp([\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\"].join(\"|\"), \"g\");\n\n/**\n *\n * Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string.\n * Adapted from code originally released by Sindre Sorhus\n * Licensed the MIT License\n *\n * @param {string} string\n * @return {string}\n */\nvar stripAnsi = function stripAnsi(string) {\n  if (typeof string !== \"string\") {\n    throw new TypeError(\"Expected a `string`, got `\".concat(_typeof(string), \"`\"));\n  }\n  return string.replace(ansiRegex, \"\");\n};\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n    options.hot = true;\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n    options.liveReload = true;\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\");\n\n    // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Invalid\");\n  },\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n    options.overlay = value;\n    decodeOverlayOptions(options.overlay);\n  },\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n    options.reconnect = value;\n  },\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n    if (isProgressSupported()) {\n      if (typeof options.progress === \"string\") {\n        var progress = document.querySelector(\"wds-progress\");\n        if (!progress) {\n          defineProgressElement();\n          progress = document.createElement(\"wds-progress\");\n          document.body.appendChild(progress);\n        }\n        progress.setAttribute(\"progress\", data.percent);\n        progress.setAttribute(\"type\", options.progress);\n      }\n    }\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n        header = _formatProblem.header,\n        body = _formatProblem.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Warnings\", printableWarnings);\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n    var overlayWarningsSetting = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n    if (overlayWarningsSetting) {\n      var warningsToDisplay = typeof overlayWarningsSetting === \"function\" ? _warnings.filter(overlayWarningsSetting) : _warnings;\n      if (warningsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"warning\",\n          messages: _warnings\n        });\n      }\n    }\n    if (params && params.preventReloading) {\n      return;\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n        header = _formatProblem2.header,\n        body = _formatProblem2.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Errors\", printableErrors);\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n    var overlayErrorsSettings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n    if (overlayErrorsSettings) {\n      var errorsToDisplay = typeof overlayErrorsSettings === \"function\" ? _errors.filter(overlayErrorsSettings) : _errors;\n      if (errorsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"error\",\n          messages: _errors\n        });\n      }\n    }\n  },\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Close\");\n  }\n};\n\n/**\n * @param {{ protocol?: string, auth?: string, hostname?: string, port?: string, pathname?: string, search?: string, hash?: string, slashes?: boolean }} objURL\n * @returns {string}\n */\nvar formatURL = function formatURL(objURL) {\n  var protocol = objURL.protocol || \"\";\n  if (protocol && protocol.substr(-1) !== \":\") {\n    protocol += \":\";\n  }\n  var auth = objURL.auth || \"\";\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, \":\");\n    auth += \"@\";\n  }\n  var host = \"\";\n  if (objURL.hostname) {\n    host = auth + (objURL.hostname.indexOf(\":\") === -1 ? objURL.hostname : \"[\".concat(objURL.hostname, \"]\"));\n    if (objURL.port) {\n      host += \":\".concat(objURL.port);\n    }\n  }\n  var pathname = objURL.pathname || \"\";\n  if (objURL.slashes) {\n    host = \"//\".concat(host || \"\");\n    if (pathname && pathname.charAt(0) !== \"/\") {\n      pathname = \"/\".concat(pathname);\n    }\n  } else if (!host) {\n    host = \"\";\n  }\n  var search = objURL.search || \"\";\n  if (search && search.charAt(0) !== \"?\") {\n    search = \"?\".concat(search);\n  }\n  var hash = objURL.hash || \"\";\n  if (hash && hash.charAt(0) !== \"#\") {\n    hash = \"#\".concat(hash);\n  }\n  pathname = pathname.replace(/[?#]/g,\n  /**\n   * @param {string} match\n   * @returns {string}\n   */\n  function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace(\"#\", \"%23\");\n  return \"\".concat(protocol).concat(host).concat(pathname).concat(search).concat(hash);\n};\n\n/**\n * @param {URL & { fromCurrentScript?: boolean }} parsedURL\n * @returns {string}\n */\nvar createSocketURL = function createSocketURL(parsedURL) {\n  var hostname = parsedURL.hostname;\n\n  // Node.js module parses it as `::`\n  // `new URL(urlString, [baseURLString])` parses it as '[::]'\n  var isInAddrAny = hostname === \"0.0.0.0\" || hostname === \"::\" || hostname === \"[::]\";\n\n  // why do we need this check?\n  // hostname n/a for file protocol (example, when using electron, ionic)\n  // see: https://github.com/webpack/webpack-dev-server/pull/384\n  if (isInAddrAny && self.location.hostname && self.location.protocol.indexOf(\"http\") === 0) {\n    hostname = self.location.hostname;\n  }\n  var socketURLProtocol = parsedURL.protocol || self.location.protocol;\n\n  // When https is used in the app, secure web sockets are always necessary because the browser doesn't accept non-secure web sockets.\n  if (socketURLProtocol === \"auto:\" || hostname && isInAddrAny && self.location.protocol === \"https:\") {\n    socketURLProtocol = self.location.protocol;\n  }\n  socketURLProtocol = socketURLProtocol.replace(/^(?:http|.+-extension|file)/i, \"ws\");\n  var socketURLAuth = \"\";\n\n  // `new URL(urlString, [baseURLstring])` doesn't have `auth` property\n  // Parse authentication credentials in case we need them\n  if (parsedURL.username) {\n    socketURLAuth = parsedURL.username;\n\n    // Since HTTP basic authentication does not allow empty username,\n    // we only include password if the username is not empty.\n    if (parsedURL.password) {\n      // Result: <username>:<password>\n      socketURLAuth = socketURLAuth.concat(\":\", parsedURL.password);\n    }\n  }\n\n  // In case the host is a raw IPv6 address, it can be enclosed in\n  // the brackets as the brackets are needed in the final URL string.\n  // Need to remove those as url.format blindly adds its own set of brackets\n  // if the host string contains colons. That would lead to non-working\n  // double brackets (e.g. [[::]]) host\n  //\n  // All of these web socket url params are optionally passed in through resourceQuery,\n  // so we need to fall back to the default if they are not provided\n  var socketURLHostname = (hostname || self.location.hostname || \"localhost\").replace(/^\\[(.*)\\]$/, \"$1\");\n  var socketURLPort = parsedURL.port;\n  if (!socketURLPort || socketURLPort === \"0\") {\n    socketURLPort = self.location.port;\n  }\n\n  // If path is provided it'll be passed in via the resourceQuery as a\n  // query param so it has to be parsed out of the querystring in order for the\n  // client to open the socket to the correct location.\n  var socketURLPathname = \"/ws\";\n  if (parsedURL.pathname && !parsedURL.fromCurrentScript) {\n    socketURLPathname = parsedURL.pathname;\n  }\n  return formatURL({\n    protocol: socketURLProtocol,\n    auth: socketURLAuth,\n    hostname: socketURLHostname,\n    port: socketURLPort,\n    pathname: socketURLPathname,\n    slashes: true\n  });\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);\nexport { getCurrentScriptSource, parseURL, createSocketURL };", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "_typeof", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "iterator", "constructor", "prototype", "webpackHotLog", "hotEmitter", "socket", "formatProblem", "createOverlay", "log", "setLogLevel", "sendMessage", "isProgressSupported", "defineProgressElement", "decodeOverlayOptions", "overlayOptions", "property", "overlayFilterFunctionString", "decodeURIComponent", "Function", "concat", "status", "isUnloading", "currentHash", "__webpack_hash__", "getCurrentScriptSource", "document", "currentScript", "getAttribute", "scriptElements", "scripts", "scriptElementsWithSrc", "Array", "element", "Error", "parseURL", "resourceQuery", "result", "searchParams", "slice", "split", "pair", "scriptSource", "scriptSourceURL", "URL", "self", "location", "href", "error", "fromCurrentScript", "parsedResourceQuery", "__resourceQuery", "enabledFeatures", "Progress", "Overlay", "options", "hot", "liveReload", "progress", "overlay", "JSON", "parse", "errors", "warnings", "runtimeErrors", "logging", "reconnect", "setAllLogLevel", "level", "logEnabledFeatures", "features", "listEnabledFeatures", "logString", "key", "info", "addEventListener", "window", "trustedTypesPolicyName", "catchRuntimeError", "send", "reloadApp", "_ref", "currentStatus", "previousHash", "isInitial", "indexOf", "applyReload", "rootWindow", "intervalId", "clearInterval", "reload", "search", "toLowerCase", "allowToHot", "allowToLiveReload", "emit", "postMessage", "setInterval", "protocol", "parent", "ansiRegex", "RegExp", "join", "stripAnsi", "string", "replace", "onSocketMessage", "invalid", "type", "hash", "_hash", "progressUpdate", "data", "pluginName", "percent", "msg", "querySelector", "createElement", "body", "append<PERSON><PERSON><PERSON>", "setAttribute", "stillOk", "ok", "staticChanged", "file", "_warnings", "params", "warn", "printableWarnings", "map", "_formatProblem", "header", "overlayWarningsSetting", "warningsToDisplay", "messages", "preventReloading", "_errors", "printableErrors", "_formatProblem2", "overlayErrorsSettings", "errorsToDisplay", "_error", "close", "formatURL", "objURL", "substr", "auth", "encodeURIComponent", "host", "hostname", "port", "pathname", "slashes", "char<PERSON>t", "match", "createSocketURL", "parsedURL", "isInAddrAny", "socketURLProtocol", "socketURLAuth", "username", "password", "socketURLHostname", "socketURLPort", "socketURLPathname", "socketURL"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/webpack-dev-server/client/index.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport hotEmitter from \"webpack/hot/emitter.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, createOverlay } from \"./overlay.js\";\nimport { log, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport { isProgressSupported, defineProgressElement } from \"./progress.js\";\n\n/**\n * @typedef {Object} OverlayOptions\n * @property {boolean | (error: Error) => boolean} [warnings]\n * @property {boolean | (error: Error) => boolean} [errors]\n * @property {boolean | (error: Error) => boolean} [runtimeErrors]\n * @property {string} [trustedTypesPolicyName]\n */\n\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | OverlayOptions} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @param {boolean | { warnings?: boolean | string; errors?: boolean | string; runtimeErrors?: boolean | string; }} overlayOptions\n */\nvar decodeOverlayOptions = function decodeOverlayOptions(overlayOptions) {\n  if (_typeof(overlayOptions) === \"object\") {\n    [\"warnings\", \"errors\", \"runtimeErrors\"].forEach(function (property) {\n      if (typeof overlayOptions[property] === \"string\") {\n        var overlayFilterFunctionString = decodeURIComponent(overlayOptions[property]);\n\n        // eslint-disable-next-line no-new-func\n        overlayOptions[property] = new Function(\"message\", \"var callback = \".concat(overlayFilterFunctionString, \"\\n        return callback(message)\"));\n      }\n    });\n  }\n};\n\n/**\n * @type {Status}\n */\nvar status = {\n  isUnloading: false,\n  // eslint-disable-next-line camelcase\n  currentHash: __webpack_hash__\n};\n\n/**\n * @returns {string}\n */\nvar getCurrentScriptSource = function getCurrentScriptSource() {\n  // `document.currentScript` is the most accurate way to find the current script,\n  // but is not supported in all browsers.\n  if (document.currentScript) {\n    return document.currentScript.getAttribute(\"src\");\n  }\n\n  // Fallback to getting all scripts running in the document.\n  var scriptElements = document.scripts || [];\n  var scriptElementsWithSrc = Array.prototype.filter.call(scriptElements, function (element) {\n    return element.getAttribute(\"src\");\n  });\n  if (scriptElementsWithSrc.length > 0) {\n    var currentScript = scriptElementsWithSrc[scriptElementsWithSrc.length - 1];\n    return currentScript.getAttribute(\"src\");\n  }\n\n  // Fail as there was no script to use.\n  throw new Error(\"[webpack-dev-server] Failed to get current script source.\");\n};\n\n/**\n * @param {string} resourceQuery\n * @returns {{ [key: string]: string | boolean }}\n */\nvar parseURL = function parseURL(resourceQuery) {\n  /** @type {{ [key: string]: string }} */\n  var result = {};\n  if (typeof resourceQuery === \"string\" && resourceQuery !== \"\") {\n    var searchParams = resourceQuery.slice(1).split(\"&\");\n    for (var i = 0; i < searchParams.length; i++) {\n      var pair = searchParams[i].split(\"=\");\n      result[pair[0]] = decodeURIComponent(pair[1]);\n    }\n  } else {\n    // Else, get the url from the <script> this file was called with.\n    var scriptSource = getCurrentScriptSource();\n    var scriptSourceURL;\n    try {\n      // The placeholder `baseURL` with `window.location.href`,\n      // is to allow parsing of path-relative or protocol-relative URLs,\n      // and will have no effect if `scriptSource` is a fully valid URL.\n      scriptSourceURL = new URL(scriptSource, self.location.href);\n    } catch (error) {\n      // URL parsing failed, do nothing.\n      // We will still proceed to see if we can recover using `resourceQuery`\n    }\n    if (scriptSourceURL) {\n      result = scriptSourceURL;\n      result.fromCurrentScript = true;\n    }\n  }\n  return result;\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\nvar enabledFeatures = {\n  \"Hot Module Replacement\": false,\n  \"Live Reloading\": false,\n  Progress: false,\n  Overlay: false\n};\n\n/** @type {Options} */\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  enabledFeatures[\"Hot Module Replacement\"] = true;\n}\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  enabledFeatures[\"Live Reloading\"] = true;\n}\nif (parsedResourceQuery.progress === \"true\") {\n  options.progress = true;\n  enabledFeatures.Progress = true;\n}\nif (parsedResourceQuery.overlay) {\n  try {\n    options.overlay = JSON.parse(parsedResourceQuery.overlay);\n  } catch (e) {\n    log.error(\"Error parsing overlay options from resource query:\", e);\n  }\n\n  // Fill in default \"true\" params for partially-specified objects.\n  if (_typeof(options.overlay) === \"object\") {\n    options.overlay = _objectSpread({\n      errors: true,\n      warnings: true,\n      runtimeErrors: true\n    }, options.overlay);\n    decodeOverlayOptions(options.overlay);\n  }\n  enabledFeatures.Overlay = true;\n}\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n\n/**\n * @param {string} level\n */\nvar setAllLogLevel = function setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n};\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var listEnabledFeatures = Object.keys(features);\n  if (!features || listEnabledFeatures.length === 0) {\n    return;\n  }\n  var logString = \"Server started:\";\n\n  // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n  for (var i = 0; i < listEnabledFeatures.length; i++) {\n    var key = listEnabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  }\n  // replace last comma with a period\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\nlogEnabledFeatures(enabledFeatures);\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar overlay = typeof window !== \"undefined\" ? createOverlay(_typeof(options.overlay) === \"object\" ? {\n  trustedTypesPolicyName: options.overlay.trustedTypesPolicyName,\n  catchRuntimeError: options.overlay.runtimeErrors\n} : {\n  trustedTypesPolicyName: false,\n  catchRuntimeError: options.overlay\n}) : {\n  send: function send() {}\n};\n\n/**\n * @param {Options} options\n * @param {Status} currentStatus\n */\nvar reloadApp = function reloadApp(_ref, currentStatus) {\n  var hot = _ref.hot,\n    liveReload = _ref.liveReload;\n  if (currentStatus.isUnloading) {\n    return;\n  }\n  var currentHash = currentStatus.currentHash,\n    previousHash = currentStatus.previousHash;\n  var isInitial = currentHash.indexOf(/** @type {string} */previousHash) >= 0;\n  if (isInitial) {\n    return;\n  }\n\n  /**\n   * @param {Window} rootWindow\n   * @param {number} intervalId\n   */\n  function applyReload(rootWindow, intervalId) {\n    clearInterval(intervalId);\n    log.info(\"App updated. Reloading...\");\n    rootWindow.location.reload();\n  }\n  var search = self.location.search.toLowerCase();\n  var allowToHot = search.indexOf(\"webpack-dev-server-hot=false\") === -1;\n  var allowToLiveReload = search.indexOf(\"webpack-dev-server-live-reload=false\") === -1;\n  if (hot && allowToHot) {\n    log.info(\"App hot update...\");\n    hotEmitter.emit(\"webpackHotUpdate\", currentStatus.currentHash);\n    if (typeof self !== \"undefined\" && self.window) {\n      // broadcast update to window\n      self.postMessage(\"webpackHotUpdate\".concat(currentStatus.currentHash), \"*\");\n    }\n  }\n  // allow refreshing the page only if liveReload isn't disabled\n  else if (liveReload && allowToLiveReload) {\n    var rootWindow = self;\n\n    // use parent window for reload (in case we're in an iframe with no valid src)\n    var intervalId = self.setInterval(function () {\n      if (rootWindow.location.protocol !== \"about:\") {\n        // reload immediately if protocol is valid\n        applyReload(rootWindow, intervalId);\n      } else {\n        rootWindow = rootWindow.parent;\n        if (rootWindow.parent === rootWindow) {\n          // if parent equals current window we've reached the root which would continue forever, so trigger a reload anyways\n          applyReload(rootWindow, intervalId);\n        }\n      }\n    });\n  }\n};\nvar ansiRegex = new RegExp([\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\"].join(\"|\"), \"g\");\n\n/**\n *\n * Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string.\n * Adapted from code originally released by Sindre Sorhus\n * Licensed the MIT License\n *\n * @param {string} string\n * @return {string}\n */\nvar stripAnsi = function stripAnsi(string) {\n  if (typeof string !== \"string\") {\n    throw new TypeError(\"Expected a `string`, got `\".concat(_typeof(string), \"`\"));\n  }\n  return string.replace(ansiRegex, \"\");\n};\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n    options.hot = true;\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n    options.liveReload = true;\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\");\n\n    // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Invalid\");\n  },\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n    options.overlay = value;\n    decodeOverlayOptions(options.overlay);\n  },\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n    options.reconnect = value;\n  },\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n    if (isProgressSupported()) {\n      if (typeof options.progress === \"string\") {\n        var progress = document.querySelector(\"wds-progress\");\n        if (!progress) {\n          defineProgressElement();\n          progress = document.createElement(\"wds-progress\");\n          document.body.appendChild(progress);\n        }\n        progress.setAttribute(\"progress\", data.percent);\n        progress.setAttribute(\"type\", options.progress);\n      }\n    }\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n        header = _formatProblem.header,\n        body = _formatProblem.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Warnings\", printableWarnings);\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n    var overlayWarningsSetting = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n    if (overlayWarningsSetting) {\n      var warningsToDisplay = typeof overlayWarningsSetting === \"function\" ? _warnings.filter(overlayWarningsSetting) : _warnings;\n      if (warningsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"warning\",\n          messages: _warnings\n        });\n      }\n    }\n    if (params && params.preventReloading) {\n      return;\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n        header = _formatProblem2.header,\n        body = _formatProblem2.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Errors\", printableErrors);\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n    var overlayErrorsSettings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n    if (overlayErrorsSettings) {\n      var errorsToDisplay = typeof overlayErrorsSettings === \"function\" ? _errors.filter(overlayErrorsSettings) : _errors;\n      if (errorsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"error\",\n          messages: _errors\n        });\n      }\n    }\n  },\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Close\");\n  }\n};\n\n/**\n * @param {{ protocol?: string, auth?: string, hostname?: string, port?: string, pathname?: string, search?: string, hash?: string, slashes?: boolean }} objURL\n * @returns {string}\n */\nvar formatURL = function formatURL(objURL) {\n  var protocol = objURL.protocol || \"\";\n  if (protocol && protocol.substr(-1) !== \":\") {\n    protocol += \":\";\n  }\n  var auth = objURL.auth || \"\";\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, \":\");\n    auth += \"@\";\n  }\n  var host = \"\";\n  if (objURL.hostname) {\n    host = auth + (objURL.hostname.indexOf(\":\") === -1 ? objURL.hostname : \"[\".concat(objURL.hostname, \"]\"));\n    if (objURL.port) {\n      host += \":\".concat(objURL.port);\n    }\n  }\n  var pathname = objURL.pathname || \"\";\n  if (objURL.slashes) {\n    host = \"//\".concat(host || \"\");\n    if (pathname && pathname.charAt(0) !== \"/\") {\n      pathname = \"/\".concat(pathname);\n    }\n  } else if (!host) {\n    host = \"\";\n  }\n  var search = objURL.search || \"\";\n  if (search && search.charAt(0) !== \"?\") {\n    search = \"?\".concat(search);\n  }\n  var hash = objURL.hash || \"\";\n  if (hash && hash.charAt(0) !== \"#\") {\n    hash = \"#\".concat(hash);\n  }\n  pathname = pathname.replace(/[?#]/g,\n  /**\n   * @param {string} match\n   * @returns {string}\n   */\n  function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace(\"#\", \"%23\");\n  return \"\".concat(protocol).concat(host).concat(pathname).concat(search).concat(hash);\n};\n\n/**\n * @param {URL & { fromCurrentScript?: boolean }} parsedURL\n * @returns {string}\n */\nvar createSocketURL = function createSocketURL(parsedURL) {\n  var hostname = parsedURL.hostname;\n\n  // Node.js module parses it as `::`\n  // `new URL(urlString, [baseURLString])` parses it as '[::]'\n  var isInAddrAny = hostname === \"0.0.0.0\" || hostname === \"::\" || hostname === \"[::]\";\n\n  // why do we need this check?\n  // hostname n/a for file protocol (example, when using electron, ionic)\n  // see: https://github.com/webpack/webpack-dev-server/pull/384\n  if (isInAddrAny && self.location.hostname && self.location.protocol.indexOf(\"http\") === 0) {\n    hostname = self.location.hostname;\n  }\n  var socketURLProtocol = parsedURL.protocol || self.location.protocol;\n\n  // When https is used in the app, secure web sockets are always necessary because the browser doesn't accept non-secure web sockets.\n  if (socketURLProtocol === \"auto:\" || hostname && isInAddrAny && self.location.protocol === \"https:\") {\n    socketURLProtocol = self.location.protocol;\n  }\n  socketURLProtocol = socketURLProtocol.replace(/^(?:http|.+-extension|file)/i, \"ws\");\n  var socketURLAuth = \"\";\n\n  // `new URL(urlString, [baseURLstring])` doesn't have `auth` property\n  // Parse authentication credentials in case we need them\n  if (parsedURL.username) {\n    socketURLAuth = parsedURL.username;\n\n    // Since HTTP basic authentication does not allow empty username,\n    // we only include password if the username is not empty.\n    if (parsedURL.password) {\n      // Result: <username>:<password>\n      socketURLAuth = socketURLAuth.concat(\":\", parsedURL.password);\n    }\n  }\n\n  // In case the host is a raw IPv6 address, it can be enclosed in\n  // the brackets as the brackets are needed in the final URL string.\n  // Need to remove those as url.format blindly adds its own set of brackets\n  // if the host string contains colons. That would lead to non-working\n  // double brackets (e.g. [[::]]) host\n  //\n  // All of these web socket url params are optionally passed in through resourceQuery,\n  // so we need to fall back to the default if they are not provided\n  var socketURLHostname = (hostname || self.location.hostname || \"localhost\").replace(/^\\[(.*)\\]$/, \"$1\");\n  var socketURLPort = parsedURL.port;\n  if (!socketURLPort || socketURLPort === \"0\") {\n    socketURLPort = self.location.port;\n  }\n\n  // If path is provided it'll be passed in via the resourceQuery as a\n  // query param so it has to be parsed out of the querystring in order for the\n  // client to open the socket to the correct location.\n  var socketURLPathname = \"/ws\";\n  if (parsedURL.pathname && !parsedURL.fromCurrentScript) {\n    socketURLPathname = parsedURL.pathname;\n  }\n  return formatURL({\n    protocol: socketURLProtocol,\n    auth: socketURLAuth,\n    hostname: socketURLHostname,\n    port: socketURLPort,\n    pathname: socketURLPathname,\n    slashes: true\n  });\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);\nexport { getCurrentScriptSource, parseURL, createSocketURL };"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIwB,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIyB,OAAO,CAACxB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK5B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC6B,IAAI,CAAC3B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIyB,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIM,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK7B,CAAC,GAAG8B,MAAM,GAAGC,MAAM,EAAE9B,CAAC,CAAC;AAAE;AAC3T,SAASwB,OAAOA,CAACpB,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOoB,OAAO,GAAG,UAAU,IAAI,OAAOC,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACM,QAAQ,GAAG,UAAU3B,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOqB,MAAM,IAAIrB,CAAC,CAAC4B,WAAW,KAAKP,MAAM,IAAIrB,CAAC,KAAKqB,MAAM,CAACQ,SAAS,GAAG,QAAQ,GAAG,OAAO7B,CAAC;EAAE,CAAC,EAAEoB,OAAO,CAACpB,CAAC,CAAC;AAAE;AAC7T;AACA;AACA,OAAO8B,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,aAAa,EAAEC,aAAa,QAAQ,cAAc;AAC3D,SAASC,GAAG,EAAEC,WAAW,QAAQ,gBAAgB;AACjD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,mBAAmB,EAAEC,qBAAqB,QAAQ,eAAe;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,cAAc,EAAE;EACvE,IAAIrB,OAAO,CAACqB,cAAc,CAAC,KAAK,QAAQ,EAAE;IACxC,CAAC,UAAU,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAChC,OAAO,CAAC,UAAUiC,QAAQ,EAAE;MAClE,IAAI,OAAOD,cAAc,CAACC,QAAQ,CAAC,KAAK,QAAQ,EAAE;QAChD,IAAIC,2BAA2B,GAAGC,kBAAkB,CAACH,cAAc,CAACC,QAAQ,CAAC,CAAC;;QAE9E;QACAD,cAAc,CAACC,QAAQ,CAAC,GAAG,IAAIG,QAAQ,CAAC,SAAS,EAAE,iBAAiB,CAACC,MAAM,CAACH,2BAA2B,EAAE,oCAAoC,CAAC,CAAC;MACjJ;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,IAAII,MAAM,GAAG;EACXC,WAAW,EAAE,KAAK;EAClB;EACAC,WAAW,EAAEC;AACf,CAAC;;AAED;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;EAC7D;EACA;EACA,IAAIC,QAAQ,CAACC,aAAa,EAAE;IAC1B,OAAOD,QAAQ,CAACC,aAAa,CAACC,YAAY,CAAC,KAAK,CAAC;EACnD;;EAEA;EACA,IAAIC,cAAc,GAAGH,QAAQ,CAACI,OAAO,IAAI,EAAE;EAC3C,IAAIC,qBAAqB,GAAGC,KAAK,CAAC7B,SAAS,CAAC5B,MAAM,CAACsB,IAAI,CAACgC,cAAc,EAAE,UAAUI,OAAO,EAAE;IACzF,OAAOA,OAAO,CAACL,YAAY,CAAC,KAAK,CAAC;EACpC,CAAC,CAAC;EACF,IAAIG,qBAAqB,CAACjD,MAAM,GAAG,CAAC,EAAE;IACpC,IAAI6C,aAAa,GAAGI,qBAAqB,CAACA,qBAAqB,CAACjD,MAAM,GAAG,CAAC,CAAC;IAC3E,OAAO6C,aAAa,CAACC,YAAY,CAAC,KAAK,CAAC;EAC1C;;EAEA;EACA,MAAM,IAAIM,KAAK,CAAC,2DAA2D,CAAC;AAC9E,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,aAAa,EAAE;EAC9C;EACA,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAI,OAAOD,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,EAAE,EAAE;IAC7D,IAAIE,YAAY,GAAGF,aAAa,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACpD,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,YAAY,CAACxD,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC5C,IAAIiD,IAAI,GAAGH,YAAY,CAAC9C,CAAC,CAAC,CAACgD,KAAK,CAAC,GAAG,CAAC;MACrCH,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGvB,kBAAkB,CAACuB,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC,MAAM;IACL;IACA,IAAIC,YAAY,GAAGjB,sBAAsB,CAAC,CAAC;IAC3C,IAAIkB,eAAe;IACnB,IAAI;MACF;MACA;MACA;MACAA,eAAe,GAAG,IAAIC,GAAG,CAACF,YAAY,EAAEG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC7D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACA;IAAA;IAEF,IAAIL,eAAe,EAAE;MACnBN,MAAM,GAAGM,eAAe;MACxBN,MAAM,CAACY,iBAAiB,GAAG,IAAI;IACjC;EACF;EACA,OAAOZ,MAAM;AACf,CAAC;AACD,IAAIa,mBAAmB,GAAGf,QAAQ,CAACgB,eAAe,CAAC;AACnD,IAAIC,eAAe,GAAG;EACpB,wBAAwB,EAAE,KAAK;EAC/B,gBAAgB,EAAE,KAAK;EACvBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,IAAIC,OAAO,GAAG;EACZC,GAAG,EAAE,KAAK;EACVC,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE;AACX,CAAC;AACD,IAAIT,mBAAmB,CAACM,GAAG,KAAK,MAAM,EAAE;EACtCD,OAAO,CAACC,GAAG,GAAG,IAAI;EAClBJ,eAAe,CAAC,wBAAwB,CAAC,GAAG,IAAI;AAClD;AACA,IAAIF,mBAAmB,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;EACjDK,OAAO,CAACE,UAAU,GAAG,IAAI;EACzBL,eAAe,CAAC,gBAAgB,CAAC,GAAG,IAAI;AAC1C;AACA,IAAIF,mBAAmB,CAACQ,QAAQ,KAAK,MAAM,EAAE;EAC3CH,OAAO,CAACG,QAAQ,GAAG,IAAI;EACvBN,eAAe,CAACC,QAAQ,GAAG,IAAI;AACjC;AACA,IAAIH,mBAAmB,CAACS,OAAO,EAAE;EAC/B,IAAI;IACFJ,OAAO,CAACI,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACX,mBAAmB,CAACS,OAAO,CAAC;EAC3D,CAAC,CAAC,OAAO3F,CAAC,EAAE;IACVyC,GAAG,CAACuC,KAAK,CAAC,oDAAoD,EAAEhF,CAAC,CAAC;EACpE;;EAEA;EACA,IAAI0B,OAAO,CAAC6D,OAAO,CAACI,OAAO,CAAC,KAAK,QAAQ,EAAE;IACzCJ,OAAO,CAACI,OAAO,GAAG/E,aAAa,CAAC;MAC9BkF,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE;IACjB,CAAC,EAAET,OAAO,CAACI,OAAO,CAAC;IACnB7C,oBAAoB,CAACyC,OAAO,CAACI,OAAO,CAAC;EACvC;EACAP,eAAe,CAACE,OAAO,GAAG,IAAI;AAChC;AACA,IAAIJ,mBAAmB,CAACe,OAAO,EAAE;EAC/BV,OAAO,CAACU,OAAO,GAAGf,mBAAmB,CAACe,OAAO;AAC/C;AACA,IAAI,OAAOf,mBAAmB,CAACgB,SAAS,KAAK,WAAW,EAAE;EACxDX,OAAO,CAACW,SAAS,GAAGlE,MAAM,CAACkD,mBAAmB,CAACgB,SAAS,CAAC;AAC3D;;AAEA;AACA;AACA;AACA,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD;EACAhE,aAAa,CAACM,WAAW,CAAC0D,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,KAAK,GAAG,MAAM,GAAGA,KAAK,CAAC;EAClF1D,WAAW,CAAC0D,KAAK,CAAC;AACpB,CAAC;AACD,IAAIb,OAAO,CAACU,OAAO,EAAE;EACnBE,cAAc,CAACZ,OAAO,CAACU,OAAO,CAAC;AACjC;AACA,IAAII,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;EAC7D,IAAIC,mBAAmB,GAAGpG,MAAM,CAACC,IAAI,CAACkG,QAAQ,CAAC;EAC/C,IAAI,CAACA,QAAQ,IAAIC,mBAAmB,CAACzF,MAAM,KAAK,CAAC,EAAE;IACjD;EACF;EACA,IAAI0F,SAAS,GAAG,iBAAiB;;EAEjC;EACA,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,mBAAmB,CAACzF,MAAM,EAAEU,CAAC,EAAE,EAAE;IACnD,IAAIiF,GAAG,GAAGF,mBAAmB,CAAC/E,CAAC,CAAC;IAChCgF,SAAS,IAAI,GAAG,CAACpD,MAAM,CAACqD,GAAG,EAAE,GAAG,CAAC,CAACrD,MAAM,CAACkD,QAAQ,CAACG,GAAG,CAAC,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,CAAC;EACvF;EACA;EACAD,SAAS,GAAGA,SAAS,CAACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACnB,MAAM,CAAC,GAAG,CAAC;EAC9CX,GAAG,CAACiE,IAAI,CAACF,SAAS,CAAC;AACrB,CAAC;AACDH,kBAAkB,CAACjB,eAAe,CAAC;AACnCP,IAAI,CAAC8B,gBAAgB,CAAC,cAAc,EAAE,YAAY;EAChDtD,MAAM,CAACC,WAAW,GAAG,IAAI;AAC3B,CAAC,CAAC;AACF,IAAIqC,OAAO,GAAG,OAAOiB,MAAM,KAAK,WAAW,GAAGpE,aAAa,CAACd,OAAO,CAAC6D,OAAO,CAACI,OAAO,CAAC,KAAK,QAAQ,GAAG;EAClGkB,sBAAsB,EAAEtB,OAAO,CAACI,OAAO,CAACkB,sBAAsB;EAC9DC,iBAAiB,EAAEvB,OAAO,CAACI,OAAO,CAACK;AACrC,CAAC,GAAG;EACFa,sBAAsB,EAAE,KAAK;EAC7BC,iBAAiB,EAAEvB,OAAO,CAACI;AAC7B,CAAC,CAAC,GAAG;EACHoB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAEC,aAAa,EAAE;EACtD,IAAI1B,GAAG,GAAGyB,IAAI,CAACzB,GAAG;IAChBC,UAAU,GAAGwB,IAAI,CAACxB,UAAU;EAC9B,IAAIyB,aAAa,CAAC5D,WAAW,EAAE;IAC7B;EACF;EACA,IAAIC,WAAW,GAAG2D,aAAa,CAAC3D,WAAW;IACzC4D,YAAY,GAAGD,aAAa,CAACC,YAAY;EAC3C,IAAIC,SAAS,GAAG7D,WAAW,CAAC8D,OAAO,CAAC,qBAAqBF,YAAY,CAAC,IAAI,CAAC;EAC3E,IAAIC,SAAS,EAAE;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASE,WAAWA,CAACC,UAAU,EAAEC,UAAU,EAAE;IAC3CC,aAAa,CAACD,UAAU,CAAC;IACzB/E,GAAG,CAACiE,IAAI,CAAC,2BAA2B,CAAC;IACrCa,UAAU,CAACzC,QAAQ,CAAC4C,MAAM,CAAC,CAAC;EAC9B;EACA,IAAIC,MAAM,GAAG9C,IAAI,CAACC,QAAQ,CAAC6C,MAAM,CAACC,WAAW,CAAC,CAAC;EAC/C,IAAIC,UAAU,GAAGF,MAAM,CAACN,OAAO,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;EACtE,IAAIS,iBAAiB,GAAGH,MAAM,CAACN,OAAO,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC;EACrF,IAAI7B,GAAG,IAAIqC,UAAU,EAAE;IACrBpF,GAAG,CAACiE,IAAI,CAAC,mBAAmB,CAAC;IAC7BrE,UAAU,CAAC0F,IAAI,CAAC,kBAAkB,EAAEb,aAAa,CAAC3D,WAAW,CAAC;IAC9D,IAAI,OAAOsB,IAAI,KAAK,WAAW,IAAIA,IAAI,CAAC+B,MAAM,EAAE;MAC9C;MACA/B,IAAI,CAACmD,WAAW,CAAC,kBAAkB,CAAC5E,MAAM,CAAC8D,aAAa,CAAC3D,WAAW,CAAC,EAAE,GAAG,CAAC;IAC7E;EACF;EACA;EAAA,KACK,IAAIkC,UAAU,IAAIqC,iBAAiB,EAAE;IACxC,IAAIP,UAAU,GAAG1C,IAAI;;IAErB;IACA,IAAI2C,UAAU,GAAG3C,IAAI,CAACoD,WAAW,CAAC,YAAY;MAC5C,IAAIV,UAAU,CAACzC,QAAQ,CAACoD,QAAQ,KAAK,QAAQ,EAAE;QAC7C;QACAZ,WAAW,CAACC,UAAU,EAAEC,UAAU,CAAC;MACrC,CAAC,MAAM;QACLD,UAAU,GAAGA,UAAU,CAACY,MAAM;QAC9B,IAAIZ,UAAU,CAACY,MAAM,KAAKZ,UAAU,EAAE;UACpC;UACAD,WAAW,CAACC,UAAU,EAAEC,UAAU,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIY,SAAS,GAAG,IAAIC,MAAM,CAAC,CAAC,8HAA8H,EAAE,0DAA0D,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;AAEvO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,MAAM,EAAE;EACzC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAI1G,SAAS,CAAC,4BAA4B,CAACsB,MAAM,CAAC1B,OAAO,CAAC8G,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;EAChF;EACA,OAAOA,MAAM,CAACC,OAAO,CAACL,SAAS,EAAE,EAAE,CAAC;AACtC,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBlD,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,IAAIN,mBAAmB,CAACM,GAAG,KAAK,OAAO,EAAE;MACvC;IACF;IACAD,OAAO,CAACC,GAAG,GAAG,IAAI;EACpB,CAAC;EACDC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,IAAIP,mBAAmB,CAAC,aAAa,CAAC,KAAK,OAAO,EAAE;MAClD;IACF;IACAK,OAAO,CAACE,UAAU,GAAG,IAAI;EAC3B,CAAC;EACDkD,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1BlG,GAAG,CAACiE,IAAI,CAAC,6BAA6B,CAAC;;IAEvC;IACA,IAAInB,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACoB,IAAI,CAAC;QACX6B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACAjG,WAAW,CAAC,SAAS,CAAC;EACxB,CAAC;EACD;AACF;AACA;EACEkG,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;IACzBzF,MAAM,CAAC8D,YAAY,GAAG9D,MAAM,CAACE,WAAW;IACxCF,MAAM,CAACE,WAAW,GAAGuF,KAAK;EAC5B,CAAC;EACD7C,OAAO,EAAEE,cAAc;EACvB;AACF;AACA;EACER,OAAO,EAAE,SAASA,OAAOA,CAACtE,KAAK,EAAE;IAC/B,IAAI,OAAOqC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IACA6B,OAAO,CAACI,OAAO,GAAGtE,KAAK;IACvByB,oBAAoB,CAACyC,OAAO,CAACI,OAAO,CAAC;EACvC,CAAC;EACD;AACF;AACA;EACEO,SAAS,EAAE,SAASA,SAASA,CAAC7E,KAAK,EAAE;IACnC,IAAI6D,mBAAmB,CAACgB,SAAS,KAAK,OAAO,EAAE;MAC7C;IACF;IACAX,OAAO,CAACW,SAAS,GAAG7E,KAAK;EAC3B,CAAC;EACD;AACF;AACA;EACEqE,QAAQ,EAAE,SAASA,QAAQA,CAACrE,KAAK,EAAE;IACjCkE,OAAO,CAACG,QAAQ,GAAGrE,KAAK;EAC1B,CAAC;EACD;AACF;AACA;EACE,iBAAiB,EAAE,SAAS0H,cAAcA,CAACC,IAAI,EAAE;IAC/C,IAAIzD,OAAO,CAACG,QAAQ,EAAE;MACpBjD,GAAG,CAACiE,IAAI,CAAC,EAAE,CAACtD,MAAM,CAAC4F,IAAI,CAACC,UAAU,GAAG,GAAG,CAAC7F,MAAM,CAAC4F,IAAI,CAACC,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC7F,MAAM,CAAC4F,IAAI,CAACE,OAAO,EAAE,MAAM,CAAC,CAAC9F,MAAM,CAAC4F,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAAC;IAClI;IACA,IAAIvG,mBAAmB,CAAC,CAAC,EAAE;MACzB,IAAI,OAAO2C,OAAO,CAACG,QAAQ,KAAK,QAAQ,EAAE;QACxC,IAAIA,QAAQ,GAAGhC,QAAQ,CAAC0F,aAAa,CAAC,cAAc,CAAC;QACrD,IAAI,CAAC1D,QAAQ,EAAE;UACb7C,qBAAqB,CAAC,CAAC;UACvB6C,QAAQ,GAAGhC,QAAQ,CAAC2F,aAAa,CAAC,cAAc,CAAC;UACjD3F,QAAQ,CAAC4F,IAAI,CAACC,WAAW,CAAC7D,QAAQ,CAAC;QACrC;QACAA,QAAQ,CAAC8D,YAAY,CAAC,UAAU,EAAER,IAAI,CAACE,OAAO,CAAC;QAC/CxD,QAAQ,CAAC8D,YAAY,CAAC,MAAM,EAAEjE,OAAO,CAACG,QAAQ,CAAC;MACjD;IACF;IACA/C,WAAW,CAAC,UAAU,EAAEqG,IAAI,CAAC;EAC/B,CAAC;EACD,UAAU,EAAE,SAASS,OAAOA,CAAA,EAAG;IAC7BhH,GAAG,CAACiE,IAAI,CAAC,kBAAkB,CAAC;IAC5B,IAAInB,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACoB,IAAI,CAAC;QACX6B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACAjG,WAAW,CAAC,SAAS,CAAC;EACxB,CAAC;EACD+G,EAAE,EAAE,SAASA,EAAEA,CAAA,EAAG;IAChB/G,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI4C,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACoB,IAAI,CAAC;QACX6B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA5B,SAAS,CAACzB,OAAO,EAAElC,MAAM,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACE,gBAAgB,EAAE,SAASsG,aAAaA,CAACC,IAAI,EAAE;IAC7CnH,GAAG,CAACiE,IAAI,CAAC,EAAE,CAACtD,MAAM,CAACwG,IAAI,GAAG,IAAI,CAACxG,MAAM,CAACwG,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,kDAAkD,CAAC,CAAC;IACnH/E,IAAI,CAACC,QAAQ,CAAC4C,MAAM,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;AACA;EACE3B,QAAQ,EAAE,SAASA,QAAQA,CAAC8D,SAAS,EAAEC,MAAM,EAAE;IAC7CrH,GAAG,CAACsH,IAAI,CAAC,2BAA2B,CAAC;IACrC,IAAIC,iBAAiB,GAAGH,SAAS,CAACI,GAAG,CAAC,UAAUjF,KAAK,EAAE;MACrD,IAAIkF,cAAc,GAAG3H,aAAa,CAAC,SAAS,EAAEyC,KAAK,CAAC;QAClDmF,MAAM,GAAGD,cAAc,CAACC,MAAM;QAC9Bb,IAAI,GAAGY,cAAc,CAACZ,IAAI;MAC5B,OAAO,EAAE,CAAClG,MAAM,CAAC+G,MAAM,EAAE,IAAI,CAAC,CAAC/G,MAAM,CAACmF,SAAS,CAACe,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IACF3G,WAAW,CAAC,UAAU,EAAEqH,iBAAiB,CAAC;IAC1C,KAAK,IAAIxI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwI,iBAAiB,CAAClJ,MAAM,EAAEU,CAAC,EAAE,EAAE;MACjDiB,GAAG,CAACsH,IAAI,CAACC,iBAAiB,CAACxI,CAAC,CAAC,CAAC;IAChC;IACA,IAAI4I,sBAAsB,GAAG,OAAO7E,OAAO,CAACI,OAAO,KAAK,SAAS,GAAGJ,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAACI,QAAQ;IACjI,IAAIqE,sBAAsB,EAAE;MAC1B,IAAIC,iBAAiB,GAAG,OAAOD,sBAAsB,KAAK,UAAU,GAAGP,SAAS,CAACtJ,MAAM,CAAC6J,sBAAsB,CAAC,GAAGP,SAAS;MAC3H,IAAIQ,iBAAiB,CAACvJ,MAAM,EAAE;QAC5B6E,OAAO,CAACoB,IAAI,CAAC;UACX6B,IAAI,EAAE,aAAa;UACnBxC,KAAK,EAAE,SAAS;UAChBkE,QAAQ,EAAET;QACZ,CAAC,CAAC;MACJ;IACF;IACA,IAAIC,MAAM,IAAIA,MAAM,CAACS,gBAAgB,EAAE;MACrC;IACF;IACAvD,SAAS,CAACzB,OAAO,EAAElC,MAAM,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACEyC,MAAM,EAAE,SAASA,MAAMA,CAAC0E,OAAO,EAAE;IAC/B/H,GAAG,CAACuC,KAAK,CAAC,2CAA2C,CAAC;IACtD,IAAIyF,eAAe,GAAGD,OAAO,CAACP,GAAG,CAAC,UAAUjF,KAAK,EAAE;MACjD,IAAI0F,eAAe,GAAGnI,aAAa,CAAC,OAAO,EAAEyC,KAAK,CAAC;QACjDmF,MAAM,GAAGO,eAAe,CAACP,MAAM;QAC/Bb,IAAI,GAAGoB,eAAe,CAACpB,IAAI;MAC7B,OAAO,EAAE,CAAClG,MAAM,CAAC+G,MAAM,EAAE,IAAI,CAAC,CAAC/G,MAAM,CAACmF,SAAS,CAACe,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IACF3G,WAAW,CAAC,QAAQ,EAAE8H,eAAe,CAAC;IACtC,KAAK,IAAIjJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiJ,eAAe,CAAC3J,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC/CiB,GAAG,CAACuC,KAAK,CAACyF,eAAe,CAACjJ,CAAC,CAAC,CAAC;IAC/B;IACA,IAAImJ,qBAAqB,GAAG,OAAOpF,OAAO,CAACI,OAAO,KAAK,SAAS,GAAGJ,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAACG,MAAM;IAC9H,IAAI6E,qBAAqB,EAAE;MACzB,IAAIC,eAAe,GAAG,OAAOD,qBAAqB,KAAK,UAAU,GAAGH,OAAO,CAACjK,MAAM,CAACoK,qBAAqB,CAAC,GAAGH,OAAO;MACnH,IAAII,eAAe,CAAC9J,MAAM,EAAE;QAC1B6E,OAAO,CAACoB,IAAI,CAAC;UACX6B,IAAI,EAAE,aAAa;UACnBxC,KAAK,EAAE,OAAO;UACdkE,QAAQ,EAAEE;QACZ,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD;AACF;AACA;EACExF,KAAK,EAAE,SAASA,KAAKA,CAAC6F,MAAM,EAAE;IAC5BpI,GAAG,CAACuC,KAAK,CAAC6F,MAAM,CAAC;EACnB,CAAC;EACDC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtBrI,GAAG,CAACiE,IAAI,CAAC,eAAe,CAAC;IACzB,IAAInB,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACoB,IAAI,CAAC;QACX6B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACAjG,WAAW,CAAC,OAAO,CAAC;EACtB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIoI,SAAS,GAAG,SAASA,SAASA,CAACC,MAAM,EAAE;EACzC,IAAI9C,QAAQ,GAAG8C,MAAM,CAAC9C,QAAQ,IAAI,EAAE;EACpC,IAAIA,QAAQ,IAAIA,QAAQ,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3C/C,QAAQ,IAAI,GAAG;EACjB;EACA,IAAIgD,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAI,EAAE;EAC5B,IAAIA,IAAI,EAAE;IACRA,IAAI,GAAGC,kBAAkB,CAACD,IAAI,CAAC;IAC/BA,IAAI,GAAGA,IAAI,CAACzC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAChCyC,IAAI,IAAI,GAAG;EACb;EACA,IAAIE,IAAI,GAAG,EAAE;EACb,IAAIJ,MAAM,CAACK,QAAQ,EAAE;IACnBD,IAAI,GAAGF,IAAI,IAAIF,MAAM,CAACK,QAAQ,CAAChE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG2D,MAAM,CAACK,QAAQ,GAAG,GAAG,CAACjI,MAAM,CAAC4H,MAAM,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC;IACxG,IAAIL,MAAM,CAACM,IAAI,EAAE;MACfF,IAAI,IAAI,GAAG,CAAChI,MAAM,CAAC4H,MAAM,CAACM,IAAI,CAAC;IACjC;EACF;EACA,IAAIC,QAAQ,GAAGP,MAAM,CAACO,QAAQ,IAAI,EAAE;EACpC,IAAIP,MAAM,CAACQ,OAAO,EAAE;IAClBJ,IAAI,GAAG,IAAI,CAAChI,MAAM,CAACgI,IAAI,IAAI,EAAE,CAAC;IAC9B,IAAIG,QAAQ,IAAIA,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC1CF,QAAQ,GAAG,GAAG,CAACnI,MAAM,CAACmI,QAAQ,CAAC;IACjC;EACF,CAAC,MAAM,IAAI,CAACH,IAAI,EAAE;IAChBA,IAAI,GAAG,EAAE;EACX;EACA,IAAIzD,MAAM,GAAGqD,MAAM,CAACrD,MAAM,IAAI,EAAE;EAChC,IAAIA,MAAM,IAAIA,MAAM,CAAC8D,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACtC9D,MAAM,GAAG,GAAG,CAACvE,MAAM,CAACuE,MAAM,CAAC;EAC7B;EACA,IAAIkB,IAAI,GAAGmC,MAAM,CAACnC,IAAI,IAAI,EAAE;EAC5B,IAAIA,IAAI,IAAIA,IAAI,CAAC4C,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClC5C,IAAI,GAAG,GAAG,CAACzF,MAAM,CAACyF,IAAI,CAAC;EACzB;EACA0C,QAAQ,GAAGA,QAAQ,CAAC9C,OAAO,CAAC,OAAO;EACnC;AACF;AACA;AACA;EACE,UAAUiD,KAAK,EAAE;IACf,OAAOP,kBAAkB,CAACO,KAAK,CAAC;EAClC,CAAC,CAAC;EACF/D,MAAM,GAAGA,MAAM,CAACc,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;EACnC,OAAO,EAAE,CAACrF,MAAM,CAAC8E,QAAQ,CAAC,CAAC9E,MAAM,CAACgI,IAAI,CAAC,CAAChI,MAAM,CAACmI,QAAQ,CAAC,CAACnI,MAAM,CAACuE,MAAM,CAAC,CAACvE,MAAM,CAACyF,IAAI,CAAC;AACtF,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAI8C,eAAe,GAAG,SAASA,eAAeA,CAACC,SAAS,EAAE;EACxD,IAAIP,QAAQ,GAAGO,SAAS,CAACP,QAAQ;;EAEjC;EACA;EACA,IAAIQ,WAAW,GAAGR,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,MAAM;;EAEpF;EACA;EACA;EACA,IAAIQ,WAAW,IAAIhH,IAAI,CAACC,QAAQ,CAACuG,QAAQ,IAAIxG,IAAI,CAACC,QAAQ,CAACoD,QAAQ,CAACb,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACzFgE,QAAQ,GAAGxG,IAAI,CAACC,QAAQ,CAACuG,QAAQ;EACnC;EACA,IAAIS,iBAAiB,GAAGF,SAAS,CAAC1D,QAAQ,IAAIrD,IAAI,CAACC,QAAQ,CAACoD,QAAQ;;EAEpE;EACA,IAAI4D,iBAAiB,KAAK,OAAO,IAAIT,QAAQ,IAAIQ,WAAW,IAAIhH,IAAI,CAACC,QAAQ,CAACoD,QAAQ,KAAK,QAAQ,EAAE;IACnG4D,iBAAiB,GAAGjH,IAAI,CAACC,QAAQ,CAACoD,QAAQ;EAC5C;EACA4D,iBAAiB,GAAGA,iBAAiB,CAACrD,OAAO,CAAC,8BAA8B,EAAE,IAAI,CAAC;EACnF,IAAIsD,aAAa,GAAG,EAAE;;EAEtB;EACA;EACA,IAAIH,SAAS,CAACI,QAAQ,EAAE;IACtBD,aAAa,GAAGH,SAAS,CAACI,QAAQ;;IAElC;IACA;IACA,IAAIJ,SAAS,CAACK,QAAQ,EAAE;MACtB;MACAF,aAAa,GAAGA,aAAa,CAAC3I,MAAM,CAAC,GAAG,EAAEwI,SAAS,CAACK,QAAQ,CAAC;IAC/D;EACF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,iBAAiB,GAAG,CAACb,QAAQ,IAAIxG,IAAI,CAACC,QAAQ,CAACuG,QAAQ,IAAI,WAAW,EAAE5C,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;EACvG,IAAI0D,aAAa,GAAGP,SAAS,CAACN,IAAI;EAClC,IAAI,CAACa,aAAa,IAAIA,aAAa,KAAK,GAAG,EAAE;IAC3CA,aAAa,GAAGtH,IAAI,CAACC,QAAQ,CAACwG,IAAI;EACpC;;EAEA;EACA;EACA;EACA,IAAIc,iBAAiB,GAAG,KAAK;EAC7B,IAAIR,SAAS,CAACL,QAAQ,IAAI,CAACK,SAAS,CAAC3G,iBAAiB,EAAE;IACtDmH,iBAAiB,GAAGR,SAAS,CAACL,QAAQ;EACxC;EACA,OAAOR,SAAS,CAAC;IACf7C,QAAQ,EAAE4D,iBAAiB;IAC3BZ,IAAI,EAAEa,aAAa;IACnBV,QAAQ,EAAEa,iBAAiB;IAC3BZ,IAAI,EAAEa,aAAa;IACnBZ,QAAQ,EAAEa,iBAAiB;IAC3BZ,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC;AACD,IAAIa,SAAS,GAAGV,eAAe,CAACzG,mBAAmB,CAAC;AACpD5C,MAAM,CAAC+J,SAAS,EAAE3D,eAAe,EAAEnD,OAAO,CAACW,SAAS,CAAC;AACrD,SAASzC,sBAAsB,EAAEU,QAAQ,EAAEwH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}