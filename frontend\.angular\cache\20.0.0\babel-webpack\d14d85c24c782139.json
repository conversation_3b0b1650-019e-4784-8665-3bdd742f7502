{"ast": null, "code": "import { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class PaymentService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n  }\n  createPaymentOrder(request) {\n    return this.http.post(`${environment.apiUrl}/payments/create-order`, request, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  verifyPayment(verification) {\n    return this.http.post(`${environment.apiUrl}/payments/verify`, verification, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  getPaymentStatus(orderId) {\n    return this.http.get(`${environment.apiUrl}/payments/status/${orderId}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  getMyPayments() {\n    return this.http.get(`${environment.apiUrl}/payments/my-payments`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  refundPayment(request) {\n    return this.http.post(`${environment.apiUrl}/payments/refund`, request, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  initiateRazorpayPayment(order) {\n    return new Promise((resolve, reject) => {\n      if (!window.Razorpay) {\n        reject(new Error('Razorpay SDK not loaded'));\n        return;\n      }\n      const currentUser = this.authService.currentUserValue;\n      if (!currentUser) {\n        reject(new Error('User not authenticated'));\n        return;\n      }\n      const options = {\n        key: order.key,\n        amount: order.amount * 100,\n        // Convert to smallest currency unit\n        currency: order.currency,\n        name: environment.appName,\n        description: 'Payment for services',\n        order_id: order.orderId,\n        handler: response => {\n          resolve(response);\n        },\n        prefill: {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          contact: currentUser.phone\n        },\n        theme: {\n          color: '#3f51b5'\n        },\n        modal: {\n          ondismiss: () => {\n            reject(new Error('Payment cancelled by user'));\n          }\n        }\n      };\n      const razorpayInstance = new window.Razorpay(options);\n      razorpayInstance.open();\n    });\n  }\n  processPayment(request) {\n    return new Observable(observer => {\n      // Step 1: Create order\n      this.createPaymentOrder(request).subscribe({\n        next: order => {\n          // Step 2: Open Razorpay checkout\n          this.initiateRazorpayPayment(order).then(razorpayResponse => {\n            // Step 3: Verify payment\n            const verification = {\n              orderId: razorpayResponse.razorpay_order_id,\n              paymentId: razorpayResponse.razorpay_payment_id,\n              signature: razorpayResponse.razorpay_signature\n            };\n            this.verifyPayment(verification).subscribe({\n              next: response => {\n                observer.next(response);\n                observer.complete();\n              },\n              error: error => {\n                observer.error(error);\n              }\n            });\n          }).catch(error => {\n            observer.error(error);\n          });\n        },\n        error: error => {\n          observer.error(error);\n        }\n      });\n    });\n  }\n  formatCurrency(amount, currency) {\n    const formatter = new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: currency,\n      minimumFractionDigits: 2\n    });\n    return formatter.format(amount);\n  }\n  getPaymentStatusColor(status) {\n    switch (status) {\n      case 'paid':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'failed':\n        return 'danger';\n      case 'cancelled':\n        return 'secondary';\n      case 'refunded':\n        return 'info';\n      default:\n        return 'secondary';\n    }\n  }\n  getStatusIcon(status) {\n    switch (status) {\n      case 'paid':\n        return 'check_circle';\n      case 'pending':\n        return 'schedule';\n      case 'failed':\n        return 'error';\n      case 'cancelled':\n        return 'cancel';\n      case 'refunded':\n        return 'undo';\n      default:\n        return 'help';\n    }\n  }\n  validateAmount(amount, currency) {\n    if (!amount || amount <= 0) {\n      return {\n        valid: false,\n        error: 'Amount must be greater than 0'\n      };\n    }\n    const minAmount = currency === 'INR' ? 1 : 0.5; // Minimum amounts\n    const maxAmount = currency === 'INR' ? 1000000 : 10000; // Maximum amounts\n    if (amount < minAmount) {\n      return {\n        valid: false,\n        error: `Minimum amount is ${this.formatCurrency(minAmount, currency)}`\n      };\n    }\n    if (amount > maxAmount) {\n      return {\n        valid: false,\n        error: `Maximum amount is ${this.formatCurrency(maxAmount, currency)}`\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n  convertCurrency(amount, fromCurrency, toCurrency) {\n    // Simple conversion for demo - in production, use real exchange rates\n    if (fromCurrency === toCurrency) return amount;\n    const rates = {\n      'INR_TO_USD': 0.012,\n      'USD_TO_INR': 83.0\n    };\n    const rateKey = `${fromCurrency}_TO_${toCurrency}`;\n    return rates[rateKey] ? amount * rates[rateKey] : amount;\n  }\n  handleError(error) {\n    let errorMessage = 'Payment service error occurred';\n    if (error.error instanceof ErrorEvent) {\n      errorMessage = error.error.message;\n    } else {\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    console.error('Payment Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function PaymentService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PaymentService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PaymentService,\n    factory: PaymentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Observable", "throwError", "catchError", "environment", "PaymentService", "constructor", "http", "authService", "createPaymentOrder", "request", "post", "apiUrl", "headers", "getAuthHeaders", "pipe", "handleError", "verifyPayment", "verification", "getPaymentStatus", "orderId", "get", "getMyPayments", "refundPayment", "initiateRazorpayPayment", "order", "Promise", "resolve", "reject", "window", "Razorpay", "Error", "currentUser", "currentUserValue", "options", "key", "amount", "currency", "name", "appName", "description", "order_id", "handler", "response", "prefill", "firstName", "lastName", "email", "contact", "phone", "theme", "color", "modal", "ondismiss", "razorpayInstance", "open", "processPayment", "observer", "subscribe", "next", "then", "razorpayResponse", "razorpay_order_id", "paymentId", "razorpay_payment_id", "signature", "razorpay_signature", "complete", "error", "catch", "formatCurrency", "formatter", "Intl", "NumberFormat", "style", "minimumFractionDigits", "format", "getPaymentStatusColor", "status", "getStatusIcon", "validateAmount", "valid", "minAmount", "maxAmount", "convertCurrency", "fromCurrency", "to<PERSON><PERSON><PERSON><PERSON>", "rates", "rateKey", "errorMessage", "ErrorEvent", "message", "console", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\services\\payment.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport {\n  Payment,\n  PaymentRequest,\n  PaymentOrder,\n  PaymentVerification,\n  PaymentResponse,\n  RazorpayOptions,\n  RazorpayResponse,\n  RefundRequest\n} from '../models/payment.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PaymentService {\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  createPaymentOrder(request: PaymentRequest): Observable<PaymentOrder> {\n    return this.http.post<PaymentOrder>(`${environment.apiUrl}/payments/create-order`, request, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  verifyPayment(verification: PaymentVerification): Observable<PaymentResponse> {\n    return this.http.post<PaymentResponse>(`${environment.apiUrl}/payments/verify`, verification, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  getPaymentStatus(orderId: string): Observable<{ payment: Payment | null }> {\n    return this.http.get<{ payment: Payment | null }>(`${environment.apiUrl}/payments/status/${orderId}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  getMyPayments(): Observable<{ payments: Payment[] }> {\n    return this.http.get<{ payments: Payment[] }>(`${environment.apiUrl}/payments/my-payments`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  refundPayment(request: RefundRequest): Observable<PaymentResponse> {\n    return this.http.post<PaymentResponse>(`${environment.apiUrl}/payments/refund`, request, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  initiateRazorpayPayment(order: PaymentOrder): Promise<RazorpayResponse> {\n    return new Promise((resolve, reject) => {\n      if (!window.Razorpay) {\n        reject(new Error('Razorpay SDK not loaded'));\n        return;\n      }\n\n      const currentUser = this.authService.currentUserValue;\n      if (!currentUser) {\n        reject(new Error('User not authenticated'));\n        return;\n      }\n\n      const options: RazorpayOptions = {\n        key: order.key,\n        amount: order.amount * 100, // Convert to smallest currency unit\n        currency: order.currency,\n        name: environment.appName,\n        description: 'Payment for services',\n        order_id: order.orderId,\n        handler: (response: RazorpayResponse) => {\n          resolve(response);\n        },\n        prefill: {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          contact: currentUser.phone\n        },\n        theme: {\n          color: '#3f51b5'\n        },\n        modal: {\n          ondismiss: () => {\n            reject(new Error('Payment cancelled by user'));\n          }\n        }\n      };\n\n      const razorpayInstance = new window.Razorpay(options);\n      razorpayInstance.open();\n    });\n  }\n\n  processPayment(request: PaymentRequest): Observable<PaymentResponse> {\n    return new Observable(observer => {\n      // Step 1: Create order\n      this.createPaymentOrder(request).subscribe({\n        next: (order) => {\n          // Step 2: Open Razorpay checkout\n          this.initiateRazorpayPayment(order).then((razorpayResponse) => {\n            // Step 3: Verify payment\n            const verification: PaymentVerification = {\n              orderId: razorpayResponse.razorpay_order_id,\n              paymentId: razorpayResponse.razorpay_payment_id,\n              signature: razorpayResponse.razorpay_signature\n            };\n\n            this.verifyPayment(verification).subscribe({\n              next: (response) => {\n                observer.next(response);\n                observer.complete();\n              },\n              error: (error) => {\n                observer.error(error);\n              }\n            });\n          }).catch((error) => {\n            observer.error(error);\n          });\n        },\n        error: (error) => {\n          observer.error(error);\n        }\n      });\n    });\n  }\n\n  formatCurrency(amount: number, currency: string): string {\n    const formatter = new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: currency,\n      minimumFractionDigits: 2\n    });\n    return formatter.format(amount);\n  }\n\n  getPaymentStatusColor(status: string): string {\n    switch (status) {\n      case 'paid': return 'success';\n      case 'pending': return 'warning';\n      case 'failed': return 'danger';\n      case 'cancelled': return 'secondary';\n      case 'refunded': return 'info';\n      default: return 'secondary';\n    }\n  }\n\n  getStatusIcon(status: string): string {\n    switch (status) {\n      case 'paid': return 'check_circle';\n      case 'pending': return 'schedule';\n      case 'failed': return 'error';\n      case 'cancelled': return 'cancel';\n      case 'refunded': return 'undo';\n      default: return 'help';\n    }\n  }\n\n\n\n  validateAmount(amount: number, currency: string): { valid: boolean; error?: string } {\n    if (!amount || amount <= 0) {\n      return { valid: false, error: 'Amount must be greater than 0' };\n    }\n\n    const minAmount = currency === 'INR' ? 1 : 0.5; // Minimum amounts\n    const maxAmount = currency === 'INR' ? 1000000 : 10000; // Maximum amounts\n\n    if (amount < minAmount) {\n      return { valid: false, error: `Minimum amount is ${this.formatCurrency(minAmount, currency)}` };\n    }\n\n    if (amount > maxAmount) {\n      return { valid: false, error: `Maximum amount is ${this.formatCurrency(maxAmount, currency)}` };\n    }\n\n    return { valid: true };\n  }\n\n  convertCurrency(amount: number, fromCurrency: string, toCurrency: string): number {\n    // Simple conversion for demo - in production, use real exchange rates\n    if (fromCurrency === toCurrency) return amount;\n    \n    const rates: { [key: string]: number } = {\n      'INR_TO_USD': 0.012,\n      'USD_TO_INR': 83.0\n    };\n\n    const rateKey = `${fromCurrency}_TO_${toCurrency}`;\n    return rates[rateKey] ? amount * rates[rateKey] : amount;\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'Payment service error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      errorMessage = error.error.message;\n    } else {\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    \n    console.error('Payment Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAa,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;;;;AAgB5D,OAAM,MAAOC,cAAc;EACzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHC,kBAAkBA,CAACC,OAAuB;IACxC,OAAO,IAAI,CAACH,IAAI,CAACI,IAAI,CAAe,GAAGP,WAAW,CAACQ,MAAM,wBAAwB,EAAEF,OAAO,EAAE;MAC1FG,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACH;EAEAC,aAAaA,CAACC,YAAiC;IAC7C,OAAO,IAAI,CAACX,IAAI,CAACI,IAAI,CAAkB,GAAGP,WAAW,CAACQ,MAAM,kBAAkB,EAAEM,YAAY,EAAE;MAC5FL,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACH;EAEAG,gBAAgBA,CAACC,OAAe;IAC9B,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAA8B,GAAGjB,WAAW,CAACQ,MAAM,oBAAoBQ,OAAO,EAAE,EAAE;MACpGP,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACH;EAEAM,aAAaA,CAAA;IACX,OAAO,IAAI,CAACf,IAAI,CAACc,GAAG,CAA0B,GAAGjB,WAAW,CAACQ,MAAM,uBAAuB,EAAE;MAC1FC,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACH;EAEAO,aAAaA,CAACb,OAAsB;IAClC,OAAO,IAAI,CAACH,IAAI,CAACI,IAAI,CAAkB,GAAGP,WAAW,CAACQ,MAAM,kBAAkB,EAAEF,OAAO,EAAE;MACvFG,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACH;EAEAQ,uBAAuBA,CAACC,KAAmB;IACzC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACC,MAAM,CAACC,QAAQ,EAAE;QACpBF,MAAM,CAAC,IAAIG,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC5C;MACF;MAEA,MAAMC,WAAW,GAAG,IAAI,CAACxB,WAAW,CAACyB,gBAAgB;MACrD,IAAI,CAACD,WAAW,EAAE;QAChBJ,MAAM,CAAC,IAAIG,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC3C;MACF;MAEA,MAAMG,OAAO,GAAoB;QAC/BC,GAAG,EAAEV,KAAK,CAACU,GAAG;QACdC,MAAM,EAAEX,KAAK,CAACW,MAAM,GAAG,GAAG;QAAE;QAC5BC,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;QACxBC,IAAI,EAAElC,WAAW,CAACmC,OAAO;QACzBC,WAAW,EAAE,sBAAsB;QACnCC,QAAQ,EAAEhB,KAAK,CAACL,OAAO;QACvBsB,OAAO,EAAGC,QAA0B,IAAI;UACtChB,OAAO,CAACgB,QAAQ,CAAC;QACnB,CAAC;QACDC,OAAO,EAAE;UACPN,IAAI,EAAE,GAAGN,WAAW,CAACa,SAAS,IAAIb,WAAW,CAACc,QAAQ,EAAE;UACxDC,KAAK,EAAEf,WAAW,CAACe,KAAK;UACxBC,OAAO,EAAEhB,WAAW,CAACiB;SACtB;QACDC,KAAK,EAAE;UACLC,KAAK,EAAE;SACR;QACDC,KAAK,EAAE;UACLC,SAAS,EAAEA,CAAA,KAAK;YACdzB,MAAM,CAAC,IAAIG,KAAK,CAAC,2BAA2B,CAAC,CAAC;UAChD;;OAEH;MAED,MAAMuB,gBAAgB,GAAG,IAAIzB,MAAM,CAACC,QAAQ,CAACI,OAAO,CAAC;MACrDoB,gBAAgB,CAACC,IAAI,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAC9C,OAAuB;IACpC,OAAO,IAAIT,UAAU,CAACwD,QAAQ,IAAG;MAC/B;MACA,IAAI,CAAChD,kBAAkB,CAACC,OAAO,CAAC,CAACgD,SAAS,CAAC;QACzCC,IAAI,EAAGlC,KAAK,IAAI;UACd;UACA,IAAI,CAACD,uBAAuB,CAACC,KAAK,CAAC,CAACmC,IAAI,CAAEC,gBAAgB,IAAI;YAC5D;YACA,MAAM3C,YAAY,GAAwB;cACxCE,OAAO,EAAEyC,gBAAgB,CAACC,iBAAiB;cAC3CC,SAAS,EAAEF,gBAAgB,CAACG,mBAAmB;cAC/CC,SAAS,EAAEJ,gBAAgB,CAACK;aAC7B;YAED,IAAI,CAACjD,aAAa,CAACC,YAAY,CAAC,CAACwC,SAAS,CAAC;cACzCC,IAAI,EAAGhB,QAAQ,IAAI;gBACjBc,QAAQ,CAACE,IAAI,CAAChB,QAAQ,CAAC;gBACvBc,QAAQ,CAACU,QAAQ,EAAE;cACrB,CAAC;cACDC,KAAK,EAAGA,KAAK,IAAI;gBACfX,QAAQ,CAACW,KAAK,CAACA,KAAK,CAAC;cACvB;aACD,CAAC;UACJ,CAAC,CAAC,CAACC,KAAK,CAAED,KAAK,IAAI;YACjBX,QAAQ,CAACW,KAAK,CAACA,KAAK,CAAC;UACvB,CAAC,CAAC;QACJ,CAAC;QACDA,KAAK,EAAGA,KAAK,IAAI;UACfX,QAAQ,CAACW,KAAK,CAACA,KAAK,CAAC;QACvB;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAE,cAAcA,CAAClC,MAAc,EAAEC,QAAgB;IAC7C,MAAMkC,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAC/CC,KAAK,EAAE,UAAU;MACjBrC,QAAQ,EAAEA,QAAQ;MAClBsC,qBAAqB,EAAE;KACxB,CAAC;IACF,OAAOJ,SAAS,CAACK,MAAM,CAACxC,MAAM,CAAC;EACjC;EAEAyC,qBAAqBA,CAACC,MAAc;IAClC,QAAQA,MAAM;MACZ,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC,KAAK,UAAU;QAAE,OAAO,MAAM;MAC9B;QAAS,OAAO,WAAW;IAC7B;EACF;EAEAC,aAAaA,CAACD,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,SAAS;QAAE,OAAO,UAAU;MACjC,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,WAAW;QAAE,OAAO,QAAQ;MACjC,KAAK,UAAU;QAAE,OAAO,MAAM;MAC9B;QAAS,OAAO,MAAM;IACxB;EACF;EAIAE,cAAcA,CAAC5C,MAAc,EAAEC,QAAgB;IAC7C,IAAI,CAACD,MAAM,IAAIA,MAAM,IAAI,CAAC,EAAE;MAC1B,OAAO;QAAE6C,KAAK,EAAE,KAAK;QAAEb,KAAK,EAAE;MAA+B,CAAE;IACjE;IAEA,MAAMc,SAAS,GAAG7C,QAAQ,KAAK,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IAChD,MAAM8C,SAAS,GAAG9C,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;IAExD,IAAID,MAAM,GAAG8C,SAAS,EAAE;MACtB,OAAO;QAAED,KAAK,EAAE,KAAK;QAAEb,KAAK,EAAE,qBAAqB,IAAI,CAACE,cAAc,CAACY,SAAS,EAAE7C,QAAQ,CAAC;MAAE,CAAE;IACjG;IAEA,IAAID,MAAM,GAAG+C,SAAS,EAAE;MACtB,OAAO;QAAEF,KAAK,EAAE,KAAK;QAAEb,KAAK,EAAE,qBAAqB,IAAI,CAACE,cAAc,CAACa,SAAS,EAAE9C,QAAQ,CAAC;MAAE,CAAE;IACjG;IAEA,OAAO;MAAE4C,KAAK,EAAE;IAAI,CAAE;EACxB;EAEAG,eAAeA,CAAChD,MAAc,EAAEiD,YAAoB,EAAEC,UAAkB;IACtE;IACA,IAAID,YAAY,KAAKC,UAAU,EAAE,OAAOlD,MAAM;IAE9C,MAAMmD,KAAK,GAA8B;MACvC,YAAY,EAAE,KAAK;MACnB,YAAY,EAAE;KACf;IAED,MAAMC,OAAO,GAAG,GAAGH,YAAY,OAAOC,UAAU,EAAE;IAClD,OAAOC,KAAK,CAACC,OAAO,CAAC,GAAGpD,MAAM,GAAGmD,KAAK,CAACC,OAAO,CAAC,GAAGpD,MAAM;EAC1D;EAEQpB,WAAWA,CAACoD,KAAU;IAC5B,IAAIqB,YAAY,GAAG,gCAAgC;IAEnD,IAAIrB,KAAK,CAACA,KAAK,YAAYsB,UAAU,EAAE;MACrCD,YAAY,GAAGrB,KAAK,CAACA,KAAK,CAACuB,OAAO;IACpC,CAAC,MAAM;MACLF,YAAY,GAAGrB,KAAK,CAACA,KAAK,EAAEuB,OAAO,IAAIvB,KAAK,CAACuB,OAAO,IAAI,eAAevB,KAAK,CAACU,MAAM,EAAE;IACvF;IAEAc,OAAO,CAACxB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAOlE,UAAU,CAAC,MAAM,IAAI6B,KAAK,CAAC0D,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAI,CAAA,G;qCAvMUxF,cAAc,EAAAyF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAd/F,cAAc;IAAAgG,OAAA,EAAdhG,cAAc,CAAAiG,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}