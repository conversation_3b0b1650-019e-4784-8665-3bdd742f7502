export const environment = {
  production: false,
  apiUrl: 'http://localhost:3002/api',
  appName: 'SecureApp',
  version: '1.0.0',
  features: {
    twoFactorAuth: true,
    otpLogin: true,
    payments: true,
    emailVerification: true,
    phoneVerification: true,
    disposableEmailBlocking: true
  },
  security: {
    jwtTokenKey: 'secure_app_token',
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    maxLoginAttempts: 5,
    lockoutDuration: 30 * 60 * 1000 // 30 minutes
  },
  razorpay: {
    keyId: 'rzp_test_1234567890', // Demo key
    currency: 'INR',
    supportedCurrencies: ['INR', 'USD']
  }
};
