#!/usr/bin/env python3
"""
Test script to simulate exact frontend behavior
Tests the same flow that the Angular frontend uses
"""

import requests
import json
import jwt
import base64

# Configuration
BASE_URL = "http://localhost:3002/api"
HEADERS = {
    'Content-Type': 'application/json',
    'Origin': 'http://localhost:4200',
    'X-Requested-With': 'XMLHttpRequest'
}

def simulate_frontend_token_parsing(token):
    """Simulate how the frontend parses JWT tokens"""
    print(f"🔍 Simulating Frontend Token Parsing...")
    
    try:
        # This is what the frontend does: atob() to decode base64
        parts = token.split('.')
        if len(parts) != 3:
            print(f"❌ Invalid JWT format: {len(parts)} parts")
            return False
        
        # Decode payload (second part) - this is what atob() does
        payload_b64 = parts[1]
        # Add padding if needed
        payload_b64 += '=' * (4 - len(payload_b64) % 4)
        
        try:
            payload_json = base64.urlsafe_b64decode(payload_b64).decode('utf-8')
            payload_data = json.loads(payload_json)
            print(f"✅ Frontend can parse token payload:")
            print(f"   User ID: {payload_data.get('id')} / {payload_data.get('securityId')}")
            print(f"   Email: {payload_data.get('email')}")
            print(f"   Roles: {payload_data.get('roles')}")
            print(f"   Expires: {payload_data.get('exp')}")
            return True
        except Exception as e:
            print(f"❌ Frontend token parsing failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Token parsing error: {e}")
        return False

def test_signup_flow():
    """Test the complete signup flow as frontend would do it"""
    print(f"\n🧪 Testing Signup Flow (Frontend Simulation)...")
    
    signup_data = {
        "firstName": "Frontend",
        "lastName": "Test",
        "email": f"frontend_test_{int(__import__('time').time())}@example.com",
        "password": "FrontendTest123!",
        "password_confirmation": "FrontendTest123!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data, headers=HEADERS)
        print(f"📊 Signup Status: {response.status_code}")
        
        if response.status_code == 422:
            print(f"❌ Signup validation error: {response.text}")
            return None
        elif response.status_code != 201:
            print(f"❌ Signup failed: {response.text}")
            return None
        
        data = response.json()
        token = data.get('token')
        user = data.get('user')
        
        if not token:
            print(f"❌ No token in signup response")
            return None
        
        print(f"✅ Signup successful!")
        print(f"🔑 Token: {token[:50]}...")
        print(f"👤 User: {json.dumps(user, indent=2)}")
        
        # Test frontend token parsing
        if simulate_frontend_token_parsing(token):
            return token, user
        else:
            return None
            
    except Exception as e:
        print(f"❌ Signup error: {e}")
        return None

def test_login_flow():
    """Test the complete login flow as frontend would do it"""
    print(f"\n🧪 Testing Login Flow (Frontend Simulation)...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "AdminPass123!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data, headers=HEADERS)
        print(f"📊 Login Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return None
        
        data = response.json()
        token = data.get('token')
        user = data.get('user')
        
        if not token:
            print(f"❌ No token in login response")
            return None
        
        print(f"✅ Login successful!")
        print(f"🔑 Token: {token[:50]}...")
        print(f"👤 User: {json.dumps(user, indent=2)}")
        
        # Test frontend token parsing
        if simulate_frontend_token_parsing(token):
            return token, user
        else:
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_authenticated_requests(token):
    """Test authenticated requests as frontend would do them"""
    print(f"\n🧪 Testing Authenticated Requests...")
    
    auth_headers = {
        **HEADERS,
        'Authorization': f'Bearer {token}'
    }
    
    # Test profile endpoint
    try:
        response = requests.get(f"{BASE_URL}/auth/profile", headers=auth_headers)
        print(f"📊 Profile Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Profile access successful!")
            print(f"👤 Profile: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Profile access failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Profile request error: {e}")
    
    # Test /auth/me endpoint (alias for profile)
    try:
        response = requests.get(f"{BASE_URL}/auth/me", headers=auth_headers)
        print(f"📊 /auth/me Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ /auth/me access successful!")
        else:
            print(f"❌ /auth/me access failed: {response.text}")
            
    except Exception as e:
        print(f"❌ /auth/me request error: {e}")

def main():
    """Main test function"""
    print("🚀 Starting Frontend Simulation Tests")
    print("=" * 60)
    
    # Test signup flow
    signup_result = test_signup_flow()
    
    # Test login flow
    login_result = test_login_flow()
    
    # Test authenticated requests with login token
    if login_result:
        token, user = login_result
        test_authenticated_requests(token)
    
    print("\n" + "=" * 60)
    print("🏁 Frontend Simulation Tests Complete")
    
    if signup_result or login_result:
        print("✅ JWT authentication is working correctly for frontend!")
        print("✅ Frontend should be able to parse and use the tokens")
    else:
        print("❌ JWT authentication issues detected")

if __name__ == "__main__":
    main()
