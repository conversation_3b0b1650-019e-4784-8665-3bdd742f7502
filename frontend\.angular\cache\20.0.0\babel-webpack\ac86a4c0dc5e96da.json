{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSelectModule } from '@angular/material/select';\n// Components\nimport { ProfileComponent } from './profile.component';\nimport { TwoFactorSetupComponent } from '../../components/auth/two-factor/two-factor-setup.component';\nimport { TwoFactorManagementComponent } from '../../components/auth/two-factor/two-factor-management.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}, {\n  path: 'delete-account',\n  loadComponent: () => import('../../components/account-deletion/account-deletion.component').then(c => c.AccountDeletionComponent)\n}];\nexport class ProfileModule {\n  static #_ = this.ɵfac = function ProfileModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProfileModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatProgressSpinnerModule, MatSnackBarModule, MatTabsModule, MatDividerModule, MatCheckboxModule, MatDialogModule, MatTooltipModule, MatSelectModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent, TwoFactorSetupComponent, TwoFactorManagementComponent],\n    imports: [CommonModule, i1.RouterModule, ReactiveFormsModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatProgressSpinnerModule, MatSnackBarModule, MatTabsModule, MatDividerModule, MatCheckboxModule, MatDialogModule, MatTooltipModule, MatSelectModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatTabsModule", "MatDividerModule", "MatCheckboxModule", "MatDialogModule", "MatTooltipModule", "MatSelectModule", "ProfileComponent", "TwoFactorSetupComponent", "TwoFactorManagementComponent", "routes", "path", "component", "loadComponent", "then", "c", "AccountDeletionComponent", "ProfileModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\modules\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSelectModule } from '@angular/material/select';\n\n// Components\nimport { ProfileComponent } from './profile.component';\nimport { TwoFactorSetupComponent } from '../../components/auth/two-factor/two-factor-setup.component';\nimport { TwoFactorManagementComponent } from '../../components/auth/two-factor/two-factor-management.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfileComponent\n  },\n  {\n    path: 'delete-account',\n    loadComponent: () => import('../../components/account-deletion/account-deletion.component').then(c => c.AccountDeletionComponent)\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfileComponent,\n    TwoFactorSetupComponent,\n    TwoFactorManagementComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    ReactiveFormsModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatTabsModule,\n    MatDividerModule,\n    MatCheckboxModule,\n    MatDialogModule,\n    MatTooltipModule,\n    MatSelectModule\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAE1D;AACA,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,4BAA4B,QAAQ,kEAAkE;;;AAE/G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,gBAAgB;EACtBE,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8DAA8D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,wBAAwB;CACjI,CACF;AA4BD,OAAM,MAAOC,aAAa;EAAA,QAAAC,CAAA,G;qCAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA,G;UAAbF;EAAa;EAAA,QAAAG,EAAA,G;cAnBtB9B,YAAY,EACZC,YAAY,CAAC8B,QAAQ,CAACX,MAAM,CAAC,EAC7BlB,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,iBAAiB,EACjBC,eAAe,EACfC,gBAAgB,EAChBC,eAAe;EAAA;;;2EAGNW,aAAa;IAAAK,YAAA,GAxBtBf,gBAAgB,EAChBC,uBAAuB,EACvBC,4BAA4B;IAAAc,OAAA,GAG5BjC,YAAY,EAAAkC,EAAA,CAAAjC,YAAA,EAEZC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,iBAAiB,EACjBC,eAAe,EACfC,gBAAgB,EAChBC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}