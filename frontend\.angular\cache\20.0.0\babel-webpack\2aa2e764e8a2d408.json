{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin(...args) {\n  const resultSelector = popResultSelector(args);\n  const {\n    args: sources,\n    keys\n  } = argsArgArrayOrObject(args);\n  const result = new Observable(subscriber => {\n    const {\n      length\n    } = sources;\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n    const values = new Array(length);\n    let remainingCompletions = length;\n    let remainingEmissions = length;\n    for (let sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      let hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, value => {\n        if (!hasValue) {\n          hasValue = true;\n          remainingEmissions--;\n        }\n        values[sourceIndex] = value;\n      }, () => remainingCompletions--, undefined, () => {\n        if (!remainingCompletions || !hasValue) {\n          if (!remainingEmissions) {\n            subscriber.next(keys ? createObject(keys, values) : values);\n          }\n          subscriber.complete();\n        }\n      }));\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "innerFrom", "popResultSelector", "createOperatorSubscriber", "mapOneOrManyArgs", "createObject", "fork<PERSON><PERSON>n", "args", "resultSelector", "sources", "keys", "result", "subscriber", "length", "complete", "values", "Array", "remainingCompletions", "remainingEmissions", "sourceIndex", "hasValue", "subscribe", "value", "undefined", "next", "pipe"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/rxjs/dist/esm/internal/observable/forkJoin.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin(...args) {\n    const resultSelector = popResultSelector(args);\n    const { args: sources, keys } = argsArgArrayOrObject(args);\n    const result = new Observable((subscriber) => {\n        const { length } = sources;\n        if (!length) {\n            subscriber.complete();\n            return;\n        }\n        const values = new Array(length);\n        let remainingCompletions = length;\n        let remainingEmissions = length;\n        for (let sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n            let hasValue = false;\n            innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                if (!hasValue) {\n                    hasValue = true;\n                    remainingEmissions--;\n                }\n                values[sourceIndex] = value;\n            }, () => remainingCompletions--, undefined, () => {\n                if (!remainingCompletions || !hasValue) {\n                    if (!remainingEmissions) {\n                        subscriber.next(keys ? createObject(keys, values) : values);\n                    }\n                    subscriber.complete();\n                }\n            }));\n        }\n    });\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAO,SAASC,QAAQA,CAAC,GAAGC,IAAI,EAAE;EAC9B,MAAMC,cAAc,GAAGN,iBAAiB,CAACK,IAAI,CAAC;EAC9C,MAAM;IAAEA,IAAI,EAAEE,OAAO;IAAEC;EAAK,CAAC,GAAGV,oBAAoB,CAACO,IAAI,CAAC;EAC1D,MAAMI,MAAM,GAAG,IAAIZ,UAAU,CAAEa,UAAU,IAAK;IAC1C,MAAM;MAAEC;IAAO,CAAC,GAAGJ,OAAO;IAC1B,IAAI,CAACI,MAAM,EAAE;MACTD,UAAU,CAACE,QAAQ,CAAC,CAAC;MACrB;IACJ;IACA,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACH,MAAM,CAAC;IAChC,IAAII,oBAAoB,GAAGJ,MAAM;IACjC,IAAIK,kBAAkB,GAAGL,MAAM;IAC/B,KAAK,IAAIM,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGN,MAAM,EAAEM,WAAW,EAAE,EAAE;MAC3D,IAAIC,QAAQ,GAAG,KAAK;MACpBnB,SAAS,CAACQ,OAAO,CAACU,WAAW,CAAC,CAAC,CAACE,SAAS,CAAClB,wBAAwB,CAACS,UAAU,EAAGU,KAAK,IAAK;QACtF,IAAI,CAACF,QAAQ,EAAE;UACXA,QAAQ,GAAG,IAAI;UACfF,kBAAkB,EAAE;QACxB;QACAH,MAAM,CAACI,WAAW,CAAC,GAAGG,KAAK;MAC/B,CAAC,EAAE,MAAML,oBAAoB,EAAE,EAAEM,SAAS,EAAE,MAAM;QAC9C,IAAI,CAACN,oBAAoB,IAAI,CAACG,QAAQ,EAAE;UACpC,IAAI,CAACF,kBAAkB,EAAE;YACrBN,UAAU,CAACY,IAAI,CAACd,IAAI,GAAGL,YAAY,CAACK,IAAI,EAAEK,MAAM,CAAC,GAAGA,MAAM,CAAC;UAC/D;UACAH,UAAU,CAACE,QAAQ,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,CAAC;EACF,OAAON,cAAc,GAAGG,MAAM,CAACc,IAAI,CAACrB,gBAAgB,CAACI,cAAc,CAAC,CAAC,GAAGG,MAAM;AAClF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}