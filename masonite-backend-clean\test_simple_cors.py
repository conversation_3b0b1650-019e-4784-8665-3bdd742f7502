#!/usr/bin/env python3
"""
Simple CORS Test
Test a single endpoint to see if CORS headers are being set
"""

import requests

def test_simple_cors():
    """Test a simple endpoint with CORS headers"""
    url = "http://localhost:3002/api/payments/test"
    
    # Test with Origin header
    headers = {
        'Origin': 'http://localhost:4200',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"🔍 Testing GET {url}")
        print(f"   Status: {response.status_code}")
        print(f"   All Headers: {dict(response.headers)}")
        
        # Check for CORS headers specifically
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }
        
        print(f"   CORS Headers: {cors_headers}")
        
        if cors_headers['Access-Control-Allow-Origin']:
            print("   ✅ CORS headers are being set!")
        else:
            print("   ❌ CORS headers are NOT being set!")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_options_request():
    """Test OPTIONS preflight request"""
    url = "http://localhost:3002/api/auth/signup"
    
    headers = {
        'Origin': 'http://localhost:4200',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
    }
    
    try:
        response = requests.options(url, headers=headers, timeout=10)
        print(f"\n🔍 Testing OPTIONS {url}")
        print(f"   Status: {response.status_code}")
        print(f"   All Headers: {dict(response.headers)}")
        
        # Check for CORS headers specifically
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }
        
        print(f"   CORS Headers: {cors_headers}")
        
        if cors_headers['Access-Control-Allow-Origin']:
            print("   ✅ CORS preflight headers are being set!")
        else:
            print("   ❌ CORS preflight headers are NOT being set!")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Simple CORS Test...")
    print("=" * 50)
    
    test_simple_cors()
    test_options_request()
    
    print("\n" + "=" * 50)
    print("✅ Simple CORS test completed!")
