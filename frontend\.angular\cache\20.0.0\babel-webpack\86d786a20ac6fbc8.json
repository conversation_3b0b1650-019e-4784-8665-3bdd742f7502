{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '@guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/auth/login',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'account/confirm-deletion',\n  loadComponent: () => import('./components/account-deletion/deletion-confirmation.component').then(c => c.DeletionConfirmationComponent)\n}, {\n  path: 'data-restoration',\n  loadComponent: () => import('./components/data-restoration/data-restoration.component').then(c => c.DataRestorationComponent)\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./modules/dashboard/dashboard.module').then(m => m.DashboardModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'payment',\n  loadChildren: () => import('./modules/payment/payment.module').then(m => m.PaymentModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'profile',\n  loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'unauthorized',\n  loadChildren: () => import('./modules/shared/shared.module').then(m => m.SharedModule)\n}, {\n  path: '**',\n  redirectTo: '/auth/login'\n}];\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes, {\n      enableTracing: false,\n      // Set to true for debugging\n      scrollPositionRestoration: 'top'\n    }), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "loadComponent", "c", "DeletionConfirmationComponent", "DataRestorationComponent", "DashboardModule", "canActivate", "data", "requireEmailVerification", "PaymentModule", "ProfileModule", "SharedModule", "AppRoutingModule", "_", "_2", "_3", "forRoot", "enableTracing", "scrollPositionRestoration", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AuthGuard } from '@guards/auth.guard';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/auth/login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule)\n  },\n  {\n    path: 'account/confirm-deletion',\n    loadComponent: () => import('./components/account-deletion/deletion-confirmation.component').then(c => c.DeletionConfirmationComponent)\n  },\n  {\n    path: 'data-restoration',\n    loadComponent: () => import('./components/data-restoration/data-restoration.component').then(c => c.DataRestorationComponent)\n  },\n  {\n    path: 'dashboard',\n    loadChildren: () => import('./modules/dashboard/dashboard.module').then(m => m.DashboardModule),\n    canActivate: [AuthGuard],\n    data: { requireEmailVerification: true }\n  },\n  {\n    path: 'payment',\n    loadChildren: () => import('./modules/payment/payment.module').then(m => m.PaymentModule),\n    canActivate: [AuthGuard],\n    data: { requireEmailVerification: true }\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule),\n    canActivate: [AuthGuard],\n    data: { requireEmailVerification: true }\n  },\n  {\n    path: 'unauthorized',\n    loadChildren: () => import('./modules/shared/shared.module').then(m => m.SharedModule)\n  },\n  {\n    path: '**',\n    redirectTo: '/auth/login'\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes, {\n    enableTracing: false, // Set to true for debugging\n    scrollPositionRestoration: 'top'\n  })],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,oBAAoB;;;AAE9C,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CAChF,EACD;EACEN,IAAI,EAAE,0BAA0B;EAChCO,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+DAA+D,CAAC,CAACH,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACC,6BAA6B;CACvI,EACD;EACET,IAAI,EAAE,kBAAkB;EACxBO,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0DAA0D,CAAC,CAACH,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACE,wBAAwB;CAC7H,EACD;EACEV,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,eAAe,CAAC;EAC/FC,WAAW,EAAE,CAACd,SAAS,CAAC;EACxBe,IAAI,EAAE;IAAEC,wBAAwB,EAAE;EAAI;CACvC,EACD;EACEd,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,aAAa,CAAC;EACzFH,WAAW,EAAE,CAACd,SAAS,CAAC;EACxBe,IAAI,EAAE;IAAEC,wBAAwB,EAAE;EAAI;CACvC,EACD;EACEd,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,aAAa,CAAC;EACzFJ,WAAW,EAAE,CAACd,SAAS,CAAC;EACxBe,IAAI,EAAE;IAAEC,wBAAwB,EAAE;EAAI;CACvC,EACD;EACEd,IAAI,EAAE,cAAc;EACpBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,YAAY;CACtF,EACD;EACEjB,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF;AASD,OAAM,MAAOiB,gBAAgB;EAAA,QAAAC,CAAA,G;qCAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cANjBxB,YAAY,CAACyB,OAAO,CAACvB,MAAM,EAAE;MACrCwB,aAAa,EAAE,KAAK;MAAE;MACtBC,yBAAyB,EAAE;KAC5B,CAAC,EACQ3B,YAAY;EAAA;;;2EAEXqB,gBAAgB;IAAAO,OAAA,GAAAC,EAAA,CAAA7B,YAAA;IAAA8B,OAAA,GAFjB9B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}