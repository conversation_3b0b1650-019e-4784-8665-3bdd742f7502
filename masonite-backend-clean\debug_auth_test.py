#!/usr/bin/env python3
"""
Debug Authentication Test
Test the authentication flow step by step to identify issues
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001/api"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "DebugPassword123!"

def test_auth_flow():
    print("🔍 Debug Authentication Flow Test")
    print("=" * 50)
    
    session = requests.Session()
    
    # Step 1: Register user
    print("\n1️⃣ Testing Registration...")
    register_data = {
        'firstName': 'Debug',
        'lastName': 'Test',
        'email': TEST_EMAIL,
        'password': TEST_PASSWORD,
        'password_confirmation': TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/auth/register", json=register_data)
    print(f"   Status: {response.status_code}")
    
    if response.status_code in [200, 201]:
        data = response.json()
        token = data.get('token')
        user = data.get('user', {})
        print(f"   ✅ Registration successful")
        print(f"   📝 Token: {token[:20]}..." if token else "   ❌ No token received")
        print(f"   👤 User ID: {user.get('id')}")
        print(f"   📧 Email: {user.get('email')}")
    elif response.status_code == 422:
        # User might already exist, try login
        print(f"   ⚠️ User might already exist, trying login...")
        
        # Step 2: Login
        print("\n2️⃣ Testing Login...")
        login_data = {
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD
        }
        
        response = session.post(f"{BASE_URL}/auth/login", json=login_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            user = data.get('user', {})
            print(f"   ✅ Login successful")
            print(f"   📝 Token: {token[:20]}..." if token else "   ❌ No token received")
            print(f"   👤 User ID: {user.get('id')}")
            print(f"   📧 Email: {user.get('email')}")
        else:
            print(f"   ❌ Login failed: {response.text}")
            return
    else:
        print(f"   ❌ Registration failed: {response.text}")
        return
    
    # Step 3: Test protected endpoint
    print("\n3️⃣ Testing Protected Endpoint...")
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    response = session.get(f"{BASE_URL}/auth/profile", headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Profile access successful")
        print(f"   👤 Profile data: {json.dumps(data, indent=2)}")
    else:
        print(f"   ❌ Profile access failed: {response.text}")
        
        # Debug: Check what the middleware is receiving
        print("\n🔍 Debug Information:")
        print(f"   📝 Token being sent: {token}")
        print(f"   📋 Headers: {headers}")
        
        # Try a simple test endpoint
        print("\n4️⃣ Testing Simple Endpoint...")
        response = session.get(f"{BASE_URL}/payments/test")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Step 4: Test token refresh
    print("\n5️⃣ Testing Token Refresh...")
    response = session.post(f"{BASE_URL}/auth/refresh", headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        new_token = data.get('token')
        print(f"   ✅ Token refresh successful")
        print(f"   📝 New token: {new_token[:20]}..." if new_token else "   ❌ No new token received")
    else:
        print(f"   ❌ Token refresh failed: {response.text}")

if __name__ == "__main__":
    test_auth_flow()
