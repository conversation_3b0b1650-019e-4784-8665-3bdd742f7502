"""
Masonite 4 API Authentication Controller
Maintains exact LoopBack API compatibility for existing frontend
"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.authentication import Auth
from masonite.validation import Validator
from masonite.mail import Mail
from app.models.User import User
from app.mailables.EmailVerification import EmailVerification
from app.mailables.PasswordReset import PasswordReset
from app.controllers.CorsController import CorsController
from datetime import datetime, timedelta


class AuthController(Controller):
    """
    API Authentication Controller 
    Maintains exact compatibility with LoopBack endpoints
    """
    
    def login(self, request: Request, response: Response, auth: Auth, validate: Validator):
        """
        POST /api/auth/login
        User login with email/username and password
        Returns JWT token compatible with LoopBack format
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['email', 'password']),
            validate.email('email'),
            validate.string('password')        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        email = request.input('email')
        password = request.input('password')
        
        # Use built-in Masonite authentication
        user = auth.attempt(email, password)
        
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Invalid email or password'
                }
            }, 401)
          # Generate JWT token using Masonite's built-in token generation
        token = user.generate_api_token()
        
        # Add CORS headers and return LoopBack-compatible response
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'token': token,
            'user': {
                'id': user.id,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False
            }
        })
    
    def register(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/register
        User registration compatible with LoopBack format
        """
        # Validate registration data with strong password requirements - LoopBack compatible fields
        errors = request.validate(
            validate.required(['firstName', 'lastName', 'email', 'password', 'password_confirmation']),
            validate.email('email'),
            validate.string('firstName'),
            validate.string('lastName'),
            validate.strong('password', length=8, special=1, uppercase=1),  # Built-in strong password validation
            validate.confirmed('password')  # Built-in password confirmation validation
        )

        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        # Check if user already exists
        existing_user = User.where('email', request.input('email')).first()
        if existing_user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'User with this email already exists',
                    'details': {'email': ['The email has already been taken.']}
                }
            }, 422)

        # Create full name from firstName and lastName
        full_name = f"{request.input('firstName')} {request.input('lastName')}"

        # Create user manually for better control
        try:
            # Create user instance and set password
            user = User()
            user.name = full_name
            user.first_name = request.input('firstName')
            user.last_name = request.input('lastName')
            user.email = request.input('email')
            user.is_active = True
            user.roles = 'user'
            user.set_password(request.input('password'))
            user.save()
        except Exception as e:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': f'User registration failed: {str(e)}'
                }
            }, 422)
          # Generate email verification token and send verification email
        verification_token = user.generate_email_verification_token()
        
        try:
            # Send email verification email using correct Masonite syntax
            mailable = EmailVerification(user, verification_token).to(user.email)
            Mail.mailable(mailable).send()
        except Exception as e:
            # Log the error but don't fail registration
            print(f"Failed to send verification email: {e}")
        
        # Generate JWT token
        token = user.generate_api_token()

        # Add CORS headers and return LoopBack-compatible response
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'token': token,
            'user': {
                'id': user.id,
                'firstName': request.input('firstName'),
                'lastName': request.input('lastName'),
                'email': user.email,
                'emailVerified': False,
                'twoFactorEnabled': False
            }
        }, 201)
    
    def logout(self, request: Request, response: Response):
        """
        POST /api/auth/logout
        User logout - invalidate token
        """
        # Get the current user from request (set by middleware)
        user = request.user()
        if user:
            # Invalidate the JWT token by clearing api_token
            user.api_token = None
            user.save()
        
        return response.json({
            'message': 'Logged out successfully'
        })
    
    def refresh(self, request: Request, response: Response):
        """
        POST /api/auth/refresh
        Refresh JWT token
        """
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'No valid token provided'
                }
            }, 401)
        
        # Generate new JWT token
        token = user.generate_api_token()
        
        return response.json({
            'token': token,
            'user': {
                'id': user.id,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False
            }
        })
    
    def verify_email(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/verify-email
        Email verification endpoint - compatible with LoopBack
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['token']),
            validate.string('token')
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        token = request.input('token')
        
        # Find user with valid verification token
        user = User.where('email_verification_token', token)\
                   .where('email_verification_expires', '>', datetime.now())\
                   .first()
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Invalid or expired verification token'
                }
            }, 400)
        
        # Mark email as verified
        user.mark_email_as_verified()
        
        # Return LoopBack-compatible response
        return response.json({
            'message': 'Email verified successfully',
            'user': {
                'id': user.id,
                'email': user.email,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'emailVerified': True
            }
        })
    
    def forgot_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/forgot-password
        Password reset request - compatible with LoopBack
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['email']),
            validate.email('email')
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)        
        email = request.input('email')
        
        # Find user by email
        user = User.where('email', email).first()
        
        if not user:
            # Don't reveal if email exists or not (security best practice)
            return response.json({
                'message': 'If the email exists, a password reset link has been sent'
            })
        
        # Generate password reset token
        reset_token = user.generate_password_reset_token()        
        try:
            # Send password reset email using built-in Mail system
            mailable = PasswordReset(user, reset_token).to(user.email)
            Mail.mailable(mailable).send()
        except Exception as e:
            # Log the error but don't reveal it to prevent information disclosure
            print(f"Failed to send password reset email: {e}")
        
        return response.json({
            'message': 'If the email exists, a password reset link has been sent'
        })
    
    def reset_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/reset-password
        Password reset execution - compatible with LoopBack
        """        # Validate request with strong password requirements
        errors = request.validate(
            validate.required(['token', 'password']),
            validate.string('token'),
            validate.strong('password', length=8, special=1, uppercase=1)  # Built-in strong password validation
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        token = request.input('token')
        new_password = request.input('password')
        
        # Find user with valid reset token
        user = User.where('password_reset_token', token)\
                   .where('password_reset_expires', '>', datetime.now())\
                   .first()
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Invalid or expired reset token'
                }
            }, 400)
        
        # Update password and clear reset token
        user.set_password(new_password)
        user.clear_password_reset_token()
        
        return response.json({
            'message': 'Password reset successfully'
        })
    
    def profile(self, request: Request, response: Response):
        """
        GET /api/auth/profile
        Get current authenticated user profile
        Protected endpoint that requires valid JWT token
        """
        # The user should be set by the JWT middleware
        user = request.user()
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)
        
        # Return user profile in LoopBack-compatible format
        return response.json({
            'user': {
                'id': user.id,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False,
                'createdAt': user.created_at.isoformat() if user.created_at else None,
                'updatedAt': user.updated_at.isoformat() if user.updated_at else None
            }
        })

    def update_profile(self, request: Request, response: Response, validate: Validator):
        """
        PATCH /api/auth/profile
        Update user profile - compatible with LoopBack
        """
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # Validate profile update data
        errors = request.validate(
            validate.string('firstName'),
            validate.string('lastName'),
            validate.email('email')
        )

        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        # Update user fields if provided
        if request.input('firstName'):
            user.first_name = request.input('firstName')
        if request.input('lastName'):
            user.last_name = request.input('lastName')
        if request.input('email'):
            # Check if email is already taken by another user
            existing_user = User.where('email', request.input('email')).where('id', '!=', user.id).first()
            if existing_user:
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'Email already taken',
                        'details': {'email': ['The email has already been taken.']}
                    }
                }, 422)
            user.email = request.input('email')
            # Reset email verification if email changed
            user.email_verified_at = None

        # Update full name if first or last name changed
        if request.input('firstName') or request.input('lastName'):
            first_name = request.input('firstName') or user.first_name or ''
            last_name = request.input('lastName') or user.last_name or ''
            user.name = f"{first_name} {last_name}".strip()

        user.save()

        return response.json({
            'message': 'Profile updated successfully',
            'user': {
                'id': user.id,
                'firstName': user.first_name,
                'lastName': user.last_name,
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False,
                'updatedAt': user.updated_at.isoformat() if user.updated_at else None
            }
        })

    def change_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/change-password
        Change user password - compatible with LoopBack
        """
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # Validate password change data
        errors = request.validate(
            validate.required(['currentPassword', 'newPassword']),
            validate.string('currentPassword'),
            validate.strong('newPassword', length=8, special=1, uppercase=1)
        )

        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        # Verify current password using User model method
        if not user.verify_password(request.input('currentPassword')):
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Current password is incorrect'
                }
            }, 400)

        # Update password
        user.set_password(request.input('newPassword'))
        user.save()

        return response.json({
            'message': 'Password changed successfully'
        })

    def resend_verification(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/resend-verification
        Resend email verification - compatible with LoopBack
        """
        # Validate request
        errors = request.validate(
            validate.required(['email']),
            validate.email('email')
        )

        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        email = request.input('email')
        user = User.where('email', email).first()

        if not user:
            # Don't reveal if email exists or not
            return response.json({
                'message': 'If the email exists and is unverified, a verification link has been sent'
            })

        if user.is_email_verified():
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Email is already verified'
                }
            }, 400)

        # Generate new verification token and send email
        verification_token = user.generate_email_verification_token()

        try:
            mailable = EmailVerification(user, verification_token).to(user.email)
            Mail.mailable(mailable).send()
        except Exception as e:
            print(f"Failed to send verification email: {e}")

        return response.json({
            'message': 'If the email exists and is unverified, a verification link has been sent'
        })
