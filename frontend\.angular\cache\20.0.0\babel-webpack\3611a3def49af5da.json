{"ast": null, "code": "import { _IdGenerator } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, hasModifier<PERSON>ey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ElementRef, ChangeDetectorRef, signal, EventEmitter, isSignal, Output, ViewChild } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n  /** Label for the option group. */\n  label;\n  /** whether the option group is disabled. */\n  disabled = false;\n  /** Unique id for the underlying label. */\n  _labelId = inject(_IdGenerator).getId('mat-optgroup-label-');\n  /** Whether the group is in inert a11y mode. */\n  _inert;\n  constructor() {\n    const parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n      optional: true\n    });\n    this._inert = parent?.inertGroups ?? false;\n  }\n  static ɵfac = function MatOptgroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOptgroup)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOptgroup,\n    selectors: [[\"mat-optgroup\"]],\n    hostAttrs: [1, \"mat-mdc-optgroup\"],\n    hostVars: 3,\n    hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n      }\n    },\n    inputs: {\n      label: \"label\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    exportAs: [\"matOptgroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTGROUP,\n      useExisting: MatOptgroup\n    }])],\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 4,\n    consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n    template: function MatOptgroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵprojection(4, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n        i0.ɵɵproperty(\"id\", ctx._labelId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n      }\n    },\n    styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"]\n    }]\n  }], () => [], {\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  source;\n  isUserInput;\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n  _element = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n    optional: true\n  });\n  group = inject(MAT_OPTGROUP, {\n    optional: true\n  });\n  _signalDisableRipple = false;\n  _selected = false;\n  _active = false;\n  _mostRecentViewValue = '';\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** The form value of the option. */\n  value;\n  /** The unique ID of the option. */\n  id = inject(_IdGenerator).getId('mat-option-');\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled();\n  }\n  set disabled(value) {\n    this._disabled.set(value);\n  }\n  _disabled = signal(false);\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return this._signalDisableRipple ? this._parent.disableRipple() : !!this._parent?.disableRipple;\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  /** Event emitted when the option is selected or deselected. */\n  // tslint:disable-next-line:no-output-on-prefix\n  onSelectionChange = new EventEmitter();\n  /** Element containing the option's text. */\n  _text;\n  /** Emits when the state of the option changes and any parents have to be notified. */\n  _stateChanges = new Subject();\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n  static ɵfac = function MatOption_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOption)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOption,\n    selectors: [[\"mat-option\"]],\n    viewQuery: function MatOption_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n    hostVars: 11,\n    hostBindings: function MatOption_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n          return ctx._selectViaInteraction();\n        })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n        i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      value: \"value\",\n      id: \"id\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      onSelectionChange: \"onSelectionChange\"\n    },\n    exportAs: [\"matOption\"],\n    ngContentSelectors: _c4,\n    decls: 8,\n    vars: 5,\n    consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n    template: function MatOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵconditionalCreate(0, MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 2, 0);\n        i0.ɵɵprojection(4, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(5, MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3);\n        i0.ɵɵconditionalCreate(6, MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n        i0.ɵɵelement(7, \"div\", 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.multiple ? 0 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵconditional(!ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.group && ctx.group._inert ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatPseudoCheckbox, MatRipple],\n    styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mat-list-list-item-selected-container-color: var(--mat-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatPseudoCheckbox, MatRipple],\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mat-list-list-item-selected-container-color: var(--mat-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nexport { MatOption as M, _countGroupLabelsBeforeOption as _, MatOptgroup as a, _getOptionScrollPosition as b, MAT_OPTION_PARENT_COMPONENT as c, MAT_OPTGROUP as d, MatOptionSelectionChange as e };", "map": {"version": 3, "names": ["_IdGenerator", "ENTER", "SPACE", "hasModifierKey", "i0", "InjectionToken", "inject", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "ElementRef", "ChangeDetectorRef", "signal", "EventEmitter", "isSignal", "Output", "ViewChild", "Subject", "M", "<PERSON><PERSON><PERSON><PERSON>", "MatPseudoCheckbox", "_", "_StructuralStylesLoader", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "_c0", "_c1", "_c2", "_c3", "_c4", "MatOption_Conditional_0_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "disabled", "selected", "MatOption_Conditional_5_Template", "MatOption_Conditional_6_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "group", "label", "MAT_OPTION_PARENT_COMPONENT", "MAT_OPTGROUP", "MatOptgroup", "_labelId", "getId", "_inert", "constructor", "parent", "optional", "inertGroups", "ɵfac", "MatOptgroup_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatOptgroup_HostBindings", "ɵɵattribute", "toString", "inputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngContentSelectors", "decls", "vars", "consts", "template", "MatOptgroup_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵclassProp", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "providers", "transform", "MatOptionSelectionChange", "source", "isUserInput", "MatOption", "_element", "_changeDetectorRef", "_parent", "_signalDisableRipple", "_selected", "_active", "_mostRecentViewValue", "multiple", "value", "id", "_disabled", "set", "disable<PERSON><PERSON><PERSON>", "hideSingleSelectionIndicator", "onSelectionChange", "_text", "_stateChanges", "<PERSON><PERSON><PERSON><PERSON>", "load", "active", "viewValue", "nativeElement", "textContent", "trim", "select", "emitEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectionChangeEvent", "deselect", "focus", "_origin", "options", "element", "_getHostElement", "setActiveStyles", "setInactiveStyles", "get<PERSON><PERSON><PERSON>", "_handleKeydown", "event", "keyCode", "_selectViaInteraction", "preventDefault", "_getTabIndex", "ngAfterViewChecked", "next", "ngOnDestroy", "complete", "emit", "MatOption_Factory", "viewQuery", "MatOption_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "MatOption_HostBindings", "ɵɵlistener", "MatOption_click_HostBindingHandler", "MatOption_keydown_HostBindingHandler", "$event", "ɵɵdomProperty", "outputs", "MatOption_Template", "ɵɵconditionalCreate", "ɵɵconditional", "dependencies", "imports", "static", "_countGroupLabelsBeforeOption", "optionIndex", "optionGroups", "length", "optionsArray", "toArray", "groups", "groupCounter", "i", "_getOptionScrollPosition", "optionOffset", "optionHeight", "currentScrollPosition", "panelHeight", "Math", "max", "a", "b", "c", "d", "e"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/material/fesm2022/option-BzhYL_xC.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, hasModifier<PERSON><PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ElementRef, ChangeDetectorRef, signal, EventEmitter, isSignal, Output, ViewChild } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n    /** Label for the option group. */\n    label;\n    /** whether the option group is disabled. */\n    disabled = false;\n    /** Unique id for the underlying label. */\n    _labelId = inject(_IdGenerator).getId('mat-optgroup-label-');\n    /** Whether the group is in inert a11y mode. */\n    _inert;\n    constructor() {\n        const parent = inject(MAT_OPTION_PARENT_COMPONENT, { optional: true });\n        this._inert = parent?.inertGroups ?? false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptgroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatOptgroup, isStandalone: true, selector: \"mat-optgroup\", inputs: { label: \"label\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"attr.role\": \"_inert ? null : \\\"group\\\"\", \"attr.aria-disabled\": \"_inert ? null : disabled.toString()\", \"attr.aria-labelledby\": \"_inert ? null : _labelId\" }, classAttribute: \"mat-mdc-optgroup\" }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], exportAs: [\"matOptgroup\"], ngImport: i0, template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptgroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-optgroup', exportAs: 'matOptgroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-mdc-optgroup',\n                        '[attr.role]': '_inert ? null : \"group\"',\n                        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n                        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n                    }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { label: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n    source;\n    isUserInput;\n    constructor(\n    /** Reference to the option that emitted the event. */\n    source, \n    /** Whether the change in the option's value was a result of a user action. */\n    isUserInput = false) {\n        this.source = source;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n    _element = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parent = inject(MAT_OPTION_PARENT_COMPONENT, { optional: true });\n    group = inject(MAT_OPTGROUP, { optional: true });\n    _signalDisableRipple = false;\n    _selected = false;\n    _active = false;\n    _mostRecentViewValue = '';\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n        return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n        return this._selected;\n    }\n    /** The form value of the option. */\n    value;\n    /** The unique ID of the option. */\n    id = inject(_IdGenerator).getId('mat-option-');\n    /** Whether the option is disabled. */\n    get disabled() {\n        return (this.group && this.group.disabled) || this._disabled();\n    }\n    set disabled(value) {\n        this._disabled.set(value);\n    }\n    _disabled = signal(false);\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n        return this._signalDisableRipple\n            ? this._parent.disableRipple()\n            : !!this._parent?.disableRipple;\n    }\n    /** Whether to display checkmark for single-selection. */\n    get hideSingleSelectionIndicator() {\n        return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n    }\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    onSelectionChange = new EventEmitter();\n    /** Element containing the option's text. */\n    _text;\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    _stateChanges = new Subject();\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_StructuralStylesLoader);\n        styleLoader.load(_VisuallyHiddenLoader);\n        this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n        return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n        // TODO(kara): Add input property alternative for node envs.\n        return (this._text?.nativeElement.textContent || '').trim();\n    }\n    /** Selects the option. */\n    select(emitEvent = true) {\n        if (!this._selected) {\n            this._selected = true;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Deselects the option. */\n    deselect(emitEvent = true) {\n        if (this._selected) {\n            this._selected = false;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n        // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n        // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n        const element = this._getHostElement();\n        if (typeof element.focus === 'function') {\n            element.focus(options);\n        }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n        if (!this._active) {\n            this._active = true;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n        if (this._active) {\n            this._active = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n            this._selectViaInteraction();\n            // Prevent the page from scrolling down and form submits.\n            event.preventDefault();\n        }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n        if (!this.disabled) {\n            this._selected = this.multiple ? !this._selected : true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent(true);\n        }\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n    // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n    // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n        // Since parent components could be using the option's label to display the selected values\n        // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n        // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n        // relatively cheap, however we still limit them only to selected options in order to avoid\n        // hitting the DOM too often.\n        if (this._selected) {\n            const viewValue = this.viewValue;\n            if (viewValue !== this._mostRecentViewValue) {\n                if (this._mostRecentViewValue) {\n                    this._stateChanges.next();\n                }\n                this._mostRecentViewValue = viewValue;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n        this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOption, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatOption, isStandalone: true, selector: \"mat-option\", inputs: { value: \"value\", id: \"id\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { onSelectionChange: \"onSelectionChange\" }, host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_selectViaInteraction()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-list-item--selected\": \"selected\", \"class.mat-mdc-option-multiple\": \"multiple\", \"class.mat-mdc-option-active\": \"active\", \"class.mdc-list-item--disabled\": \"disabled\", \"id\": \"id\", \"attr.aria-selected\": \"selected\", \"attr.aria-disabled\": \"disabled.toString()\" }, classAttribute: \"mat-mdc-option mdc-list-item\" }, viewQueries: [{ propertyName: \"_text\", first: true, predicate: [\"text\"], descendants: true, static: true }], exportAs: [\"matOption\"], ngImport: i0, template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mat-list-list-item-selected-container-color: var(--mat-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"], dependencies: [{ kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-option', exportAs: 'matOption', host: {\n                        'role': 'option',\n                        '[class.mdc-list-item--selected]': 'selected',\n                        '[class.mat-mdc-option-multiple]': 'multiple',\n                        '[class.mat-mdc-option-active]': 'active',\n                        '[class.mdc-list-item--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n                        // [WAI ARIA Listbox authoring practices guide](\n                        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n                        // selected option has either aria-selected or aria-checked  set to true. All options that are\n                        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n                        // aria-selected implementation of Chips and List components.\n                        //\n                        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n                        // every option as \"selected\" (#21491).\n                        '[attr.aria-selected]': 'selected',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '(click)': '_selectViaInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        'class': 'mat-mdc-option mdc-list-item',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatPseudoCheckbox, MatRipple], template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mat-list-list-item-selected-container-color: var(--mat-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { value: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], onSelectionChange: [{\n                type: Output\n            }], _text: [{\n                type: ViewChild,\n                args: ['text', { static: true }]\n            }] } });\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n    if (optionGroups.length) {\n        let optionsArray = options.toArray();\n        let groups = optionGroups.toArray();\n        let groupCounter = 0;\n        for (let i = 0; i < optionIndex + 1; i++) {\n            if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n                groupCounter++;\n            }\n        }\n        return groupCounter;\n    }\n    return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n    if (optionOffset < currentScrollPosition) {\n        return optionOffset;\n    }\n    if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n        return Math.max(0, optionOffset - panelHeight + optionHeight);\n    }\n    return currentScrollPosition;\n}\n\nexport { MatOption as M, _countGroupLabelsBeforeOption as _, MatOptgroup as a, _getOptionScrollPosition as b, MAT_OPTION_PARENT_COMPONENT as c, MAT_OPTGROUP as d, MatOptionSelectionChange as e };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,KAAK,EAAEC,KAAK,EAAEC,cAAc,QAAQ,uBAAuB;AACpE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACxN,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASD,CAAC,IAAIE,iBAAiB,QAAQ,gCAAgC;AACvE,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,sBAAsB,EAAEC,qBAAqB,QAAQ,sBAAsB;;AAEpF;AACA;AACA;AAFA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA8C6F7B,EAAE,CAAA+B,SAAA,4BAoNk6C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApNr6ChC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAkC,UAAA,aAAAF,MAAA,CAAAG,QAoNqzC,CAAC,UAAAH,MAAA,CAAAI,QAAA,0BAAuD,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApNh3C7B,EAAE,CAAA+B,SAAA,4BAoNu4D,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApN14DhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAkC,UAAA,aAAAF,MAAA,CAAAG,QAoNuxD,CAAC;EAAA;AAAA;AAAA,SAAAG,iCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApN1xD7B,EAAE,CAAAuC,cAAA,aAoNkiE,CAAC;IApNriEvC,EAAE,CAAAwC,MAAA,EAoNqjE,CAAC;IApNxjExC,EAAE,CAAAyC,YAAA,CAoN4jE,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAG,MAAA,GApN/jEhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAA0C,SAAA,CAoNqjE,CAAC;IApNxjE1C,EAAE,CAAA2C,kBAAA,MAAAX,MAAA,CAAAY,KAAA,CAAAC,KAAA,KAoNqjE,CAAC;EAAA;AAAA;AA/PrpE,MAAMC,2BAA2B,GAAG,IAAI7C,cAAc,CAAC,6BAA6B,CAAC;;AAErF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8C,YAAY,GAAG,IAAI9C,cAAc,CAAC,aAAa,CAAC;AACtD;AACA;AACA;AACA,MAAM+C,WAAW,CAAC;EACd;EACAH,KAAK;EACL;EACAV,QAAQ,GAAG,KAAK;EAChB;EACAc,QAAQ,GAAG/C,MAAM,CAACN,YAAY,CAAC,CAACsD,KAAK,CAAC,qBAAqB,CAAC;EAC5D;EACAC,MAAM;EACNC,WAAWA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAGnD,MAAM,CAAC4C,2BAA2B,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,IAAI,CAACH,MAAM,GAAGE,MAAM,EAAEE,WAAW,IAAI,KAAK;EAC9C;EACA,OAAOC,IAAI,YAAAC,oBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFV,WAAW;EAAA;EAC9G,OAAOW,IAAI,kBAD8E3D,EAAE,CAAA4D,iBAAA;IAAAC,IAAA,EACJb,WAAW;IAAAc,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAArC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADT7B,EAAE,CAAAmE,WAAA,SAAArC,GAAA,CAAAqB,MAAA,GACK,IAAI,GAAG,OAAO,mBAAArB,GAAA,CAAAqB,MAAA,GAAd,IAAI,GAAGrB,GAAA,CAAAK,QAAA,CAAAiC,QAAA,CAAkB,CAAC,qBAAAtC,GAAA,CAAAqB,MAAA,GAA1B,IAAI,GAAArB,GAAA,CAAAmB,QAAA;MAAA;IAAA;IAAAoB,MAAA;MAAAxB,KAAA;MAAAV,QAAA,8BAA2GhC,gBAAgB;IAAA;IAAAmE,QAAA;IAAAC,QAAA,GADtIvE,EAAE,CAAAwE,kBAAA,CAC6W,CAAC;MAAEC,OAAO,EAAE1B,YAAY;MAAE2B,WAAW,EAAE1B;IAAY,CAAC,CAAC;IAAA2B,kBAAA,EAAAnD,GAAA;IAAAoD,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qBAAAnD,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADpa7B,EAAE,CAAAiF,eAAA,CAAA1D,GAAA;QAAFvB,EAAE,CAAAuC,cAAA,aAC+lB,CAAC,aAA+C,CAAC;QADlpBvC,EAAE,CAAAwC,MAAA,EAC2pB,CAAC;QAD9pBxC,EAAE,CAAAkF,YAAA,EACorB,CAAC;QADvrBlF,EAAE,CAAAyC,YAAA,CAC2rB,CAAC,CAAQ,CAAC;QADvsBzC,EAAE,CAAAkF,YAAA,KACqwB,CAAC;MAAA;MAAA,IAAArD,EAAA;QADxwB7B,EAAE,CAAAmF,WAAA,4BAAArD,GAAA,CAAAK,QACykB,CAAC;QAD5kBnC,EAAE,CAAAkC,UAAA,OAAAJ,GAAA,CAAAmB,QAC8lB,CAAC;QADjmBjD,EAAE,CAAA0C,SAAA,EAC2pB,CAAC;QAD9pB1C,EAAE,CAAA2C,kBAAA,KAAAb,GAAA,CAAAe,KAAA,KAC2pB,CAAC;MAAA;IAAA;IAAAuC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC3vB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FvF,EAAE,CAAAwF,iBAAA,CAGJxC,WAAW,EAAc,CAAC;IACzGa,IAAI,EAAEzD,SAAS;IACfqF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEpB,QAAQ,EAAE,aAAa;MAAEe,aAAa,EAAEhF,iBAAiB,CAACsF,IAAI;MAAEL,eAAe,EAAEhF,uBAAuB,CAACsF,MAAM;MAAEC,IAAI,EAAE;QAC9I,OAAO,EAAE,kBAAkB;QAC3B,aAAa,EAAE,yBAAyB;QACxC,sBAAsB,EAAE,qCAAqC;QAC7D,wBAAwB,EAAE;MAC9B,CAAC;MAAEC,SAAS,EAAE,CAAC;QAAErB,OAAO,EAAE1B,YAAY;QAAE2B,WAAW,EAAE1B;MAAY,CAAC,CAAC;MAAE+B,QAAQ,EAAE,kTAAkT;MAAEK,MAAM,EAAE,CAAC,29BAA29B;IAAE,CAAC;EACt3C,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEvC,KAAK,EAAE,CAAC;MAChDgB,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE4B,QAAQ,EAAE,CAAC;MACX0B,IAAI,EAAEtD,KAAK;MACXkF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE5F;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM6F,wBAAwB,CAAC;EAC3BC,MAAM;EACNC,WAAW;EACX9C,WAAWA,CACX;EACA6C,MAAM,EACN;EACAC,WAAW,GAAG,KAAK,EAAE;IACjB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,QAAQ,GAAGlG,MAAM,CAACM,UAAU,CAAC;EAC7B6F,kBAAkB,GAAGnG,MAAM,CAACO,iBAAiB,CAAC;EAC9C6F,OAAO,GAAGpG,MAAM,CAAC4C,2BAA2B,EAAE;IAAEQ,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjEV,KAAK,GAAG1C,MAAM,CAAC6C,YAAY,EAAE;IAAEO,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChDiD,oBAAoB,GAAG,KAAK;EAC5BC,SAAS,GAAG,KAAK;EACjBC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,EAAE;EACzB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,OAAO,IAAI,IAAI,CAACA,OAAO,CAACK,QAAQ;EAChD;EACA;EACA,IAAIvE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoE,SAAS;EACzB;EACA;EACAI,KAAK;EACL;EACAC,EAAE,GAAG3G,MAAM,CAACN,YAAY,CAAC,CAACsD,KAAK,CAAC,aAAa,CAAC;EAC9C;EACA,IAAIf,QAAQA,CAAA,EAAG;IACX,OAAQ,IAAI,CAACS,KAAK,IAAI,IAAI,CAACA,KAAK,CAACT,QAAQ,IAAK,IAAI,CAAC2E,SAAS,CAAC,CAAC;EAClE;EACA,IAAI3E,QAAQA,CAACyE,KAAK,EAAE;IAChB,IAAI,CAACE,SAAS,CAACC,GAAG,CAACH,KAAK,CAAC;EAC7B;EACAE,SAAS,GAAGpG,MAAM,CAAC,KAAK,CAAC;EACzB;EACA,IAAIsG,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACT,oBAAoB,GAC1B,IAAI,CAACD,OAAO,CAACU,aAAa,CAAC,CAAC,GAC5B,CAAC,CAAC,IAAI,CAACV,OAAO,EAAEU,aAAa;EACvC;EACA;EACA,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,CAAC,EAAE,IAAI,CAACX,OAAO,IAAI,IAAI,CAACA,OAAO,CAACW,4BAA4B,CAAC;EACxE;EACA;EACA;EACAC,iBAAiB,GAAG,IAAIvG,YAAY,CAAC,CAAC;EACtC;EACAwG,KAAK;EACL;EACAC,aAAa,GAAG,IAAIrG,OAAO,CAAC,CAAC;EAC7BqC,WAAWA,CAAA,EAAG;IACV,MAAMiE,WAAW,GAAGnH,MAAM,CAACmB,sBAAsB,CAAC;IAClDgG,WAAW,CAACC,IAAI,CAAClG,uBAAuB,CAAC;IACzCiG,WAAW,CAACC,IAAI,CAAChG,qBAAqB,CAAC;IACvC,IAAI,CAACiF,oBAAoB,GAAG,CAAC,CAAC,IAAI,CAACD,OAAO,IAAI1F,QAAQ,CAAC,IAAI,CAAC0F,OAAO,CAACU,aAAa,CAAC;EACtF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIO,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACd,OAAO;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIe,SAASA,CAAA,EAAG;IACZ;IACA,OAAO,CAAC,IAAI,CAACL,KAAK,EAAEM,aAAa,CAACC,WAAW,IAAI,EAAE,EAAEC,IAAI,CAAC,CAAC;EAC/D;EACA;EACAC,MAAMA,CAACC,SAAS,GAAG,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAACrB,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACH,kBAAkB,CAACyB,YAAY,CAAC,CAAC;MACtC,IAAID,SAAS,EAAE;QACX,IAAI,CAACE,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAC,QAAQA,CAACH,SAAS,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAACrB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACH,kBAAkB,CAACyB,YAAY,CAAC,CAAC;MACtC,IAAID,SAAS,EAAE;QACX,IAAI,CAACE,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAE,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACpB;IACA;IACA,MAAMC,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACtC,IAAI,OAAOD,OAAO,CAACH,KAAK,KAAK,UAAU,EAAE;MACrCG,OAAO,CAACH,KAAK,CAACE,OAAO,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIG,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAC7B,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACJ,kBAAkB,CAACyB,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIS,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC9B,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACJ,kBAAkB,CAACyB,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACAU,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChB,SAAS;EACzB;EACA;EACAiB,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAACC,OAAO,KAAK9I,KAAK,IAAI6I,KAAK,CAACC,OAAO,KAAK7I,KAAK,KAAK,CAACC,cAAc,CAAC2I,KAAK,CAAC,EAAE;MAChF,IAAI,CAACE,qBAAqB,CAAC,CAAC;MAC5B;MACAF,KAAK,CAACG,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACID,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACzG,QAAQ,EAAE;MAChB,IAAI,CAACqE,SAAS,GAAG,IAAI,CAACG,QAAQ,GAAG,CAAC,IAAI,CAACH,SAAS,GAAG,IAAI;MACvD,IAAI,CAACH,kBAAkB,CAACyB,YAAY,CAAC,CAAC;MACtC,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACAe,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC3G,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAkG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACjC,QAAQ,CAACqB,aAAa;EACtC;EACAsB,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACvC,SAAS,EAAE;MAChB,MAAMgB,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,KAAK,IAAI,CAACd,oBAAoB,EAAE;QACzC,IAAI,IAAI,CAACA,oBAAoB,EAAE;UAC3B,IAAI,CAACU,aAAa,CAAC4B,IAAI,CAAC,CAAC;QAC7B;QACA,IAAI,CAACtC,oBAAoB,GAAGc,SAAS;MACzC;IACJ;EACJ;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7B,aAAa,CAAC8B,QAAQ,CAAC,CAAC;EACjC;EACA;EACAnB,yBAAyBA,CAAC7B,WAAW,GAAG,KAAK,EAAE;IAC3C,IAAI,CAACgB,iBAAiB,CAACiC,IAAI,CAAC,IAAInD,wBAAwB,CAAC,IAAI,EAAEE,WAAW,CAAC,CAAC;EAChF;EACA,OAAO1C,IAAI,YAAA4F,kBAAA1F,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyC,SAAS;EAAA;EAC5G,OAAOxC,IAAI,kBApN8E3D,EAAE,CAAA4D,iBAAA;IAAAC,IAAA,EAoNJsC,SAAS;IAAArC,SAAA;IAAAuF,SAAA,WAAAC,gBAAAzH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QApNP7B,EAAE,CAAAuJ,WAAA,CAAA9H,GAAA;MAAA;MAAA,IAAAI,EAAA;QAAA,IAAA2H,EAAA;QAAFxJ,EAAE,CAAAyJ,cAAA,CAAAD,EAAA,GAAFxJ,EAAE,CAAA0J,WAAA,QAAA5H,GAAA,CAAAqF,KAAA,GAAAqC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAA5F,SAAA,WAoNkO,QAAQ;IAAAC,QAAA;IAAAC,YAAA,WAAA2F,uBAAA/H,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QApN5O7B,EAAE,CAAA6J,UAAA,mBAAAC,mCAAA;UAAA,OAoNJhI,GAAA,CAAA8G,qBAAA,CAAsB,CAAC;QAAA,CAAf,CAAC,qBAAAmB,qCAAAC,MAAA;UAAA,OAATlI,GAAA,CAAA2G,cAAA,CAAAuB,MAAqB,CAAC;QAAA,CAAd,CAAC;MAAA;MAAA,IAAAnI,EAAA;QApNP7B,EAAE,CAAAiK,aAAA,OAAAnI,GAAA,CAAA+E,EAoNI,CAAC;QApNP7G,EAAE,CAAAmE,WAAA,kBAAArC,GAAA,CAAAM,QAAA,mBAoNJN,GAAA,CAAAK,QAAA,CAAAiC,QAAA,CAAkB,CAAC;QApNjBpE,EAAE,CAAAmF,WAAA,4BAAArD,GAAA,CAAAM,QAoNI,CAAC,4BAAAN,GAAA,CAAA6E,QAAD,CAAC,0BAAA7E,GAAA,CAAAyF,MAAD,CAAC,4BAAAzF,GAAA,CAAAK,QAAD,CAAC;MAAA;IAAA;IAAAkC,MAAA;MAAAuC,KAAA;MAAAC,EAAA;MAAA1E,QAAA,8BAAqHhC,gBAAgB;IAAA;IAAA+J,OAAA;MAAAhD,iBAAA;IAAA;IAAA5C,QAAA;IAAAK,kBAAA,EAAAhD,GAAA;IAAAiD,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAoF,mBAAAtI,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QApN5I7B,EAAE,CAAAiF,eAAA,CAAAvD,GAAA;QAAF1B,EAAE,CAAAoK,mBAAA,IAAAxI,gCAAA,gCAoNwsC,CAAC;QApN3sC5B,EAAE,CAAAkF,YAAA,EAoNs9C,CAAC;QApNz9ClF,EAAE,CAAAuC,cAAA,gBAoN4gD,CAAC;QApN/gDvC,EAAE,CAAAkF,YAAA,KAoNqiD,CAAC;QApNxiDlF,EAAE,CAAAyC,YAAA,CAoN4iD,CAAC;QApN/iDzC,EAAE,CAAAoK,mBAAA,IAAA/H,gCAAA,gCAoN0qD,CAAC;QApN7qDrC,EAAE,CAAAoK,mBAAA,IAAA9H,gCAAA,iBAoNw/D,CAAC;QApN3/DtC,EAAE,CAAA+B,SAAA,YAoNqwE,CAAC;MAAA;MAAA,IAAAF,EAAA;QApNxwE7B,EAAE,CAAAqK,aAAA,CAAAvI,GAAA,CAAA6E,QAAA,SAoNq6C,CAAC;QApNx6C3G,EAAE,CAAA0C,SAAA,EAoN04D,CAAC;QApN74D1C,EAAE,CAAAqK,aAAA,EAAAvI,GAAA,CAAA6E,QAAA,IAAA7E,GAAA,CAAAM,QAAA,KAAAN,GAAA,CAAAmF,4BAAA,SAoN04D,CAAC;QApN74DjH,EAAE,CAAA0C,SAAA,CAoN+jE,CAAC;QApNlkE1C,EAAE,CAAAqK,aAAA,CAAAvI,GAAA,CAAAc,KAAA,IAAAd,GAAA,CAAAc,KAAA,CAAAO,MAAA,SAoN+jE,CAAC;QApNlkEnD,EAAE,CAAA0C,SAAA,CAoN0sE,CAAC;QApN7sE1C,EAAE,CAAAkC,UAAA,qBAAAJ,GAAA,CAAAuG,eAAA,EAoN0sE,CAAC,sBAAAvG,GAAA,CAAAK,QAAA,IAAAL,GAAA,CAAAkF,aAAiD,CAAC;MAAA;IAAA;IAAAsD,YAAA,GAAszHpJ,iBAAiB,EAA6GD,SAAS;IAAAmE,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACzxM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtN6FvF,EAAE,CAAAwF,iBAAA,CAsNJW,SAAS,EAAc,CAAC;IACvGtC,IAAI,EAAEzD,SAAS;IACfqF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEpB,QAAQ,EAAE,WAAW;MAAEuB,IAAI,EAAE;QAClD,MAAM,EAAE,QAAQ;QAChB,iCAAiC,EAAE,UAAU;QAC7C,iCAAiC,EAAE,UAAU;QAC7C,+BAA+B,EAAE,QAAQ;QACzC,iCAAiC,EAAE,UAAU;QAC7C,MAAM,EAAE,IAAI;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,qBAAqB;QAC7C,SAAS,EAAE,yBAAyB;QACpC,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE;MACb,CAAC;MAAER,aAAa,EAAEhF,iBAAiB,CAACsF,IAAI;MAAEL,eAAe,EAAEhF,uBAAuB,CAACsF,MAAM;MAAE2E,OAAO,EAAE,CAACrJ,iBAAiB,EAAED,SAAS,CAAC;MAAE8D,QAAQ,EAAE,w9CAAw9C;MAAEK,MAAM,EAAE,CAAC,kvHAAkvH;IAAE,CAAC;EACl3K,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEwB,KAAK,EAAE,CAAC;MAChD/C,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEsG,EAAE,EAAE,CAAC;MACLhD,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE4B,QAAQ,EAAE,CAAC;MACX0B,IAAI,EAAEtD,KAAK;MACXkF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE5F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+G,iBAAiB,EAAE,CAAC;MACpBrD,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAEsG,KAAK,EAAE,CAAC;MACRtD,IAAI,EAAE/C,SAAS;MACf2E,IAAI,EAAE,CAAC,MAAM,EAAE;QAAE+E,MAAM,EAAE;MAAK,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,WAAW,EAAEvC,OAAO,EAAEwC,YAAY,EAAE;EACvE,IAAIA,YAAY,CAACC,MAAM,EAAE;IACrB,IAAIC,YAAY,GAAG1C,OAAO,CAAC2C,OAAO,CAAC,CAAC;IACpC,IAAIC,MAAM,GAAGJ,YAAY,CAACG,OAAO,CAAC,CAAC;IACnC,IAAIE,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;MACtC,IAAIJ,YAAY,CAACI,CAAC,CAAC,CAACrI,KAAK,IAAIiI,YAAY,CAACI,CAAC,CAAC,CAACrI,KAAK,KAAKmI,MAAM,CAACC,YAAY,CAAC,EAAE;QACzEA,YAAY,EAAE;MAClB;IACJ;IACA,OAAOA,YAAY;EACvB;EACA,OAAO,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAACC,YAAY,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,WAAW,EAAE;EAC9F,IAAIH,YAAY,GAAGE,qBAAqB,EAAE;IACtC,OAAOF,YAAY;EACvB;EACA,IAAIA,YAAY,GAAGC,YAAY,GAAGC,qBAAqB,GAAGC,WAAW,EAAE;IACnE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGG,WAAW,GAAGF,YAAY,CAAC;EACjE;EACA,OAAOC,qBAAqB;AAChC;AAEA,SAASlF,SAAS,IAAInF,CAAC,EAAEyJ,6BAA6B,IAAItJ,CAAC,EAAE6B,WAAW,IAAIyI,CAAC,EAAEP,wBAAwB,IAAIQ,CAAC,EAAE5I,2BAA2B,IAAI6I,CAAC,EAAE5I,YAAY,IAAI6I,CAAC,EAAE5F,wBAAwB,IAAI6F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}