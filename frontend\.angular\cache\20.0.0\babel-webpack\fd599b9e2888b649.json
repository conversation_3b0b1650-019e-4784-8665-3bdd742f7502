{"ast": null, "code": "export { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {\n  static ɵfac = function _VisuallyHiddenLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _VisuallyHiddenLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _VisuallyHiddenLoader,\n    selectors: [[\"ng-component\"]],\n    exportAs: [\"cdkVisuallyHidden\"],\n    decls: 0,\n    vars: 0,\n    template: function _VisuallyHiddenLoader_Template(rf, ctx) {},\n    styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_VisuallyHiddenLoader, [{\n    type: Component,\n    args: [{\n      exportAs: 'cdkVisuallyHidden',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _VisuallyHiddenLoader };", "map": {"version": 3, "names": ["_", "_CdkPrivateStyleLoader", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "_VisuallyHiddenLoader", "ɵfac", "_VisuallyHiddenLoader_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "exportAs", "decls", "vars", "template", "_VisuallyHiddenLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/private.mjs"], "sourcesContent": ["export { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _VisuallyHiddenLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _VisuallyHiddenLoader, isStandalone: true, selector: \"ng-component\", exportAs: [\"cdkVisuallyHidden\"], ngImport: i0, template: '', isInline: true, styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _VisuallyHiddenLoader, decorators: [{\n            type: Component,\n            args: [{ exportAs: 'cdkVisuallyHidden', encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"] }]\n        }] });\n\nexport { _VisuallyHiddenLoader };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;;AAErF;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB,OAAOC,IAAI,YAAAC,8BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,qBAAqB;EAAA;EACxH,OAAOI,IAAI,kBAD8ER,EAAE,CAAAS,iBAAA;IAAAC,IAAA,EACJN,qBAAqB;IAAAO,SAAA;IAAAC,QAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAChH;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FtB,EAAE,CAAAuB,iBAAA,CAGJnB,qBAAqB,EAAc,CAAC;IACnHM,IAAI,EAAET,SAAS;IACfuB,IAAI,EAAE,CAAC;MAAEZ,QAAQ,EAAE,mBAAmB;MAAEQ,aAAa,EAAElB,iBAAiB,CAACuB,IAAI;MAAEV,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAElB,uBAAuB,CAACuB,MAAM;MAAEP,MAAM,EAAE,CAAC,oQAAoQ;IAAE,CAAC;EACla,CAAC,CAAC;AAAA;AAEV,SAASf,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}