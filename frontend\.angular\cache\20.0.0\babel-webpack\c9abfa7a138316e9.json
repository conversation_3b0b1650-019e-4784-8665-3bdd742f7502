{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Disable2FADialogComponent } from '../disable-2fa-dialog/disable-2fa-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/two-factor.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"../../../services/auth.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/divider\";\nimport * as i14 from \"./two-factor-setup.component\";\nfunction TwoFactorManagementComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-two-factor-setup\", 6);\n    i0.ɵɵlistener(\"setupComplete\", function TwoFactorManagementComponent_div_2_Template_app_two_factor_setup_setupComplete_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSetupComplete());\n    })(\"cancelled\", function TwoFactorManagementComponent_div_2_Template_app_two_factor_setup_cancelled_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSetupCancelled());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"mat-icon\", 13);\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Enhance your account security\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Two-factor authentication adds an extra layer of security to your account by requiring a second form of verification when signing in.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 15)(11, \"h4\");\n    i0.ɵɵtext(12, \"Benefits:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"ul\")(14, \"li\");\n    i0.ɵɵtext(15, \"Protects against password theft\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\");\n    i0.ɵɵtext(17, \"Reduces risk of unauthorized access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"Works with popular authenticator apps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵtext(21, \"Includes backup codes for recovery\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\", 16)(23, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_12_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.enableTwoFactor());\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"add_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Enable Two-Factor Authentication \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" 2FA code is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code must be 6 digits \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_spinner_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 44);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Disable 2FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 35)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Disable Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n    i0.ɵɵtext(6, \"Enter your password and a 2FA code to confirm\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 36);\n    i0.ɵɵlistener(\"ngSubmit\", function TwoFactorManagementComponent_div_3_div_13_div_41_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDisableTwoFactor());\n    });\n    i0.ɵɵelementStart(9, \"mat-form-field\", 37)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 38);\n    i0.ɵɵtemplate(13, TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_13_Template, 2, 0, \"mat-error\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 37)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"6-digit 2FA Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 39);\n    i0.ɵɵtemplate(18, TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_18_Template, 2, 0, \"mat-error\", 2)(19, TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_19_Template, 2, 0, \"mat-error\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 40)(21, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_div_41_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.hideDisableForm());\n    });\n    i0.ɵɵtext(22, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 42);\n    i0.ɵɵtemplate(24, TwoFactorManagementComponent_div_3_div_13_div_41_mat_spinner_24_Template, 1, 0, \"mat-spinner\", 43)(25, TwoFactorManagementComponent_div_3_div_13_div_41_span_25_Template, 2, 0, \"span\", 2);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.disableForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.disableForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.disableForm.get(\"code\")) == null ? null : tmp_5_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r1.disableForm.get(\"code\")) == null ? null : tmp_6_0.hasError(\"pattern\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading || ctx_r1.disableForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"mat-icon\", 20);\n    i0.ɵɵtext(3, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Your account is protected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Two-factor authentication is active on your account. You'll be prompted for a verification code when signing in from new devices.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 22)(11, \"div\", 23)(12, \"h4\");\n    i0.ɵɵtext(13, \"Test & Manage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 24)(15, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendTestCode());\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"send\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Send Test Code \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(19, \"mat-divider\");\n    i0.ɵɵelementStart(20, \"div\", 26)(21, \"h4\");\n    i0.ɵɵtext(22, \"Danger Zone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\", 27)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Disabling 2FA will make your account less secure \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 28)(28, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showDisableForm2FA());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"remove_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Disable with Password & Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 30)(33, \"p\", 31)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"help_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" Can't access your authenticator device? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openEmailDisableDialog());\n    });\n    i0.ɵɵelementStart(38, \"mat-icon\");\n    i0.ɵɵtext(39, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" Disable via Email Verification \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(41, TwoFactorManagementComponent_div_3_div_13_div_41_Template, 26, 8, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading || ctx_r1.showDisableForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showDisableForm);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-card\", 8)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Two-Factor Authentication \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8, \" Status: \");\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\");\n    i0.ɵɵtemplate(12, TwoFactorManagementComponent_div_3_div_12_Template, 27, 1, \"div\", 9)(13, TwoFactorManagementComponent_div_3_div_13_Template, 42, 4, \"div\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r1.twoFactorStatus.enabled ? \"enabled-icon\" : \"disabled-icon\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.twoFactorStatus.enabled ? \"security\" : \"security\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r1.twoFactorStatus.enabled ? \"status-enabled\" : \"status-disabled\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.twoFactorStatus.enabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.twoFactorStatus.enabled);\n  }\n}\nexport class TwoFactorManagementComponent {\n  constructor(twoFactorService, formBuilder, snackBar, dialog, authService) {\n    this.twoFactorService = twoFactorService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.twoFactorStatus = {\n      enabled: false\n    };\n    this.loading = false;\n    this.showSetup = false;\n    this.showDisableForm = false;\n    this.disableForm = this.formBuilder.group({\n      password: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n  ngOnInit() {\n    this.loadStatus();\n  }\n  loadStatus() {\n    this.loading = true;\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: status => {\n        this.twoFactorStatus = status;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Failed to load 2FA status:', error);\n        this.loading = false;\n      }\n    });\n  }\n  enableTwoFactor() {\n    this.showSetup = true;\n  }\n  onSetupComplete() {\n    this.showSetup = false;\n    this.loadStatus();\n    this.snackBar.open('Two-factor authentication has been enabled!', 'Close', {\n      duration: 5000\n    });\n  }\n  onSetupCancelled() {\n    this.showSetup = false;\n  }\n  showDisableForm2FA() {\n    this.showDisableForm = true;\n  }\n  hideDisableForm() {\n    this.showDisableForm = false;\n    this.disableForm.reset();\n  }\n  onDisableTwoFactor() {\n    if (this.disableForm.invalid) {\n      this.markFormGroupTouched(this.disableForm);\n      return;\n    }\n    this.loading = true;\n    const {\n      password,\n      code\n    } = this.disableForm.value;\n    this.twoFactorService.disable2FA(code).subscribe({\n      next: response => {\n        this.snackBar.open('Two-factor authentication has been disabled!', 'Close', {\n          duration: 5000\n        });\n        this.hideDisableForm();\n        this.loadStatus();\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to disable 2FA', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  sendTestCode() {\n    this.loading = true;\n    this.twoFactorService.send2FASMS().subscribe({\n      next: response => {\n        this.snackBar.open('Test code sent to your phone!', 'Close', {\n          duration: 3000\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to send test code', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  /**\n   * Open email disable dialog for users who can't access their authenticator\n   */\n  openEmailDisableDialog() {\n    const currentUser = this.authService.currentUserValue;\n    if (!currentUser) {\n      this.snackBar.open('User information not available. Please refresh and try again.', 'Close', {\n        duration: 5000\n      });\n      return;\n    }\n    const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n      width: '500px',\n      maxWidth: '90vw',\n      disableClose: false,\n      data: {\n        email: currentUser.email,\n        allCodesUsed: false,\n        // From profile, codes may not be exhausted\n        source: 'profile'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        // Email sent successfully, show confirmation\n        this.snackBar.open('Disable confirmation email sent! Check your inbox for the confirmation link.', 'Close', {\n          duration: 8000,\n          panelClass: ['success-snackbar']\n        });\n      }\n    });\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static #_ = this.ɵfac = function TwoFactorManagementComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TwoFactorManagementComponent)(i0.ɵɵdirectiveInject(i1.TwoFactorService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TwoFactorManagementComponent,\n    selectors: [[\"app-two-factor-management\"]],\n    standalone: false,\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"two-factor-management\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"management-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"30\"], [3, \"setupComplete\", \"cancelled\"], [1, \"management-container\"], [1, \"status-card\"], [\"class\", \"disabled-state\", 4, \"ngIf\"], [\"class\", \"enabled-state\", 4, \"ngIf\"], [1, \"disabled-state\"], [1, \"info-section\"], [1, \"info-icon\"], [1, \"info-content\"], [1, \"benefits\"], [1, \"action-section\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"enabled-state\"], [1, \"success-section\"], [1, \"success-icon\"], [1, \"success-content\"], [1, \"management-actions\"], [1, \"action-group\"], [1, \"button-group\"], [\"mat-stroked-button\", \"\", 3, \"click\", \"disabled\"], [1, \"danger-zone\"], [1, \"warning-text\"], [1, \"disable-options\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [1, \"alternative-disable\"], [1, \"help-text\"], [\"mat-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"class\", \"disable-form-container\", 4, \"ngIf\"], [1, \"disable-form-container\"], [1, \"disable-card\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"123456\", \"maxlength\", \"6\", \"autocomplete\", \"off\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"]],\n    template: function TwoFactorManagementComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, TwoFactorManagementComponent_div_1_Template, 2, 0, \"div\", 1)(2, TwoFactorManagementComponent_div_2_Template, 2, 0, \"div\", 2)(3, TwoFactorManagementComponent_div_3_Template, 14, 8, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.showSetup);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showSetup);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.showSetup);\n      }\n    },\n    dependencies: [i6.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.FormGroupDirective, i2.FormControlName, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatButton, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatError, i11.MatInput, i12.MatProgressSpinner, i13.MatDivider, i14.TwoFactorSetupComponent],\n    styles: [\".two-factor-management[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .enabled-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .disabled-icon[_ngcontent-%COMP%] {\\n  color: #757575;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]   .status-enabled[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-weight: 500;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]   .status-disabled[_ngcontent-%COMP%] {\\n  color: #757575;\\n  font-weight: 500;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n  margin-top: 4px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-of-type {\\n  margin-bottom: 16px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  color: #666;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n  padding: 16px;\\n  background-color: #e8f5e8;\\n  border-radius: 8px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #333;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #d32f2f;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 16px 0;\\n  color: #f57c00;\\n  font-size: 14px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]    > button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 16px;\\n  text-align: center;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  margin: 0 0 12px 0;\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  color: #6c757d;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%] {\\n  border: 1px solid #ffcdd2;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 16px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 600px) {\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%] {\\n    align-self: center;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n    align-self: center;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]    > button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "Disable2FADialogComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "TwoFactorManagementComponent_div_2_Template_app_two_factor_setup_setupComplete_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onSetupComplete", "TwoFactorManagementComponent_div_2_Template_app_two_factor_setup_cancelled_1_listener", "onSetupCancelled", "ɵɵtext", "TwoFactorManagementComponent_div_3_div_12_Template_button_click_23_listener", "_r3", "enableTwoFactor", "ɵɵadvance", "ɵɵproperty", "loading", "TwoFactorManagementComponent_div_3_div_13_div_41_Template_form_ngSubmit_8_listener", "_r5", "onDisableTwoFactor", "ɵɵtemplate", "TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_13_Template", "TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_18_Template", "TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_19_Template", "TwoFactorManagementComponent_div_3_div_13_div_41_Template_button_click_21_listener", "hideDisableForm", "TwoFactorManagementComponent_div_3_div_13_div_41_mat_spinner_24_Template", "TwoFactorManagementComponent_div_3_div_13_div_41_span_25_Template", "disableForm", "tmp_4_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_5_0", "tmp_6_0", "invalid", "TwoFactorManagementComponent_div_3_div_13_Template_button_click_15_listener", "_r4", "sendTestCode", "TwoFactorManagementComponent_div_3_div_13_Template_button_click_28_listener", "showDisableForm2FA", "TwoFactorManagementComponent_div_3_div_13_Template_button_click_37_listener", "openEmailDisableDialog", "TwoFactorManagementComponent_div_3_div_13_div_41_Template", "showDisableForm", "TwoFactorManagementComponent_div_3_div_12_Template", "TwoFactorManagementComponent_div_3_div_13_Template", "ɵɵclassMap", "twoFactorStatus", "enabled", "ɵɵtextInterpolate1", "TwoFactorManagementComponent", "constructor", "twoFactorService", "formBuilder", "snackBar", "dialog", "authService", "showSetup", "group", "password", "required", "code", "pattern", "ngOnInit", "loadStatus", "get2FAStatus", "subscribe", "next", "status", "error", "console", "open", "duration", "reset", "markFormGroupTouched", "value", "disable2FA", "response", "message", "send2FASMS", "currentUser", "currentUserValue", "dialogRef", "width", "max<PERSON><PERSON><PERSON>", "disableClose", "data", "email", "allCodesUsed", "source", "afterClosed", "result", "success", "panelClass", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "TwoFactorService", "i2", "FormBuilder", "i3", "MatSnackBar", "i4", "MatDialog", "i5", "AuthService", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "TwoFactorManagementComponent_Template", "rf", "ctx", "TwoFactorManagementComponent_div_1_Template", "TwoFactorManagementComponent_div_2_Template", "TwoFactorManagementComponent_div_3_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\two-factor\\two-factor-management.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\components\\auth\\two-factor\\two-factor-management.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { TwoFactorService } from '../../../services/two-factor.service';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { Disable2FADialogComponent } from '../disable-2fa-dialog/disable-2fa-dialog.component';\r\n\r\n@Component({\r\n  selector: 'app-two-factor-management',\r\n  templateUrl: './two-factor-management.component.html',\r\n  styleUrls: ['./two-factor-management.component.scss'],\r\n  standalone: false\r\n})\r\nexport class TwoFactorManagementComponent implements OnInit {\r\n  twoFactorStatus = { enabled: false };\r\n  loading = false;\r\n  showSetup = false;\r\n  disableForm: FormGroup;\r\n  showDisableForm = false;\r\n\r\n  constructor(\r\n    private twoFactorService: TwoFactorService,\r\n    private formBuilder: FormBuilder,\r\n    private snackBar: MatSnackBar,\r\n    private dialog: MatDialog,\r\n    private authService: AuthService\r\n  ) {\r\n    this.disableForm = this.formBuilder.group({\r\n      password: ['', [Validators.required]],\r\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadStatus();\r\n  }\r\n\r\n  loadStatus(): void {\r\n    this.loading = true;\r\n    this.twoFactorService.get2FAStatus().subscribe({\r\n      next: (status) => {\r\n        this.twoFactorStatus = status;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to load 2FA status:', error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  enableTwoFactor(): void {\r\n    this.showSetup = true;\r\n  }\r\n\r\n  onSetupComplete(): void {\r\n    this.showSetup = false;\r\n    this.loadStatus();\r\n    this.snackBar.open('Two-factor authentication has been enabled!', 'Close', { duration: 5000 });\r\n  }\r\n\r\n  onSetupCancelled(): void {\r\n    this.showSetup = false;\r\n  }\r\n\r\n  showDisableForm2FA(): void {\r\n    this.showDisableForm = true;\r\n  }\r\n\r\n  hideDisableForm(): void {\r\n    this.showDisableForm = false;\r\n    this.disableForm.reset();\r\n  }\r\n\r\n  onDisableTwoFactor(): void {\r\n    if (this.disableForm.invalid) {\r\n      this.markFormGroupTouched(this.disableForm);\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const { password, code } = this.disableForm.value;\r\n\r\n    this.twoFactorService.disable2FA(code).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open('Two-factor authentication has been disabled!', 'Close', { duration: 5000 });\r\n        this.hideDisableForm();\r\n        this.loadStatus();\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(\r\n          error.error?.message || 'Failed to disable 2FA',\r\n          'Close',\r\n          { duration: 5000 }\r\n        );\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  sendTestCode(): void {\r\n    this.loading = true;\r\n    this.twoFactorService.send2FASMS().subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open('Test code sent to your phone!', 'Close', { duration: 3000 });\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(\r\n          error.error?.message || 'Failed to send test code',\r\n          'Close',\r\n          { duration: 5000 }\r\n        );\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Open email disable dialog for users who can't access their authenticator\r\n   */\r\n  openEmailDisableDialog(): void {\r\n    const currentUser = this.authService.currentUserValue;\r\n    if (!currentUser) {\r\n      this.snackBar.open('User information not available. Please refresh and try again.', 'Close', { duration: 5000 });\r\n      return;\r\n    }\r\n\r\n    const dialogRef = this.dialog.open(Disable2FADialogComponent, {\r\n      width: '500px',\r\n      maxWidth: '90vw',\r\n      disableClose: false,\r\n      data: {\r\n        email: currentUser.email,\r\n        allCodesUsed: false, // From profile, codes may not be exhausted\r\n        source: 'profile'\r\n      }\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result?.success) {\r\n        // Email sent successfully, show confirmation\r\n        this.snackBar.open(\r\n          'Disable confirmation email sent! Check your inbox for the confirmation link.',\r\n          'Close',\r\n          { duration: 8000, panelClass: ['success-snackbar'] }\r\n        );\r\n      }\r\n    });\r\n  }\r\n\r\n  private markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n}\r\n", "<div class=\"two-factor-management\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading && !showSetup\" class=\"loading-container\">\r\n    <mat-spinner diameter=\"30\"></mat-spinner>\r\n  </div>\r\n\r\n  <!-- Setup Mode -->\r\n  <div *ngIf=\"showSetup\">\r\n    <app-two-factor-setup\r\n      (setupComplete)=\"onSetupComplete()\"\r\n      (cancelled)=\"onSetupCancelled()\">\r\n    </app-two-factor-setup>\r\n  </div>\r\n\r\n  <!-- Management Mode -->\r\n  <div *ngIf=\"!loading && !showSetup\" class=\"management-container\">\r\n    <mat-card class=\"status-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon [class]=\"twoFactorStatus.enabled ? 'enabled-icon' : 'disabled-icon'\">\r\n            {{ twoFactorStatus.enabled ? 'security' : 'security' }}\r\n          </mat-icon>\r\n          Two-Factor Authentication\r\n        </mat-card-title>\r\n        <mat-card-subtitle>\r\n          Status: \r\n          <span [class]=\"twoFactorStatus.enabled ? 'status-enabled' : 'status-disabled'\">\r\n            {{ twoFactorStatus.enabled ? 'Enabled' : 'Disabled' }}\r\n          </span>\r\n        </mat-card-subtitle>\r\n      </mat-card-header>\r\n\r\n      <mat-card-content>\r\n        <!-- 2FA Disabled State -->\r\n        <div *ngIf=\"!twoFactorStatus.enabled\" class=\"disabled-state\">\r\n          <div class=\"info-section\">\r\n            <mat-icon class=\"info-icon\">info</mat-icon>\r\n            <div class=\"info-content\">\r\n              <p><strong>Enhance your account security</strong></p>\r\n              <p>Two-factor authentication adds an extra layer of security to your account by requiring a second form of verification when signing in.</p>\r\n              \r\n              <div class=\"benefits\">\r\n                <h4>Benefits:</h4>\r\n                <ul>\r\n                  <li>Protects against password theft</li>\r\n                  <li>Reduces risk of unauthorized access</li>\r\n                  <li>Works with popular authenticator apps</li>\r\n                  <li>Includes backup codes for recovery</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"action-section\">\r\n            <button \r\n              mat-raised-button \r\n              color=\"primary\" \r\n              (click)=\"enableTwoFactor()\"\r\n              [disabled]=\"loading\">\r\n              <mat-icon>add_circle</mat-icon>\r\n              Enable Two-Factor Authentication\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 2FA Enabled State -->\r\n        <div *ngIf=\"twoFactorStatus.enabled\" class=\"enabled-state\">\r\n          <div class=\"success-section\">\r\n            <mat-icon class=\"success-icon\">check_circle</mat-icon>\r\n            <div class=\"success-content\">\r\n              <p><strong>Your account is protected</strong></p>\r\n              <p>Two-factor authentication is active on your account. You'll be prompted for a verification code when signing in from new devices.</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"management-actions\">\r\n            <div class=\"action-group\">\r\n              <h4>Test & Manage</h4>\r\n              <div class=\"button-group\">\r\n                <button \r\n                  mat-stroked-button \r\n                  (click)=\"sendTestCode()\"\r\n                  [disabled]=\"loading\">\r\n                  <mat-icon>send</mat-icon>\r\n                  Send Test Code\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <mat-divider></mat-divider>\r\n\r\n            <div class=\"danger-zone\">\r\n              <h4>Danger Zone</h4>\r\n              <p class=\"warning-text\">\r\n                <mat-icon>warning</mat-icon>\r\n                Disabling 2FA will make your account less secure\r\n              </p>\r\n              \r\n              <div class=\"disable-options\">\r\n                <button \r\n                  mat-stroked-button \r\n                  color=\"warn\"\r\n                  (click)=\"showDisableForm2FA()\"\r\n                  [disabled]=\"loading || showDisableForm\">\r\n                  <mat-icon>remove_circle</mat-icon>\r\n                  Disable with Password & Code\r\n                </button>\r\n\r\n                <div class=\"alternative-disable\">\r\n                  <p class=\"help-text\">\r\n                    <mat-icon>help_outline</mat-icon>\r\n                    Can't access your authenticator device?\r\n                  </p>\r\n                  <button \r\n                    mat-button \r\n                    color=\"accent\"\r\n                    (click)=\"openEmailDisableDialog()\"\r\n                    [disabled]=\"loading\">\r\n                    <mat-icon>email</mat-icon>\r\n                    Disable via Email Verification\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Disable Form -->\r\n          <div *ngIf=\"showDisableForm\" class=\"disable-form-container\">\r\n            <mat-card class=\"disable-card\">\r\n              <mat-card-header>\r\n                <mat-card-title>Disable Two-Factor Authentication</mat-card-title>\r\n                <mat-card-subtitle>Enter your password and a 2FA code to confirm</mat-card-subtitle>\r\n              </mat-card-header>\r\n\r\n              <mat-card-content>\r\n                <form [formGroup]=\"disableForm\" (ngSubmit)=\"onDisableTwoFactor()\">\r\n                  <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>Current Password</mat-label>\r\n                    <input \r\n                      matInput \r\n                      type=\"password\"\r\n                      formControlName=\"password\"\r\n                      autocomplete=\"current-password\">\r\n                    <mat-error *ngIf=\"disableForm.get('password')?.hasError('required')\">\r\n                      Password is required\r\n                    </mat-error>\r\n                  </mat-form-field>\r\n\r\n                  <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>6-digit 2FA Code</mat-label>\r\n                    <input \r\n                      matInput \r\n                      formControlName=\"code\"\r\n                      placeholder=\"123456\"\r\n                      maxlength=\"6\"\r\n                      autocomplete=\"off\">\r\n                    <mat-error *ngIf=\"disableForm.get('code')?.hasError('required')\">\r\n                      2FA code is required\r\n                    </mat-error>\r\n                    <mat-error *ngIf=\"disableForm.get('code')?.hasError('pattern')\">\r\n                      Code must be 6 digits\r\n                    </mat-error>\r\n                  </mat-form-field>\r\n\r\n                  <div class=\"form-actions\">\r\n                    <button mat-button type=\"button\" (click)=\"hideDisableForm()\" [disabled]=\"loading\">\r\n                      Cancel\r\n                    </button>\r\n                    <button mat-raised-button color=\"warn\" type=\"submit\" [disabled]=\"loading || disableForm.invalid\">\r\n                      <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\r\n                      <span *ngIf=\"!loading\">Disable 2FA</span>\r\n                    </button>\r\n                  </div>\r\n                </form>\r\n              </mat-card-content>\r\n            </mat-card>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAASC,yBAAyB,QAAQ,oDAAoD;;;;;;;;;;;;;;;;;;ICJ5FC,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAE,SAAA,qBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIJH,EADF,CAAAC,cAAA,UAAuB,8BAGc;IAAjCD,EADA,CAAAI,UAAA,2BAAAC,0FAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAiBF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC,uBAAAC,sFAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CACtBF,MAAA,CAAAK,gBAAA,EAAkB;IAAA,EAAC;IAEpCb,EADE,CAAAG,YAAA,EAAuB,EACnB;;;;;;IAwBIH,EAFJ,CAAAC,cAAA,cAA6D,cACjC,mBACI;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAEtCH,EADL,CAAAC,cAAA,cAA0B,QACrB,aAAQ;IAAAD,EAAA,CAAAc,MAAA,oCAA6B;IAASd,EAAT,CAAAG,YAAA,EAAS,EAAI;IACrDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,4IAAqI;IAAAd,EAAA,CAAAG,YAAA,EAAI;IAG1IH,EADF,CAAAC,cAAA,eAAsB,UAChB;IAAAD,EAAA,CAAAc,MAAA,iBAAS;IAAAd,EAAA,CAAAG,YAAA,EAAK;IAEhBH,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAc,MAAA,uCAA+B;IAAAd,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,2CAAmC;IAAAd,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,6CAAqC;IAAAd,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,0CAAkC;IAI9Cd,EAJ8C,CAAAG,YAAA,EAAK,EACxC,EACD,EACF,EACF;IAGJH,EADF,CAAAC,cAAA,eAA4B,kBAKH;IADrBD,EAAA,CAAAI,UAAA,mBAAAW,4EAAA;MAAAf,EAAA,CAAAM,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAS,eAAA,EAAiB;IAAA,EAAC;IAE3BjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,kBAAU;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAc,MAAA,0CACF;IAEJd,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IALAH,EAAA,CAAAkB,SAAA,IAAoB;IAApBlB,EAAA,CAAAmB,UAAA,aAAAX,MAAA,CAAAY,OAAA,CAAoB;;;;;IAqFdpB,EAAA,CAAAC,cAAA,gBAAqE;IACnED,EAAA,CAAAc,MAAA,6BACF;IAAAd,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAc,MAAA,6BACF;IAAAd,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAc,MAAA,8BACF;IAAAd,EAAA,CAAAG,YAAA,EAAY;;;;;IAQVH,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAG,YAAA,EAAO;;;;;;IAxC/CH,EAHN,CAAAC,cAAA,cAA4D,mBAC3B,sBACZ,qBACC;IAAAD,EAAA,CAAAc,MAAA,wCAAiC;IAAAd,EAAA,CAAAG,YAAA,EAAiB;IAClEH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAc,MAAA,oDAA6C;IAClEd,EADkE,CAAAG,YAAA,EAAoB,EACpE;IAGhBH,EADF,CAAAC,cAAA,uBAAkB,eACkD;IAAlCD,EAAA,CAAAI,UAAA,sBAAAiB,mFAAA;MAAArB,EAAA,CAAAM,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAe,kBAAA,EAAoB;IAAA,EAAC;IAE7DvB,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,iBAIkC;IAClCF,EAAA,CAAAwB,UAAA,KAAAC,sEAAA,uBAAqE;IAGvEzB,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,iBAKqB;IAIrBF,EAHA,CAAAwB,UAAA,KAAAE,sEAAA,uBAAiE,KAAAC,sEAAA,uBAGD;IAGlE3B,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,eAA0B,kBAC0D;IAAjDD,EAAA,CAAAI,UAAA,mBAAAwB,mFAAA;MAAA5B,EAAA,CAAAM,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqB,eAAA,EAAiB;IAAA,EAAC;IAC1D7B,EAAA,CAAAc,MAAA,gBACF;IAAAd,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAiG;IAE/FD,EADA,CAAAwB,UAAA,KAAAM,wEAAA,0BAA2C,KAAAC,iEAAA,kBACpB;IAMnC/B,EALU,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV,EACP;;;;;;;IAzCMH,EAAA,CAAAkB,SAAA,GAAyB;IAAzBlB,EAAA,CAAAmB,UAAA,cAAAX,MAAA,CAAAwB,WAAA,CAAyB;IAQfhC,EAAA,CAAAkB,SAAA,GAAuD;IAAvDlB,EAAA,CAAAmB,UAAA,UAAAc,OAAA,GAAAzB,MAAA,CAAAwB,WAAA,CAAAE,GAAA,+BAAAD,OAAA,CAAAE,QAAA,aAAuD;IAavDnC,EAAA,CAAAkB,SAAA,GAAmD;IAAnDlB,EAAA,CAAAmB,UAAA,UAAAiB,OAAA,GAAA5B,MAAA,CAAAwB,WAAA,CAAAE,GAAA,2BAAAE,OAAA,CAAAD,QAAA,aAAmD;IAGnDnC,EAAA,CAAAkB,SAAA,EAAkD;IAAlDlB,EAAA,CAAAmB,UAAA,UAAAkB,OAAA,GAAA7B,MAAA,CAAAwB,WAAA,CAAAE,GAAA,2BAAAG,OAAA,CAAAF,QAAA,YAAkD;IAMDnC,EAAA,CAAAkB,SAAA,GAAoB;IAApBlB,EAAA,CAAAmB,UAAA,aAAAX,MAAA,CAAAY,OAAA,CAAoB;IAG5BpB,EAAA,CAAAkB,SAAA,GAA2C;IAA3ClB,EAAA,CAAAmB,UAAA,aAAAX,MAAA,CAAAY,OAAA,IAAAZ,MAAA,CAAAwB,WAAA,CAAAM,OAAA,CAA2C;IAChFtC,EAAA,CAAAkB,SAAA,EAAa;IAAblB,EAAA,CAAAmB,UAAA,SAAAX,MAAA,CAAAY,OAAA,CAAa;IACpBpB,EAAA,CAAAkB,SAAA,EAAc;IAAdlB,EAAA,CAAAmB,UAAA,UAAAX,MAAA,CAAAY,OAAA,CAAc;;;;;;IAtG/BpB,EAFJ,CAAAC,cAAA,cAA2D,cAC5B,mBACI;IAAAD,EAAA,CAAAc,MAAA,mBAAY;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAEjDH,EADL,CAAAC,cAAA,cAA6B,QACxB,aAAQ;IAAAD,EAAA,CAAAc,MAAA,gCAAyB;IAASd,EAAT,CAAAG,YAAA,EAAS,EAAI;IACjDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,wIAAiI;IAExId,EAFwI,CAAAG,YAAA,EAAI,EACpI,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAgC,eACJ,UACpB;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAAAd,EAAA,CAAAG,YAAA,EAAK;IAEpBH,EADF,CAAAC,cAAA,eAA0B,kBAID;IADrBD,EAAA,CAAAI,UAAA,mBAAAmC,4EAAA;MAAAvC,EAAA,CAAAM,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,YAAA,EAAc;IAAA,EAAC;IAExBzC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,YAAI;IAAAd,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAc,MAAA,wBACF;IAEJd,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAENH,EAAA,CAAAE,SAAA,mBAA2B;IAGzBF,EADF,CAAAC,cAAA,eAAyB,UACnB;IAAAD,EAAA,CAAAc,MAAA,mBAAW;IAAAd,EAAA,CAAAG,YAAA,EAAK;IAElBH,EADF,CAAAC,cAAA,aAAwB,gBACZ;IAAAD,EAAA,CAAAc,MAAA,eAAO;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAc,MAAA,0DACF;IAAAd,EAAA,CAAAG,YAAA,EAAI;IAGFH,EADF,CAAAC,cAAA,eAA6B,kBAKe;IADxCD,EAAA,CAAAI,UAAA,mBAAAsC,4EAAA;MAAA1C,EAAA,CAAAM,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmC,kBAAA,EAAoB;IAAA,EAAC;IAE9B3C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAc,MAAA,sCACF;IAAAd,EAAA,CAAAG,YAAA,EAAS;IAILH,EAFJ,CAAAC,cAAA,eAAiC,aACV,gBACT;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAc,MAAA,iDACF;IAAAd,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,kBAIuB;IADrBD,EAAA,CAAAI,UAAA,mBAAAwC,4EAAA;MAAA5C,EAAA,CAAAM,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqC,sBAAA,EAAwB;IAAA,EAAC;IAElC7C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAc,MAAA,wCACF;IAIRd,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAGNH,EAAA,CAAAwB,UAAA,KAAAsB,yDAAA,mBAA4D;IAkD9D9C,EAAA,CAAAG,YAAA,EAAM;;;;IA/FIH,EAAA,CAAAkB,SAAA,IAAoB;IAApBlB,EAAA,CAAAmB,UAAA,aAAAX,MAAA,CAAAY,OAAA,CAAoB;IAqBpBpB,EAAA,CAAAkB,SAAA,IAAuC;IAAvClB,EAAA,CAAAmB,UAAA,aAAAX,MAAA,CAAAY,OAAA,IAAAZ,MAAA,CAAAuC,eAAA,CAAuC;IAcrC/C,EAAA,CAAAkB,SAAA,GAAoB;IAApBlB,EAAA,CAAAmB,UAAA,aAAAX,MAAA,CAAAY,OAAA,CAAoB;IAUxBpB,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAAmB,UAAA,SAAAX,MAAA,CAAAuC,eAAA,CAAqB;;;;;IA5G3B/C,EAJR,CAAAC,cAAA,aAAiE,kBACjC,sBACX,qBACC,eACiE;IAC7ED,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAc,MAAA,kCACF;IAAAd,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IACjBD,EAAA,CAAAc,MAAA,gBACA;IAAAd,EAAA,CAAAC,cAAA,WAA+E;IAC7ED,EAAA,CAAAc,MAAA,IACF;IAEJd,EAFI,CAAAG,YAAA,EAAO,EACW,EACJ;IAElBH,EAAA,CAAAC,cAAA,wBAAkB;IAkChBD,EAhCA,CAAAwB,UAAA,KAAAwB,kDAAA,kBAA6D,KAAAC,kDAAA,mBAgCF;IAkHjEjD,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;;;;IAjKYH,EAAA,CAAAkB,SAAA,GAAoE;IAApElB,EAAA,CAAAkD,UAAA,CAAA1C,MAAA,CAAA2C,eAAA,CAAAC,OAAA,oCAAoE;IAC5EpD,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAqD,kBAAA,MAAA7C,MAAA,CAAA2C,eAAA,CAAAC,OAAA,gCACF;IAKMpD,EAAA,CAAAkB,SAAA,GAAwE;IAAxElB,EAAA,CAAAkD,UAAA,CAAA1C,MAAA,CAAA2C,eAAA,CAAAC,OAAA,wCAAwE;IAC5EpD,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAqD,kBAAA,MAAA7C,MAAA,CAAA2C,eAAA,CAAAC,OAAA,+BACF;IAMIpD,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAmB,UAAA,UAAAX,MAAA,CAAA2C,eAAA,CAAAC,OAAA,CAA8B;IAgC9BpD,EAAA,CAAAkB,SAAA,EAA6B;IAA7BlB,EAAA,CAAAmB,UAAA,SAAAX,MAAA,CAAA2C,eAAA,CAAAC,OAAA,CAA6B;;;ADpD3C,OAAM,MAAOE,4BAA4B;EAOvCC,YACUC,gBAAkC,EAClCC,WAAwB,EACxBC,QAAqB,EACrBC,MAAiB,EACjBC,WAAwB;IAJxB,KAAAJ,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAXrB,KAAAT,eAAe,GAAG;MAAEC,OAAO,EAAE;IAAK,CAAE;IACpC,KAAAhC,OAAO,GAAG,KAAK;IACf,KAAAyC,SAAS,GAAG,KAAK;IAEjB,KAAAd,eAAe,GAAG,KAAK;IASrB,IAAI,CAACf,WAAW,GAAG,IAAI,CAACyB,WAAW,CAACK,KAAK,CAAC;MACxCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAACkE,QAAQ,CAAC,CAAC;MACrCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAACkE,QAAQ,EAAElE,UAAU,CAACoE,OAAO,CAAC,SAAS,CAAC,CAAC;KAChE,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAChD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACoC,gBAAgB,CAACa,YAAY,EAAE,CAACC,SAAS,CAAC;MAC7CC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACrB,eAAe,GAAGqB,MAAM;QAC7B,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACrD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAH,eAAeA,CAAA;IACb,IAAI,CAAC4C,SAAS,GAAG,IAAI;EACvB;EAEAlD,eAAeA,CAAA;IACb,IAAI,CAACkD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,CAACV,QAAQ,CAACiB,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EAChG;EAEA/D,gBAAgBA,CAAA;IACd,IAAI,CAACgD,SAAS,GAAG,KAAK;EACxB;EAEAlB,kBAAkBA,CAAA;IAChB,IAAI,CAACI,eAAe,GAAG,IAAI;EAC7B;EAEAlB,eAAeA,CAAA;IACb,IAAI,CAACkB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACf,WAAW,CAAC6C,KAAK,EAAE;EAC1B;EAEAtD,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACS,WAAW,CAACM,OAAO,EAAE;MAC5B,IAAI,CAACwC,oBAAoB,CAAC,IAAI,CAAC9C,WAAW,CAAC;MAC3C;IACF;IAEA,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,MAAM;MAAE2C,QAAQ;MAAEE;IAAI,CAAE,GAAG,IAAI,CAACjC,WAAW,CAAC+C,KAAK;IAEjD,IAAI,CAACvB,gBAAgB,CAACwB,UAAU,CAACf,IAAI,CAAC,CAACK,SAAS,CAAC;MAC/CC,IAAI,EAAGU,QAAQ,IAAI;QACjB,IAAI,CAACvB,QAAQ,CAACiB,IAAI,CAAC,8CAA8C,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC/F,IAAI,CAAC/C,eAAe,EAAE;QACtB,IAAI,CAACuC,UAAU,EAAE;QACjB,IAAI,CAAChD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACf,QAAQ,CAACiB,IAAI,CAChBF,KAAK,CAACA,KAAK,EAAES,OAAO,IAAI,uBAAuB,EAC/C,OAAO,EACP;UAAEN,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACxD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAqB,YAAYA,CAAA;IACV,IAAI,CAACrB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACoC,gBAAgB,CAAC2B,UAAU,EAAE,CAACb,SAAS,CAAC;MAC3CC,IAAI,EAAGU,QAAQ,IAAI;QACjB,IAAI,CAACvB,QAAQ,CAACiB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChF,IAAI,CAACxD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACf,QAAQ,CAACiB,IAAI,CAChBF,KAAK,CAACA,KAAK,EAAES,OAAO,IAAI,0BAA0B,EAClD,OAAO,EACP;UAAEN,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACxD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;;;EAGAyB,sBAAsBA,CAAA;IACpB,MAAMuC,WAAW,GAAG,IAAI,CAACxB,WAAW,CAACyB,gBAAgB;IACrD,IAAI,CAACD,WAAW,EAAE;MAChB,IAAI,CAAC1B,QAAQ,CAACiB,IAAI,CAAC,+DAA+D,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAChH;IACF;IAEA,MAAMU,SAAS,GAAG,IAAI,CAAC3B,MAAM,CAACgB,IAAI,CAAC5E,yBAAyB,EAAE;MAC5DwF,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,KAAK;MACnBC,IAAI,EAAE;QACJC,KAAK,EAAEP,WAAW,CAACO,KAAK;QACxBC,YAAY,EAAE,KAAK;QAAE;QACrBC,MAAM,EAAE;;KAEX,CAAC;IAEFP,SAAS,CAACQ,WAAW,EAAE,CAACxB,SAAS,CAACyB,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEC,OAAO,EAAE;QACnB;QACA,IAAI,CAACtC,QAAQ,CAACiB,IAAI,CAChB,8EAA8E,EAC9E,OAAO,EACP;UAAEC,QAAQ,EAAE,IAAI;UAAEqB,UAAU,EAAE,CAAC,kBAAkB;QAAC,CAAE,CACrD;MACH;IACF,CAAC,CAAC;EACJ;EAEQnB,oBAAoBA,CAACoB,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAChE,GAAG,CAACqE,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qCAhJUpD,4BAA4B,EAAAtD,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAO,EAAA,CAAAC,SAAA,GAAAnH,EAAA,CAAA2G,iBAAA,CAAAS,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA5BhE,4BAA4B;IAAAiE,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdzC9H,EAAA,CAAAC,cAAA,aAAmC;QAejCD,EAbA,CAAAwB,UAAA,IAAAwG,2CAAA,iBAA6D,IAAAC,2CAAA,iBAKtC,IAAAC,2CAAA,kBAQ0C;QAsKnElI,EAAA,CAAAG,YAAA,EAAM;;;QAnLEH,EAAA,CAAAkB,SAAA,EAA2B;QAA3BlB,EAAA,CAAAmB,UAAA,SAAA4G,GAAA,CAAA3G,OAAA,KAAA2G,GAAA,CAAAlE,SAAA,CAA2B;QAK3B7D,EAAA,CAAAkB,SAAA,EAAe;QAAflB,EAAA,CAAAmB,UAAA,SAAA4G,GAAA,CAAAlE,SAAA,CAAe;QAQf7D,EAAA,CAAAkB,SAAA,EAA4B;QAA5BlB,EAAA,CAAAmB,UAAA,UAAA4G,GAAA,CAAA3G,OAAA,KAAA2G,GAAA,CAAAlE,SAAA,CAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}