"""CORS Configuration

Configure Cross-Origin Resource Sharing (CORS) headers for API endpoints.
This allows frontend applications from different domains to access the API.
"""

CORS = {
    "paths": ["api/*"],  # Apply CORS to all API routes
    "allowed_methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    "allowed_origins": [
        "http://localhost:4200",  # Angular frontend
        "http://localhost:3000",  # Alternative frontend port
        "http://localhost:3001",  # Alternative frontend port
        "*"  # Allow all origins for development
    ],
    "allowed_origins_patterns": [],
    "allowed_headers": [
        "Origin",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Accept",
        "X-CSRF-Token",
        "x-ratelimit-limit",
        "x-ratelimit-remaining",
        "x-ratelimit-reset",
        "x-ratelimit-retryafter",
        "retry-after"
    ],
    "exposed_headers": [
        "x-ratelimit-limit",
        "x-ratelimit-remaining",
        "x-ratelimit-reset",
        "x-ratelimit-retryafter",
        "retry-after"
    ],
    "max_age": 86400,  # 24 hours
    "supports_credentials": True,  # Required for JWT authentication
}
