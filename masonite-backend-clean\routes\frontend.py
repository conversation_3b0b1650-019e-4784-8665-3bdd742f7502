"""Frontend Compatibility Routes - Essential routes without /api prefix"""

from masonite.routes import Route

ROUTES = [
    # Essential CORS preflight OPTIONS routes for frontend compatibility
    Route.options('/auth/signup', 'CorsController@preflight').name('frontend.cors.auth.signup'),
    Route.options('/auth/login', 'CorsController@preflight').name('frontend.cors.auth.login'),

    # Essential authentication endpoints that frontend needs (no /api prefix)
    Route.post('/auth/login', 'AuthController@login').name('frontend.auth.login').middleware('throttle:100/minute'),
    Route.post('/auth/signup', 'AuthController@register').name('frontend.auth.signup').middleware('throttle:50/minute'),
]
