{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const isAuthenticated = this.authService.isAuthenticated;\n    const isTokenExpired = this.authService.isTokenExpired();\n    const user = this.authService.currentUserValue;\n    const hasToken = !!this.authService.getToken();\n    console.log('🔒 Auth Guard - Route access check:', {\n      route: state.url,\n      isAuthenticated,\n      isTokenExpired,\n      hasToken,\n      hasUser: !!user,\n      userEmail: user?.email,\n      emailVerified: user?.emailVerified,\n      requireEmailVerification: route.data?.['requireEmailVerification']\n    });\n    if (isAuthenticated && !isTokenExpired) {\n      // Check if email verification is required\n      if (route.data?.['requireEmailVerification'] && !this.authService.isEmailVerified) {\n        console.log('🔒 Auth Guard - Email verification required, redirecting to verify-email');\n        this.router.navigate(['/auth/verify-email']);\n        return false;\n      }\n      // Check if 2FA is required\n      if (route.data?.['require2FA'] && !this.authService.isTwoFactorEnabled) {\n        console.log('🔒 Auth Guard - 2FA required, redirecting to setup-2fa');\n        this.router.navigate(['/auth/setup-2fa']);\n        return false;\n      }\n      console.log('✅ Auth Guard - Access granted');\n      return true;\n    }\n    // Not authenticated, redirect to login\n    console.log('❌ Auth Guard - Access denied, redirecting to login');\n    this.router.navigate(['/auth/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n  static #_ = this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated", "isTokenExpired", "user", "currentUserValue", "hasToken", "getToken", "console", "log", "url", "<PERSON><PERSON>ser", "userEmail", "email", "emailVerified", "requireEmailVerification", "data", "isEmailVerified", "navigate", "isTwoFactorEnabled", "queryParams", "returnUrl", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    const isAuthenticated = this.authService.isAuthenticated;\n    const isTokenExpired = this.authService.isTokenExpired();\n    const user = this.authService.currentUserValue;\n    const hasToken = !!this.authService.getToken();\n    \n    console.log('🔒 Auth Guard - Route access check:', {\n      route: state.url,\n      isAuthenticated,\n      isTokenExpired,\n      hasToken,\n      hasUser: !!user,\n      userEmail: user?.email,\n      emailVerified: user?.emailVerified,\n      requireEmailVerification: route.data?.['requireEmailVerification']\n    });\n    \n    if (isAuthenticated && !isTokenExpired) {\n      // Check if email verification is required\n      if (route.data?.['requireEmailVerification'] && !this.authService.isEmailVerified) {\n        console.log('🔒 Auth Guard - Email verification required, redirecting to verify-email');\n        this.router.navigate(['/auth/verify-email']);\n        return false;\n      }\n\n      // Check if 2FA is required\n      if (route.data?.['require2FA'] && !this.authService.isTwoFactorEnabled) {\n        console.log('🔒 Auth Guard - 2FA required, redirecting to setup-2fa');\n        this.router.navigate(['/auth/setup-2fa']);\n        return false;\n      }\n\n      console.log('✅ Auth Guard - Access granted');\n      return true;\n    }\n\n    // Not authenticated, redirect to login\n    console.log('❌ Auth Guard - Access denied, redirecting to login');\n    this.router.navigate(['/auth/login'], {\n      queryParams: { returnUrl: state.url }\n    });\n    return false;\n  }\n}\n"], "mappings": ";;;AAQA,OAAM,MAAOA,SAAS;EACpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,MAAMC,eAAe,GAAG,IAAI,CAACL,WAAW,CAACK,eAAe;IACxD,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACM,cAAc,EAAE;IACxD,MAAMC,IAAI,GAAG,IAAI,CAACP,WAAW,CAACQ,gBAAgB;IAC9C,MAAMC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACT,WAAW,CAACU,QAAQ,EAAE;IAE9CC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;MACjDT,KAAK,EAAEC,KAAK,CAACS,GAAG;MAChBR,eAAe;MACfC,cAAc;MACdG,QAAQ;MACRK,OAAO,EAAE,CAAC,CAACP,IAAI;MACfQ,SAAS,EAAER,IAAI,EAAES,KAAK;MACtBC,aAAa,EAAEV,IAAI,EAAEU,aAAa;MAClCC,wBAAwB,EAAEf,KAAK,CAACgB,IAAI,GAAG,0BAA0B;KAClE,CAAC;IAEF,IAAId,eAAe,IAAI,CAACC,cAAc,EAAE;MACtC;MACA,IAAIH,KAAK,CAACgB,IAAI,GAAG,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAACnB,WAAW,CAACoB,eAAe,EAAE;QACjFT,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;QACvF,IAAI,CAACX,MAAM,CAACoB,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAC5C,OAAO,KAAK;MACd;MAEA;MACA,IAAIlB,KAAK,CAACgB,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAACnB,WAAW,CAACsB,kBAAkB,EAAE;QACtEX,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,IAAI,CAACX,MAAM,CAACoB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACzC,OAAO,KAAK;MACd;MAEAV,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,OAAO,IAAI;IACb;IAEA;IACAD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjE,IAAI,CAACX,MAAM,CAACoB,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;MACpCE,WAAW,EAAE;QAAEC,SAAS,EAAEpB,KAAK,CAACS;MAAG;KACpC,CAAC;IACF,OAAO,KAAK;EACd;EAAC,QAAAY,CAAA,G;qCApDU3B,SAAS,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAATlC,SAAS;IAAAmC,OAAA,EAATnC,SAAS,CAAAoC,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}