{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, DOCUMENT, Injector, afterNextRender, ViewChild, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { OverlayConfig, createGlobalPositionStrategy, createOverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './icon-button-DxiIc1ex.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-BnMiRtmT.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-CObeNzjn.mjs';\nimport './index-BFRo2fUq.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  _overlayRef;\n  /** The instance of the component making up the content of the snack bar. */\n  instance;\n  /**\n   * The instance of the component making up the content of the snack bar.\n   * @docs-private\n   */\n  containerInstance;\n  /** Subject for notifying the user that the snack bar has been dismissed. */\n  _afterDismissed = new Subject();\n  /** Subject for notifying the user that the snack bar has opened and appeared. */\n  _afterOpened = new Subject();\n  /** Subject for notifying the user that the snack bar action was called. */\n  _onAction = new Subject();\n  /**\n   * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n   * dismissed before the duration passes.\n   */\n  _durationTimeoutId;\n  /** Whether the snack bar was dismissed using the action button. */\n  _dismissedByAction = false;\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n  politeness = 'polite';\n  /**\n   * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n   * component or template, the announcement message will default to the specified message.\n   */\n  announcementMessage = '';\n  /**\n   * The view container that serves as the parent for the snackbar for the purposes of dependency\n   * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n   */\n  viewContainerRef;\n  /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n  duration = 0;\n  /** Extra CSS classes to be added to the snack bar container. */\n  panelClass;\n  /** Text layout direction for the snack bar. */\n  direction;\n  /** Data being injected into the child component. */\n  data = null;\n  /** The horizontal position to place the snack bar. */\n  horizontalPosition = 'center';\n  /** The vertical position to place the snack bar. */\n  verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n  static ɵfac = function MatSnackBarLabel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarLabel)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarLabel,\n    selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n  static ɵfac = function MatSnackBarActions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarActions)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarActions,\n    selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n  static ɵfac = function MatSnackBarAction_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarAction)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarAction,\n    selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  snackBarRef = inject(MatSnackBarRef);\n  data = inject(MAT_SNACK_BAR_DATA);\n  constructor() {}\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n  static ɵfac = function SimpleSnackBar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SimpleSnackBar)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: SimpleSnackBar,\n    selectors: [[\"simple-snack-bar\"]],\n    hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n    exportAs: [\"matSnackBar\"],\n    decls: 3,\n    vars: 2,\n    consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"matButton\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n    template: function SimpleSnackBar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtext(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(2, SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.hasAction ? 2 : -1);\n      }\n    },\n    dependencies: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n    styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button matButton matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"]\n    }]\n  }], () => [], null);\n})();\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n  _ngZone = inject(NgZone);\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _platform = inject(Platform);\n  _animationsDisabled = _animationsDisabled();\n  snackBarConfig = inject(MatSnackBarConfig);\n  _document = inject(DOCUMENT);\n  _trackedModals = new Set();\n  _enterFallback;\n  _exitFallback;\n  _injector = inject(Injector);\n  /** The number of milliseconds to wait before announcing the snack bar's content. */\n  _announceDelay = 150;\n  /** The timeout for announcing the snack bar's content. */\n  _announceTimeoutId;\n  /** Whether the component has been destroyed. */\n  _destroyed = false;\n  /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n  _portalOutlet;\n  /** Subject for notifying that the snack bar has announced to screen readers. */\n  _onAnnounce = new Subject();\n  /** Subject for notifying that the snack bar has exited from view. */\n  _onExit = new Subject();\n  /** Subject for notifying that the snack bar has finished entering the view. */\n  _onEnter = new Subject();\n  /** The state of the snack bar animations. */\n  _animationState = 'void';\n  /** aria-live value for the live region. */\n  _live;\n  /**\n   * Element that will have the `mdc-snackbar__label` class applied if the attached component\n   * or template does not have it. This ensures that the appropriate structure, typography, and\n   * color is applied to the attached view.\n   */\n  _label;\n  /**\n   * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n   * JAWS does not read out aria-live message.\n   */\n  _role;\n  /** Unique ID of the aria-live element. */\n  _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n  constructor() {\n    super();\n    const config = this.snackBarConfig;\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (config.politeness === 'assertive' && !config.announcementMessage) {\n      this._live = 'assertive';\n    } else if (config.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /**\n   * Attaches a DOM portal to the snack bar container.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  };\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(animationName) {\n    if (animationName === EXIT_ANIMATION) {\n      this._completeExit();\n    } else if (animationName === ENTER_ANIMATION) {\n      clearTimeout(this._enterFallback);\n      this._ngZone.run(() => {\n        this._onEnter.next();\n        this._onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n      // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n      if (this._animationsDisabled) {\n        afterNextRender(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n        }, {\n          injector: this._injector\n        });\n      } else {\n        clearTimeout(this._enterFallback);\n        this._enterFallback = setTimeout(() => {\n          // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n          // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n          this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n          this.onAnimationEnd(ENTER_ANIMATION);\n        }, 200);\n      }\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    if (this._destroyed) {\n      return of(undefined);\n    }\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n      if (this._animationsDisabled) {\n        afterNextRender(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n        }, {\n          injector: this._injector\n        });\n      } else {\n        clearTimeout(this._exitFallback);\n        this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n      }\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n  _completeExit() {\n    clearTimeout(this._exitFallback);\n    queueMicrotask(() => {\n      this._onExit.next();\n      this._onExit.complete();\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (this._announceTimeoutId) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._announceTimeoutId = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        const element = this._elementRef.nativeElement;\n        const inertElement = element.querySelector('[aria-hidden]');\n        const liveElement = element.querySelector('[aria-live]');\n        if (inertElement && liveElement) {\n          // If an element in the snack bar content is focused before being moved\n          // track it and restore focus after moving to the live region.\n          let focusedElement = null;\n          if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n            focusedElement = document.activeElement;\n          }\n          inertElement.removeAttribute('aria-hidden');\n          liveElement.appendChild(inertElement);\n          focusedElement?.focus();\n          this._onAnnounce.next();\n          this._onAnnounce.complete();\n        }\n      }, this._announceDelay);\n    });\n  }\n  static ɵfac = function MatSnackBarContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSnackBarContainer,\n    selectors: [[\"mat-snack-bar-container\"]],\n    viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\"],\n    hostVars: 6,\n    hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"animationend\", function MatSnackBarContainer_animationend_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event.animationName);\n        })(\"animationcancel\", function MatSnackBarContainer_animationcancel_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event.animationName);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-snack-bar-container-enter\", ctx._animationState === \"visible\")(\"mat-snack-bar-container-exit\", ctx._animationState === \"hidden\")(\"mat-snack-bar-container-animations-enabled\", !ctx._animationsDisabled);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 3,\n    consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\", \"mat-mdc-snackbar-surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatSnackBarContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n        i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"div\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n        '(animationend)': 'onAnimationEnd($event.animationName)',\n        '(animationcancel)': 'onAnimationEnd($event.animationName)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n  _live = inject(LiveAnnouncer);\n  _injector = inject(Injector);\n  _breakpointObserver = inject(BreakpointObserver);\n  _parentSnackBar = inject(MatSnackBar, {\n    optional: true,\n    skipSelf: true\n  });\n  _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n  _animationsDisabled = _animationsDisabled();\n  /**\n   * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n   * If there is a parent snack-bar service, all operations should delegate to that parent\n   * via `_openedSnackBarRef`.\n   */\n  _snackBarRefAtThisLevel = null;\n  /** The component that should be rendered as the snack bar's simple component. */\n  simpleSnackBarComponent = SimpleSnackBar;\n  /** The container component that attaches the provided template or component. */\n  snackBarContainerComponent = MatSnackBarContainer;\n  /** The CSS class to apply for handset mode. */\n  handsetCssClass = 'mat-mdc-snack-bar-handset';\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor() {}\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    const positionStrategy = createGlobalPositionStrategy(this._injector);\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    overlayConfig.disableAnimations = this._animationsDisabled;\n    return createOverlayRef(this._injector, overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n  static ɵfac = function MatSnackBar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBar)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatSnackBar,\n    factory: MatSnackBar.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n  static ɵfac = function MatSnackBarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSnackBarModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatSnackBar],\n    imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatSnackBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n  // Represents\n  // trigger('state', [\n  //   state(\n  //     'void, hidden',\n  //     style({\n  //       transform: 'scale(0.8)',\n  //       opacity: 0,\n  //     }),\n  //   ),\n  //   state(\n  //     'visible',\n  //     style({\n  //       transform: 'scale(1)',\n  //       opacity: 1,\n  //     }),\n  //   ),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition(\n  //     '* => void, * => hidden',\n  //     animate(\n  //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n  //       style({\n  //         opacity: 0,\n  //       }),\n  //     ),\n  //   ),\n  // ])\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: {\n    type: 7,\n    name: 'state',\n    'definitions': [{\n      type: 0,\n      name: 'void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(0.8)',\n          opacity: 0\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)',\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void, * => hidden',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgZone", "ElementRef", "ChangeDetectorRef", "DOCUMENT", "Injector", "afterNextRender", "ViewChild", "TemplateRef", "Injectable", "NgModule", "Subject", "of", "MatButton", "MatButtonModule", "_IdGenerator", "LiveAnnouncer", "Platform", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "_", "_animationsDisabled", "BreakpointObserver", "Breakpoints", "OverlayConfig", "createGlobalPositionStrategy", "createOverlayRef", "OverlayModule", "takeUntil", "M", "MatCommonModule", "SimpleSnackBar_Conditional_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "SimpleSnackBar_Conditional_2_Template_button_click_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "action", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "data", "_c0", "MatSnackBarContainer_ng_template_4_Template", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "_overlayRef", "instance", "containerInstance", "_afterDismissed", "_afterOpened", "_onAction", "_durationTimeoutId", "_dismissedByAction", "constructor", "_onExit", "subscribe", "_finishDismiss", "dismiss", "closed", "exit", "clearTimeout", "dismissWithAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "politeness", "announcementMessage", "viewContainerRef", "panelClass", "direction", "horizontalPosition", "verticalPosition", "MatSnackBarLabel", "ɵfac", "MatSnackBarLabel_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatSnackBarActions", "MatSnackBarActions_Factory", "MatSnackBarAction", "MatSnackBarAction_Factory", "SimpleSnackBar", "snackBarRef", "hasAction", "SimpleSnackBar_Factory", "ɵcmp", "ɵɵdefineComponent", "exportAs", "decls", "vars", "consts", "template", "SimpleSnackBar_Template", "ɵɵconditionalCreate", "message", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "imports", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatSnackBarContainer", "_ngZone", "_elementRef", "_changeDetectorRef", "_platform", "snackBarConfig", "_document", "_trackedModals", "Set", "_enterFallback", "_exitFallback", "_injector", "_announce<PERSON><PERSON>y", "_announceTimeoutId", "_destroyed", "_portalOutlet", "_onAnnounce", "_animationState", "_live", "_label", "_role", "_liveElementId", "getId", "config", "FIREFOX", "attachComponentPortal", "portal", "_assertNotAttached", "result", "_afterPortalAttached", "attachTemplatePortal", "attachDomPortal", "onAnimationEnd", "animationName", "_completeExit", "run", "enter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_screenReaderAnnounce", "queueMicrotask", "injector", "nativeElement", "classList", "add", "undefined", "setAttribute", "ngOnDestroy", "_clearFromModals", "element", "panelClasses", "Array", "isArray", "for<PERSON>ach", "cssClass", "_exposeToModals", "label", "labelClass", "toggle", "querySelector", "id", "modals", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "Error", "runOutsideAngular", "inertElement", "liveElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "MatSnackBarContainer_Factory", "viewQuery", "MatSnackBarContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "MatSnackBarContainer_HostBindings", "MatSnackBarContainer_animationend_HostBindingHandler", "$event", "MatSnackBarContainer_animationcancel_HostBindingHandler", "ɵɵclassProp", "features", "ɵɵInheritDefinitionFeature", "MatSnackBarContainer_Template", "ɵɵtemplate", "ɵɵelement", "ɵɵattribute", "<PERSON><PERSON><PERSON>", "static", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "MatSnackBar", "_breakpointObserver", "_parentSnackBar", "optional", "skipSelf", "_defaultConfig", "_snackBarRefAtThisLevel", "simpleSnackBarComponent", "snackBarContainerComponent", "handsetCssClass", "_openedSnackBarRef", "parent", "value", "openFromComponent", "component", "_attach", "openFromTemplate", "open", "_config", "_attachSnackBarContainer", "overlayRef", "userInjector", "create", "providers", "provide", "useValue", "containerPortal", "containerRef", "attach", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "observe", "HandsetPortrait", "pipe", "detachments", "state", "overlayElement", "matches", "announce", "_animateSnackBar", "overlayConfig", "positionStrategy", "isRtl", "isLeft", "isRight", "left", "right", "centerHorizontally", "top", "bottom", "disableAnimations", "MatSnackBar_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "DIRECTIVES", "MatSnackBarModule", "MatSnackBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "matSnackBarAnimations", "snackBarState", "name", "transform", "opacity", "offset", "expr", "animation", "timings", "options"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, DOCUMENT, Injector, afterNextRender, ViewChild, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { OverlayConfig, createGlobalPositionStrategy, createOverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './icon-button-DxiIc1ex.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-BnMiRtmT.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-CObeNzjn.mjs';\nimport './index-BFRo2fUq.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    _overlayRef;\n    /** The instance of the component making up the content of the snack bar. */\n    instance;\n    /**\n     * The instance of the component making up the content of the snack bar.\n     * @docs-private\n     */\n    containerInstance;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    _afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    _afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    _onAction = new Subject();\n    /**\n     * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n     * dismissed before the duration passes.\n     */\n    _durationTimeoutId;\n    /** Whether the snack bar was dismissed using the action button. */\n    _dismissedByAction = false;\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    politeness = 'polite';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    announcementMessage = '';\n    /**\n     * The view container that serves as the parent for the snackbar for the purposes of dependency\n     * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n     */\n    viewContainerRef;\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    duration = 0;\n    /** Extra CSS classes to be added to the snack bar container. */\n    panelClass;\n    /** Text layout direction for the snack bar. */\n    direction;\n    /** Data being injected into the child component. */\n    data = null;\n    /** The horizontal position to place the snack bar. */\n    horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarLabel, isStandalone: true, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarActions, isStandalone: true, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarAction, isStandalone: true, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\nclass SimpleSnackBar {\n    snackBarRef = inject(MatSnackBarRef);\n    data = inject(MAT_SNACK_BAR_DATA);\n    constructor() { }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SimpleSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: SimpleSnackBar, isStandalone: true, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button matButton matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"], dependencies: [{ kind: \"component\", type: MatButton, selector: \"    button[matButton], a[matButton], button[mat-button], button[mat-raised-button],    button[mat-flat-button], button[mat-stroked-button], a[mat-button], a[mat-raised-button],    a[mat-flat-button], a[mat-stroked-button]  \", inputs: [\"matButton\"], exportAs: [\"matButton\", \"matAnchor\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button matButton matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _platform = inject(Platform);\n    _animationsDisabled = _animationsDisabled();\n    snackBarConfig = inject(MatSnackBarConfig);\n    _document = inject(DOCUMENT);\n    _trackedModals = new Set();\n    _enterFallback;\n    _exitFallback;\n    _injector = inject(Injector);\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    _announceDelay = 150;\n    /** The timeout for announcing the snack bar's content. */\n    _announceTimeoutId;\n    /** Whether the component has been destroyed. */\n    _destroyed = false;\n    /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n    _portalOutlet;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    _onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    _onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    _onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    _animationState = 'void';\n    /** aria-live value for the live region. */\n    _live;\n    /**\n     * Element that will have the `mdc-snackbar__label` class applied if the attached component\n     * or template does not have it. This ensures that the appropriate structure, typography, and\n     * color is applied to the attached view.\n     */\n    _label;\n    /**\n     * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n     * JAWS does not read out aria-live message.\n     */\n    _role;\n    /** Unique ID of the aria-live element. */\n    _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n    constructor() {\n        super();\n        const config = this.snackBarConfig;\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (config.politeness === 'assertive' && !config.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (config.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    };\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(animationName) {\n        if (animationName === EXIT_ANIMATION) {\n            this._completeExit();\n        }\n        else if (animationName === ENTER_ANIMATION) {\n            clearTimeout(this._enterFallback);\n            this._ngZone.run(() => {\n                this._onEnter.next();\n                this._onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n            // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n            this._changeDetectorRef.markForCheck();\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n            if (this._animationsDisabled) {\n                afterNextRender(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n                }, { injector: this._injector });\n            }\n            else {\n                clearTimeout(this._enterFallback);\n                this._enterFallback = setTimeout(() => {\n                    // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n                    // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n                    this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n                    this.onAnimationEnd(ENTER_ANIMATION);\n                }, 200);\n            }\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        if (this._destroyed) {\n            return of(undefined);\n        }\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            this._changeDetectorRef.markForCheck();\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n            if (this._animationsDisabled) {\n                afterNextRender(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n                }, { injector: this._injector });\n            }\n            else {\n                clearTimeout(this._exitFallback);\n                this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n            }\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n    }\n    _completeExit() {\n        clearTimeout(this._exitFallback);\n        queueMicrotask(() => {\n            this._onExit.next();\n            this._onExit.complete();\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n        // `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (this._announceTimeoutId) {\n            return;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            this._announceTimeoutId = setTimeout(() => {\n                if (this._destroyed) {\n                    return;\n                }\n                const element = this._elementRef.nativeElement;\n                const inertElement = element.querySelector('[aria-hidden]');\n                const liveElement = element.querySelector('[aria-live]');\n                if (inertElement && liveElement) {\n                    // If an element in the snack bar content is focused before being moved\n                    // track it and restore focus after moving to the live region.\n                    let focusedElement = null;\n                    if (this._platform.isBrowser &&\n                        document.activeElement instanceof HTMLElement &&\n                        inertElement.contains(document.activeElement)) {\n                        focusedElement = document.activeElement;\n                    }\n                    inertElement.removeAttribute('aria-hidden');\n                    liveElement.appendChild(inertElement);\n                    focusedElement?.focus();\n                    this._onAnnounce.next();\n                    this._onAnnounce.complete();\n                }\n            }, this._announceDelay);\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarContainer, isStandalone: true, selector: \"mat-snack-bar-container\", host: { listeners: { \"animationend\": \"onAnimationEnd($event.animationName)\", \"animationcancel\": \"onAnimationEnd($event.animationName)\" }, properties: { \"class.mat-snack-bar-container-enter\": \"_animationState === \\\"visible\\\"\", \"class.mat-snack-bar-container-exit\": \"_animationState === \\\"hidden\\\"\", \"class.mat-snack-bar-container-animations-enabled\": \"!_animationsDisabled\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }, { propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, imports: [CdkPortalOutlet], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n                        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n                        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n                        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n                        '(animationend)': 'onAnimationEnd($event.animationName)',\n                        '(animationcancel)': 'onAnimationEnd($event.animationName)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }], _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n    _live = inject(LiveAnnouncer);\n    _injector = inject(Injector);\n    _breakpointObserver = inject(BreakpointObserver);\n    _parentSnackBar = inject(MatSnackBar, { optional: true, skipSelf: true });\n    _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n    _animationsDisabled = _animationsDisabled();\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    _snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    handsetCssClass = 'mat-mdc-snack-bar-handset';\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor() { }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        const positionStrategy = createGlobalPositionStrategy(this._injector);\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        overlayConfig.disableAnimations = this._animationsDisabled;\n        return createOverlayRef(this._injector, overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBar, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, providers: [MatSnackBar], imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        PortalModule,\n                        MatButtonModule,\n                        MatCommonModule,\n                        SimpleSnackBar,\n                        ...DIRECTIVES,\n                    ],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatSnackBar],\n                }]\n        }] });\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n    // Represents\n    // trigger('state', [\n    //   state(\n    //     'void, hidden',\n    //     style({\n    //       transform: 'scale(0.8)',\n    //       opacity: 0,\n    //     }),\n    //   ),\n    //   state(\n    //     'visible',\n    //     style({\n    //       transform: 'scale(1)',\n    //       opacity: 1,\n    //     }),\n    //   ),\n    //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    //   transition(\n    //     '* => void, * => hidden',\n    //     animate(\n    //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n    //       style({\n    //         opacity: 0,\n    //       }),\n    //     ),\n    //   ),\n    // ])\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: {\n        type: 7,\n        name: 'state',\n        'definitions': [\n            {\n                type: 0,\n                name: 'void, hidden',\n                styles: { type: 6, styles: { transform: 'scale(0.8)', opacity: 0 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'visible',\n                styles: { type: 6, styles: { transform: 'scale(1)', opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => visible',\n                animation: { type: 4, styles: null, timings: '150ms cubic-bezier(0, 0, 0.2, 1)' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void, * => hidden',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAClP,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,eAAe,QAAQ,cAAc;AACzD,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAASC,aAAa,EAAEC,4BAA4B,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AACnH,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,kCAAkC;AACzC,OAAO,sBAAsB;AAC7B,OAAO,mBAAmB;;AAE1B;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAgI6F3C,EAAE,CAAA4C,gBAAA;IAAF5C,EAAE,CAAA6C,cAAA,YAsD0Q,CAAC,eAA8D,CAAC;IAtD5U7C,EAAE,CAAA8C,UAAA,mBAAAC,8DAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAsD+TF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAtD3UpD,EAAE,CAAAqD,MAAA,EAsDsW,CAAC;IAtDzWrD,EAAE,CAAAsD,YAAA,CAsD+W,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GAtD5XjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuD,SAAA,EAsDsW,CAAC;IAtDzWvD,EAAE,CAAAwD,kBAAA,MAAAP,MAAA,CAAAQ,IAAA,CAAAL,MAAA,KAsDsW,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,SAAAC,4CAAAlB,EAAA,EAAAC,GAAA;AArLtc,MAAMkB,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAW;EACX;EACAC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;EACAC,eAAe,GAAG,IAAIlD,OAAO,CAAC,CAAC;EAC/B;EACAmD,YAAY,GAAG,IAAInD,OAAO,CAAC,CAAC;EAC5B;EACAoD,SAAS,GAAG,IAAIpD,OAAO,CAAC,CAAC;EACzB;AACJ;AACA;AACA;EACIqD,kBAAkB;EAClB;EACAC,kBAAkB,GAAG,KAAK;EAC1BC,WAAWA,CAACN,iBAAiB,EAAEF,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACO,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;EACpE;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACT,eAAe,CAACU,MAAM,EAAE;MAC9B,IAAI,CAACX,iBAAiB,CAACY,IAAI,CAAC,CAAC;IACjC;IACAC,YAAY,CAAC,IAAI,CAACT,kBAAkB,CAAC;EACzC;EACA;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACX,SAAS,CAACQ,MAAM,EAAE;MACxB,IAAI,CAACN,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACF,SAAS,CAACY,IAAI,CAAC,CAAC;MACrB,IAAI,CAACZ,SAAS,CAACa,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACN,OAAO,CAAC,CAAC;IAClB;IACAG,YAAY,CAAC,IAAI,CAACT,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACIa,eAAeA,CAAA,EAAG;IACd,IAAI,CAACH,iBAAiB,CAAC,CAAC;EAC5B;EACA;EACAI,aAAaA,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACf,kBAAkB,GAAGgB,UAAU,CAAC,MAAM,IAAI,CAACV,OAAO,CAAC,CAAC,EAAEf,IAAI,CAAC0B,GAAG,CAACF,QAAQ,EAAEzB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA4B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACS,MAAM,EAAE;MAC3B,IAAI,CAACT,YAAY,CAACa,IAAI,CAAC,CAAC;MACxB,IAAI,CAACb,YAAY,CAACc,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA;EACAP,cAAcA,CAAA,EAAG;IACb,IAAI,CAACX,WAAW,CAACyB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACpB,SAAS,CAACQ,MAAM,EAAE;MACxB,IAAI,CAACR,SAAS,CAACa,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAI,CAACf,eAAe,CAACc,IAAI,CAAC;MAAES,iBAAiB,EAAE,IAAI,CAACnB;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACJ,eAAe,CAACe,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACX,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxB,eAAe;EAC/B;EACA;EACAyB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1B,iBAAiB,CAAC2B,QAAQ;EAC1C;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzB,SAAS;EACzB;AACJ;;AAEA;AACA,MAAM0B,kBAAkB,GAAG,IAAI9F,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAM+F,iBAAiB,CAAC;EACpB;EACAC,UAAU,GAAG,QAAQ;EACrB;AACJ;AACA;AACA;EACIC,mBAAmB,GAAG,EAAE;EACxB;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;EACAd,QAAQ,GAAG,CAAC;EACZ;EACAe,UAAU;EACV;EACAC,SAAS;EACT;EACA5C,IAAI,GAAG,IAAI;EACX;EACA6C,kBAAkB,GAAG,QAAQ;EAC7B;EACAC,gBAAgB,GAAG,QAAQ;AAC/B;;AAEA;AACA,MAAMC,gBAAgB,CAAC;EACnB,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,gBAAgB;EAAA;EACnH,OAAOI,IAAI,kBAD8E5G,EAAE,CAAA6G,iBAAA;IAAAC,IAAA,EACJN,gBAAgB;IAAAO,SAAA;IAAAC,SAAA;EAAA;AAC3G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FjH,EAAE,CAAAkH,iBAAA,CAGJV,gBAAgB,EAAc,CAAC;IAC9GM,IAAI,EAAE5G,SAAS;IACfiH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,kBAAkB,CAAC;EACrB,OAAOb,IAAI,YAAAc,2BAAAZ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFW,kBAAkB;EAAA;EACrH,OAAOV,IAAI,kBAf8E5G,EAAE,CAAA6G,iBAAA;IAAAC,IAAA,EAeJQ,kBAAkB;IAAAP,SAAA;IAAAC,SAAA;EAAA;AAC7G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjB6FjH,EAAE,CAAAkH,iBAAA,CAiBJI,kBAAkB,EAAc,CAAC;IAChHR,IAAI,EAAE5G,SAAS;IACfiH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMG,iBAAiB,CAAC;EACpB,OAAOf,IAAI,YAAAgB,0BAAAd,iBAAA;IAAA,YAAAA,iBAAA,IAAwFa,iBAAiB;EAAA;EACpH,OAAOZ,IAAI,kBA7B8E5G,EAAE,CAAA6G,iBAAA;IAAAC,IAAA,EA6BJU,iBAAiB;IAAAT,SAAA;IAAAC,SAAA;EAAA;AAC5G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/B6FjH,EAAE,CAAAkH,iBAAA,CA+BJM,iBAAiB,EAAc,CAAC;IAC/GV,IAAI,EAAE5G,SAAS;IACfiH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,cAAc,CAAC;EACjBC,WAAW,GAAGxH,MAAM,CAAC4D,cAAc,CAAC;EACpCN,IAAI,GAAGtD,MAAM,CAAC4F,kBAAkB,CAAC;EACjCvB,WAAWA,CAAA,EAAG,CAAE;EAChB;EACApB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACuE,WAAW,CAAC3C,iBAAiB,CAAC,CAAC;EACxC;EACA;EACA,IAAI4C,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACnE,IAAI,CAACL,MAAM;EAC7B;EACA,OAAOqD,IAAI,YAAAoB,uBAAAlB,iBAAA;IAAA,YAAAA,iBAAA,IAAwFe,cAAc;EAAA;EACjH,OAAOI,IAAI,kBAtD8E9H,EAAE,CAAA+H,iBAAA;IAAAjB,IAAA,EAsDJY,cAAc;IAAAX,SAAA;IAAAC,SAAA;IAAAgB,QAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAA5F,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAtDZzC,EAAE,CAAA6C,cAAA,YAsD6L,CAAC;QAtDhM7C,EAAE,CAAAqD,MAAA,EAsDmN,CAAC;QAtDtNrD,EAAE,CAAAsD,YAAA,CAsDyN,CAAC;QAtD5NtD,EAAE,CAAAsI,mBAAA,IAAA9F,qCAAA,gBAsD8O,CAAC;MAAA;MAAA,IAAAC,EAAA;QAtDjPzC,EAAE,CAAAuD,SAAA,CAsDmN,CAAC;QAtDtNvD,EAAE,CAAAwD,kBAAA,MAAAd,GAAA,CAAAe,IAAA,CAAA8E,OAAA,MAsDmN,CAAC;QAtDtNvI,EAAE,CAAAuD,SAAA,CAsD4X,CAAC;QAtD/XvD,EAAE,CAAAwI,aAAA,CAAA9F,GAAA,CAAAkF,SAAA,SAsD4X,CAAC;MAAA;IAAA;IAAAa,YAAA,GAAsGtH,SAAS,EAAyUqF,gBAAgB,EAA+Dc,kBAAkB,EAAiEE,iBAAiB;IAAAkB,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACvkC;AACA;EAAA,QAAA3B,SAAA,oBAAAA,SAAA,KAxD6FjH,EAAE,CAAAkH,iBAAA,CAwDJQ,cAAc,EAAc,CAAC;IAC5GZ,IAAI,EAAE1G,SAAS;IACf+G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEY,QAAQ,EAAE,aAAa;MAAEW,aAAa,EAAEtI,iBAAiB,CAACwI,IAAI;MAAED,eAAe,EAAEtI,uBAAuB,CAACwI,MAAM;MAAEC,OAAO,EAAE,CAAC5H,SAAS,EAAEqF,gBAAgB,EAAEc,kBAAkB,EAAEE,iBAAiB,CAAC;MAAEH,IAAI,EAAE;QACjO,OAAO,EAAE;MACb,CAAC;MAAEe,QAAQ,EAAE,yNAAyN;MAAEM,MAAM,EAAE,CAAC,2CAA2C;IAAE,CAAC;EAC3S,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMM,eAAe,GAAG,sBAAsB;AAC9C,MAAMC,cAAc,GAAG,qBAAqB;AAC5C;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAAS1H,gBAAgB,CAAC;EAChD2H,OAAO,GAAGhJ,MAAM,CAACI,MAAM,CAAC;EACxB6I,WAAW,GAAGjJ,MAAM,CAACK,UAAU,CAAC;EAChC6I,kBAAkB,GAAGlJ,MAAM,CAACM,iBAAiB,CAAC;EAC9C6I,SAAS,GAAGnJ,MAAM,CAACoB,QAAQ,CAAC;EAC5BO,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3CyH,cAAc,GAAGpJ,MAAM,CAAC6F,iBAAiB,CAAC;EAC1CwD,SAAS,GAAGrJ,MAAM,CAACO,QAAQ,CAAC;EAC5B+I,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1BC,cAAc;EACdC,aAAa;EACbC,SAAS,GAAG1J,MAAM,CAACQ,QAAQ,CAAC;EAC5B;EACAmJ,cAAc,GAAG,GAAG;EACpB;EACAC,kBAAkB;EAClB;EACAC,UAAU,GAAG,KAAK;EAClB;EACAC,aAAa;EACb;EACAC,WAAW,GAAG,IAAIjJ,OAAO,CAAC,CAAC;EAC3B;EACAwD,OAAO,GAAG,IAAIxD,OAAO,CAAC,CAAC;EACvB;EACA4E,QAAQ,GAAG,IAAI5E,OAAO,CAAC,CAAC;EACxB;EACAkJ,eAAe,GAAG,MAAM;EACxB;EACAC,KAAK;EACL;AACJ;AACA;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,KAAK;EACL;EACAC,cAAc,GAAGpK,MAAM,CAACkB,YAAY,CAAC,CAACmJ,KAAK,CAAC,+BAA+B,CAAC;EAC5EhG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,MAAMiG,MAAM,GAAG,IAAI,CAAClB,cAAc;IAClC;IACA;IACA,IAAIkB,MAAM,CAACxE,UAAU,KAAK,WAAW,IAAI,CAACwE,MAAM,CAACvE,mBAAmB,EAAE;MAClE,IAAI,CAACkE,KAAK,GAAG,WAAW;IAC5B,CAAC,MACI,IAAIK,MAAM,CAACxE,UAAU,KAAK,KAAK,EAAE;MAClC,IAAI,CAACmE,KAAK,GAAG,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,QAAQ;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAACd,SAAS,CAACoB,OAAO,EAAE;MACxB,IAAI,IAAI,CAACN,KAAK,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACE,KAAK,GAAG,QAAQ;MACzB;MACA,IAAI,IAAI,CAACF,KAAK,KAAK,WAAW,EAAE;QAC5B,IAAI,CAACE,KAAK,GAAG,OAAO;MACxB;IACJ;EACJ;EACA;EACAK,qBAAqBA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACb,aAAa,CAACU,qBAAqB,CAACC,MAAM,CAAC;IAC/D,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,OAAOD,MAAM;EACjB;EACA;EACAE,oBAAoBA,CAACJ,MAAM,EAAE;IACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACb,aAAa,CAACe,oBAAoB,CAACJ,MAAM,CAAC;IAC9D,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,OAAOD,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIG,eAAe,GAAIL,MAAM,IAAK;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACb,aAAa,CAACgB,eAAe,CAACL,MAAM,CAAC;IACzD,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,OAAOD,MAAM;EACjB,CAAC;EACD;EACAI,cAAcA,CAACC,aAAa,EAAE;IAC1B,IAAIA,aAAa,KAAKlC,cAAc,EAAE;MAClC,IAAI,CAACmC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI,IAAID,aAAa,KAAKnC,eAAe,EAAE;MACxCjE,YAAY,CAAC,IAAI,CAAC4E,cAAc,CAAC;MACjC,IAAI,CAACR,OAAO,CAACkC,GAAG,CAAC,MAAM;QACnB,IAAI,CAACxF,QAAQ,CAACZ,IAAI,CAAC,CAAC;QACpB,IAAI,CAACY,QAAQ,CAACX,QAAQ,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA;EACAoG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;MAClB,IAAI,CAACG,eAAe,GAAG,SAAS;MAChC;MACA;MACA,IAAI,CAACd,kBAAkB,CAACkC,YAAY,CAAC,CAAC;MACtC,IAAI,CAAClC,kBAAkB,CAACmC,aAAa,CAAC,CAAC;MACvC,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,IAAI,CAAC3J,mBAAmB,EAAE;QAC1BlB,eAAe,CAAC,MAAM;UAClB,IAAI,CAACuI,OAAO,CAACkC,GAAG,CAAC,MAAMK,cAAc,CAAC,MAAM,IAAI,CAACR,cAAc,CAAClC,eAAe,CAAC,CAAC,CAAC;QACtF,CAAC,EAAE;UAAE2C,QAAQ,EAAE,IAAI,CAAC9B;QAAU,CAAC,CAAC;MACpC,CAAC,MACI;QACD9E,YAAY,CAAC,IAAI,CAAC4E,cAAc,CAAC;QACjC,IAAI,CAACA,cAAc,GAAGrE,UAAU,CAAC,MAAM;UACnC;UACA;UACA,IAAI,CAAC8D,WAAW,CAACwC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC9E,IAAI,CAACZ,cAAc,CAAClC,eAAe,CAAC;QACxC,CAAC,EAAE,GAAG,CAAC;MACX;IACJ;EACJ;EACA;EACAlE,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACkF,UAAU,EAAE;MACjB,OAAO9I,EAAE,CAAC6K,SAAS,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAAC5C,OAAO,CAACkC,GAAG,CAAC,MAAM;MACnB;MACA;MACA;MACA,IAAI,CAAClB,eAAe,GAAG,QAAQ;MAC/B,IAAI,CAACd,kBAAkB,CAACkC,YAAY,CAAC,CAAC;MACtC;MACA;MACA;MACA,IAAI,CAACnC,WAAW,CAACwC,aAAa,CAACI,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3D;MACA;MACAjH,YAAY,CAAC,IAAI,CAACgF,kBAAkB,CAAC;MACrC,IAAI,IAAI,CAACjI,mBAAmB,EAAE;QAC1BlB,eAAe,CAAC,MAAM;UAClB,IAAI,CAACuI,OAAO,CAACkC,GAAG,CAAC,MAAMK,cAAc,CAAC,MAAM,IAAI,CAACR,cAAc,CAACjC,cAAc,CAAC,CAAC,CAAC;QACrF,CAAC,EAAE;UAAE0C,QAAQ,EAAE,IAAI,CAAC9B;QAAU,CAAC,CAAC;MACpC,CAAC,MACI;QACD9E,YAAY,CAAC,IAAI,CAAC6E,aAAa,CAAC;QAChC,IAAI,CAACA,aAAa,GAAGtE,UAAU,CAAC,MAAM,IAAI,CAAC4F,cAAc,CAACjC,cAAc,CAAC,EAAE,GAAG,CAAC;MACnF;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACxE,OAAO;EACvB;EACA;EACAwH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACkC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACd,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZrG,YAAY,CAAC,IAAI,CAAC6E,aAAa,CAAC;IAChC8B,cAAc,CAAC,MAAM;MACjB,IAAI,CAACjH,OAAO,CAACQ,IAAI,CAAC,CAAC;MACnB,IAAI,CAACR,OAAO,CAACS,QAAQ,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI6F,oBAAoBA,CAAA,EAAG;IACnB,MAAMoB,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACwC,aAAa;IAC9C,MAAMQ,YAAY,GAAG,IAAI,CAAC7C,cAAc,CAACnD,UAAU;IACnD,IAAIgG,YAAY,EAAE;MACd,IAAIC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;QAC7B;QACAA,YAAY,CAACG,OAAO,CAACC,QAAQ,IAAIL,OAAO,CAACN,SAAS,CAACC,GAAG,CAACU,QAAQ,CAAC,CAAC;MACrE,CAAC,MACI;QACDL,OAAO,CAACN,SAAS,CAACC,GAAG,CAACM,YAAY,CAAC;MACvC;IACJ;IACA,IAAI,CAACK,eAAe,CAAC,CAAC;IACtB;IACA;IACA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACrC,MAAM,CAACuB,aAAa;IACvC,MAAMe,UAAU,GAAG,qBAAqB;IACxCD,KAAK,CAACb,SAAS,CAACe,MAAM,CAACD,UAAU,EAAE,CAACD,KAAK,CAACG,aAAa,CAAC,IAAIF,UAAU,EAAE,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;EACIF,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,MAAMK,EAAE,GAAG,IAAI,CAACvC,cAAc;IAC9B,MAAMwC,MAAM,GAAG,IAAI,CAACvD,SAAS,CAACwD,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAAC5D,cAAc,CAACqC,GAAG,CAACqB,KAAK,CAAC;MAC9B,IAAI,CAACC,QAAQ,EAAE;QACXD,KAAK,CAACnB,YAAY,CAAC,WAAW,EAAEc,EAAE,CAAC;MACvC,CAAC,MACI,IAAIM,QAAQ,CAACE,OAAO,CAACR,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCK,KAAK,CAACnB,YAAY,CAAC,WAAW,EAAEoB,QAAQ,GAAG,GAAG,GAAGN,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;EACAZ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACzC,cAAc,CAAC8C,OAAO,CAACY,KAAK,IAAI;MACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACjD,cAAc,EAAE,EAAE,CAAC,CAACkD,IAAI,CAAC,CAAC;QACjE,IAAIF,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;UACrBC,KAAK,CAACnB,YAAY,CAAC,WAAW,EAAEuB,QAAQ,CAAC;QAC7C,CAAC,MACI;UACDJ,KAAK,CAACO,eAAe,CAAC,WAAW,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACjE,cAAc,CAACkE,KAAK,CAAC,CAAC;EAC/B;EACA;EACA9C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACZ,aAAa,CAAC2D,WAAW,CAAC,CAAC,KAAK,OAAO3G,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrF,MAAM4G,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;EACIpC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC1B,kBAAkB,EAAE;MACzB;IACJ;IACA,IAAI,CAACZ,OAAO,CAAC2E,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC/D,kBAAkB,GAAGzE,UAAU,CAAC,MAAM;QACvC,IAAI,IAAI,CAAC0E,UAAU,EAAE;UACjB;QACJ;QACA,MAAMmC,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACwC,aAAa;QAC9C,MAAMmC,YAAY,GAAG5B,OAAO,CAACU,aAAa,CAAC,eAAe,CAAC;QAC3D,MAAMmB,WAAW,GAAG7B,OAAO,CAACU,aAAa,CAAC,aAAa,CAAC;QACxD,IAAIkB,YAAY,IAAIC,WAAW,EAAE;UAC7B;UACA;UACA,IAAIC,cAAc,GAAG,IAAI;UACzB,IAAI,IAAI,CAAC3E,SAAS,CAAC4E,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CN,YAAY,CAACO,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;YAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;UAC3C;UACAL,YAAY,CAACL,eAAe,CAAC,aAAa,CAAC;UAC3CM,WAAW,CAACO,WAAW,CAACR,YAAY,CAAC;UACrCE,cAAc,EAAEO,KAAK,CAAC,CAAC;UACvB,IAAI,CAACtE,WAAW,CAACjF,IAAI,CAAC,CAAC;UACvB,IAAI,CAACiF,WAAW,CAAChF,QAAQ,CAAC,CAAC;QAC/B;MACJ,CAAC,EAAE,IAAI,CAAC4E,cAAc,CAAC;IAC3B,CAAC,CAAC;EACN;EACA,OAAOrD,IAAI,YAAAgI,6BAAA9H,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuC,oBAAoB;EAAA;EACvH,OAAOpB,IAAI,kBAhW8E9H,EAAE,CAAA+H,iBAAA;IAAAjB,IAAA,EAgWJoC,oBAAoB;IAAAnC,SAAA;IAAA2H,SAAA,WAAAC,2BAAAlM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhWlBzC,EAAE,CAAA4O,WAAA,CAgWwkBnN,eAAe;QAhWzlBzB,EAAE,CAAA4O,WAAA,CAAAlL,GAAA;MAAA;MAAA,IAAAjB,EAAA;QAAA,IAAAoM,EAAA;QAAF7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAuH,aAAA,GAAA4E,EAAA,CAAAG,KAAA;QAAFhP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAA2H,MAAA,GAAAwE,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAhI,SAAA;IAAAiI,QAAA;IAAAC,YAAA,WAAAC,kCAAA1M,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzC,EAAE,CAAA8C,UAAA,0BAAAsM,qDAAAC,MAAA;UAAA,OAgWJ3M,GAAA,CAAAwI,cAAA,CAAAmE,MAAA,CAAAlE,aAAmC,CAAC;QAAA,CAAjB,CAAC,6BAAAmE,wDAAAD,MAAA;UAAA,OAApB3M,GAAA,CAAAwI,cAAA,CAAAmE,MAAA,CAAAlE,aAAmC,CAAC;QAAA,CAAjB,CAAC;MAAA;MAAA,IAAA1I,EAAA;QAhWlBzC,EAAE,CAAAuP,WAAA,kCAAA7M,GAAA,CAAAyH,eAAA,KAgWgB,SAAD,CAAC,iCAAAzH,GAAA,CAAAyH,eAAA,aAAD,CAAC,gDAAAzH,GAAA,CAAAZ,mBAAD,CAAC;MAAA;IAAA;IAAA0N,QAAA,GAhWlBxP,EAAE,CAAAyP,0BAAA;IAAAxH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAsH,8BAAAjN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzC,EAAE,CAAA6C,cAAA,YAgW00B,CAAC,eAA6M,CAAC,YAAqI,CAAC;QAhWjqC7C,EAAE,CAAA2P,UAAA,IAAAhM,2CAAA,wBAgWqsC,CAAC;QAhWxsC3D,EAAE,CAAAsD,YAAA,CAgWitC,CAAC;QAhWptCtD,EAAE,CAAA4P,SAAA,SAgWu6C,CAAC;QAhW16C5P,EAAE,CAAAsD,YAAA,CAgWi7C,CAAC,CAAO,CAAC;MAAA;MAAA,IAAAb,EAAA;QAhW57CzC,EAAE,CAAAuD,SAAA,EAgW62C,CAAC;QAhWh3CvD,EAAE,CAAA6P,WAAA,cAAAnN,GAAA,CAAA0H,KAAA,UAAA1H,GAAA,CAAA4H,KAAA,QAAA5H,GAAA,CAAA6H,cAAA;MAAA;IAAA;IAAA9B,YAAA,GAgWm3IhH,eAAe;IAAAiH,MAAA;IAAAC,aAAA;EAAA;AACj+I;AACA;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KAlW6FjH,EAAE,CAAAkH,iBAAA,CAkWJgC,oBAAoB,EAAc,CAAC;IAClHpC,IAAI,EAAE1G,SAAS;IACf+G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAEwB,eAAe,EAAEtI,uBAAuB,CAACwP,OAAO;MAAEnH,aAAa,EAAEtI,iBAAiB,CAACwI,IAAI;MAAEE,OAAO,EAAE,CAACtH,eAAe,CAAC;MAAE4F,IAAI,EAAE;QAC7J,OAAO,EAAE,0CAA0C;QACnD,uCAAuC,EAAE,+BAA+B;QACxE,sCAAsC,EAAE,8BAA8B;QACtE,oDAAoD,EAAE,sBAAsB;QAC5E,gBAAgB,EAAE,sCAAsC;QACxD,mBAAmB,EAAE;MACzB,CAAC;MAAEe,QAAQ,EAAE,irBAAirB;MAAEM,MAAM,EAAE,CAAC,83FAA83F;IAAE,CAAC;EACtlH,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEuB,aAAa,EAAE,CAAC;MACxDnD,IAAI,EAAEjG,SAAS;MACfsG,IAAI,EAAE,CAAC1F,eAAe,EAAE;QAAEsO,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE1F,MAAM,EAAE,CAAC;MACTvD,IAAI,EAAEjG,SAAS;MACfsG,IAAI,EAAE,CAAC,OAAO,EAAE;QAAE4I,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAASC,qCAAqCA,CAAA,EAAG;EAC7C,OAAO,IAAIhK,iBAAiB,CAAC,CAAC;AAClC;AACA;AACA,MAAMiK,6BAA6B,GAAG,IAAIhQ,cAAc,CAAC,+BAA+B,EAAE;EACtFiQ,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMI,WAAW,CAAC;EACdhG,KAAK,GAAGjK,MAAM,CAACmB,aAAa,CAAC;EAC7BuI,SAAS,GAAG1J,MAAM,CAACQ,QAAQ,CAAC;EAC5B0P,mBAAmB,GAAGlQ,MAAM,CAAC4B,kBAAkB,CAAC;EAChDuO,eAAe,GAAGnQ,MAAM,CAACiQ,WAAW,EAAE;IAAEG,QAAQ,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzEC,cAAc,GAAGtQ,MAAM,CAAC8P,6BAA6B,CAAC;EACtDnO,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3C;AACJ;AACA;AACA;AACA;EACI4O,uBAAuB,GAAG,IAAI;EAC9B;EACAC,uBAAuB,GAAGjJ,cAAc;EACxC;EACAkJ,0BAA0B,GAAG1H,oBAAoB;EACjD;EACA2H,eAAe,GAAG,2BAA2B;EAC7C;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACT,eAAe;IACnC,OAAOS,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACJ,uBAAuB;EAC5E;EACA,IAAII,kBAAkBA,CAACE,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACV,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACQ,kBAAkB,GAAGE,KAAK;IACnD,CAAC,MACI;MACD,IAAI,CAACN,uBAAuB,GAAGM,KAAK;IACxC;EACJ;EACAxM,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyM,iBAAiBA,CAACC,SAAS,EAAEzG,MAAM,EAAE;IACjC,OAAO,IAAI,CAAC0G,OAAO,CAACD,SAAS,EAAEzG,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2G,gBAAgBA,CAAChJ,QAAQ,EAAEqC,MAAM,EAAE;IAC/B,OAAO,IAAI,CAAC0G,OAAO,CAAC/I,QAAQ,EAAEqC,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4G,IAAIA,CAAC9I,OAAO,EAAEnF,MAAM,GAAG,EAAE,EAAEqH,MAAM,EAAE;IAC/B,MAAM6G,OAAO,GAAG;MAAE,GAAG,IAAI,CAACb,cAAc;MAAE,GAAGhG;IAAO,CAAC;IACrD;IACA;IACA6G,OAAO,CAAC7N,IAAI,GAAG;MAAE8E,OAAO;MAAEnF;IAAO,CAAC;IAClC;IACA;IACA,IAAIkO,OAAO,CAACpL,mBAAmB,KAAKqC,OAAO,EAAE;MACzC+I,OAAO,CAACpL,mBAAmB,GAAG6F,SAAS;IAC3C;IACA,OAAO,IAAI,CAACkF,iBAAiB,CAAC,IAAI,CAACN,uBAAuB,EAAEW,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACI1M,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACkM,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAAClM,OAAO,CAAC,CAAC;IACrC;EACJ;EACAqH,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,IAAI,CAACyE,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAAC9L,OAAO,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACI2M,wBAAwBA,CAACC,UAAU,EAAE/G,MAAM,EAAE;IACzC,MAAMgH,YAAY,GAAGhH,MAAM,IAAIA,MAAM,CAACtE,gBAAgB,IAAIsE,MAAM,CAACtE,gBAAgB,CAACwF,QAAQ;IAC1F,MAAMA,QAAQ,GAAGhL,QAAQ,CAAC+Q,MAAM,CAAC;MAC7BX,MAAM,EAAEU,YAAY,IAAI,IAAI,CAAC5H,SAAS;MACtC8H,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE5L,iBAAiB;QAAE6L,QAAQ,EAAEpH;MAAO,CAAC;IAChE,CAAC,CAAC;IACF,MAAMqH,eAAe,GAAG,IAAIpQ,eAAe,CAAC,IAAI,CAACkP,0BAA0B,EAAEnG,MAAM,CAACtE,gBAAgB,EAAEwF,QAAQ,CAAC;IAC/G,MAAMoG,YAAY,GAAGP,UAAU,CAACQ,MAAM,CAACF,eAAe,CAAC;IACvDC,YAAY,CAAC9N,QAAQ,CAACsF,cAAc,GAAGkB,MAAM;IAC7C,OAAOsH,YAAY,CAAC9N,QAAQ;EAChC;EACA;AACJ;AACA;EACIkN,OAAOA,CAACc,OAAO,EAAEC,UAAU,EAAE;IACzB,MAAMzH,MAAM,GAAG;MAAE,GAAG,IAAIzE,iBAAiB,CAAC,CAAC;MAAE,GAAG,IAAI,CAACyK,cAAc;MAAE,GAAGyB;IAAW,CAAC;IACpF,MAAMV,UAAU,GAAG,IAAI,CAACW,cAAc,CAAC1H,MAAM,CAAC;IAC9C,MAAM2H,SAAS,GAAG,IAAI,CAACb,wBAAwB,CAACC,UAAU,EAAE/G,MAAM,CAAC;IACnE,MAAM9C,WAAW,GAAG,IAAI5D,cAAc,CAACqO,SAAS,EAAEZ,UAAU,CAAC;IAC7D,IAAIS,OAAO,YAAYnR,WAAW,EAAE;MAChC,MAAM8J,MAAM,GAAG,IAAIjJ,cAAc,CAACsQ,OAAO,EAAE,IAAI,EAAE;QAC7CI,SAAS,EAAE5H,MAAM,CAAChH,IAAI;QACtBkE;MACJ,CAAC,CAAC;MACFA,WAAW,CAAC1D,QAAQ,GAAGmO,SAAS,CAACpH,oBAAoB,CAACJ,MAAM,CAAC;IACjE,CAAC,MACI;MACD,MAAMe,QAAQ,GAAG,IAAI,CAAC2G,eAAe,CAAC7H,MAAM,EAAE9C,WAAW,CAAC;MAC1D,MAAMiD,MAAM,GAAG,IAAIlJ,eAAe,CAACuQ,OAAO,EAAElG,SAAS,EAAEJ,QAAQ,CAAC;MAChE,MAAM4G,UAAU,GAAGH,SAAS,CAACzH,qBAAqB,CAACC,MAAM,CAAC;MAC1D;MACAjD,WAAW,CAAC1D,QAAQ,GAAGsO,UAAU,CAACtO,QAAQ;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAACoM,mBAAmB,CACnBmC,OAAO,CAACxQ,WAAW,CAACyQ,eAAe,CAAC,CACpCC,IAAI,CAACrQ,SAAS,CAACmP,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC,CAAC,CACzCjO,SAAS,CAACkO,KAAK,IAAI;MACpBpB,UAAU,CAACqB,cAAc,CAAChH,SAAS,CAACe,MAAM,CAAC,IAAI,CAACiE,eAAe,EAAE+B,KAAK,CAACE,OAAO,CAAC;IACnF,CAAC,CAAC;IACF,IAAIrI,MAAM,CAACvE,mBAAmB,EAAE;MAC5B;MACAkM,SAAS,CAAClI,WAAW,CAACxF,SAAS,CAAC,MAAM;QAClC,IAAI,CAAC0F,KAAK,CAAC2I,QAAQ,CAACtI,MAAM,CAACvE,mBAAmB,EAAEuE,MAAM,CAACxE,UAAU,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAAC+M,gBAAgB,CAACrL,WAAW,EAAE8C,MAAM,CAAC;IAC1C,IAAI,CAACqG,kBAAkB,GAAGnJ,WAAW;IACrC,OAAO,IAAI,CAACmJ,kBAAkB;EAClC;EACA;EACAkC,gBAAgBA,CAACrL,WAAW,EAAE8C,MAAM,EAAE;IAClC;IACA9C,WAAW,CAAChC,cAAc,CAAC,CAAC,CAACjB,SAAS,CAAC,MAAM;MACzC;MACA,IAAI,IAAI,CAACoM,kBAAkB,IAAInJ,WAAW,EAAE;QACxC,IAAI,CAACmJ,kBAAkB,GAAG,IAAI;MAClC;MACA,IAAIrG,MAAM,CAACvE,mBAAmB,EAAE;QAC5B,IAAI,CAACkE,KAAK,CAACuD,KAAK,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF;IACA,IAAIlD,MAAM,CAACpF,QAAQ,IAAIoF,MAAM,CAACpF,QAAQ,GAAG,CAAC,EAAE;MACxCsC,WAAW,CAAC/B,WAAW,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAMiD,WAAW,CAACvC,aAAa,CAACqF,MAAM,CAACpF,QAAQ,CAAC,CAAC;IACzF;IACA,IAAI,IAAI,CAACyL,kBAAkB,EAAE;MACzB;MACA;MACA,IAAI,CAACA,kBAAkB,CAACnL,cAAc,CAAC,CAAC,CAACjB,SAAS,CAAC,MAAM;QACrDiD,WAAW,CAACzD,iBAAiB,CAACoH,KAAK,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACwF,kBAAkB,CAAClM,OAAO,CAAC,CAAC;IACrC,CAAC,MACI;MACD;MACA+C,WAAW,CAACzD,iBAAiB,CAACoH,KAAK,CAAC,CAAC;IACzC;EACJ;EACA;AACJ;AACA;AACA;EACI6G,cAAcA,CAAC1H,MAAM,EAAE;IACnB,MAAMwI,aAAa,GAAG,IAAIhR,aAAa,CAAC,CAAC;IACzCgR,aAAa,CAAC5M,SAAS,GAAGoE,MAAM,CAACpE,SAAS;IAC1C,MAAM6M,gBAAgB,GAAGhR,4BAA4B,CAAC,IAAI,CAAC2H,SAAS,CAAC;IACrE;IACA,MAAMsJ,KAAK,GAAG1I,MAAM,CAACpE,SAAS,KAAK,KAAK;IACxC,MAAM+M,MAAM,GAAG3I,MAAM,CAACnE,kBAAkB,KAAK,MAAM,IAC9CmE,MAAM,CAACnE,kBAAkB,KAAK,OAAO,IAAI,CAAC6M,KAAM,IAChD1I,MAAM,CAACnE,kBAAkB,KAAK,KAAK,IAAI6M,KAAM;IAClD,MAAME,OAAO,GAAG,CAACD,MAAM,IAAI3I,MAAM,CAACnE,kBAAkB,KAAK,QAAQ;IACjE,IAAI8M,MAAM,EAAE;MACRF,gBAAgB,CAACI,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MACI,IAAID,OAAO,EAAE;MACdH,gBAAgB,CAACK,KAAK,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI;MACDL,gBAAgB,CAACM,kBAAkB,CAAC,CAAC;IACzC;IACA;IACA,IAAI/I,MAAM,CAAClE,gBAAgB,KAAK,KAAK,EAAE;MACnC2M,gBAAgB,CAACO,GAAG,CAAC,GAAG,CAAC;IAC7B,CAAC,MACI;MACDP,gBAAgB,CAACQ,MAAM,CAAC,GAAG,CAAC;IAChC;IACAT,aAAa,CAACC,gBAAgB,GAAGA,gBAAgB;IACjDD,aAAa,CAACU,iBAAiB,GAAG,IAAI,CAAC7R,mBAAmB;IAC1D,OAAOK,gBAAgB,CAAC,IAAI,CAAC0H,SAAS,EAAEoJ,aAAa,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACIX,eAAeA,CAAC7H,MAAM,EAAE9C,WAAW,EAAE;IACjC,MAAM8J,YAAY,GAAGhH,MAAM,IAAIA,MAAM,CAACtE,gBAAgB,IAAIsE,MAAM,CAACtE,gBAAgB,CAACwF,QAAQ;IAC1F,OAAOhL,QAAQ,CAAC+Q,MAAM,CAAC;MACnBX,MAAM,EAAEU,YAAY,IAAI,IAAI,CAAC5H,SAAS;MACtC8H,SAAS,EAAE,CACP;QAAEC,OAAO,EAAE7N,cAAc;QAAE8N,QAAQ,EAAElK;MAAY,CAAC,EAClD;QAAEiK,OAAO,EAAE7L,kBAAkB;QAAE8L,QAAQ,EAAEpH,MAAM,CAAChH;MAAK,CAAC;IAE9D,CAAC,CAAC;EACN;EACA,OAAOgD,IAAI,YAAAmN,oBAAAjN,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyJ,WAAW;EAAA;EAC9G,OAAOyD,KAAK,kBA/lB6E7T,EAAE,CAAA8T,kBAAA;IAAAC,KAAA,EA+lBY3D,WAAW;IAAAD,OAAA,EAAXC,WAAW,CAAA3J,IAAA;IAAAyJ,UAAA,EAAc;EAAM;AAC1I;AACA;EAAA,QAAAjJ,SAAA,oBAAAA,SAAA,KAjmB6FjH,EAAE,CAAAkH,iBAAA,CAimBJkJ,WAAW,EAAc,CAAC;IACzGtJ,IAAI,EAAE/F,UAAU;IAChBoG,IAAI,EAAE,CAAC;MAAE+I,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAM8D,UAAU,GAAG,CAAC9K,oBAAoB,EAAE1C,gBAAgB,EAAEc,kBAAkB,EAAEE,iBAAiB,CAAC;AAClG,MAAMyM,iBAAiB,CAAC;EACpB,OAAOxN,IAAI,YAAAyN,0BAAAvN,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsN,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBAzmB8EnU,EAAE,CAAAoU,gBAAA;IAAAtN,IAAA,EAymBSmN;EAAiB;EAKrH,OAAOI,IAAI,kBA9mB8ErU,EAAE,CAAAsU,gBAAA;IAAA3C,SAAA,EA8mBuC,CAACvB,WAAW,CAAC;IAAArH,OAAA,GAAY3G,aAAa,EAChKR,YAAY,EACZR,eAAe,EACfmB,eAAe,EACfmF,cAAc,EAAEnF,eAAe;EAAA;AAC3C;AACA;EAAA,QAAA0E,SAAA,oBAAAA,SAAA,KApnB6FjH,EAAE,CAAAkH,iBAAA,CAonBJ+M,iBAAiB,EAAc,CAAC;IAC/GnN,IAAI,EAAE9F,QAAQ;IACdmG,IAAI,EAAE,CAAC;MACC4B,OAAO,EAAE,CACL3G,aAAa,EACbR,YAAY,EACZR,eAAe,EACfmB,eAAe,EACfmF,cAAc,EACd,GAAGsM,UAAU,CAChB;MACDO,OAAO,EAAE,CAAChS,eAAe,EAAE,GAAGyR,UAAU,CAAC;MACzCrC,SAAS,EAAE,CAACvB,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,qBAAqB,GAAG;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE;IACX3N,IAAI,EAAE,CAAC;IACP4N,IAAI,EAAE,OAAO;IACb,aAAa,EAAE,CACX;MACI5N,IAAI,EAAE,CAAC;MACP4N,IAAI,EAAE,cAAc;MACpBhM,MAAM,EAAE;QAAE5B,IAAI,EAAE,CAAC;QAAE4B,MAAM,EAAE;UAAEiM,SAAS,EAAE,YAAY;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACI/N,IAAI,EAAE,CAAC;MACP4N,IAAI,EAAE,SAAS;MACfhM,MAAM,EAAE;QAAE5B,IAAI,EAAE,CAAC;QAAE4B,MAAM,EAAE;UAAEiM,SAAS,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACnF,CAAC,EACD;MACI/N,IAAI,EAAE,CAAC;MACPgO,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAE;QAAEjO,IAAI,EAAE,CAAC;QAAE4B,MAAM,EAAE,IAAI;QAAEsM,OAAO,EAAE;MAAmC,CAAC;MACjFC,OAAO,EAAE;IACb,CAAC,EACD;MACInO,IAAI,EAAE,CAAC;MACPgO,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE;QACPjO,IAAI,EAAE,CAAC;QACP4B,MAAM,EAAE;UAAE5B,IAAI,EAAE,CAAC;UAAE4B,MAAM,EAAE;YAAEkM,OAAO,EAAE;UAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASlP,kBAAkB,EAAEkK,6BAA6B,EAAED,qCAAqC,EAAEI,WAAW,EAAE5I,iBAAiB,EAAEF,kBAAkB,EAAEtB,iBAAiB,EAAEkD,oBAAoB,EAAE1C,gBAAgB,EAAEyN,iBAAiB,EAAElQ,cAAc,EAAE2D,cAAc,EAAE8M,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}