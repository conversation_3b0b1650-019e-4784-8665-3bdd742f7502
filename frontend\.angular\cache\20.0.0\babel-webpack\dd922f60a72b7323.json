{"ast": null, "code": "function coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport { coerceArray as c };", "map": {"version": 3, "names": ["coerce<PERSON><PERSON><PERSON>", "value", "Array", "isArray", "c"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@angular/cdk/fesm2022/array-I1yfCXUO.mjs"], "sourcesContent": ["function coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n\nexport { coerceArray as c };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,KAAK,EAAE;EACxB,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACjD;AAEA,SAASD,WAAW,IAAII,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}